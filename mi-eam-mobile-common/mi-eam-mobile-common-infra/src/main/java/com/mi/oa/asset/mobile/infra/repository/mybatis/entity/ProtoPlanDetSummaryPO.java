package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/07/28/11:04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("proto_plan_det_summary")
public class ProtoPlanDetSummaryPO implements Serializable {
    /**
     *  id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 内购价格 actual_damages
     */
    private BigDecimal actualDamages;

    /**
     * 内购计划id inner_purchase_plan_id
     */
    private Integer innerPurchasePlanId;

    /**
     * 上市日期 list_date
     */
    private Date listDate;

    /**
     * 上市后描述 list_desc
     */
    private String listDesc;

    /**
     * 当前市价 market_price
     */
    private BigDecimal marketPrice;

    /**
     * 物料描述 sku_name
     */
    private String skuName;

    /**
     * 小米料号 sku_code
     */
    private String skuCode;

    /**
     * 数量 num
     */
    private Integer num;

    /**
     * 定价依据 price_basis
     */
    private String priceBasis;

    /**
     * 定价配置编码 price_code
     */
    private String priceCode;

    /**
     * 配置描述 price_desc
     */
    private String priceDesc;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 参考内购价 reference_damages
     */
    private BigDecimal referenceDamages;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}