package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/01/18/02:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "fun_status",autoResultMap = true)
public class FunStatusPO {
    /**
     * 主键 status_id
     */
    private String statusId;

    /**
     * 功能ID fun_id
     */
    private String funId;

    /**
     * 控件代号 control_code
     */
    private String controlCode;

    /**
     * 未提交值 audit0
     */
    private String audit0;

    /**
     * 已提交值 audit1
     */
    private String audit1;

    /**
     * 审批中值 audit2
     */
    private String audit2;

    /**
     * 审批通过值 audit3
     */
    private String audit3;

    /**
     * 审批否决值 audit4
     */
    private String audit4;

    /**
     * 退回值 audit_b
     */
    private String auditB;

    /**
     * 终止值 audit_e
     */
    private String auditE;

    /**
     * 说明 status_memo
     */
    private String statusMemo;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}