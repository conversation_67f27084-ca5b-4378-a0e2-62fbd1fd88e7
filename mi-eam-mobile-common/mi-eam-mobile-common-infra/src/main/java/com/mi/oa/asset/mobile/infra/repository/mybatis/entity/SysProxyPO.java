package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/07/12/02:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "sys_proxy",autoResultMap = true)
public class SysProxyPO {
    /**
     * 主键 proxy_id
     */
    private String proxyId;

    /**
     * 记录状态 auditing
     */
    private String auditing;

    /**
     * 设置日期 set_date
     */
    private Date setDate;

    /**
     * 设置人 set_user
     */
    private String setUser;

    /**
     * 代理人ID user_id
     */
    private String userId;

    /**
     * 代理人账号 user_code
     */
    private String userCode;

    /**
     * 代理人 user_name
     */
    private String userName;

    /**
     * 被代理人ID to_user_id
     */
    private String toUserId;

    /**
     * 被代理人账号 to_user_code
     */
    private String toUserCode;

    /**
     * 被代理人 to_user_name
     */
    private String toUserName;

    /**
     * 代理结束日期 end_date
     */
    private Date endDate;

    /**
     * 代理开始日期 start_date
     */
    private Date startDate;

    /**
     * 设置说明 set_memo
     */
    private String setMemo;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}