package com.mi.oa.asset.mobile.infra.dto.mdm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/12
 */
@Data
public class MdmBaseVO<T> {

    private String msg;

    private int total;

    @SerializedName("pageIndex")
    @JsonProperty("pageIndex")
    private int pageNum;

    private int pageSize;

    @SerializedName("dataList")
    @JsonProperty("dataList")
    private List<T> list;
}
