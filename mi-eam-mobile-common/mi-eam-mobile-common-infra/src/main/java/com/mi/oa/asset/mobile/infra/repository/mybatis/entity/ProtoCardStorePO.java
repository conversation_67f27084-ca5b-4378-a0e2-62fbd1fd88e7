package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/05/17/03:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_card_store",autoResultMap = true)
public class ProtoCardStorePO {
    /**
     *  id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务类型 mrp_type
     */
    private String mrpType;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 试产阶段 stage_name
     */
    private String stageName;

    /**
     * 类别 sku_code_type
     */
    private String skuCodeType;

    /**
     * 小米料号 sku_code
     */
    private String skuCode;

    /**
     * 描述 sku_name
     */
    private String skuName;

    /**
     * 新旧属性 asset_newold
     */
    private String assetNewold;

    /**
     * 是否良品 is_complete
     */
    private String isComplete;

    /**
     * 库房编码
     */
    private String houseCode;

    /**
     * 库房数量
     */
    private BigDecimal num;

    /**
     * 占用领用单号 occupy_num
     */
    private BigDecimal occupyNum;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}