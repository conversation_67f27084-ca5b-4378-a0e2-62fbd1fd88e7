package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.Date;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/1
 */
@Data
@TableName(value = "proto_asn_order")
public class ProtoAsnOrderPO {

    @TableId(type = IdType.INPUT)
    private String orderId;

    /**
     * ASN 单号
     */
    @SerializedName("ZZASN")
    private String orderCode;

    /**
     * 送货单类型
     */
    @SerializedName("ZZASN_TYPE")
    private String asnType;

    /**
     * 收货人地址
     */
    @SerializedName("ZZSHIP_ADDR")
    private String shipAddr;

    /**
     * 收货人姓名
     */
    @SerializedName("ZZSHIP_NAME")
    private String shipName;

    /**
     * 收货人电话
     */
    @SerializedName("ZZSHIP_PHON")
    private String shipPhone;

    /**
     * 承运商名称
     */
    @SerializedName("ZZCARRIER")
    private String carrier;

    /**
     * 送货员/司机姓名
     */
    @SerializedName("ZZDRIVER_NAME")
    private String driverName;

    /**
     * 送货员电话
     */
    @SerializedName("ZZDRIVER_PHON")
    private String driverPhone;

    /**
     * 预计送达日期
     */
    @SerializedName("ZZARRIVE_TIME")
    private String arriveTime;

    /**
     * 承运单号
     */
    @SerializedName("ZZCARRIER_NO")
    private String carrierNo;

    /**
     * 运输车型
     */
    @SerializedName("ZZTRANS_MODEL")
    private String transModel;

    /**
     * 司机驾驶证号
     */
    @SerializedName("ZZDRIVER_LICENSE")
    private String driverLicense;

    /**
     * 车牌号
     */
    @SerializedName("ZZLICENSE_NUMB")
    private String licenseNumb;

    /**
     * 已取消
     */
    @SerializedName("ZZCANCELED")
    private String canceled;

    /**
     * 铅封号
     */
    @SerializedName("ZZSEAL_NO")
    private String sealNo;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名
     */
    private String projectName;

    /**
     * 0：未收货
     * 1：部分收货
     * 2：完全收货
     * 5: 已取消
     */
    private Integer orderStatus;

    /**
     * 出货工厂编号
     */
    private String factoryCode;

    /**
     * 出货工厂
     */
    private String factoryName;

    /**
     * 出货日期
     */
    private String outDate;

    /**
     * 最后收货日期
     */
    private Date inDate;

    /**
     * 最后收货人账号
     */
    private String userCode;

    /**
     * 最后收货人
     */
    private String userName;

    /**
     * 最后收货人工号
     */
    private String empCode;

    /**
     * 是否删除1：是 0：否
     */
    private Integer isDelete;

    /**
     * 采购组织
     */
    private String ekorg;

    /**
     * 是否开始收货 0：没有开始收货 1：开始收货
     */
    private String isReceipt;

    /**
     * 采购凭证类型 ZNB:原材料 ZWW:成品、半成品 ZDB 米通模式
     */
    private String bsart;

    /**
     * 业务类型 手机：mobile 电视：tv 生态链：ecochain 笔记本电脑：laptop 可穿戴：wearable 机器人：robot
     */
    private String mrpType;

    /**
     * sap工厂
     */
    private String sapFactory;
    /**
     * 代工厂编码  （昌平-米通试产采购订单模式 新增字段  SRM.VDATA.XM959 返回字段LIFNR_DG）
     */
    @SerializedName("LIFNR_DG")
    private String foundryCode;

    /**
     * 试产阶段
     */
    private String stageName;

    /**
     * 试产阶段id
     */
    private String stageId;

    /**
     * 添加日期
     */
    private Date addDate;

    /**
     * 修改日期
     */
    private Date modifyDate;

    /**
     * 同步状态 默认0
     */
    private String synStatus;

    /**
     * 发货数量
     */
    private Integer outNum;

    /**
     * 收货数量
     */
    private Integer inNum;

    /**
     * 未收货数据
     */
    private Integer dueInNum;

    /**
     * '仓库名称'
     */
    private String houseName;

    /**
     * '仓库ID'
     */
    private String houseId;

    /**
     * '仓库编号'
     */
    private String houseCode;
}
