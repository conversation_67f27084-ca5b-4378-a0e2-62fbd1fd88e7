package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/01/24/06:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_third_user",autoResultMap = true)
public class ProtoThirdUserPO implements Serializable {
    /**
     * 主键 user_id
     */
    @TableId(type = IdType.INPUT)
    private String userId;

    /**
     * 记录状态 auditing
     */
    private String auditing;

    /**
     * 人员姓名 user_name
     */
    private String userName;

    /**
     * 人员工号 emp_code
     */
    private String empCode;

    /**
     * 人员账号 user_code
     */
    private String userCode;

    /**
     * P账号成本中心 center_code
     */
    private String centerCode;

    /**
     * 二手库领用权限 is_old_out_right
     */
    private String isOldOutRight;

    /**
     * 是否离职 is_quit
     */
    private String isQuit;

    /**
     * 交接账号 handover_code
     */
    private String handoverCode;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 成本中心名称 center_name
     */
    private String centerName;

    /**
     * 供应商编码 provider_code
     */
    private String providerCode;

    /**
     * 供应商名称 provider_name
     */
    private String providerName;

    /**
     * 邮箱 email
     */
    private String email;

    /**
     * 账号有效期
     */
    private Date validDate;

    /**
     * 业务类型
     */
    private String mrpType;
}