package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/01/05/03:58
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_apply_mat",autoResultMap = true)
public class ProtoApplyMatPO {
    /**
     * 主键 apply_mat_id
     */
    @TableId(type = IdType.INPUT)
    private String applyMatId;

    /**
     * 料号 sku_code
     */
    private String skuCode;

    /**
     * 物料描述 sku_name
     */
    private String skuName;

    /**
     * 申请数量 apply_num
     */
    private BigDecimal applyNum;

    /**
     * 其中保密套拆除数量 secret_num
     */
    private BigDecimal secretNum;

    /**
     * 备注 det_remark
     */
    private String detRemark;

    /**
     * 领用申请主键 apply_id
     */
    private String applyId;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 已发放数量 out_num
     */
    private BigDecimal outNum;

    /**
     * 已申请待发放数量 due_claimed_num
     */
    private BigDecimal dueClaimedNum;

    /**
     * 已发放拆保密套量 out_open_num
     */
    private BigDecimal outOpenNum;

    /**
     * SAP行号 sap_item_row_no
     */
    private String sapItemRowNo;

    /**
     * 业务类型
     */
    private String mrpType;

    /**
     * 商品ID
     */
    private String goodsId;
}