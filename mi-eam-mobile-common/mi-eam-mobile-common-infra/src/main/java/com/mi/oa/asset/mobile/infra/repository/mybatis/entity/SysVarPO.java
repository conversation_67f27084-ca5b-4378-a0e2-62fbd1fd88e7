package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/01/10/03:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "sys_var",autoResultMap = true)
public class SysVarPO {
    /**
     * 属性ID var_id
     */
    @TableId(type = IdType.INPUT)
    private String varId;

    /**
     * 属性编码 var_code
     */
    private String varCode;

    /**
     * 属性名称 var_name
     */
    private String varName;

    /**
     * 属性值 var_value
     */
    private String varValue;

    /**
     * 属性状态 var_status
     */
    private String varStatus;

    /**
     * 属性描述 var_memo
     */
    private String varMemo;

    /**
     * 记录添加人 add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人 modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 用于页面 use_page
     */
    private String usePage;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}