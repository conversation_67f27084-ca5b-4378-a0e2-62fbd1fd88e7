package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.util.Date;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/01/24/05:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_project_list", autoResultMap = true)
public class ProtoProjectListPO {
    /**
     * 主键 project_list_id
     */
    @TableId(type = IdType.INPUT)
    private String projectListId;

    /**
     * 项目名称 project_name
     */
    private String projectName;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 父级code parent_code
     */
    private String parentCode;

    /**
     * 是否新旧 is_old
     */
    private BigDecimal isOld;

    /**
     * 业务线类型 project_type
     */
    private String projectType;

    /**
     * PM名称 pm_user_name
     */
    private String pmUserName;

    /**
     * PM工号 pm_emp_code
     */
    private String pmEmpCode;

    /**
     * PM账号 pm_user_code
     */
    private String pmUserCode;

    /**
     * 记录状态 auditing
     */
    private String auditing;

    /**
     * 源项目ID
     */
    private String oriProjectId;

    /**
     * 项目类型 产品项目 | ODM项目 | 预研项目 | 可穿戴项目
     */
    private String type;

    /**
     * 项目等级  S类|A类|B类|C类
     */
    private String projectLevel;

    /**
     * 产品类别
     */
    private String productType;

    /**
     * 产品系列
     */
    private String productSeries;

    /**
     * 业务类型
     */
    private String mrpType;
}