package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/08/23/06:42
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_pay_account",autoResultMap = true)
public class ProtoPayAccountPO {
    /**
     *  id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务类型 mrp_type
     */
    private String mrpType;

    /**
     * 对账日期 account_date
     */
    private Date accountDate;

    /**
     * 支付渠道 pay_order_type
     */
    private String payOrderType;

    /**
     * 渠道商户号 channel_num
     */
    private String channelNum;

    /**
     * 收款金额 pay_amount
     */
    private BigDecimal payAmount;

    /**
     * 退款金额 refund_amount
     */
    private BigDecimal refundAmount;

    /**
     * 手续费 charge_amount
     */
    private BigDecimal chargeAmount;

    /**
     * 净收益 net_earning
     */
    private BigDecimal netEarning;

    /**
     * 入账总流水 enter_account_amount
     */
    private BigDecimal enterAccountAmount;

    /**
     * 出账总流水 out_account_amount
     */
    private BigDecimal outAccountAmount;

    /**
     * 交易手续费净值 sum_charge_amount
     */
    private BigDecimal sumChargeAmount;

    /**
     * 提现金额 draw_amount
     */
    private BigDecimal drawAmount;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}