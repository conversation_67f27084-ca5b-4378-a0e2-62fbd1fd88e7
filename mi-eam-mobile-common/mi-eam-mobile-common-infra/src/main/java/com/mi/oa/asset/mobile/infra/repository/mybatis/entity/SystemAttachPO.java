package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Copyright (c) 2022 XiaoMi Inc.All Rights Reserved.
 * Description:
 *
 * <AUTHOR>
 * @date 2022/7/11 09:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName("sys_attach")
public class SystemAttachPO implements Serializable {
    /**
     * 附件ID attach_id
     */
    @TableId(value = "attach_id", type = IdType.INPUT)
    private String attachId;

    /**
     * 表名 table_name
     */
    private String tableName;

    /**
     * 记录ID data_id
     */
    private String dataId;

    /**
     * 附件名称 attach_name
     */
    private String attachName;

    /**
     * 文件类型 content_type
     */
    private String contentType;

    /**
     * 相关字段 attach_field
     */
    private String attachField;

    /**
     * 功能ID fun_id
     */
    private String funId;

    /**
     * 功能名称 fun_name
     */
    private String funName;

    /**
     * 记录添加人 add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人 modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 上传人 upload_user
     */
    private String uploadUser;

    /**
     * 上传日期 upload_date
     */
    private Date uploadDate;

    /**
     * 附件路径 attach_path
     */
    private String attachPath;

    /**
     * 附件类型 attach_type
     */
    private String attachType;

    /**
     * 附件库ID store_id
     */
    private String storeId;

    /**
     * 附件库 store_no
     */
    private BigDecimal storeNo;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 文件大小 file_size
     */
    private BigDecimal fileSize;
}