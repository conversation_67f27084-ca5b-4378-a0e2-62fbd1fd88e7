package com.mi.oa.asset.mobile.infra.remote.entity;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/9/28 19:14
 */

@Data
@Builder
public class BpmDTO {

    private String processKey;

    private String status;

    private String processTitle;

    private String startUser;

    @Builder.Default
    private Map<String, Object> variables = new HashMap<>();

    @Builder.Default
    private List<Approver> approvePathList = new ArrayList<>();

    private Object business;

    @Builder.Default
    private String webContent = "";

    @Builder.Default
    private String appContent = "";

    private String businessKey;

    @Data
    @Builder
    public static class Approver {

        private String task;

        private String approver;
    }

    public void addVariable(String key, Object value) {
        variables.put(key, value);
    }

}


