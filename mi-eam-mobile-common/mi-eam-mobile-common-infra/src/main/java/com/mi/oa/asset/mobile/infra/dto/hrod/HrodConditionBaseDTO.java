package com.mi.oa.asset.mobile.infra.dto.hrod;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @Desc HROD 接口条件定义基类
 * <AUTHOR>
 * @Date 2021/9/9 21:57
 */

@Data
@SuperBuilder
@NoArgsConstructor
public class HrodConditionBaseDTO {

    @Builder.Default
    private Integer pageNum = 1;

    @Builder.Default
    private Integer pageSize = 50;

    @SerializedName("key")
    @JsonProperty("key")
    private String keyword;
}


