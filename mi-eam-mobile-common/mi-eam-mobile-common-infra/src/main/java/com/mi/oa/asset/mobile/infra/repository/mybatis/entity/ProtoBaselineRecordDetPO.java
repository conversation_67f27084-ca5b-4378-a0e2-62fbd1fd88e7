package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.math.BigDecimal;
import java.util.Date;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/03/03/05:25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_baseline_record_det",autoResultMap = true)
public class ProtoBaselineRecordDetPO {
    /**
     * 主键 baseline_record_det_id
     */
    private String baselineRecordDetId;

    /**
     * 基线变更单id baseline_record_id
     */
    private String baselineRecordId;

    /**
     * 基线类型 baseline_type
     */
    private String baselineType;

    /**
     * 调整前 baseline_upd_before
     */
    private String baselineUpdBefore;

    /**
     * 调整后 baseline_upd_after
     */
    private String baselineUpdAfter;

    /**
     * 调整类型 baseline_upd_type
     */
    private String baselineUpdType;

    /**
     * 基线类型排序 baseline_type_serno
     */
    private BigDecimal baselineTypeSerno;

    /**
     * 辅助功能字段 baseline_assist
     */
    private String baselineAssist;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}