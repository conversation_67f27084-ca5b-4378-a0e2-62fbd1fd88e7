package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/01/10/03:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "sys_tablecode")
public class SysTableCodePO {

    /**
     * 表名 table_name
     */
    private String tableName;

    /**
     * 扩展缺省为年月 code_ext
     */
    private String codeExt;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 最大值 max_value
     */
    private Integer maxValue;
}