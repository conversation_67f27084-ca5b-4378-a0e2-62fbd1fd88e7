package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;

import java.math.BigDecimal;
import java.util.Date;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/02/25/02:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_unreturn_mat",autoResultMap = true)
public class ProtoUnReturnMatPO {
    /**
     * 主键 unreturn_mat_id
     */
    @TableId(type = IdType.INPUT)
    private String unreturnMatId;

    /**
     * 小米料号 sku_code
     */
    private String skuCode;

    /**
     * 物料描述 sku_name
     */
    private String skuName;

    /**
     * 设备编号SN device_code
     */
    private String deviceCode;

    /**
     * 领用单号 apply_code
     */
    private String applyCode;

    /**
     * 备注 remark
     */
    private String remark;

    /**
     * 无法归还ID unreturn_id
     */
    private String unreturnId;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 镭雕号 laser_code
     */
    private String laserCode;

    /**
     * IMEI号 imei
     */
    private String imei;

    /**
     * 试产阶段 stage_name
     */
    private String stageName;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 新旧属性 asset_newold
     */
    private String assetNewold;

    /**
     * 校验状态 check_status
     */
    private String checkStatus;

    /**
     * 校验结果 check_result
     */
    private String checkResult;

    /**
     * '保密套是否拆除'
     */
    private String isOpen;

    /**
     * 上市时间
     */
    private Date listDate;

    /**
     * '上市后描述'
     */
    private String listDesc;

    /**
     * '参考赔偿金额'
     */
    private BigDecimal referenceDamages;

    /**
     * '实际赔偿金额'
     */
    private BigDecimal actualDamages;

    /**
     * '处置依据'
     */
    private String disposalBasis;
}