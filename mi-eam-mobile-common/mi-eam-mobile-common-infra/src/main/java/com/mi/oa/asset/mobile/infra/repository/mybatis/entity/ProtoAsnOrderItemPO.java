package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/1
 */
@Data
@TableName("proto_asn_order_item")
public class ProtoAsnOrderItemPO {

    @TableId(type = IdType.INPUT)
    private String itemId;

    /**
     * asn单号
     */
    @SerializedName("ZZASN")
    private String asnNo;

    /**
     * ASN行号
     */
    @SerializedName("ZZITEM_ID")
    private String itemRowNo;

    /**
     * 采购单号
     */
    @SerializedName("ZZPO_NO")
    private String poNo;

    /**
     * 采购单PO行项目
     */
    @SerializedName("ZZPO_ITEM_ID")
    private String poItemId;

    /**
     * 小米料号
     */
    private String skuCode;

    /**
     * 小米料号描述
     */
    @SerializedName("MAKTX")
    private String skuName;

    /**
     * 商品ID
     */
    private String goodsId;

    /**
     * 小米物料号
     */
    @SerializedName("ZZPRODUCT_ID")
    private String productId;

    /**
     * 工厂料号
     */
    @SerializedName("ZZVPRODUCT_ID")
    private String vproductId;

    /**
     * 老系统sku字段
     */
    @SerializedName("ZZSKU")
    private String sku;

    /**
     * 订单单位计的数量(发货数量)
     */
    @SerializedName("ZZQUANTITY")
    private BigDecimal quantity;

    /**
     * 计量单位
     */
    @SerializedName("ZZUNIT")
    private String unit;

    /**
     * 新增时间
     */
    private LocalDateTime addDate;

    /**
     * 修改时间
     */
    private LocalDateTime modifyDate;

    /**
     * 品牌
     */
    @SerializedName("BRAND")
    private String brand;

    /**
     * 产地
     */
    @SerializedName("PLACE")
    private String place;

    /**
     * 是否删除1：是 0：否
     */
    private Integer isDelete;

    /**
     * 老系统is_identification字段
     */
    @SerializedName("IS_IDENTIFICATION")
    private String isIdentification;

    /**
     * 业务类型 手机：mobile 电视：tv 生态链：ecochain 笔记本电脑：laptop 可穿戴：wearable 机器人：robot
     */
    private String mrpType;

    /**
     * 项目编号
     */
    @SerializedName("APP_MODELS")
    private String projectCode;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 与proto_asn_order表关联
     */
    private String orderId;
}
