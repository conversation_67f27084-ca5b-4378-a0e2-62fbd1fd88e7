package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/08/01/10:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_order", autoResultMap = true)
public class ProtoOrderPO {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务线
     */
    private String mrpType;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 内购类型
     */
    private String purchaseType;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 内购计划号 plan_code
     */
    private String planCode;

    /**
     * 内购计划主题 plan_theme
     */
    private String planTheme;

    /**
     * 内购状态 order_status
     */
    private String orderStatus;

    /**
     * 商城售价 salePrice
     */
    private BigDecimal salePrice;

    /**
     * 购买金额 pay_amount
     */
    private BigDecimal payAmount;

    /**
     * 购买人姓名 user_name
     */
    private String userName;

    /**
     * 购买人账号 user_code
     */
    private String userCode;

    /**
     * 工号
     */
    private String empCode;

    /**
     * 原始台账状态
     */
    private String originCardStatus;

    /**
     * 原使用人账号
     */
    private String oriUserCode;

    /**
     * 原使用人姓名
     */
    private String oriUserName;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门
     */
    private String deptName;

    /**
     * 支付方式 pay_way
     */
    private String payWay;

    /**
     * 支付链接
     */
    private String payLink;

    /**
     * 设备编号SN device_code
     */
    private String deviceCode;

    /**
     * 小米料号 sku_code
     */
    private String skuCode;

    /**
     * 物料描述 sku_name
     */
    private String skuName;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 镭雕号 laser_code
     */
    private String laserCode;

    /**
     * IMEI号 imei
     */
    private String imei;

    /**
     * 自提地址编码
     */
    private String locationCode;

    /**
     * 自提地址名称
     */
    private String locationName;

    /**
     * 价格编码
     */
    private String priceCode;

    /**
     * 成色
     */
    private String newPercent;

    /**
     * 销售订单 sales_order
     */
    private String salesOrder;

    /**
     * 交易订单号 trade_order_code
     */
    private String tradeOrderCode;

    /**
     * 交易流水号 trade_water_code
     */
    private String tradeWaterCode;

    /**
     * 交易时间 trade_date
     */
    private Date tradeDate;

    /**
     * 订单创建人 add_user_name
     */
    private String addUserName;

    /**
     * 订单更新人 modify_user_name
     */
    private String modifyUserName;

    /**
     * 创建人账号 add_userid
     */
    private String addUserid;

    /**
     * 订单创建时间 add_date
     */
    private Date addDate;

    /**
     * 更新人账号 modify_userid
     */
    private String modifyUserid;

    /**
     * 订单更新时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 订单支付方式 1:小米钱包 2:支付宝
     */
    private String payOrderType;

    /**
     * 提货方式，self：自提，post：邮寄
     */
    private String deliveryMethod;

    /**
     * 收货人
     */
    private String consignee;

    /**
     * 收货人手机号
     */
    private String consigneeMobile;

    /**
     * 收货人地址
     */
    private String consigneeAddress;

    /**
     * 售后订单处理状态 0-未提交，1-已提交，2-已确认
     */
    private Integer processStatus;

    /**
     * 售后类型：退货退款-1，换货-2
     */
    private Integer afterSaleType;

    /**
     * 售后原因
     */
    private String afterSaleReason;
}