package com.mi.oa.asset.mobile.infra.dto.hrod;

import com.google.gson.JsonElement;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Type;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/19 20:21
 */

public class ListToCommaStringAdapter implements JsonSerializer<List<Comparable>> {

    @Override
    public JsonElement serialize(List list, Type type, JsonSerializationContext jsonSerializationContext) {
        if(CollectionUtils.isEmpty(list)) return null;

        return new JsonPrimitive(StringUtils.join(list, ','));
    }
}
