package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/04/14/04:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_factory_list",autoResultMap = true)
public class ProtoFactoryListPO {
    /**
     * 主键 factory_list_id
     */
    @TableId(type = IdType.INPUT)
    private String factoryListId;

    /**
     * 工厂名称 factory_name
     */
    private String factoryName;

    /**
     * 工厂编号 factory_code
     */
    private String factoryCode;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 采购组织 purchase_org
     */
    private String purchaseOrg;

    /**
     * 状态 status
     */
    private String status;

    /**
     * 代工厂代码 proxy_factory_code
     */
    private String proxyFactoryCode;

    /**
     * 代工厂名称 proxy_factory_name
     */
    private String proxyFactoryName;

    /**
     * 代工厂简称 proxy_factory_short_name
     */
    private String proxyFactoryShortName;

    /**
     * 代工厂类型 type
     */
    private String type;

    /**
     * 手机业务模块 mobile_business_type
     */
    private String mobileBusinessType;

    /**
     * 电视业务模块 tv_business_type
     */
    private String tvBusinessType;

    /**
     * 代工厂-工厂结合名称 proxy_union_factory_name
     */
    private String proxyUnionFactoryName;

    /**
     * 生态链业务模块 eco_business_type
     */
    private String ecoBusinessType;

    /**
     * 记录状态 auditing
     */
    private String auditing;
}