package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/01/06/02:51
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "mail_template_param",autoResultMap = true)
public class MailTemplateParamPO {
    /**
     * 主键 param_id
     */
    private String paramId;

    /**
     * 序号 param_index
     */
    private BigDecimal paramIndex;

    /**
     * 名称 param_name
     */
    private String paramName;

    /**
     * 标题 param_title
     */
    private String paramTitle;

    /**
     * 缺省值 param_value
     */
    private String paramValue;

    /**
     * 模板ID template_id
     */
    private String templateId;

    /**
     * 类型 param_type
     */
    private String paramType;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}