package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
<<<<<<< HEAD
 * @date 2022/06/15/03:51
=======
 * @date 2022/06/22/03:26
>>>>>>> huyong-tran
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_dispose_rule",autoResultMap = true)
public class ProtoDisposeRulePO {
    /**
     *  id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 处置序列号 order_code
     */
    private String orderCode;

    /**
     * 业务分类 bus_sub_type
     */
    private String busSubType;

    /**
     * 业务描述 remark
     */
    private String remark;

    /**
     * 状态 auditing
     */
    private String auditing;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 处置类型 disposal_type
     */
    private String disposalType;
}