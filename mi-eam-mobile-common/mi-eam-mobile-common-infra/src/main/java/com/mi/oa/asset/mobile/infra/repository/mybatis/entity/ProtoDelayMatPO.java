package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/02/18/05:43
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_delay_mat",autoResultMap = true)
public class ProtoDelayMatPO {
    /**
     * 主键 delay_mat_id
     */
    @TableId(type = IdType.INPUT)
    private String delayMatId;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 试产阶段 stage_name
     */
    private String stageName;

    /**
     * 配置 device_type
     */
    private String deviceType;

    /**
     * 镭雕号 laser_code
     */
    private String laserCode;

    /**
     * IMEI号 imei
     */
    private String imei;

    /**
     * 设备编号SN device_code
     */
    private String deviceCode;

    /**
     * 当前归还时间 end_date
     */
    private Date endDate;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 延期归还申请ID delay_id
     */
    private String delayId;

    /**
     * 小米料号 sku_code
     */
    private String skuCode;

    /**
     * 描述 sku_name
     */
    private String skuName;

    /**
     * 延期归还时间 delay_date
     */
    private Date delayDate;

    /**
     * 新旧属性 asset_newold
     */
    private String assetNewold;
}