package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/01/20/05:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "funall_control",autoResultMap = true)
public class FunAllControlPO {
    /**
     * 控件ID control_id
     */
    private String controlId;

    /**
     * 控件类型 control_type
     */
    private String controlType;

    /**
     * 控件代码 control_code
     */
    private String controlCode;

    /**
     * 控件名称 control_name
     */
    private String controlName;

    /**
     * 控件值 value_data
     */
    private String valueData;

    /**
     * 显示值 display_data
     */
    private String displayData;

    /**
     * 控件序号 control_index
     */
    private BigDecimal controlIndex;

    /**
     * 布局页面 layout_page
     */
    private String layoutPage;

    /**
     * 功能ID fun_id
     */
    private String funId;

    /**
     * 记录添加人 add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人 modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 控件属性 control_prop
     */
    private String controlProp;

    /**
     * 英文描述 title_en
     */
    private String titleEn;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 控件状态 control_state
     */
    private String controlState;

    /**
     * T1描述 title_t1
     */
    private String titleT1;

    /**
     * T2描述 title_t2
     */
    private String titleT2;

    /**
     * T3描述 title_t3
     */
    private String titleT3;
}