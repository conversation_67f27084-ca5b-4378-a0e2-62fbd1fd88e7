package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/03/24/06:51
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_card_sku_total",autoResultMap = true)
public class ProtoCardSkuTotalPO {
    /**
     *  total_id
     */
    @TableId(type = IdType.INPUT)
    private Integer totalId;

    /**
     * 业务类型 mrp_type
     */
    private String mrpType;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * PM pm
     */
    private String pm;

    /**
     * 上市时间 list_date
     */
    private Date listDate;

    /**
     * 阶段 stage_name
     */
    private String stageName;

    /**
     * 类别 sku_code_type
     */
    private String skuCodeType;

    /**
     * 小米料号 sku_code
     */
    private String skuCode;

    /**
     * 描述 sku_name
     */
    private String skuName;

    /**
     * 合计 total_num
     */
    private BigDecimal totalNum;

    /**
     * 在库一手 zkys_num
     */
    private BigDecimal zkysNum;

    /**
     * 在库二手 zkes_num
     */
    private BigDecimal zkesNum;

    /**
     * 一手在用 yszy_num
     */
    private BigDecimal yszyNum;

    /**
     * 二手在用 eszy_num
     */
    private BigDecimal eszyNum;

    /**
     * 无法归还 wfgh_num
     */
    private BigDecimal wfghNum;

    /**
     * 已报废 ybf_num
     */
    private BigDecimal ybfNum;

    /**
     * 已扣款 ykk_num
     */
    private BigDecimal ykkNum;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}