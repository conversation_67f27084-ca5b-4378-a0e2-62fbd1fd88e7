package com.mi.oa.asset.mobile.infra.dto.srm;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/2
 */
@Data
public class PoDetailListDTO {

    @SerializedName("ET_HEADER")
    private List<PoDetailDTO> list;


    @Data
    public static class PoDetailDTO{

        /**
         * PO单号
         */
        @SerializedName("ZZPO_NO")
        private String poNo;

        /**
         * 供货商编码
         */
        @SerializedName("LIFNR")
        private String supplierCode;

        /**
         * 供货商名称
         */
        @SerializedName("VENDOR_NAME")
        private String supplierName;

        /**
         * ZNB:原材料 ZWW:成品、半成品
         */
        @SerializedName("BSART")
        private String purchaseType;

        /**
         * 采购组织
         */
        @SerializedName("EKORG")
        private String organization;

        @SerializedName("ITEMS")
        private List<PoItemDTO> items;

    }

    @Data
    public static class PoItemDTO {

        @SerializedName("WERKS")
        private String sapFactory;

        @SerializedName("LGORT")
        private String warehouse;
    }
}
