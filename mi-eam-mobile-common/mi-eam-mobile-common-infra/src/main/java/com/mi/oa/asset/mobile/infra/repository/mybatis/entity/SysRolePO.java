package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/01/25/03:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "sys_role",autoResultMap = true)
public class SysRolePO {
    /**
     * 角色ID role_id
     */
    @TableId(type = IdType.INPUT)
    private String roleId;

    /**
     * 角色名称 role_name
     */
    private String roleName;

    /**
     * 角色描述 role_memo
     */
    private String roleMemo;

    /**
     * 记录添加人 add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人 modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 模板ID templet_id
     */
    private String templetId;

    /**
     * 模板名称 templet_name
     */
    private String templetName;

    /**
     * 部门ID dept_id
     */
    private String deptId;

    /**
     * 部门名称 dept_name
     */
    private String deptName;

    /**
     * 角色编号 role_no
     */
    private String roleNo;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 版本类型 bus_type
     */
    private String busType;

    /**
     * 类型 role_type
     */
    private String roleType;

    /**
     * 是否申请 is_apply
     */
    private String isApply;

    /**
     * 业务类型
     */
    private String mrpType;
}