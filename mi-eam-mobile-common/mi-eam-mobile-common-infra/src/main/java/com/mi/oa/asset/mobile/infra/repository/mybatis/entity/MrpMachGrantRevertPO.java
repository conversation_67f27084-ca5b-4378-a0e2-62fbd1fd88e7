package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/03/30/09:41
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "mrp_mach_grant_revert",autoResultMap = true)
public class MrpMachGrantRevertPO {
    /**
     *  id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private String id;

    /**
     *  sku_mat_code
     */
    private String skuMatCode;

    /**
     *  goods_id
     */
    private String goodsId;

    /**
     *  sku_mat_desc
     */
    private String skuMatDesc;

    /**
     *  sn
     */
    private String sn;

    /**
     *  apply_no
     */
    private String applyNo;

    /**
     *  use_code
     */
    private String useCode;

    /**
     *  use_name
     */
    private String useName;

    /**
     *  grant_no
     */
    private String grantNo;

    /**
     *  type
     */
    private String type;

    /**
     *  un_revert_no
     */
    private String unRevertNo;

    /**
     *  apply_user
     */
    private String applyUser;

    /**
     *  apply_display_name
     */
    private String applyDisplayName;

    /**
     *  grant_time 发放时间
     */
    private Date grantTime;

    /**
     *  revert_no
     */
    private String revertNo;

    /**
     *  revert_apply_time
     */
    private Date revertApplyTime;

    /**
     *  un_revert_apply_date
     */
    private String unRevertApplyDate;

    /**
     *  un_revert_approval_time
     */
    private Date unRevertApprovalTime;

    /**
     *  apply_date
     */
    private String applyDate;

    /**
     *  revert_time 归还时间
     */
    private Date revertTime;

    /**
     *  grant_user
     */
    private String grantUser;

    /**
     *  revert_user
     */
    private String revertUser;

    /**
     *  status
     */
    private String status;

    /**
     *  un_revert_remark
     */
    private String unRevertRemark;

    /**
     *  add_user
     */
    private String addUser;

    /**
     *  add_display_name
     */
    private String addDisplayName;

    /**
     *  add_time
     */
    private Date addTime;

    /**
     *  update_user
     */
    private String updateUser;

    /**
     *  update_display_name
     */
    private String updateDisplayName;

    /**
     *  update_time
     */
    private Date updateTime;

    /**
     *  original_apply_user
     */
    private String originalApplyUser;

    /**
     *  original_apply_display_name
     */
    private String originalApplyDisplayName;

    /**
     *  project
     */
    private String project;

    /**
     *  product_stage
     */
    private String productStage;

    /**
     *  apply_row_num
     */
    private String applyRowNum;

    /**
     *  grant_row_num
     */
    private String grantRowNum;

    /**
     *  mrp_type
     */
    private String mrpType;

    /**
     *  sn_extra
     */
    private String snExtra;

    /**
     *  is_dismantle
     */
    private String isDismantle;

    /**
     *  dismantle_user
     */
    private String dismantleUser;

    /**
     *  dismantle_display_name
     */
    private String dismantleDisplayName;

    /**
     *  dismantle_time
     */
    private Date dismantleTime;

    /**
     *  is_confirm
     */
    private String isConfirm;
}