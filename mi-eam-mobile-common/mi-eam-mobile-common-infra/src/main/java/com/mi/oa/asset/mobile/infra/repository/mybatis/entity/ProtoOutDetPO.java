package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/01/20/02:34
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_out_det",autoResultMap = true)
public class ProtoOutDetPO {
    /**
     * 主键 det_id
     */
    @TableId(type = IdType.INPUT)
    private String detId;

    /**
     * 小米料号 sku_code
     */
    private String skuCode;

    /**
     * 描述 sku_name
     */
    private String skuName;

    /**
     * 设备编号SN device_code
     */
    private String deviceCode;

    /**
     * 镭雕号 laser_code
     */
    private String laserCode;

    /**
     * IMEI号 imei
     */
    private String imei;

    /**
     * 出库外键 out_id
     */
    private String outId;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 领用申请明细外键 apply_mat_id
     */
    private String applyMatId;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 试产阶段 stage_name
     */
    private String stageName;

    /**
     * 库存外键 device_id
     */
    private String deviceId;

    /**
     * 保密套是否拆除 is_open
     */
    private String isOpen;

    /**
     * SAP行号 sap_item_row_no
     */
    private String sapItemRowNo;

    /**
     * ASN单号 order_code
     */
    private String orderCode;

    /**
     * 采购凭证类型 ZNB:原材料 ZWW:成品、半成品 ZDB 米通模式
     */
    private String bsart;

    /**
     * 收货记录编号 in_code
     */
    private String inCode;

    /**
     * 收货状态 in_status
     */
    private String inStatus;

    /**
     * 配置 device_type
     */
    private String deviceType;

    /**
     * 校验状态 check_status
     */
    private String checkStatus;

    /**
     * 校验结果 check_result
     */
    private String checkResult;

    /**
     * 出库SAP行号 out_sap_item_row_no
     */
    private String outSapItemRowNo;

    /**
     * 明细单号 det_code
     */
    private String detCode;
}