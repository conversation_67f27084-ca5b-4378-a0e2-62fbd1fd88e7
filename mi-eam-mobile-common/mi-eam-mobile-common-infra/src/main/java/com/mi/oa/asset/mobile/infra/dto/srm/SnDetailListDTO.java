package com.mi.oa.asset.mobile.infra.dto.srm;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/2
 */
@Data
public class SnDetailListDTO {


    @SerializedName("ET_DATA")
    private List<SnDetailDTO> detailList;

    @Data
    public static class SnDetailDTO{

        @SerializedName("SN")
        private String sn;

        @SerializedName("IMEI")
        private String imei;

        @SerializedName("EX_DATA")
        private String snExDataStr;

        /**
         * 镭雕号
         */
        private String ldh;

        /**
         * 配置
         */
        private String configuration;

        /**
         * 阶段
         */
        private String stageName;

        /**
         * 物料编码
         */
        private String sku;

        /**
         * 物料描述
         */
        private String maktx;

        private String asnItemNo;

    }

    @Data
    public static class SnExDataDTO {

        private String sn;

        private String lifnr;

        @SerializedName("asn_item_no")
        private String asnItemNo;

        @SerializedName("MAC")
        private String mac;

        @SerializedName("MACNO")
        private String macno;

        @SerializedName("sku")
        private String sku;

        @SerializedName("maktx")
        private String maktx;

        @SerializedName("expand")
        private List<SubSnDataDTO> subSnList;
    }

    @Data
    public static class SubSnDataDTO {

        @SerializedName("field_name")
        private String fieldName;

        @SerializedName("field_value")
        private String fieldValue;

    }
}
