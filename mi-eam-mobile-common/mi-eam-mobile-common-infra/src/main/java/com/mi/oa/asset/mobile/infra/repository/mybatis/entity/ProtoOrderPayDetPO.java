package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/08/03/11:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_order_pay_det",autoResultMap = true)
public class ProtoOrderPayDetPO {
    /**
     *  id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务类型 mrp_type
     */
    private String mrpType;

    /**
     * 业务单号 order_code
     */
    private String orderCode;

    /**
     * 单据类型 disposal_type
     */
    private String disposalType;

    /**
     * 业务类型 disposal_role
     */
    private String disposalRole;

    /**
     * 支付金额 pay_amount
     */
    private BigDecimal payAmount;

    /**
     * 手续费
     */
    private BigDecimal chargeAmount;

    /**
     * 渠道商户号
     */
    private String channelNum;

    /**
     * 支付方式 pay_type
     */
    private String payType;

    /**
     * SN device_code
     */
    private String deviceCode;

    /**
     * 小米料号 sku_code
     */
    private String skuCode;

    /**
     * 描述 sku_name
     */
    private String skuName;

    /**
     * 上市后描述 list_desc
     */
    private String listDesc;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 交易订单号 trade_order_code
     */
    private String tradeOrderCode;

    /**
     * 交易流水号 trade_water_code
     */
    private String tradeWaterCode;

    /**
     * 交易时间 trade_date
     */
    private Date tradeDate;

    /**
     * 申请人账号 user_code
     */
    private String userCode;

    /**
     * 申请人姓名 user_name
     */
    private String userName;

    /**
     * 申请人工号 emp_code
     */
    private String empCode;

    /**
     * 申请人部门编号 dept_code
     */
    private String deptCode;

    /**
     * 申请人部门名称 dept_name
     */
    private String deptName;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 业务类型 0:内购 1:处置 bus_type
     */
    private String busType;

    /**
      * 订单支付方式 1:小米钱包 2:支付宝
     */
    private String payOrderType;
}