package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/06/23/11:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_device_scan_det",autoResultMap = true)
public class ProtoDeviceScanDetPO {
    /**
     *  det_id
     */
    @TableId(type = IdType.AUTO)
    private Integer detId;

    /**
     * 设备编号SN device_code
     */
    private String deviceCode;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 试产阶段 stage_name
     */
    private String stageName;

    /**
     * 镭雕号 laser_code
     */
    private String laserCode;

    /**
     * IMEI号 imei
     */
    private String imei;

    /**
     * 配置 device_type
     */
    private String deviceType;

    /**
     * 小米料号 sku_code
     */
    private String skuCode;

    /**
     * 物料描述 sku_name
     */
    private String skuName;

    /**
     * 盘点状态 scan_result
     */
    private String scanResult;

    /**
     * 备注 remark
     */
    private String remark;

    /**
     * 任务清单ID scan_id
     */
    private String scanId;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 盘点计划ID plan_id
     */
    private String planId;

    /**
     * 是否异常 is_error
     */
    private String isError;

    /**
     * 归还时间 end_date
     */
    private Date endDate;

    /**
     * 上市时间 list_date
     */
    private Date listDate;

    /**
     * 保密套是否拆除 is_open
     */
    private String isOpen;

    /**
     * 状态 use_state
     */
    private String useState;

    /**
     * A error_a
     */
    private BigDecimal errorA;

    /**
     * B error_b
     */
    private BigDecimal errorB;

    /**
     * C error_c
     */
    private BigDecimal errorC;

    /**
     * D error_d
     */
    private BigDecimal errorD;

    /**
     * E error_e
     */
    private BigDecimal errorE;

    /**
     * F error_f
     */
    private BigDecimal errorF;

    /**
     * G error_g
     */
    private BigDecimal errorG;

    /**
     * H error_h
     */
    private BigDecimal errorH;

    /**
     * Check（统计A-F） total_error
     */
    private String totalError;
}