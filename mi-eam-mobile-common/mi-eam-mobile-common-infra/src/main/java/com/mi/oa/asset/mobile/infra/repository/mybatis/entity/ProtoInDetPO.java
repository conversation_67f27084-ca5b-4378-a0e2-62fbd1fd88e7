package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/12/10/03:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_in_det",autoResultMap = true)
public class ProtoInDetPO {
    /**
     * 主键 det_id
     */
    @TableId(type = IdType.INPUT)
    private String detId;

    /**
     * 小米料号 sku_code
     */
    private String skuCode;

    /**
     * 描述 sku_name
     */
    private String skuName;

    /**
     * 配置 device_type
     */
    private String deviceType;

    /**
     * 镭雕号 laser_code
     */
    private String laserCode;

    /**
     * IMEI号 imei
     */
    private String imei;

    /**
     * 设备编号SN device_code
     */
    private String deviceCode;

    /**
     * 收货单ID in_id
     */
    private String inId;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private String addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 匹配状态 auditing
     */
    private String auditing;

    /**
     * 匹配日期 audit_date
     */
    private String auditDate;

    /**
     * ASN订单明细ID order_det_id
     */
    private String orderDetId;

    /**
     * ASN行号 item_row_no
     */
    private String itemRowNo;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 试产阶段 stage_name
     */
    private String stageName;

    /**
     * 领用申请单号 apply_code
     */
    private String applyCode;

    /**
     * 是否已归还 is_back
     */
    private String isBack;

    /**
     * SAP行号 sap_item_row_no
     */
    private String sapItemRowNo;

    /**
     * 领用方类型 apply_user_type
     */
    private String applyUserType;

    /**
     * 最近发放日期 out_date
     */
    private Date outDate;

    /**
     * 新旧属性 asset_newold
     */
    private String assetNewold;

    /**
     * 样机生产地 make_cn
     */
    private String makeCn;

    /**
     * 是否良品 is_complete
     */
    private String isComplete;

    /**
     * 是否已反馈 is_feekback
     */
    private String isFeekback;

    /**
     * ASN单号 order_code
     */
    private String orderCode;

    /**
     * 收货记录编号 in_code
     */
    private String inCode;

    /**
     * SAP消息类型 sap_ret_type
     */
    private String sapRetType;

    /**
     * 错误消息 error_msg
     */
    private String errorMsg;

    /**
     * 手工匹配操作人 manual_user_name
     */
    private String manualUserName;

    /**
     * 手工匹配操作人账号 manual_user_code
     */
    private String manualUserCode;

    /**
     * 手工匹配操作人工号 manual_emp_code
     */
    private String manualEmpCode;

    /**
     * 手工匹配时间 manual_audit_date
     */
    private Date manualAuditDate;

    /**
     * 校验状态 check_status
     */
    private String checkStatus;

    /**
     * 校验结果 check_result
     */
    private String checkResult;
    /**
     * 过账日期
     */
    private String postingDate;

    /**
     * 业务类型 mrp_type
     */
    private String mrpType;
}