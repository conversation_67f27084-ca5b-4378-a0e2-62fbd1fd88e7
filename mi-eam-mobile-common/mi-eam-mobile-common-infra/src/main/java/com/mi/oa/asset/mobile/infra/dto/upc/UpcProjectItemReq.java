package com.mi.oa.asset.mobile.infra.dto.upc;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Description: your description
 *
 * <AUTHOR>
 * @date 2023/8/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpcProjectItemReq {
    /**
     * 项父项目id
     */
    private Integer parentId;
    /**
     * 父项目code   （项目代码不唯一）
     */
    private String parentCode;
    /**
     * 父项目中文名  （项目中文名不唯一）
     */
    private String parentName;
}
