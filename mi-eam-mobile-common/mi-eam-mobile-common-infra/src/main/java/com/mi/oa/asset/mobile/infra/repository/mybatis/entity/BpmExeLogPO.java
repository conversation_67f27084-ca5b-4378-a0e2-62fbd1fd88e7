package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/01/05/03:29
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "bpm_exelog",autoResultMap = true)
public class BpmExeLogPO {
    /**
     * 主键 exelog_id
     */
    @TableId(type = IdType.INPUT)
    private String exelogId;

    /**
     * 单据编号 order_code
     */
    private String orderCode;

    /**
     * 单据id order_id
     */
    private String orderId;

    /**
     * 单据名称 order_name
     */
    private String orderName;

    /**
     * 执行人 do_user
     */
    private String doUser;

    /**
     * 执行时间 do_date
     */
    private Date doDate;

    /**
     * 执行代号 exe_code
     */
    private String exeCode;

    /**
     * 执行结果 exe_desc
     */
    private String exeDesc;

    /**
     * 功能ID fun_id
     */
    private String funId;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     *  exe_body
     */
    private String exeBody;
}