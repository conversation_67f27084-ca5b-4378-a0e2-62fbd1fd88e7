package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/07/11/03:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "sys_attach",autoResultMap = true)
public class SysAttachPO {
    /**
     * 附件ID attach_id
     */
    @TableId
    private String attachId;

    /**
     * 表名 table_name
     */
    private String tableName;

    /**
     * 记录ID data_id
     */
    private String dataId;

    /**
     * 附件名称 attach_name
     */
    private String attachName;

    /**
     * 文件类型 content_type
     */
    private String contentType;

    /**
     * 相关字段 attach_field
     */
    private String attachField;

    /**
     * 功能ID fun_id
     */
    private String funId;

    /**
     * 功能名称 fun_name
     */
    private String funName;

    /**
     * 记录添加人 add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addDate;

    /**
     * 修改人 modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyDate;

    /**
     * 上传人 upload_user
     */
    private String uploadUser;

    /**
     * 上传日期 upload_date
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date uploadDate;

    /**
     * 附件路径 attach_path
     */
    private String attachPath;

    /**
     * 附件类型 attach_type
     */
    private String attachType;

    /**
     * 附件库ID store_id
     */
    private String storeId;

    /**
     * 附件库 store_no
     */
    private BigDecimal storeNo;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 文件大小 file_size
     */
    private BigDecimal fileSize;
}