package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/02/07/10:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_sleeve_mat",autoResultMap = true)
public class ProtoSleeveMatPO {
    /**
     * 主键 sleeve_mat_id
     */
    @TableId(type = IdType.INPUT)
    private String sleeveMatId;

    /**
     * 设备编号SN device_code
     */
    private String deviceCode;

    /**
     * 镭雕号 laser_code
     */
    private String laserCode;

    /**
     * IMEI号 imei
     */
    private String imei;

    /**
     * 配置 device_type
     */
    private String deviceType;

    /**
     * 小米料号 sku_code
     */
    private String skuCode;

    /**
     * 物料描述 sku_name
     */
    private String skuName;

    /**
     * 领用单号 apply_code
     */
    private String applyCode;

    /**
     * 申请人 last_apply_user_name
     */
    private String lastApplyUserName;

    /**
     * 申请人工号 last_apply_emp_code
     */
    private String lastApplyEmpCode;

    /**
     * 申请人账号 last_apply_user_code
     */
    private String lastApplyUserCode;

    /**
     * 备注 remark
     */
    private String remark;

    /**
     * 保密套拆除ID sleeve_id
     */
    private String sleeveId;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 初始领用人 apply_user_name
     */
    private String applyUserName;

    /**
     * 初始领用人账号 apply_user_code
     */
    private String applyUserCode;

    /**
     * 初始领用人工号 apply_emp_code
     */
    private String applyEmpCode;

    /**
     * 新旧属性 asset_newold
     */
    private String assetNewold;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 试产阶段 stage_name
     */
    private String stageName;

    /**
     * 校验状态 check_status
     */
    private String checkStatus;

    /**
     * 校验结果 check_result
     */
    private String checkResult;

    /**
     * 业务类型 mrp_type
     */
    private String mrpType;
}