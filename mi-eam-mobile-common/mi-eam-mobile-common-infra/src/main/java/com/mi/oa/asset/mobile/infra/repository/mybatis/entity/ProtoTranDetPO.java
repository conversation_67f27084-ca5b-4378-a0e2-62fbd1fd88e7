package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/12/23/11:12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_tran_det",autoResultMap = true)
public class ProtoTranDetPO {
    /**
     * 主键 tran_det_id
     */
    @TableId(type = IdType.INPUT)
    private String tranDetId;

    /**
     * 项目名称 project_name
     */
    private String projectName;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 试产阶段 stage_name
     */
    private String stageName;

    /**
     * 小米料号 sku_code
     */
    private String skuCode;

    /**
     * 设备编号SN device_code
     */
    private String deviceCode;

    /**
     * 领用单号 apply_code
     */
    private String applyCode;

    /**
     * 物料描述 sku_name
     */
    private String skuName;

    /**
     * 镭雕号 laser_code
     */
    private String laserCode;

    /**
     * IMEI号 imei
     */
    private String imei;

    /**
     * 备注 remark
     */
    private String remark;

    /**
     * 转移ID tran_id
     */
    private String tranId;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * SAP行号 sap_item_row_no
     */
    private String sapItemRowNo;

    /**
     * 新旧属性 asset_newold
     */
    private String assetNewold;

    /**
     * 发放单号 out_code
     */
    private String outCode;

    /**
     * 转出成本中心 center_code
     */
    private String centerCode;

    /**
     * 公司代码 comp_dept_code
     */
    private String compDeptCode;

    /**
     * 初始领用单行项目 apply_itemid
     */
    private String applyItemid;

    /**
     * 配置 device_type
     */
    private String deviceType;

    /**
     * 样机生产地 make_cn
     */
    private String makeCn;

    /**
     * SAP转移单号 sap_tran_code
     */
    private String sapTranCode;

    /**
     * 批次号 batch_id
     */
    private String batchId;

    /**
     * SAP消息类型 sap_ret_type
     */
    private String sapRetType;

    /**
     * 校验状态 check_status
     */
    private String checkStatus;

    /**
     * 校验结果 check_result
     */
    private String checkResult;

    /**
     * 转入成本中心 in_center_code
     */
    private String inCenterCode;

    /**
     * 转入公司代码 in_comp_dept_code
     */
    private String inCompDeptCode;

    /**
     * 是否成功 is_success
     */
    private String isSuccess;

    /**
     * sap 107行号
     */
    private String sap107ItemRowNo;

    /**
     * 业务类型 mrp_type
     */
    private String mrpType;
}