package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.math.BigDecimal;
import java.util.Date;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/02/28/03:54
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_baseline",autoResultMap = true)
public class ProtoBaselinePO {
    /**
     * 主键 baseline_id
     */
    @TableId(type = IdType.INPUT)
    private String baselineId;

    /**
     * 基线编码 baseline_code
     */
    private String baselineCode;

    /**
     * 基线范围 scope
     */
    private String scope;

    /**
     * 项目类型 project_type
     */
    private String projectType;

    /**
     * 系列 series
     */
    private String series;

    /**
     * P00 p00
     */
    private BigDecimal p00;

    /**
     * P10 p10
     */
    private BigDecimal p10;

    /**
     * P20 p20
     */
    private BigDecimal p20;

    /**
     * P30 p30
     */
    private BigDecimal p30;

    /**
     * 合计 total
     */
    private BigDecimal total;

    /**
     * 基线接口人 charge_person_name
     */
    private String cpName;

    /**
     * 基线接口人账号 charge_person_code
     */
    private String cpCode;

    /**
     * 基线状态 state
     */
    private String state;

    /**
     * 基线备注 remark
     */
    private String remark;

    /**
     * 接口人部门编码 charge_person_dept_code
     */
    private String cpDeptCode;

    /**
     * 接口人部门名称 charge_person_dept_name
     */
    private String cpDeptName;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}