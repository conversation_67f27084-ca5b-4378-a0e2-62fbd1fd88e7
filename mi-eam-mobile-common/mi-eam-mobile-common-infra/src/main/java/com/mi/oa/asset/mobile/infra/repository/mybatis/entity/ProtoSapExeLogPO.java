package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SAP接口执行日志DTO
 *
 * <AUTHOR>
 * @date 1.0 2021-11-09
 */
@Data
@TableName("sap_exelog")
public class ProtoSapExeLogPO implements Serializable {

    @TableId(type = IdType.INPUT)
    private String exelogId;

    /**
     * 功能ID
     */
    private String funId;

    /**
     * 单据ID
     */
    private String orderId;

    /**
     * 单据编号
     */
    private String orderCode;

    /**
     * 单据名称
     */
    private String orderName;

    /**
     * 执行人
     */
    private String doUser;

    /**
     * 执行时间
     */
    private Date doDate;

    /**
     * 执行代号：S成功，E失败
     */
    private String exeCode;

    /**
     * 执行结果
     */
    private String exeDesc;

    /**
     * 执行报文
     */
    private String exeBody;
}