package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/03/31/11:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_card_dept_total",autoResultMap = true)
public class ProtoCardDeptTotalPO {
    /**
     *  total_id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务类型 mrp_type
     */
    private String mrpType;

    /**
     * 部门编码 dept_code
     */
    private String deptCode;

    /**
     * 部门名称 dept_name
     */
    private String deptName;

    /**
     * 部门全名称 long_dept_name
     */
    private String longDeptName;

    /**
     * 部门级别 dept_level
     */
    private Integer deptLevel;

    /**
     * 待归还 dgh_num
     */
    private Integer dghNum;

    /**
     * 转移中 zyz_num
     */
    private Integer zyzNum;

    /**
     * 归还中 ghz_num
     */
    private Integer ghzNum;

    /**
     * 无法归还中 wfghz_num
     */
    private Integer wfghzNum;

    /**
     * 总数 total_num
     */
    private Integer totalNum;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}