package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/08/01/10:42
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_inner_purchase_plan", autoResultMap = true)
public class ProtoInnerPurchasePlanPO {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务线
     */
    private String mrpType;

    /**
     * 内购主题 topic
     */
    private String topic;

    /**
     * 内购类型 order_type
     */
    private String orderType;

    /**
     * 单据状态 order_status
     */
    private String orderStatus;

    /**
     * 计划单号 order_no
     */
    private String orderNo;

    /**
     * 内购地点 location
     */
    private String location;

    /**
     * 服务部内购地点 location
     */
    private String locationService;

    /**
     * 验货信息 check_info
     */
    private String checkInfo;

    /**
     * 提货方式
     */
    private String deliveryMethod;

    /**
     * 内购数量 inner_purchase_num
     */
    private Integer innerPurchaseNum;

    /**
     * 预计内购收益 expect_profit
     */
    private BigDecimal expectProfit;

    /**
     * 申请部门 dept_name
     */
    private String deptName;

    /**
     * 申请部门编号 dept_code
     */
    private String deptCode;

    /**
     * 内购核算 check_order_no
     */
    private String checkOrderNo;

    /**
     * bpm唯一标识 business_key
     */
    private String businessKey;

    /**
     * 记录状态 auditing
     */
    private String auditing;

    /**
     * 申请人 apply_user_name
     */
    private String applyUserName;

    /**
     * 申请人账号 apply_user_code
     */
    private String applyUserCode;

    /**
     * 申请日期 apply_date
     */
    private Date applyDate;

    /**
     * 预计结束时间 end_date
     */
    private Date endDate;

    /**
     * 预计开始时间 begin_date
     */
    private Date beginDate;

    /**
     * 内购备注 memo
     */
    private String memo;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * current_cancel_num 已下架
     */
    private BigDecimal currentCancelNum;

    /**
     * current_end_num 实际内购数量
     */
    private BigDecimal currentEndNum;

    /**
     * current_end_profit 实际内购收益
     */
    private BigDecimal currentEndProfit;

    /**
     * process_percent 完成比例
     */
    private BigDecimal processPercent;
}