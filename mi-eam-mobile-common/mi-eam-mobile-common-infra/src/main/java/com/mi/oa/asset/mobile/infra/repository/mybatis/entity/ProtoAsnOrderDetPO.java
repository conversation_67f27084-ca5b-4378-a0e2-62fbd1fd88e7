package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/2
 */
@Data
@TableName("proto_asn_order_det")
public class ProtoAsnOrderDetPO {

    @TableId(type = IdType.INPUT)
    private String detId;

    /**
     * ASN no
     */
    private String asnNo;

    /**
     * 采购凭证类型 ZNB:原材料 ZWW:成品、半成品 ZDB 米通模式
     */
    private String bsart;
    /**
     * 最后一次收货时间
     */
    private String lastReceiptNo;

    /**
     * ASN行号
     */
    private String itemRowNo;

    /**
     * 小米科号
     */
    private String skuCode;

    /**
     * 描述
     */
    private String skuName;

    /**
     * 发货数量
     */
    private Integer deliveryNum;

    /**
     * 收获数量
     */
    private Integer receiptNum;

    /**
     * 设备编号SN
     */
    private String deviceCode;

    /**
     * IMEI号
     */
    private String imei;

    /**
     * sn扩展信息，存储格式是：,镭雕号,IMEI,配置,
     */
    private String snExtra;

    private String mac;

    private String sku;

    /**
     * 商品ID
     */
    private String goodsId;

    private String macno;

    /**
     * 是否为伪SN 0:不是伪SN 1:是伪SN
     */
    private Integer isPseudoSn;

    private String addDate;

    private String modifyDate;

    /**
     * 是否删除1：是 0：否
     */
    private Integer isDelete;

    /**
     * 业务类型 手机：mobile 电视：tv 生态链：ecochain 笔记本电脑：laptop 可穿戴：wearable 机器人：robot
     */
    private String mrpType;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * 项目名
     */
    private String projectName;

    /**
     * ASN收货ID 与proto_asn_order表关联
     */
    private String orderId;

    /**
     * 配置
     */
    private String deviceType;

    /**
     * 镭雕号
     */
    private String laserCode;

    /**
     * 收货状态 0:未收货 2:部分收货 3:已收货 5:已取消
     */
    private String inStatus;

    /**
     * 抛送SAP状态 默认:N 成功:S 失败:E
     */
    private String sapRetType;

    /**
     * 收货记录Id
     */
    private String inId;
}
