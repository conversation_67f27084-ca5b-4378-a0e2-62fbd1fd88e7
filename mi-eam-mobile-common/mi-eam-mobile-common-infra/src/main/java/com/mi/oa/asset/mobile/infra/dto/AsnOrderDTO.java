package com.mi.oa.asset.mobile.infra.dto;

import lombok.Data;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/19
 */
@Data
public class AsnOrderDTO {

    /**
     * sap工厂
     */
    private String sapFactory;

    /**
     * 阶段
     */
    private String stageName;

    /**
     * ASN单号
     */
    private String orderCode;

    /**
     * proto_asn_order_det 主键id
     */
    private String detId;

    /**
     * 配置
     */
    private String deviceType;

    /**
     * 镭雕号
     */
    private String laserCode;

    /**
     * IMEI
     */
    private String imei;

    /**
     * SKU
     */
    private String skuCode;

    /**
     * SN
     */
    private String deviceCode;

    /**
     * 项目编号
     */
    private String projectCode;

    /**
     * SKU 名称
     */
    private String skuName;

    /**
     * 行号
     */
    private String itemRowNo;

    /**
     * 商品Id
     */
    private String goodsId;
}
