package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/01/04/04:04
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_card",autoResultMap = true)
public class ProtoCardPO {
    /**
     * 主键 device_id
     */
    @TableId(type = IdType.INPUT)
    private String deviceId;

    /**
     * 业务类型 mrp_type
     */
    private String mrpType;

    /**
     * 小米料号 sku_code
     */
    private String skuCode;

    /**
     * 描述 sku_name
     */
    private String skuName;

    /**
     * 设备编号SN device_code
     */
    private String deviceCode;

    /**
     * 镭雕号 laser_code
     */
    private String laserCode;

    /**
     * IMEI号 imei
     */
    private String imei;

    /**
     * 配置 device_type
     */
    private String deviceType;

    /**
     * 初始领用申请单号 apply_code
     */
    private String applyCode;

    /**
     * 初始领用单行项目 apply_itemid
     */
    private String applyItemid;

    /**
     * 初始领用人 apply_user_name
     */
    private String applyUserName;

    /**
     * 初始领用人账号 apply_user_code
     */
    private String applyUserCode;

    /**
     * 初始领用人工号 apply_emp_code
     */
    private String applyEmpCode;

    /**
     * 项目 project_name
     */
    private String projectName;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 试产阶段 stage_name
     */
    private String stageName;

    /**
     * 样机生产地 make_cn
     */
    private String makeCn;

    /**
     * 状态 use_state
     */
    private String useState;

    /**
     * 当前使用人 user_name
     */
    private String userName;

    /**
     * 当前使用人账号 user_code
     */
    private String userCode;

    /**
     * 当前使用人工号 emp_code
     */
    private String empCode;

    /**
     * 保密套是否拆除 is_open
     */
    private String isOpen;

    /**
     * 保密套拆除人 open_user_name
     */
    private String openUserName;

    /**
     * 保密套拆除人账号 open_user_code
     */
    private String openUserCode;

    /**
     * 保密套拆除人工号 open_emp_code
     */
    private String openEmpCode;

    /**
     * 保密套拆除时间 open_date
     */
    private Date openDate;

    /**
     * 最后操作时间 last_date
     */
    private Date lastDate;

    /**
     * 初始发放日期 apply_date
     */
    private Date applyDate;

    /**
     * 最近发放日期 out_date
     */
    private Date outDate;

    /**
     * 新旧属性 asset_newold
     */
    private String assetNewold;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private String addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 仓库名称 house_name
     */
    private String houseName;

    /**
     * 仓库ID house_id
     */
    private String houseId;

    /**
     * 仓库编号 house_code
     */
    private String houseCode;

    /**
     * 最近领用申请单号 last_apply_code
     */
    private String lastApplyCode;

    /**
     * 最近领用单行项目 last_apply_itemid
     */
    private String lastApplyItemid;

    /**
     * 最近领用人 last_apply_user_name
     */
    private String lastApplyUserName;

    /**
     * 最近领用人账号 last_apply_user_code
     */
    private String lastApplyUserCode;

    /**
     * 最近领用人工号 last_apply_emp_code
     */
    private String lastApplyEmpCode;

    /**
     * 最近发放日期 last_apply_date
     */
    private Date lastApplyDate;

    /**
     * 成本中心代码 center_code
     */
    private String centerCode;

    /**
     * 成本中心名称 center_name
     */
    private String centerName;

    /**
     * 公司代码 comp_dept_code
     */
    private String compDeptCode;

    /**
     * 公司名称 comp_dept_name
     */
    private String compDeptName;

    /**
     * 领用方类型 apply_user_type
     */
    private String applyUserType;

    /**
     * 占用领用单号 occupy_apply_code
     */
    private String occupyApplyCode;

    /**
     * 是否良品 is_complete
     */
    private String isComplete;

    /**
     * sap工厂 sap_factory
     */
    private String sapFactory;

    /**
     * 收货记录编号 in_code
     */
    private String inCode;

    /**
     * 发放单号 out_code
     */
    private String outCode;

    /**
     * 订单单号 order_code
     */
    private String orderCode;

    /**
     * 原始项目编号 ori_project_code
     */
    private String oriProjectCode;

    /**
     * 原始试产阶段 ori_stage_name
     */
    private String oriStageName;

    /**
     * 同步标志 syc_flag 刷新全量部门用
     */
    private String sycFlag;

    /**
     * 新项目编号 new_project_code
     */
    private String newProjectCode;

    /**
     * 新试产阶段 new_stage_name
     */
    private String newStageName;

    /**
     * 老系统使用方 old_use_name
     */
    private String oldUseName;

    /**
     * 老系统使用方code old_use_code
     */
    private String oldUseCode;

    /**
     * 备注 remark
     */
    private String remark;

    /**
     * 部门编码 dept_code
     */
    private String deptCode;

    /**
     * 部门全名称 long_dept_name
     */
    private String longDeptName;

    /**
     * 原产国家 country
     */
    private String country;

    /**
     * 状态2 use_state_2
     */
    @TableField("use_state_2")
    private String useState2;

    /**
     * 出库SAP行号 out_sap_item_row_no
     */
    private String outSapItemRowNo;

    /**
     * 截止归还时间 end_date
     */
    private Date endDate;

    /**
     * 发放人姓名 out_user_name
     */
    private String outUserName;

    /**
     * 发放人账户 out_user_code
     */
    private String outUserCode;

    /**
     * 发放人工号 out_emp_code
     */
    private String outEmpCode;

    /**
     * 初始部门编号 apply_dept_code
     */
    private String applyDeptCode;

    /**
     * 初始部门全名称 apply_long_dept_name
     */
    private String applyLongDeptName;

    /**
     * 上市时间 list_date
     */
    private Date listDate;

    /**
     * 小米料号类别 sku_code_type
     */
    private String skuCodeType;

    /**
     * 入库时间
     */
    private Date entryDate;

    /**
     * 商品Id
     */
    private String goodsId;
}