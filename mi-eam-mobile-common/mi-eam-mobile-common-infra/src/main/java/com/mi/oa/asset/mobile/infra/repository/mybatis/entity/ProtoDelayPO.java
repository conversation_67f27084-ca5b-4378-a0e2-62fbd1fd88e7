package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/02/17/07:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_delay",autoResultMap = true)
public class ProtoDelayPO {
    /**
     * 主键 delay_id
     */
    @TableId(type = IdType.INPUT)
    private String delayId;

    /**
     * 记录状态 auditing
     */
    private String auditing;

    /**
     * 延期归还单号 delay_code
     */
    private String delayCode;

    /**
     * 申请人 apply_user_name
     */
    private String applyUserName;

    /**
     * 申请人账号 apply_user_code
     */
    private String applyUserCode;

    /**
     * 申请人工号 apply_emp_code
     */
    private String applyEmpCode;

    /**
     * 申请部门 dept_name
     */
    private String deptName;

    /**
     * 申请部门编号 dept_code
     */
    private String deptCode;

    /**
     * 申请日期 apply_date
     */
    private Date applyDate;

    /**
     * 延期归还日期 delay_date
     */
    private Date delayDate;

    /**
     * 延期说明 remark
     */
    private String remark;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 领用方类型 apply_user_type
     */
    private String applyUserType;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 小米确认人 check_user
     */
    private String checkUser;

    /**
     * 确认人账号 check_user_code
     */
    private String checkUserCode;

    /**
     * 确认人工号 check_emp_code
     */
    private String checkEmpCode;

    /**
     * bpm唯一标识 business_key
     */
    private String businessKey;

    /**
     * 项目PM pm_user_name
     */
    private String pmUserName;

    /**
     * PM账号 pm_user_code
     */
    private String pmUserCode;

    /**
     * PM工号 pm_emp_code
     */
    private String pmEmpCode;
}