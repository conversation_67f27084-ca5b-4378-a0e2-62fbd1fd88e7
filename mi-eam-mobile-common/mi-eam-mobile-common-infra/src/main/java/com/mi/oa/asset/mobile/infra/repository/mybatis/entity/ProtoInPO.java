package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/12/10/03:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_in",autoResultMap = true)
public class ProtoInPO {
    /**
     * 主键 in_id
     */
    @TableId(type = IdType.INPUT)
    private String inId;

    /**
     * 收货记录编号 in_code
     */
    private String inCode;

    /**
     * ASN单号 order_code
     */
    private String orderCode;

    /**
     * 收货状态 in_status
     */
    private String inStatus;

    /**
     * 出货工厂编号 factory_code
     */
    private String factoryCode;

    /**
     * 出货工厂 factory_name
     */
    private String factoryName;

    /**
     * 出货日期 out_date
     */
    private Date outDate;

    /**
     * 收货日期 in_date
     */
    private Date inDate;

    /**
     * 收货人 user_name
     */
    private String userName;

    /**
     * 收货人账号 user_code
     */
    private String userCode;

    /**
     * 收货人工号 emp_code
     */
    private String empCode;

    /**
     * 收货类型 in_type
     */
    private String inType;

    /**
     * ASN收货ID order_id
     */
    private String orderId;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 试产阶段 stage_name
     */
    private String stageName;

    /**
     * 仓库名称 house_name
     */
    private String houseName;

    /**
     * 仓库ID house_id
     */
    private String houseId;

    /**
     * 仓库编号 house_code
     */
    private String houseCode;

    /**
     * 是否已反馈 is_feekback
     */
    private String isFeekback;

    /**
     * 申请人 apply_user_name
     */
    private String applyUserName;

    /**
     * 申请人账号 apply_user_code
     */
    private String applyUserCode;

    /**
     * 申请人工号 apply_emp_code
     */
    private String applyEmpCode;

    /**
     * 入库数量 in_num
     */
    private BigDecimal inNum;

    /**
     * 备注 in_memo
     */
    private String inMemo;

    /**
     * 工程机归还记录状态 auditing_back
     */
    private String auditingBack;

    /**
     * 工程机归还确认记录状态 auditing_back_confirm
     */
    private String auditingBackConfirm;

    /**
     * 是否完整 is_complete
     */
    private String isComplete;

    /**
     * bpm唯一标识 business_key
     */
    private String businessKey;

    /**
     * 领用方类型 apply_user_type
     */
    private String applyUserType;

    /**
     * SAP消息类型 sap_ret_type
     */
    private String sapRetType;

    /**
     * SAP过账日期
     */
    private Date postingDate;

    /**
     * 业务类型 mrp_type
     */
    private String mrpType;

    /**
     * 申请部门 dept_name
     */
    private String deptName;
}