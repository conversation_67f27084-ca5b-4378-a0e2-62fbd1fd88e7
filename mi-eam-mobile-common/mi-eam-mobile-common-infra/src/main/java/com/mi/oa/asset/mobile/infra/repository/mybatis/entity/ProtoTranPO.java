package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/12/24/03:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_tran",autoResultMap = true)
public class ProtoTranPO {
    /**
     * 主键 tran_id
     */
    @TableId(type = IdType.INPUT)
    private String tranId;

    /**
     * 转移单号 tran_code
     */
    private String tranCode;

    /**
     * 记录状态 auditing
     */
    private String auditing;

    /**
     * 申请人 user_name
     */
    private String userName;

    /**
     * 申请人账号 user_code
     */
    private String userCode;

    /**
     * 申请人工号 emp_code
     */
    private String empCode;

    /**
     * 接收人 acc_user_name
     */
    private String accUserName;

    /**
     * 接收人账号 acc_user_code
     */
    private String accUserCode;

    /**
     * 接收人工号 acc_emp_code
     */
    private String accEmpCode;

    /**
     * 申请日期 apply_date
     */
    private Date applyDate;

    /**
     * 申请部门 dept_name
     */
    private String deptName;

    /**
     * 申请部门编码 dept_code
     */
    private String deptCode;

    /**
     * 接受部门 in_dept_name
     */
    private String inDeptName;

    /**
     * 接受部门编码 in_dept_code
     */
    private String inDeptCode;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 备注 remark
     */
    private String remark;

    /**
     * bpm唯一标识 business_key
     */
    private String businessKey;

    /**
     * 领用方类型 apply_user_type
     */
    private String applyUserType;

    /**
     * 接收人成本中心代码 acc_center_code
     */
    private String accCenterCode;

    /**
     * 申请人成本中心代码 center_code
     */
    private String centerCode;

    /**
     * 转出公司代码 out_comp_dept_code
     */
    private String outCompDeptCode;

    /**
     * 转入公司代码 in_comp_dept_code
     */
    private String inCompDeptCode;

    /**
     * 样机生产地 make_cn
     */
    private String makeCn;

    /**
     * 转移类型 tran_type
     */
    private String tranType;

    /**
     * 新旧属性 asset_newold
     */
    private String assetNewold;

    /**
     * SAP消息类型 sap_ret_type
     */
    private String sapRetType;

    /**
     * 批次号 batch_id
     */
    private String batchId;

    /**
     * 确认状态 confirm_status
     */
    private String confirmStatus;

    /**
     * 业务类型 mrp_type
     */
    private String mrpType;

    /**
     * 数量 num
     */
    private Integer num;
}