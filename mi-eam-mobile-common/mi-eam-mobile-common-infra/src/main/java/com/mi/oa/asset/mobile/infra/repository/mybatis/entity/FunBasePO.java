package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/01/07/10:56
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "fun_base",autoResultMap = true)
public class FunBasePO {
    /**
     * 功能标识 fun_id
     */
    @TableId(type = IdType.INPUT)
    private String funId;

    /**
     * 功能模块ID module_id
     */
    private String moduleId;

    /**
     * 功能名称 fun_name
     */
    private String funName;

    /**
     * 功能序号 fun_index
     */
    private BigDecimal funIndex;

    /**
     * 功能注册类型 reg_type
     */
    private String regType;

    /**
     * 拥有子功能ID subfun_id
     */
    private String subfunId;

    /**
     * 布局页面 layout_page
     */
    private String layoutPage;

    /**
     * GRID页面 grid_page
     */
    private String gridPage;

    /**
     * FORM页面 form_page
     */
    private String formPage;

    /**
     * 业务表名 table_name
     */
    private String tableName;

    /**
     * 业务表主键 pk_col
     */
    private String pkCol;

    /**
     * 业务编码字段 code_col
     */
    private String codeCol;

    /**
     * 编码前缀 code_prefix
     */
    private String codePrefix;

    /**
     * 子功能外键 fk_col
     */
    private String fkCol;

    /**
     * 有效记录值 valid_flag
     */
    private String validFlag;

    /**
     * 记录状态列 audit_col
     */
    private String auditCol;

    /**
     * 复制列 copy_col
     */
    private String copyCol;

    /**
     * FROM子句 from_sql
     */
    private String fromSql;

    /**
     * WHERE子句 where_sql
     */
    private String whereSql;

    /**
     * ORDER子句 order_sql
     */
    private String orderSql;

    /**
     * GROUP子句 group_sql
     */
    private String groupSql;

    /**
     * 用户信息 is_userinfo
     */
    private String isUserinfo;

    /**
     * 是否归档 is_archive
     */
    private String isArchive;

    /**
     * 功能状态 fun_state
     */
    private String funState;

    /**
     * 数据源名 ds_name
     */
    private String dsName;

    /**
     * 要求有数据的子功能 val_subid
     */
    private String valSubid;

    /**
     * 初始显示数据 init_show
     */
    private String initShow;

    /**
     * 是否表格编辑 isedit
     */
    private String isedit;

    /**
     * 是否查询 is_query
     */
    private String isQuery;

    /**
     * 记录添加人 add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人 modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private String modifyDate;

    /**
     * 审批界面类型 show_form
     */
    private String showForm;

    /**
     * 缺省查询字段 first_field
     */
    private String firstField;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 功能图标 icons_css
     */
    private String iconsCss;

    /**
     * 无租户过滤 not_tenant
     */
    private String notTenant;
}