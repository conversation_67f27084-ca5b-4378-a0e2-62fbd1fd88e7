package com.mi.oa.asset.mobile.infra.dto.srm;

import com.google.gson.annotations.SerializedName;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderItemPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderPO;
import lombok.Data;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/1
 */
@Data
public class ProtoAsnOrderListDTO {

    @SerializedName("ET_HEADER")
    private List<ProtoAsnOrderDTO> asnOrderList;

    @Data
    public static class ProtoAsnOrderDTO extends ProtoAsnOrderPO {

        /**
         * 发货日期
         */
        @SerializedName("ZZSHIP_DATE")
        private String shipDate;

        /**
         * 项目编号 如:A7
         */
        @SerializedName("APP_MODELS")
        private String projectCode;

        /**
         * 状态
         * S001       等待发送
         * S002       已发送
         * S003       部分入库
         * S004       已入库
         * S005       已取消
         */
        @SerializedName("STATUS")
        private String status;

        @SerializedName("ITEMS")
        private List<ProtoAsnOrderItemDTO> items;

    }

    @Data
    public static class ProtoAsnOrderItemDTO extends ProtoAsnOrderItemPO {

    }
}
