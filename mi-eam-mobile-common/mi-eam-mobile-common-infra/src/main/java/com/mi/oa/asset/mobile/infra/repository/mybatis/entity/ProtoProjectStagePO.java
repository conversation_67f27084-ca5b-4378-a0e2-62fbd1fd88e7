package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/01/27/11:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_project_stage",autoResultMap = true)
public class ProtoProjectStagePO {
    /**
     * 主键 stage_id
     */
    @TableId(type = IdType.INPUT)
    private String stageId;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 阶段 stage_name
     */
    private String stageName;

    /**
     * 料号 sku_code
     */
    private String skuCode;

    /**
     * 物料描述 sku_name
     */
    private String skuName;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 截止归还时间 end_date
     */
    private Date endDate;

    /**
     * 上市时间
     */
    private Date listDate;

    /**
     * 上市描述
     */
    private String listDesc;

    /**
     * 上市价格
     */
    private BigDecimal listMoney;

    /**
     * 当前市价
     */
    private BigDecimal marketPrice;

    /**
     * 业务类型 mrpType
     */
    private String mrpType;

    /**
     * 商品id
     */
    private String goodsId;
}