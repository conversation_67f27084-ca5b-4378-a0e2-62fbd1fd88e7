package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/11/21/02:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "sys_role_fun",autoResultMap = true)
public class SysRoleFunPO {
    /**
     * 设置ID role_fun_id
     */
    private String roleFunId;

    /**
     * 角色ID role_id
     */
    private String roleId;

    /**
     * 功能ID fun_id
     */
    private String funId;

    /**
     * 编辑权限 is_edit
     */
    private String isEdit;

    /**
     * 打印权限 is_print
     */
    private String isPrint;

    /**
     * 审批权限 is_audit
     */
    private String isAudit;

    /**
     * 记录添加人 add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人 modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 其它权限 is_other
     */
    private String isOther;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}