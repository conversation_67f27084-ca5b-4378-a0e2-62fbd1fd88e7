package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/01/10/03:10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "sys_coderule")
public class SysCodeRulePO {
    /**
     * 编码规则ID rule_id
     */
    @TableId(type = IdType.INPUT)
    private String ruleId;

    /**
     * 功能ID fun_id
     */
    private String funId;

    /**
     * 编码扩展 code_ext
     */
    private String codeExt;

    /**
     * 编码流水号 code_no
     */
    private String codeNo;

    /**
     * 记录添加人 add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人 modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 编码规则说明 code_memo
     */
    private String codeMemo;

    /**
     * 编码长度 code_length
     */
    private Integer codeLength;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}