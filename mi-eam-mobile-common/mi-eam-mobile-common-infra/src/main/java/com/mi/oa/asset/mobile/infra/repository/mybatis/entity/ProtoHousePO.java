package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/12/07/04:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_house",autoResultMap = true)
public class ProtoHousePO {
    /**
     * 主键 house_id
     */
    @TableId
    private String houseId;

    /**
     * 仓库名称 house_name
     */
    private String houseName;

    /**
     * 仓库编号 house_code
     */
    private String houseCode;

    /**
     * 是否有效 is_valid
     */
    private String isValid;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 记录状态 auditing
     */
    private String auditing;

    /**
     * 样机管理员 pm_user_name
     */
    private String pmUserName;

    /**
     * 样机管理员账号 pm_user_code
     */
    private String pmUserCode;

    /**
     * 样机管理员工号 pm_emp_code
     */
    private String pmEmpCode;

    /**
     * 业务类型 mrp_type
     */
    private String mrpType;
}