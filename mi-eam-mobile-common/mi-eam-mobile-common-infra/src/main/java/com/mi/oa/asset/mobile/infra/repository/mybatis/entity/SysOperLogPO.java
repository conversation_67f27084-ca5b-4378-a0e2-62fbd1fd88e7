package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/09/20/08:20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "sys_oper_log",autoResultMap = true)
public class SysOperLogPO {
    /**
     * 主键 id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 单据编号 order_code
     */
    private String orderCode;

    /**
     * 执行人 do_user
     */
    private String doUser;

    /**
     * 执行时间 do_date
     */
    private Date doDate;

    /**
     * 执行结果 opt_param
     */
    private String optParam;

    /**
     * 执行代号 opt_code
     */
    private String optCode;

    /**
     * 执行结果 opt_desc
     */
    private String optDesc;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;
}