package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/06/22/03:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_price",autoResultMap = true)
public class ProtoPricePO {
    /**
     *  id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务类型 bus_type
     */
    private String busType;

    /**
     * 定价配置编码 price_code
     */
    private String priceCode;

    /**
     * 是否JIS之前机型 is_jis
     */
    private String isJis;

    /**
     * 配置描述 remark
     */
    private String remark;

    /**
     * 状态 status
     */
    private String status;

    /**
     * 更新人 modify_name
     */
    private String modifyName;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}