package com.mi.oa.asset.mobile.infra.dto.upc;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Description: your description
 *
 * <AUTHOR>
 * @date 2023/8/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpcProjectDetailDTO implements Serializable {

    private Integer id;
    /**
     * 父项目id， 为0则代表是一级项目
     */
    private Integer parentId;
    /**
     * 项目层级
     */
    private Integer level;
    /**
     * 项目编码
     */
    private String projectCode;
    /**
     * 项目中文名
     */
    private String nameCn;
    /**
     * 项目英文名
     */
    private String nameEn;
    /**
     * 项目描述
     */
    private String description;
    /**
     * 创建时间
     */
    private Integer createTime;
    /**
     * 更新时间
     */
    private Integer updateTime;
    /**
     * 代工厂信息
     */
    private String factoryData;
    /**
     * 状态 0-不可用状态（废弃） 1-可用状态
     */
    private Integer status;
    /**
     * 父项目id， 为0则代表是一级项目
     */
    private Integer categoryId;
}
