package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/01/11/05:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_out",autoResultMap = true)
public class ProtoOutPO {
    /**
     * 主键 out_id
     */
    @TableId(type = IdType.INPUT)
    private String outId;

    /**
     * 记录状态 auditing
     */
    private String auditing;

    /**
     * 发放单号 out_code
     */
    private String outCode;

    /**
     * 发放类型 out_type
     */
    private String outType;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * ASN单号 order_code
     */
    private String orderCode;
    /**
     * 采购凭证类型 ZNB:原材料 ZWW:成品、半成品 ZDB
     */
    private String bsart;
    /**
     * 使用人工号 emp_code
     */
    private String empCode;

    /**
     * 使用人账户 user_code
     */
    private String userCode;

    /**
     * 使用人姓名 user_name
     */
    private String userName;

    /**
     * 申请人工号 apply_emp_code
     */
    private String applyEmpCode;

    /**
     * 申请人账户 apply_user_code
     */
    private String applyUserCode;

    /**
     * 领用申请单ID apply_id
     */
    private String applyId;

    /**
     * 领用申请单号 apply_code
     */
    private String applyCode;

    /**
     * 申请人姓名 apply_user_name
     */
    private String applyUserName;

    /**
     * 领用方类型 apply_user_type
     */
    private String applyUserType;

    /**
     * 收货记录编号 in_code
     */
    private String inCode;

    /**
     * ASN收货ID order_id
     */
    private String orderId;

    /**
     * 收货单ID in_id
     */
    private String inId;

    /**
     * 发放状态 out_status
     */
    private String outStatus;

    /**
     * 样机生产地 make_cn
     */
    private String makeCn;

    /**
     * 成本中心 center_code
     */
    private String centerCode;

    /**
     * 公司代码 comp_dept_code
     */
    private String compDeptCode;

    /**
     * sap工厂 sap_factory
     */
    private String sapFactory;

    /**
     * 申请人部门名称 apply_dept_name
     */
    private String applyDeptName;

    /**
     * 申请人部门编码 apply_dept_code
     */
    private String applyDeptCode;

    /**
     * 出库日期 out_date
     */
    private Date outDate;

    /**
     * SAP消息类型 sap_ret_type
     */
    private String sapRetType;

    /**
     * 单据类型 order_type
     */
    private String orderType;

    /**
     * 仓库名称 house_name
     */
    private String houseName;

    /**
     * 仓库编号 house_code
     */
    private String houseCode;

    /**
     * 仓库ID house_id
     */
    private String houseId;

    /**
     * 发放人工号 out_emp_code
     */
    private String outEmpCode;

    /**
     * 发放人账户 out_user_code
     */
    private String outUserCode;

    /**
     * 发放人姓名 out_user_name
     */
    private String outUserName;

    /**
     * 过账日期 posting_date
     */
    private Date postingDate;

    /**
     * 业务类型
     */
    private String mrpType;


    /**
     * 确认收货时间 check_date
     */
    private Date checkDate;
}