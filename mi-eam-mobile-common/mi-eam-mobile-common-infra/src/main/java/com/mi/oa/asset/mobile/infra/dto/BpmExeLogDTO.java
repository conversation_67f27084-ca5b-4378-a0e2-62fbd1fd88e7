package com.mi.oa.asset.mobile.infra.dto;

import com.mi.oa.asset.mobile.utils.GsonUtil;
import lombok.*;

import java.io.Serializable;

/**
 * BPM接口执行日志DTO
 *
 * <AUTHOR>
 * @date 1.0 2021-11-25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class BpmExeLogDTO implements Serializable {

    /**
     * 功能ID
     */
    private String funId;

    /**
     * 单据ID
     */
    private String orderId;

    /**
     * 单据编号
     */
    private String orderCode;

    /**
     * 单据名称
     */
    private String orderName;

    /**
     * 执行人
     */
    private String doUser;

    /**
     * 执行时间
     */
    private String doDate;

    /**
     * 执行代号：S成功，E失败
     */
    private Boolean exeCode;

    /**
     * 执行结果
     */
    private String exeDesc;

    /**
     * 执行报文
     */
    private Object exeBody;

    /**
     * 执行报文
     *
     * @return
     */
    public String getExeBodyStr() {
        return GsonUtil.toJsonString(this.exeBody);
    }
}