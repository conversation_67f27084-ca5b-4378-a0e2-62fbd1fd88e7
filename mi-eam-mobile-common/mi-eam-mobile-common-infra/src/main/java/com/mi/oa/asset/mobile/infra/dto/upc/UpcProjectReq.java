package com.mi.oa.asset.mobile.infra.dto.upc;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * Description: your description
 *
 * <AUTHOR>
 * @date 2023/8/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpcProjectReq {
    /**
     * 项目级别，1->1级项目，2->2级项目
     */
    @NotBlank
    @Min(1)
    @Max(2)
    private Integer level;
    /**
     * 分页后显示的当前页数 必填
     */
    @NotBlank
    private Integer page;
    /**
     * 分页后的每一页大小  必填
     */
    @NotBlank
    @Min(1)
    @Max(500)
    @JsonProperty("page_size")
    private Integer pageSize;
    /**
     * 分页后的每一页大小  必填
     */
    @NotBlank
    @JsonProperty("start_time")
    private String startTime;
    /**
     * 分页后的每一页大小  必填
     */
    @NotBlank
    @JsonProperty("end_time")
    private String endTime;
    /**
     * 项目分类id
     */
    @JsonProperty("category_id")
    private Integer categoryId;
    /**
     * 项目名称
     */
    @JsonProperty("project_name")
    private String projectName;
    /**
     * 项目代码
     */
    @JsonProperty("project_code")
    private String projectCode;
}
