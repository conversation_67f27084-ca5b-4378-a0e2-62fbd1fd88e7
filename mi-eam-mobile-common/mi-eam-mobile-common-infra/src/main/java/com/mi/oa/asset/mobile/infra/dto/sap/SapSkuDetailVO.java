package com.mi.oa.asset.mobile.infra.dto.sap;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * description: sap物料明细出参-生态链
 *
 * <AUTHOR>
 * @date 2023/8/29
 */
@Data
public class SapSkuDetailVO implements Serializable {
    private static final long serialVersionUID =1L;
    @SerializedName("WERKS")
    @JsonProperty("WERKS")
    private String sapFactory;
    @SerializedName("MATNR")
    @JsonProperty("MATNR")
    private String skuCode;
    @SerializedName("MAKTX")
    @JsonProperty("MAKTX")
    private String skuName;
}
