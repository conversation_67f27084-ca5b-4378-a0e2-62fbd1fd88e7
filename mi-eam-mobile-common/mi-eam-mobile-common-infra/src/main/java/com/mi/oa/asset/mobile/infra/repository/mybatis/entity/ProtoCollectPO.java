package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/01/17/03:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_collect",autoResultMap = true)
public class ProtoCollectPO {
    /**
     * 主键 collect_id
     */
    private String collectId;

    /**
     * 汇总单号 collect_code
     */
    private String collectCode;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 项目名称 project_name
     */
    private String projectName;

    /**
     * 阶段 stage_name
     */
    private String stageName;

    /**
     * PM工号 pm_emp_code
     */
    private String pmEmpCode;

    /**
     * PM账号 pm_user_code
     */
    private String pmUserCode;

    /**
     * PM名称 pm_user_name
     */
    private String pmUserName;

    /**
     * 截止日期 deal_line
     */
    private Date dealLine;

    /**
     * 试产工厂代码 factory_code
     */
    private String factoryCode;

    /**
     * 试产工厂 factory_name
     */
    private String factoryName;

    /**
     * 备注 remark
     */
    private String remark;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 记录状态 auditing
     */
    private String auditing;

    /**
     * 汇总记录状态 summary_auditing
     */
    private String summaryAuditing;
}