package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/06/23/11:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_device_plan",autoResultMap = true)
public class ProtoDevicePlanPO {
    /**
     * 主键 plan_id
     */
    @TableId(type = IdType.INPUT)
    private String planId;

    /**
     * 计划状态 plan_status
     */
    private String planStatus;

    /**
     * 计划编号 plan_code
     */
    private String planCode;

    /**
     * 生成范围方式 create_way
     */
    private String createWay;

    /**
     * 部门范围 dept_name
     */
    private String deptName;

    /**
     * 部门范围编码 dept_code
     */
    private String deptCode;

    /**
     * 人员范围 user_name
     */
    private String userName;

    /**
     * 人员范围编码 user_code
     */
    private String userCode;

    /**
     * 仓库范围 house_name
     */
    private String houseName;

    /**
     * 仓库范围编码 house_code
     */
    private String houseCode;

    /**
     * 项目范围 project_name
     */
    private String projectName;

    /**
     * 项目范围编码 project_code
     */
    private String projectCode;

    /**
     * 盘点范围 scan_range
     */
    private String scanRange;

    /**
     * 盘点主题 scan_name
     */
    private String scanName;

    /**
     * 盘点描述 memo
     */
    private String memo;

    /**
     * 计划开始日期 plan_start_date
     */
    private Date planStartDate;

    /**
     * 计划结束日期 plan_end_date
     */
    private Date planEndDate;

    /**
     * 盘点开始日期 scan_start_date
     */
    private Date scanStartDate;

    /**
     * 盘点结束日期 scan_end_date
     */
    private Date scanEndDate;

    /**
     * 编制人 edit_user_name
     */
    private String editUserName;

    /**
     * 编制人账号 edit_user_code
     */
    private String editUserCode;

    /**
     * 编制人工号 edit_emp_code
     */
    private String editEmpCode;

    /**
     * 编制时间 edit_date
     */
    private Date editDate;

    /**
     * 检查项模板 check_template
     */
    private String checkTemplate;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 是否已生成任务 is_create
     */
    private String isCreate;

    /**
     * 消息发送时间 msg_send_date
     */
    private Date msgSendDate;

    /**
     * 日常消息发送时间 dailymsg_send_date
     */
    private Date dailymsgSendDate;

    /**
     * 预盘点消息发送时间 pre_send_date
     */
    private Date preSendDate;

    /**
     * 试产阶段 stage_name
     */
    private String stageName;
}