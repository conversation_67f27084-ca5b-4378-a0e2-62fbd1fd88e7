package com.mi.oa.asset.mobile.infra.dto.sap;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 根据项目获取阶段及物料信息（领用单申请）
 * @date 2021/11/2 10:11
 */
@Data
public class SapProjectInfoDTO implements Serializable {

    /**
     * 项目
     */
    @SerializedName("PROJECT")
    @JsonProperty("PROJECT")
    private String project;

    @SerializedName("Z_TYPE")
    @JsonProperty("Z_TYPE")
    private String zType;

    @SerializedName("MATNR")
    @JsonProperty("MATNR")
    private String matnr;

    /**
     * 物料描述
     */
    @SerializedName("MAKTX")
    @JsonProperty("MAKTX")
    private String maktx;

    /**
     * 笔电上线新增三个字段 SKUCODE SMAKTX GOODSID
     */
    @SerializedName("SKUCODE")
    private String skucode;

    @SerializedName("SMAKTX")
    private String smaktx;

    @SerializedName("GOODSID")
    private String goodsid;

    @SerializedName("Z_PUR_LEVEL_H")
    @JsonProperty("Z_PUR_LEVEL_H")
    private String zPurLevelH;

    @SerializedName("Z_ASS_LEVEL_H")
    @JsonProperty("Z_ASS_LEVEL_H")
    private String zAssLevelH;

    @SerializedName("Z_P_TYPE")
    @JsonProperty("Z_P_TYPE")
    private String zPType;

    @SerializedName("Z_CPU")
    @JsonProperty("Z_CPU")
    private String zCpu;

    @SerializedName("Z_EMMC")
    @JsonProperty("Z_EMMC")
    private String zEmmc;

    @SerializedName("Z_DDR")
    @JsonProperty("Z_DDR")
    private String zDdr;

    @SerializedName("Z_VERSION")
    @JsonProperty("Z_VERSION")
    private String zVersion;

    @SerializedName("Z_COLOR")
    @JsonProperty("Z_COLOR")
    private String zColor;

    @SerializedName("COUNTRY")
    @JsonProperty("COUNTRY")
    private String country;

    @SerializedName("END_DATE")
    @JsonProperty("END_DATE")
    private String endDate;

    /**
     * 项目阶段
     */
    @SerializedName("BOM_VERSION")
    @JsonProperty("BOM_VERSION")
    private String bomVersion;
}
