package com.mi.oa.asset.mobile.infra.dto.mdm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/12
 */
@Data
public class MdmProjectVO {

    /**
     * 父项目
     */
    private String parentProjectID;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目中文名称
     */
    private String projectNameCN;

    /**
     * 项目英文名
     */
    private String projectNameEN;

    /**
     * 业务线
     */
    private String businessLine;

    /**
     * 项目类型
     */
    private String projectType;

    /**
     * 项目等级
     */
    private String projectLevel;

    /**
     * 产品类别
     */
    private String productType;

    /**
     * 项目系列
     */
    private String productSeries;

    /**
     * 系统来源  只同步R平台的数据
     */
    private String projectSourceSystem;

    /**
     * 试产工厂
     */
    private List<TrialProductionFactoryDTO> trialProductionFactory;

    /**
     * 源项目ID
     */
    private String projectOID;

    /**
     * 父级项目名称
     */
    private String parentProjectName;

    /**
     * 团队
     */
    @JsonProperty("mdm_team")
    @SerializedName("mdm_team")
    private List<TeamDTO> mdmTeam;

    @JsonProperty("mdm_productParameters")
    @SerializedName("mdm_productParameters")
    private List<ProductParameterDTO> productParameters;

    @Data
    public static class ParentDTO{

        /**
         * 编码
         */
        private String code;

        /**
         * 名称
         */
        private String name;
    }

    @Data
    public static class TrialProductionFactoryDTO {

        /**
         * 试产工厂编号
         */
        private String code;

        /**
         * 试产工厂名称
         */
        private String name;

    }

    @Data
    public static class TeamDTO{

        /**
         * 项目ID
         */
        private String projectOID;

        /**
         * 角色ID
         */
        private String roleKey;

        /**
         * 父角色ID
         */
        private String parentRoleKey;

        /**
         * 角色
         */
        private String roleName;

        /**
         * 成员
         */
        private String principals;

        /**
         * ODM成员
         */
        private String plmPrincipals;
    }

    @Data
    public static class ProductParameterDTO{

        /**
         * 参数描述
         */
        private String parameterDesc;

        private ParameterDTO productParameters;

    }

    @Data
    public static class ParameterDTO {

        /**
         * 编码
         */
        private String code;

        /**
         * 名称
         */
        private String name;

    }
}
