package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/08/02/10:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_order_pay",autoResultMap = true)
public class ProtoOrderPayPO {
    /**
     *  id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务单号 order_code
     */
    private String orderCode;

    /**
     * 单据类型 disposal_type
     */
    private String disposalType;

    /**
     * 业务类型 disposal_role
     */
    private String disposalRole;

    /**
     * 支付方式 pay_type
     */
    private String payType;

    /**
     * 支付金额 pay_amount
     */
    private BigDecimal payAmount;

    /**
     * 支付时间 pay_time
     */
    private Date payTime;

    /**
     * 申请日期 apply_date
     */
    private Date applyDate;

    /**
     * 确认状态 confirm_status
     */
    private String confirmStatus;

    /**
     * 申请人账号 user_code
     */
    private String userCode;

    /**
     * 申请人姓名 user_name
     */
    private String userName;

    /**
     * 申请人工号 emp_code
     */
    private String empCode;

    /**
     * 申请人部门编码 dept_code
     */
    private String deptCode;

    /**
     * 申请人部门全称 dept_name
     */
    private String deptName;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 业务类型0:内购 1:处置
     */
    private String busType;

    /**
     * 员工端上传凭证状态 0:未上传 1:已上传
     */
    private String taskStatus;

    /**
     * 业务类型 mobile:手机，tv:电视，wearable:可穿戴
     */
    private String mrpType;
}