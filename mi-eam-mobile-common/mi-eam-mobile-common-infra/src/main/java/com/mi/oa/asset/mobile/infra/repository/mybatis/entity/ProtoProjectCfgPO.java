package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/01/21/11:11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_project_cfg", autoResultMap = true)
public class ProtoProjectCfgPO {
    /**
     * 主键 cfg_id
     */
    @TableId(type = IdType.INPUT)
    private String cfgId;

    /**
     * 流水号 cfg_code
     */
    private String cfgCode;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 项目名称 project_name
     */
    private String projectName;

    /**
     * PM工号 pm_emp_code
     */
    private String pmEmpCode;

    /**
     * PM账号 pm_user_code
     */
    private String pmUserCode;

    /**
     * PM名称 pm_user_name
     */
    private String pmUserName;

    /**
     * 试产工厂 factory_name
     */
    private String factoryName;

    /**
     * 试产工厂代码 factory_code
     */
    private String factoryCode;

    /**
     * 驻厂人员工号 emp_code
     */
    private String empCode;

    /**
     * 驻场人员账户 user_code
     */
    private String userCode;

    /**
     * 驻厂人员名称 user_name
     */
    private String userName;

    /**
     * 工厂PM email
     */
    private String email;

    /**
     * 试产日期 fac_trial_date
     */
    private Date facTrialDate;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 最后修改人 final_user_name
     */
    private String finalUserName;

    /**
     * 记录状态 auditing
     */
    private String auditing;

    /**
     * 父级项目编码
     */
    private String parentProjectCode;

    /**
     * 项目类型 产品项目 | ODM项目 | 预研项目 | 可穿戴项目
     */
    private String type;

    /**
     * 项目等级  S类|A类|B类|C类
     */
    private String projectLevel;

    /**
     * 产品类别
     */
    private String productType;

    /**
     * 产品系列
     */
    private String productSeries;

    /**
     * 上市时间 list_date
     */
    private Date listDate;

    /**
     * 是否JIS之前机型
     */
    private String isJis;

    /**
     * 业务类型
     */
    private String mrpType;
}