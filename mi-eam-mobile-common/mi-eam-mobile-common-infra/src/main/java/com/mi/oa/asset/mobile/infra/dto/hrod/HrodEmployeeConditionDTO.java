package com.mi.oa.asset.mobile.infra.dto.hrod;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/11/3 22:38
 */

@Data
@SuperBuilder
public class HrodEmployeeConditionDTO extends HrodConditionBaseDTO {

    @SerializedName("emplid")
    @JsonProperty("emplid")
    private String empId;

    @SerializedName("emplids")
    @JsonAdapter(ListToCommaStringAdapter.class)
    @JsonProperty("emplids")
    @JsonSerialize(using = ListToCommaStringSerializer.class)
    private List<String> empIds;

    @SerializedName("oprid")
    @JsonProperty("oprid")
    private String userName;

    @SerializedName("oprids")
    @JsonAdapter(ListToCommaStringAdapter.class)
    @JsonProperty("oprids")
    @JsonSerialize(using = ListToCommaStringSerializer.class)
    private List<String> userNames;

    private String findKey;

    private String phone;

    @SerializedName("searchEmplid")
    @JsonProperty("searchEmplid")
    private String searchEmpId;
}


