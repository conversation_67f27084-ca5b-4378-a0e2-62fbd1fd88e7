package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/08/01/10:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_plan_det",autoResultMap = true)
public class ProtoPlanDetPO {
    /**
     *  id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 业务类型 mrp_type
     */
    private String mrpType;

    /**
     * 小米料号 sku_code
     */
    private String skuCode;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 原台账状态 origin_card_status
     */
    private String originCardStatus;

    /**
     * 物料描述 sku_name
     */
    private String skuName;

    /**
     * 内购计划id inner_purchase_plan_id
     */
    private Integer innerPurchasePlanId;

    /**
     * 设备编号SN device_code
     */
    private String deviceCode;

    /**
     * 内购价格 actual_damages
     */
    private BigDecimal actualDamages;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 备注 memo
     */
    private String memo;

    /**
     * 定价配置编码 price_code
     */
    private String priceCode;

    /**
     * 配置描述 price_desc
     */
    private String priceDesc;

    /**
     * 上市描述
     */
    private String listDesc;

    /**
     * 自提地址编码
     */
    private String locationCode;

    /**
     * 自提地址名称
     */
    private String locationName;
}