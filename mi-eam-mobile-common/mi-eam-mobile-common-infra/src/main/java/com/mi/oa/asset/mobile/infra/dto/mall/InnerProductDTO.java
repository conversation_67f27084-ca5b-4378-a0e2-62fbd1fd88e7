package com.mi.oa.asset.mobile.infra.dto.mall;

import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Copyright (c) 2023 XiaoMi Inc.All Rights Reserved.
 * Description: your description
 *
 * <AUTHOR>
 * @date 2023/3/16 18:38
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InnerProductDTO extends DTO {
    private String mrpType;
    private String deviceCode;
    private String skuCode;
    private String houseCode;
    private String priceCode;
    private String skuName;
    private String houseName;
    private String priceDesc;
    private Integer planId;
    private String planCode;
    private String planName;
    private String locationService;
    private String checkInfo;
    private String deliveryMethod;
    private BigDecimal currPrice;
    private String brief;
    private String pic;
    private String projectCode;
    private String laserCode;
    private String imei;
    private String userCode;
    private Integer purchaseStatus;
    private Integer snStatus;
    private Date startTime;
    private Date endTime;
}
