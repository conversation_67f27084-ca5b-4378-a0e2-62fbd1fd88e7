package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/01/05/03:22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "sys_dept",autoResultMap = true)
public class SysDeptPO implements Serializable {
    private static final long serialVersionUID = -2103586418722347559L;
    /**
     * 部门ID dept_id
     */
    @TableId(type = IdType.INPUT)
    private String deptId;

    /**
     * 部门编码 dept_code
     */
    private String deptCode;

    /**
     * 部门名称 dept_name
     */
    private String deptName;

    /**
     * 部门级别 dept_level
     */
    private BigDecimal deptLevel;

    /**
     * 负责人 manager
     */
    private String manager;

    /**
     * 联系电话 phone
     */
    private String phone;

    /**
     * 是否注销 is_novalid
     */
    private String isNovalid;

    /**
     * 备注 memo
     */
    private String memo;

    /**
     * 部门类别 dept_type
     */
    private String deptType;

    /**
     * 记录添加人 add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人 modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 部门印章 sign_pic
     */
    private String signPic;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 成本中心名称 center_name
     */
    private String centerName;

    /**
     * 成本中心 center_code
     */
    private String centerCode;

    /**
     * 所属事业部编码 bus_dept_code
     */
    private String busDeptCode;

    /**
     * 所属事业部名称 bus_dept_name
     */
    private String busDeptName;

    /**
     * 公司代码 comp_dept_code
     */
    private String compDeptCode;

    /**
     * 工厂代码 fac_dept_code
     */
    private String facDeptCode;

    /**
     * 工厂代码ID fac_dept_id
     */
    private String facDeptId;

    /**
     * 所属事业部ID bus_dept_id
     */
    private String busDeptId;

    /**
     * 地区 address_name
     */
    private String addressName;

    /**
     * 地区ID address_id
     */
    private String addressId;

    /**
     * 生效日期 begin_date
     */
    private Date beginDate;

    /**
     * 使用截止日期 end_date
     */
    private Date endDate;

    /**
     * 部门全名称 long_dept_name
     */
    private String longDeptName;

    /**
     * 采购组 psection
     */
    private String psection;

    /**
     * 公司名称 comp_dept_name
     */
    private String compDeptName;

    /**
     * 父ID parent_id
     */
    private String parentId;

    /**
     * 是否生效 is_valid
     */
    private String isValid;

    /**
     * 树形部门编码 tree_code
     */
    private String treeCode;
}