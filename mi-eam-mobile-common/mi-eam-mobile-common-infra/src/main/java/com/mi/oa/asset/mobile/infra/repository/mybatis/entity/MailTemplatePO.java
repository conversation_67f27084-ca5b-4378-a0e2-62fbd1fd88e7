package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/01/06/11:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "mail_template",autoResultMap = true)
public class MailTemplatePO {
    /**
     * 模板ID template_id
     */
    private String templateId;

    /**
     * 模板标识 template_tag
     */
    private String templateTag;

    /**
     * 模板名称 template_name
     */
    private String templateName;

    /**
     * 功能ID fun_id
     */
    private String funId;

    /**
     * 默认模板 is_default
     */
    private String isDefault;

    /**
     * 带附件 is_attach
     */
    private String isAttach;

    /**
     * 功能名称 fun_name
     */
    private String funName;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 编制人 edit_user
     */
    private String editUser;

    /**
     * 编制人ID edit_userid
     */
    private String editUserid;

    /**
     * 编制日期 edit_date
     */
    private Date editDate;

    /**
     * 模板类型 template_type
     */
    private String templateType;

    /**
     * 领用方类型 apply_user_type
     */
    private String applyUserType;

    /**
     * 业务数据查询sql query_sql
     */
    private String querySql;

    /**
     * 模板内容 template_cont
     */
    private String templateCont;
}