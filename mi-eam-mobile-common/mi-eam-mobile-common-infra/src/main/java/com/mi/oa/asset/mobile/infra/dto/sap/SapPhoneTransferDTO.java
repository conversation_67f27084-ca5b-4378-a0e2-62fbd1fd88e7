package com.mi.oa.asset.mobile.infra.dto.sap;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/8
 */
@Data
public class SapPhoneTransferDTO implements Serializable {

    /**
     * 系统ID
     */
    @SerializedName("SYSTEMID")
    @JsonProperty("EAM")
    private String systemId = "EAM";

    /**
     * 单据类别
     */
    @SerializedName("DJLB")
    @JsonProperty("DJLB")
    private String billType;

    /**
     * 组织
     */
    @SerializedName("EKORG")
    @JsonProperty("EKORG")
    private String organization = "1110";

    /**
     * sap工厂
     */
    @SerializedName("WERKS")
    @JsonProperty("WERKS")
    private String sapFactory = "1110";

    /**
     * 小米业务单据号
     */
    @JsonProperty("MIDOC_NUM")
    @SerializedName("MIDOC_NUM")
    private String midocNum;

    /**
     * 申请单业务类型
     */
    @SerializedName("RBTYP")
    @JsonProperty("RBTYP")
    private String businessType;

    /**
     * 项目
     */
    @SerializedName("PROJECT")
    @JsonProperty("PROJECT")
    private String project;

    @SerializedName("BATCH_ID")
    @JsonProperty("BATCH_ID")
    private String batchId;

    /**
     * 生产类型
     */
    @SerializedName("ZSCLX")
    @JsonProperty("ZSCLX")
    private String productType;

    /**
     * 是否外研
     */
    @SerializedName("ZSFWY")
    @JsonProperty("ZSFWY")
    private String extension;

    /**
     * 创建时间  yyyyMMdd
     */
    @SerializedName("CRDAT")
    @JsonProperty("CRDAT")
    private String createDate;

    /**
     * 申请单创建人
     */
    @SerializedName("CRTER")
    @JsonProperty("CRTER")
    private String createUserName;

    /**
     * 代工厂编码
     */
    @SerializedName("CKGC")
    @JsonProperty("CKGC")
    private String foundryCode = CommonConstant.I_LIFNR;

    /**
     * 良品/不良品
     */
    @SerializedName("GOOD_BAD")
    @JsonProperty("GOOD_BAD")
    private String goodBad;

    /**
     * 试产仓/量产仓
     */
    @SerializedName("WARE_TYPE")
    @JsonProperty("WARE_TYPE")
    private String wareType;

    /**
     * 备注
     */
    @SerializedName("REMARK")
    @JsonProperty("REMARK")
    private String remark;

    /**
     * 研发样机YFS单号
     */
    @SerializedName("ZYFS_NUM")
    @JsonProperty("ZYFS_NUM")
    private String protoYfsNum;

    /**
     * 入库工厂编码（供应商）
     */
    @SerializedName("RKGC")
    @JsonProperty("RKGC")
    private String storageFactoryCode;

    /**
     * 成本中心（发起）
     */
    @SerializedName("ZZ_KOSTL")
    @JsonProperty("ZZ_KOSTL")
    private String zzKostl;

    /**
     * 成本中心（接收）
     */
    @SerializedName("KOSTL")
    @JsonProperty("KOSTL")
    private String kostl;

    /**
     * 申请部门
     */
    @SerializedName("ZZ_DEPAPP")
    @JsonProperty("ZZ_DEPAPP")
    private String applyDept;

    /**
     * 申请者
     */
    @SerializedName("ZZ_REQNAM")
    @JsonProperty("ZZ_REQNAM")
    private String applyPerson;

    /**
     * 需求部门
     */
    @SerializedName("ZZ_DEPREQ")
    @JsonProperty("ZZ_DEPREQ")
    private String demandDept;

    /**
     * 使用人
     */
    @SerializedName("ZZ_APPNAM")
    @JsonProperty("ZZ_APPNAM")
    private String usePerson;

    /**
     * 扣款主体
     */
    @SerializedName("ZKKZT")
    @JsonProperty("ZKKZT")
    private String cutPaymentSubject;

    /**
     * 标记
     */
    @SerializedName("MARK")
    @JsonProperty("MARK")
    private String mark;
    /**
     * 采购凭证类型 ZNB:原材料 ZWW:成品、半成品 ZDB 米通模式
     */
    @SerializedName("BSART")
    @JsonProperty("BSART")
    private String bsart;

    @SerializedName("ZT")
    @JsonProperty("ZT")
    private String zt = "OK";

    @SerializedName("ITEM")
    @JsonProperty("ITEM")
    private List<Item> items;

    @Data
    public static class Item{

        /**
         * 小米业务单据号
         */
        @SerializedName("MIDOC_NUM")
        @JsonProperty("MIDOC_NUM")
        private String midocNum;

        /**
         * 小米单据行号
         */
        @SerializedName("MIDOC_ROW")
        @JsonProperty("MIDOC_ROW")
        private String midocRow;

        /**
         * 小米料号1
         */
        @SerializedName("MATERIAL1")
        @JsonProperty("MATERIAL1")
        private String materialOne;

        /**
         * 数量1
         */
        @SerializedName("QUANTITY1")
        @JsonProperty("QUANTITY1")
        private String quantityOne;

        /**
         * 发放单号
         */
        @SerializedName("MATERIAL2")
        @JsonProperty("MATERIAL2")
        private String outOrderCode;

        /**
         * 申请单号
         */
        @SerializedName("EBELN")
        @JsonProperty("EBELN")
        private String applyNo;

        /**
         * 申请单号行号
         */
        @SerializedName("EBELP")
        @JsonProperty("EBELP")
        private String applyNoRow;

        /**
         * 基本计量单位
         */
        @SerializedName("MEINS1")
        @JsonProperty("MEINS1")
        private String measure = "PC";

        /**
         * ZNB:原材料 ZWW:成品、半成品
         */
        @SerializedName("BSART")
        @JsonProperty("BSART")
        private String purchaseType;
    }
}
