package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/04/14/03:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_project_factory",autoResultMap = true)
public class ProtoProjectFactoryPO {
    /**
     * 主键 factory_id
     */
    @TableId(type = IdType.INPUT)
    private String factoryId;

    /**
     * 代工厂编号 factory_code
     */
    private String factoryCode;

    /**
     * 代工厂名称 factory_name
     */
    private String factoryName;

    /**
     * 项目Id cfg_id
     */
    private String cfgId;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * user_code
     */
    private String userCode;

    /**
     * 驻场人员
     */
    private String userName;

    /**
     * '工厂PM'
     */
    private String factoryPm;
}