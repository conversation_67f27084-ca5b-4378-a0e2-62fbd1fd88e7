package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/04/07/10:22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_auth_data_cfg",autoResultMap = true)
public class ProtoAuthDataCfgPO {
    /**
     * 主键 cfg_id
     */
    private String cfgId;

    /**
     * 记录状态 auditing
     */
    private String auditing;

    /**
     * 接口人姓名 cp_user_name
     */
    private String cpUserName;

    /**
     * 接口人工号 cp_emp_code
     */
    private String cpEmpCode;

    /**
     * 接口人账号 cp_user_code
     */
    private String cpUserCode;

    /**
     * 接口人部门编号 cp_dept_code
     */
    private String cpDeptCode;

    /**
     * 接口人部门全称 cp_long_dept_name
     */
    private String cpLongDeptName;

    /**
     * 代管部门编号 ag_dept_code
     */
    private String agDeptCode;

    /**
     * 代管部门全称 ag_long_dept_name
     */
    private String agLongDeptName;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 业务类型
     */
    private String mrpType;
}