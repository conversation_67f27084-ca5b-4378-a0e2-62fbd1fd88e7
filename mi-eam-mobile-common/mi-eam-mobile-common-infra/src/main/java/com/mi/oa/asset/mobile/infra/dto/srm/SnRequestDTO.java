package com.mi.oa.asset.mobile.infra.dto.srm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/2
 */
@Data
public class SnRequestDTO {

    /**
     * 当前页
     */
    @JsonProperty("IV_PAGE_NO")
    private Integer pageNum = 1;

    /**
     * 每页数量
     */
    @JsonProperty("IV_PAGE_LINES")
    private Integer pageSize = 10000;

    /**
     * asn单号
     */
    @JsonProperty("IV_ASN")
    private String asnNo;
}
