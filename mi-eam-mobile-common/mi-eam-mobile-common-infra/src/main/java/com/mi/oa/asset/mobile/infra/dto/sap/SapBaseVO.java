package com.mi.oa.asset.mobile.infra.dto.sap;

import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Desc SAP 接口返回定义
 * <AUTHOR>
 * @Date 2021/8/30 21:28
 */

@Data
@NoArgsConstructor
public class SapBaseVO<T> {

    private Boolean success;

    private String message;

    /**
     * SAP 反馈数据
     */
    @SerializedName(value = "ET_DATA", alternate = {"MT_DATA"})
    private List<T> data;
    /**
     * 总条数
     */
    @SerializedName("TOTAL")
    private Long total;
    /**
     * 总页数
     */
    @SerializedName("TOTAL_PAGES")
    private Long totalPages;
}


