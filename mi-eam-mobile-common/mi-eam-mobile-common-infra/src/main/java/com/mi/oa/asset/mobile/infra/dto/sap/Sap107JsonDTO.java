package com.mi.oa.asset.mobile.infra.dto.sap;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/5
 */
@Data
public class Sap107JsonDTO {

    /**
     * 业务类型  现在是 收货同步:YJRK 发放同步:YJFF 等
     */
    private String busiType;

    /**
     * 批次
     */
    private String batchId;

    /**
     * 小米业务单据号 收货时时ASN单号 其他的是业务单据号
     */
    private String midocNum;

    /**
     * 代工厂业务单据号
     */
    private String vndocNum;

    /**
     * 代工厂编码  sapFactory
     */
    private String sapFactory = "1110";
    /**
     * 采购凭证类型 ZNB:原材料 ZWW:成品、半成品 ZDB
     */
    private String bsart;

    /**
     * factory
     */
    private String factory;
    /**
     * 订单类型为ZDB时  WARE_TYPE和GOOD_BAD字段都要传GM
     */
    private String wareType;


    private List<SapItemDTO> itemDTOList;

    @Data
    public static class SapItemDTO{

        /**
         * 批次行号
         */
        private String batchRow;

        /**
         * 小米业务单据号行项目
         */
        private String  midocRow;

        /**
         * 小米料号1
         */
        private String materialOne;

        /**
         * 物料描述1
         */
        private String matDescOne;

        /**
         * 数量1
         */
        private String quantityOne;

        /**
         * 转移申请bpm通过是传项目
         */
        private String miProjOne;

        /**
         * 发放批次号（转移申请通过才需要传）
         */
        private String commItm;

        /**
         * 代工厂业务单据号
         */
        private String vndocNum;

        /**
         * 代工厂业务单据号行项目
         */
        private String vndocRow;
    }
}
