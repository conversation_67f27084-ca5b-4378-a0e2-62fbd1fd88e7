package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/02/28/04:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_baseline_record",autoResultMap = true)
public class ProtoBaselineRecordPO {
    /**
     * 主键 baseline_record_id
     */
    @TableId(type = IdType.INPUT)
    private String baselineRecordId;

    /**
     * 申请单号 apply_code
     */
    private String applyCode;

    /**
     * 申请人 apply_user_name
     */
    private String applyUserName;

    /**
     * 申请人账号 apply_user_code
     */
    private String applyUserCode;

    /**
     * 申请人部门编码 dept_code
     */
    private String deptCode;

    /**
     * 申请人部门名称 dept_name
     */
    private String deptName;

    /**
     * 变更类型 change_type
     */
    private String changeType;

    /**
     * 记录状态 auditing
     */
    private String auditing;

    /**
     * 申请时间 apply_date
     */
    private Date applyDate;

    /**
     * 申请人总经理 apply_user_manager_name
     */
    private String applyUserManagerName;

    /**
     * 申请人总经理账号 apply_user_manager_code
     */
    private String applyUserManagerCode;

    /**
     * bpm唯一标识 business_key
     */
    private String businessKey;

    /**
     * 基线编码 baselineCode
     */
    private String baselineCode;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}