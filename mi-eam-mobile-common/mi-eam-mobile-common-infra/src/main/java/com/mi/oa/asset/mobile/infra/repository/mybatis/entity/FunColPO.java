package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/01/07/11:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "fun_col",autoResultMap = true)
public class FunColPO {
    /**
     * 字段ID col_id
     */
    @TableId(type = IdType.INPUT)
    private String colId;

    /**
     * 功能ID fun_id
     */
    private String funId;

    /**
     * 字段代码 col_code
     */
    private String colCode;

    /**
     * 字段名称 col_name
     */
    private String colName;

    /**
     * 数据类型 data_type
     */
    private String dataType;

    /**
     * 是否更新 is_update
     */
    private String isUpdate;

    /**
     * 控件类型 col_control
     */
    private String colControl;

    /**
     * 控件名称 control_name
     */
    private String controlName;

    /**
     * 数据样式 format_id
     */
    private String formatId;

    /**
     * 缺省值 default_value
     */
    private String defaultValue;

    /**
     * 是否统计列 is_statcol
     */
    private String isStatcol;

    /**
     * 字段序号 col_index
     */
    private BigDecimal colIndex;

    /**
     * 是否编辑 is_edit
     */
    private String isEdit;

    /**
     * 是否必填 is_notnull
     */
    private String isNotnull;

    /**
     * BLURFUNC blur_func
     */
    private String blurFunc;

    /**
     * KEYUPFUNC keyup_func
     */
    private String keyupFunc;

    /**
     * 是否表格编辑 is_gridedit
     */
    private String isGridedit;

    /**
     * 记录添加人 add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人 modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}