package com.mi.oa.asset.mobile.infra.dto.sap;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * description: sap物料请求入参
 *
 * <AUTHOR>
 * @date 2023/8/29
 */
@Data
public class SapSkuDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @SerializedName("SYSTEMID")
    @JsonProperty("SYSTEMID")
    private String systemId = "EAM"; // 固定值
    @SerializedName("MATNR")
    @JsonProperty("MATNR")
    private String skuCode;
    @SerializedName("LAEDA_STR")
    @JsonProperty("LAEDA_STR")
    private String startTime;
    @SerializedName("LAEDA_END")
    @JsonProperty("LAEDA_END")
    private String endTime;
    @JsonProperty("PAGE_NO")
    @SerializedName("PAGE_NO")
    private Integer page;
    @SerializedName("PAGE_LINES")
    @JsonProperty("PAGE_LINES")
    private Integer pageSize;
    @SerializedName("IT_ITEMS")
    @JsonProperty("IT_ITEMS")
    private List<SapSkuItemDTO> items;

    @Data
    public static class SapSkuItemDTO implements Serializable {
        private static final long serialVersionUID = 1L;
        @SerializedName("WERKS")
        @JsonProperty("WERKS")
        private String sapFactory;
    }
}
