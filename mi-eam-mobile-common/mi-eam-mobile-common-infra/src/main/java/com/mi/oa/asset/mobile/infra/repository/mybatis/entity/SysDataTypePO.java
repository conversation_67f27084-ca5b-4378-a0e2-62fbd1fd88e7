package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/04/06/03:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "sys_datatype",autoResultMap = true)
public class SysDataTypePO {
    /**
     * 设置ID dtype_id
     */
    @TableId(type = IdType.INPUT)
    private String dtypeId;

    /**
     * 数据类别名称 dtype_name
     */
    private String dtypeName;

    /**
     * 权限字段 dtype_field
     */
    private String dtypeField;

    /**
     * 来源功能 funid
     */
    private String funid;

    /**
     * 来源字段 fun_field
     */
    private String funField;

    /**
     * 显示字段 fun_vfield
     */
    private String funVfield;

    /**
     * 记录添加人 add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人 modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 控件代码 control_code
     */
    private String controlCode;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 权限编号 dtype_code
     */
    private String dtypeCode;
}