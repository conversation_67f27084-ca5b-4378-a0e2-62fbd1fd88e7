package com.mi.oa.asset.mobile.infra.dto.hrod;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.google.gson.JsonPrimitive;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/17 12:35
 */
public class ListToCommaStringSerializer extends JsonSerializer<List<String>> {
    @Override
    public void serialize(List<String> comparables, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if(!CollectionUtils.isEmpty(comparables)) {
            jsonGenerator.writeString(StringUtils.join(comparables, ','));
        }
    }
}
