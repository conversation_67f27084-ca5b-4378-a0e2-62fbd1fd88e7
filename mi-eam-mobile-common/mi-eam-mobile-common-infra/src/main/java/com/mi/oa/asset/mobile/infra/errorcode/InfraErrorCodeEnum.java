package com.mi.oa.asset.mobile.infra.errorcode;

import com.mi.oa.infra.oaucf.core.exception.InfraErrorCode;

/**
 * by roger
 */
public enum InfraErrorCodeEnum implements InfraErrorCode {

    INFRA_UNKNOWN_ERROR(1, "基础设施层未知错误"),
    INFRA_UTILS_ERROR(2, "基础设施层工具错误"),
    IDM_SERVICE_ERROR(3, "imd调用异常"),
    IDM_USER_NOT_FOUNT(4, "idm没有找到用户数据");


    /**
     * 具体错误码
     */
    private int errCode;
    /**
     * 描述
     */
    private String errDesc;

    @Override
    public int getBizCode() {
        return 0;
    }

    @Override
    public int getErrorCode() {
        return errCode;
    }

    @Override
    public String getErrDesc() {
        return this.errDesc;
    }

    /**
     * 构造方法
     *
     * @param errCode
     * @param errDesc
     */
    InfraErrorCodeEnum(int errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }


}
