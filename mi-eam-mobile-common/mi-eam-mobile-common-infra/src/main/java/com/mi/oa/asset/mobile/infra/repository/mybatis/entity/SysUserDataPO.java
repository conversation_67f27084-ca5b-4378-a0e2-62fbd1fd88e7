package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/04/06/02:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "sys_user_data",autoResultMap = true)
public class SysUserDataPO {
    /**
     * 设置ID user_data_id
     */
    @TableId(type = IdType.INPUT)
    private String userDataId;

    /**
     * 用户ID user_id
     */
    private String userId;

    /**
     * 数据权限ID dtype_id
     */
    private String dtypeId;

    /**
     * 记录添加人 add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人 modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 数据值 dtype_data
     */
    private String dtypeData;

    /**
     * 是否含下级 has_sub
     */
    private String hasSub;

    /**
     * 显示值 display
     */
    private String display;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 角色ID role_id
     */
    private String roleId;
}