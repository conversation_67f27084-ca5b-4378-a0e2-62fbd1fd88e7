package com.mi.oa.asset.mobile.infra.dto.hrod;

import lombok.Data;

import java.util.Date;

/**
 * @Desc 员工信息
 * <AUTHOR>
 * @Date 2021/11/4 15:21
 */

@Data
public class EmployeeInfo {

    private String userName;

    private String displayName;

    private String employeeId;

    private String deptId;

    private String deptName;

    private String deptIdLv1;

    private String deptNameLv1;

    private String deptIdLv2;

    private String deptNameLv2;

    private String deptIdLv3;

    private String deptNameLv3;

    private String deptIdLv4;

    private String deptNameLv4;

    private String deptIdLv5;

    private String deptNameLv5;

    private String email;

    private String hrStatus;

    private String legalId;

    private String companyName;

    private String companyCode;

    private String companyCountryCodeChar3;

    private String costCenter;

    private String costCenterDesc;

    private String deptCostCenter;

    private String deptCostCenterDesc;

    private String leaderUserName;

    private String leaderDisplayName;

    private Date entryDate;

    public String getCompanyCode() {
        if(null == costCenter || 0 == costCenter.length() || costCenter.length() < 5) return null;

        return costCenter.substring(1, 5);
    }

    public String getFullDeptName() {
        StringBuilder fullDeptName = new StringBuilder(deptNameLv1);

        if(null != deptNameLv2 && 0 != deptNameLv2.length()) {
            fullDeptName.append("-" + deptNameLv2);
        }

        if(null != deptNameLv3 && 0 != deptNameLv3.length()) {
            fullDeptName.append("-" + deptNameLv3);
        }

        if(null != deptNameLv4 && 0 != deptNameLv4.length()) {
            fullDeptName.append("-" + deptNameLv4);
        }

        if(null != deptNameLv5 && 0 != deptNameLv5.length()) {
            fullDeptName.append("-" + deptNameLv5);
        }

        return fullDeptName.toString();
    }
}


