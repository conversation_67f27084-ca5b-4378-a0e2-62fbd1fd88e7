package com.mi.oa.asset.mobile.infra.dto.mdm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/8/17 14:29
 */

@Data
@SuperBuilder(toBuilder = true)
public class MdmBaseDTO {

    private String queryKey;

    @Builder.Default
    @SerializedName("pageIndex")
    @JsonProperty("pageIndex")
    private Integer pageNum = 1;

    @Builder.Default
    private Integer pageSize = 200;


    /**
     * 范围 yyyy-MM-dd HH:mm:ss, yyyy-MM-dd HH:mm:ss
     */
    private String modelPubDate;
}
