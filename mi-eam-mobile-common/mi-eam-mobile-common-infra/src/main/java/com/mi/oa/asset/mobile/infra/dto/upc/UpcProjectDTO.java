package com.mi.oa.asset.mobile.infra.dto.upc;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Description: your description
 *
 * <AUTHOR>
 * @date 2023/8/21
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpcProjectDTO implements Serializable {
    @SerializedName("page")
    private Integer page;
    @SerializedName("pageSize")
    private Integer pageSize;
    @SerializedName("total")
    private Long total;
    @SerializedName("data")
    private List<UpcProjectDetailDTO> data;
}
