package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

import lombok.*;

/**
 * <AUTHOR>
 * @date 2022/01/25/05:19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "sys_user_role",autoResultMap = true)
public class SysUserRolePO {
    /**
     * 设置ID user_role_id
     */
    @TableId(type = IdType.INPUT)
    private String userRoleId;

    /**
     * 用户ID user_id
     */
    private String userId;

    /**
     * 角色ID role_id
     */
    private String roleId;

    /**
     * 记录添加人 add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人 modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}