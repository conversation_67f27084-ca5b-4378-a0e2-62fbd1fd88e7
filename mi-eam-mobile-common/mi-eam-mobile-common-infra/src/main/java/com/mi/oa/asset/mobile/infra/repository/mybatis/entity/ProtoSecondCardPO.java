package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/01/07/02:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_second_card",autoResultMap = true)
public class ProtoSecondCardPO {
    /**
     * 主键 device_id
     */
    @TableId(type = IdType.INPUT)
    private String deviceId;

    /**
     * 业务类型 mrp_type
     */
    private String mrpType;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 试产阶段 stage_name
     */
    private String stageName;

    /**
     * 小米料号 sku_code
     */
    private String skuCode;

    /**
     * 描述 sku_name
     */
    private String skuName;

    /**
     * 仓库编码 house_code
     */
    private String houseCode;

    /**
     * 仓库名称 house_name
     */
    private String houseName;

    /**
     * 是否良品 is_complete
     */
    private String isComplete;

    /**
     * 数量 num
     */
    private BigDecimal num;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;
}