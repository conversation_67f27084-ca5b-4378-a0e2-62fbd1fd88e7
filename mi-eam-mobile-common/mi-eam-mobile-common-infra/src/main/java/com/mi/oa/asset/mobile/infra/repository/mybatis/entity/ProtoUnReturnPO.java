package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/02/25/02:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@TableName(value = "proto_unreturn",autoResultMap = true)
public class ProtoUnReturnPO {
    /**
     * 主键 unreturn_id
     */
    @TableId(type = IdType.INPUT)
    private String unreturnId;

    /**
     * 处置单号 unreturn_code
     */
    private String unreturnCode;

    /**
     * 记录状态 auditing
     */
    private String auditing;

    /**
     * 项目名称 project_name
     */
    private String projectName;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 申请人 apply_user_name
     */
    private String applyUserName;

    /**
     * 申请人账号 apply_user_code
     */
    private String applyUserCode;

    /**
     * 申请人工号 apply_emp_code
     */
    private String applyEmpCode;

    /**
     * 申请部门 dept_name
     */
    private String deptName;

    /**
     * 申请部门编号 dept_code
     */
    private String deptCode;

    /**
     * 申请日期 apply_date
     */
    private Date applyDate;

    /**
     * 处置原因 reason
     */
    private String reason;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * bpm唯一标识 business_key
     */
    private String businessKey;

    /**
     * 确认人 check_user
     */
    private String checkUser;

    /**
     * 确认人账号 check_user_code
     */
    private String checkUserCode;

    /**
     * 确认人工号 check_emp_code
     */
    private String checkEmpCode;

    /**
     * 用户类型 apply_user_type
     */
    private String applyUserType;

    /**
     * 处置数量 disposal_num
     */
    private BigDecimal disposalNum;

    /**
     * 处置规则 disposal_role
     */
    private String disposalRole;

    /**
     * 处置类型 disposal_type
     */
    private String disposalType;

    /**
     * 丢失地点 lose_location
     */
    private String loseLocation;

    /**
     * 丢失日期 lose_date
     */
    private Date loseDate;

    /**
     * 支付方式 pay_type
     */
    private String payType;

    /**
     * 数据泄露风险自评 data_risk
     */
    private String dataRisk;

    /**
     * 支付金额 pay_amount
     */
    private BigDecimal payAmount;

    /**
     * 存储信息明细 store_data
     */
    private String storeData;

    /**
     * 信息安全措施 data_safe_method
     */
    private String dataSafeMethod;

    /**
     * 支付订单号 pay_order_no
     */
    private String payOrderNo;

    /**
     * 支付交易号 pay_transaction_no
     */
    private String payTransactionNo;

    /**
     * 支付时间 pay_time
     */
    private Date payTime;

    /**
     * 用户支付凭证1 pay_voucher1
     */
    private String payVoucher1;

    /**
     * 用户支付凭证2 pay_voucher2
     */
    private String payVoucher2;

    /**
     * 用户支付凭证3 pay_voucher3
     */
    private String payVoucher3;

    /**
     * 资金核对凭证1 finance_voucher1
     */
    private String financeVoucher1;

    /**
     * 资金核对凭证2 finance_voucher2
     */
    private String financeVoucher2;

    /**
     * 资金核对凭证3 finance_voucher3
     */
    private String financeVoucher3;

    /**
     * 单据状态 order_status
     */
    private String orderStatus;

    /**
     * 处置规则code disposal_role_code
     */
    private String disposalRoleCode;

    /**
     * 员工端上传凭证状态 0:未上传 1:已上传
     */
    private String taskStatus;

    /**
     * 确认状态
     */
    private String confirmStatus;

    /**
     * 业务类型
     */
    private String mrpType;
}