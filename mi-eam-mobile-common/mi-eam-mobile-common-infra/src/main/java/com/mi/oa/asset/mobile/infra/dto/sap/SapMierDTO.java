package com.mi.oa.asset.mobile.infra.dto.sap;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import lombok.Data;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/4
 */
@Data
public class SapMierDTO {

    /**
     * 固定值 M10001
     */
    @JsonProperty("I_LIFNR")
    private String lif = CommonConstant.I_LIFNR;

    /**
     * 固定值 ZHS
     */
    @JsonProperty("LANG")
    private String lang = "ZHS";

    /**
     * 固定值 MierAPP
     */
    @JsonProperty("Side")
    private String side = "MierAPP";

    @JsonProperty("is_header")
    private MainOrder mainOrder;

    @JsonProperty("it_items")
    private List<OrderItems> items;

    @Data
    public static class MainOrder{

        /**
         * 业务类型 收货标识
         */
        @JsonProperty("BUSI_TYPE")
        private String busiType;

        /**
         * 批次号 系统生成
         */
        @JsonProperty("BATCH_ID")
        private String batchId;

        /**
         * 代工厂编码 SAP工厂
         * 特殊工厂[{"agent_factory_code":"P111B","sap_factory_code":"111B"}]
         * 如果factory是 P111B或111B时 vendor = factory
         */
        @JsonProperty("VENDOR")
        private String vendor = CommonConstant.I_LIFNR;

        /**
         * 采购组织 写死 1110(手机工程机)
         */
        @JsonProperty("PURC_ORG")
        private String purcOrg = "1110";

        /**
         * 业务日期  yyyyMMdd
         */
        @JsonProperty("BUSI_DATE")
        private String busiDate;

        /**
         * 小米业务单据号  10位
         */
        @JsonProperty("MIDOC_NUM")
        private String midocNum;

        /**
         * 代工厂业务单据号
         */
        @JsonProperty("VNDOC_NUM")
        private String vndocNum;

        /**
         * 原批次号码
         */
        @JsonProperty("ORI_BC_NUM")
        private String oriBcNum;

        /**
         * 备注
         */
        @JsonProperty("COMM_HDR")
        private String commHdr;

        private String wareType;
    }

    @Data
    public static class OrderItems {

        /**
         *  批次号
         */
        @JsonProperty("BATCH_ID")
        private String batchId;

        /**
         * 批次行号
         */
        @JsonProperty("BATCH_ROW")
        private String batchRow;

        /**
         * 小米业务单据号
         */
        @JsonProperty("MIDOC_NUM")
        private String midocNum;

        /**
         * 小米业务单据号行项目
         */
        @JsonProperty("MIDOC_ROW")
        private String midocRow;

        /**
         * 代工厂业务单据号
         */
        @JsonProperty("VNDOC_NUM")
        private String vndocNum;

        /**
         * 代工厂业务单据号行项目
         */
        @JsonProperty("VNDOC_ROW")
        private String vndocRow;

        /**
         * 小米料号1
         */
        @JsonProperty("MATERIAL1")
        private String materialOne;

        /**
         * 物料描述1
         */
        @JsonProperty("MAT_DESC1")
        private String matDescOne;

        /**
         * 数量1
         */
        @JsonProperty("QUANTITY1")
        private String quantityOne;

        /**
         * 项目1
         */
        @JsonProperty("MI_PROJ1")
        private String miProjOne;

        /**
         * 小米料号2
         */
        @JsonProperty("MATERIAL2")
        private String  materialTwo;

        /**
         * 物料描述2
         */
        @JsonProperty("MAT_DESC2")
        private String matDescTwo;

        /**
         * 数量2
         */
        @JsonProperty("QUANTITY2")
        private String quantityTwo;

        /**
         * 项目2
         */
        @JsonProperty("MI_PROJ2")
        private String miProjTwo;

        /**
         * 试产仓/量产仓 收货是空  发放是TP 写死
         */
        @JsonProperty("WARE_TYPE")
        private String wareType;

        /**
         * 良品/不良品 写死GM
         */
        @JsonProperty("GOOD_BAD")
        private String goodBad = "GM";

        /**
         * 工单超耗
         */
        @JsonProperty("WO_CONSUM")
        private String woConsum;

        /**
         * 备注
         */
        @JsonProperty("COMM_ITM")
        private String commItm;
    }
}
