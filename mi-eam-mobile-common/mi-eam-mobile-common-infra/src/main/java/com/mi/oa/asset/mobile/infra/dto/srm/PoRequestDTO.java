package com.mi.oa.asset.mobile.infra.dto.srm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/1
 */
@Data
public class PoRequestDTO {

    /**
     * 发货日期
     */
    @JsonProperty("BIZ_DATE")
    private String bizDate;

    /**
     * PO单号
     */
    @JsonProperty("ZZPO_NO")
    private String poNo;

    /**
     * 电视样机领用代工厂（供应商） 固定值
     */
    @JsonProperty("I_LIFNR")
    private String lif;
}
