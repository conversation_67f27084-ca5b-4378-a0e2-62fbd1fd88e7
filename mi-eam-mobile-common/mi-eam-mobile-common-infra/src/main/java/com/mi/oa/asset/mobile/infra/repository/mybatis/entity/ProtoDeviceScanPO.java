package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/06/23/02:33
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_device_scan",autoResultMap = true)
public class ProtoDeviceScanPO {
    /**
     * 主键 scan_id
     */
    private String scanId;

    /**
     * 是否生效 is_valid
     */
    private String isValid;

    /**
     * 任务编号 scan_code
     */
    private String scanCode;

    /**
     * 计划编号 plan_code
     */
    private String planCode;

    /**
     * 盘点状态 scan_status
     */
    private String scanStatus;

    /**
     * 工程机数量 device_num
     */
    private BigDecimal deviceNum;

    /**
     * 计划开始时间 plan_start_date
     */
    private Date planStartDate;

    /**
     * 计划结束时间 plan_end_date
     */
    private Date planEndDate;

    /**
     * 盘点开始时间 scan_start_date
     */
    private Date scanStartDate;

    /**
     * 盘点结束时间 scan_end_date
     */
    private Date scanEndDate;

    /**
     * 盘点人 user_name
     */
    private String userName;

    /**
     * 盘点人账号 user_code
     */
    private String userCode;

    /**
     * 盘点人工号 emp_code
     */
    private String empCode;

    /**
     * 仓库 house_name
     */
    private String houseName;

    /**
     * 仓库编码 house_code
     */
    private String houseCode;

    /**
     * 盘点范围 scan_range
     */
    private String scanRange;

    /**
     * 盘点范围类型 create_way
     */
    private String createWay;

    /**
     * 盘点计划ID plan_id
     */
    private String planId;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 部门编码 dept_code
     */
    private String deptCode;

    /**
     * 部门全名称 long_dept_name
     */
    private String longDeptName;

    /**
     * 已盘 yp_num
     */
    private BigDecimal ypNum;

    /**
     * 未盘 wp_num
     */
    private BigDecimal wpNum;

    /**
     * 盘亏 pk_num
     */
    private BigDecimal pkNum;

    /**
     * 盘盈 py_num
     */
    private BigDecimal pyNum;

    /**
     * A error_a
     */
    private BigDecimal errorA;

    /**
     * B error_b
     */
    private BigDecimal errorB;

    /**
     * C error_c
     */
    private BigDecimal errorC;

    /**
     * D error_d
     */
    private BigDecimal errorD;

    /**
     * E error_e
     */
    private BigDecimal errorE;

    /**
     * F error_f
     */
    private BigDecimal errorF;

    /**
     * G error_g
     */
    private BigDecimal errorG;

    /**
     * H error_h
     */
    private BigDecimal errorH;

    /**
     * 盘点主题 scan_name
     */
    private String scanName;

    /**
     * 盘点进度 scan_progress
     */
    private BigDecimal scanProgress;

    /**
     * 是否超出盘点时间 is_out_time
     */
    @TableField(typeHandler = ZonedDateTimeBigIntTypeHandler.class)
    private ZonedDateTime isOutTime;

    /**
     * 盘点方式 scan_way
     */
    private String scanWay;

    /**
     * 通知人工号 send_emp_code
     */
    private String sendEmpCode;

    /**
     * 通知人账号 send_user_code
     */
    private String sendUserCode;

    /**
     * 通知人 send_user_name
     */
    private String sendUserName;

    /**
     * 是否已发送 is_send
     */
    private String isSend;
}