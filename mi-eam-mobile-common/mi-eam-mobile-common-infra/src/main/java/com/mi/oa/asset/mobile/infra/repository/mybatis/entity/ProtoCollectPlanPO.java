package com.mi.oa.asset.mobile.infra.repository.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mi.oa.infra.oaucf.mybatis.handler.ZonedDateTimeBigIntTypeHandler;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/01/17/03:32
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TableName(value = "proto_collect_plan",autoResultMap = true)
public class ProtoCollectPlanPO {
    /**
     * 主键 plan_id
     */
    private String planId;

    /**
     * 记录状态 auditing
     */
    private String auditing;

    /**
     * 项目编号 project_code
     */
    private String projectCode;

    /**
     * 阶段 stage_name
     */
    private String stageName;

    /**
     * PM名称 pm_user_name
     */
    private String pmUserName;

    /**
     * PM工号 pm_emp_code
     */
    private String pmEmpCode;

    /**
     * PM账号 pm_user_code
     */
    private String pmUserCode;

    /**
     * 申请人部门编号 dept_code
     */
    private String deptCode;

    /**
     * 申请人部门名称 dept_name
     */
    private String deptName;

    /**
     * 申请人 apply_user_name
     */
    private String applyUserName;

    /**
     * 申请人工号 apply_emp_code
     */
    private String applyEmpCode;

    /**
     * 申请人账号 apply_user_code
     */
    private String applyUserCode;

    /**
     * 使用人 user_name
     */
    private String userName;

    /**
     * 使用人工号 emp_code
     */
    private String empCode;

    /**
     * 使用人账号 user_code
     */
    private String userCode;

    /**
     * 申请类别 apply_type
     */
    private String applyType;

    /**
     * 是否MRP第三方领用 is_other
     */
    private String isOther;

    /**
     * 第三方供应商编号 provider_code
     */
    private String providerCode;

    /**
     * 第三方供应商描述 provider_name
     */
    private String providerName;

    /**
     * 第三方通知邮箱 provider_email
     */
    private String providerEmail;

    /**
     * 扣款主体 debit_onwer
     */
    private String debitOnwer;

    /**
     * 样机生产地 make_cn
     */
    private String makeCn;

    /**
     * 预研项目编号 pre_project_code
     */
    private String preProjectCode;

    /**
     * 汇总单号 collect_code
     */
    private String collectCode;

    /**
     * 直属领导 leader
     */
    private String leader;

    /**
     * 备注 remark
     */
    private String remark;

    /**
     * 添加人ID add_userid
     */
    private String addUserid;

    /**
     * 添加时间 add_date
     */
    private Date addDate;

    /**
     * 修改人ID modify_userid
     */
    private String modifyUserid;

    /**
     * 修改时间 modify_date
     */
    private Date modifyDate;

    /**
     * 系统租户ID tenant_id
     */
    private String tenantId;

    /**
     * 领用方类型 apply_user_type
     */
    private String applyUserType;

    /**
     * 计划单号 plan_code
     */
    private String planCode;

    /**
     * 申请单号 apply_code
     */
    private String applyCode;

    /**
     * 样机需求汇总主键 collect_id
     */
    private String collectId;

    /**
     * 确认人 check_user
     */
    private String checkUser;

    /**
     * 确认人账号 check_user_code
     */
    private String checkUserCode;

    /**
     * 确认人工号 check_emp_code
     */
    private String checkEmpCode;
}