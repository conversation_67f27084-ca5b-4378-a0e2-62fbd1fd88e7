package com.mi.oa.asset.mobile.infra.dto.hrod;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.Date;

/**
 * @Desc Hrod 员工信息定义
 * <AUTHOR>
 * @Date 2021/9/26 22:40
 */

@Data
public class HrodEmployeeVO {

    /**
     * 用户名
     */
    @SerializedName("oprid")
    @JsonProperty("oprid")
    private String userName;

    /**
     * 姓名
     */
    @SerializedName("name")
    @JsonProperty("name")
    private String displayName;

    /**
     * 员工工号
     */
    @SerializedName("emplid")
    @JsonProperty("emplid")
    private String employeeId;

    /**
     * 部门id
     */
    @SerializedName("deptid")
    @JsonProperty("deptid")
    private String deptId;

    /**
     * 部门名称
     */
    @SerializedName("dept_desc")
    @JsonProperty("dept_desc")
    private String deptName;

    /**
     * 系统约定的一级部门id，因为接口中的一级部门通常是 小米公司
     */
    @SerializedName("mi_dept_level2")
    @JsonProperty("mi_dept_level2")
    private String deptIdLv1;

    /**
     * 系统约定的一级部门名称
     */
    @SerializedName("mi_dept_level2_desc")
    @JsonProperty("mi_dept_level2_desc")
    private String deptNameLv1;

    /**
     * 系统约定的二级部门id
     */
    @SerializedName("mi_dept_level3")
    @JsonProperty("mi_dept_level3")
    private String deptIdLv2;

    /**
     * 系统约定的二级部门名称
     */
    @SerializedName("mi_dept_level3_desc")
    @JsonProperty("mi_dept_level3_desc")
    private String deptNameLv2;

    /**
     * 系统约定的三级部门id
     */
    @SerializedName("mi_dept_level4")
    @JsonProperty("mi_dept_level4")
    private String deptIdLv3;

    /**
     * 系统约定的三级部门名称
     */
    @SerializedName("mi_dept_level4_desc")
    @JsonProperty("mi_dept_level4_desc")
    private String deptNameLv3;

    /**
     * 系统约定的四级部门id
     */
    @SerializedName("mi_dept_level5")
    @JsonProperty("mi_dept_level5")
    private String deptIdLv4;

    /**
     * 系统约定的四级部门名称
     */
    @SerializedName("mi_dept_level5_desc")
    @JsonProperty("mi_dept_level5_desc")
    private String deptNameLv4;

    /**
     * 系统约定的五级部门id
     */
    @SerializedName("mi_dept_level6")
    @JsonProperty("mi_dept_level6")
    private String deptIdLv5;

    /**
     * 系统约定的五级部门名称
     */
    @SerializedName("mi_dept_level6_desc")
    @JsonProperty("mi_dept_level6_desc")
    private String deptNameLv5;

    /**
     * 员工邮箱
     */
    @SerializedName("email_addr")
    @JsonProperty("email_addr")
    private String email;

    /**
     * hr状态 A - 在职 I - 离职
     */
    @SerializedName("hr_status")
    @JsonProperty("hr_status")
    private String hrStatus;

    /**
     * 公司主体id
     */
    @SerializedName("company")
    @JsonProperty("company")
    private String legalId;

    /**
     * 公司名称
     */
    @SerializedName("company_descr_zhs")
    @JsonProperty("company_descr_zhs")
    private String companyName;

    /**
     * 公司所属国家三位代码
     */
    @SerializedName("mi_cmpny_country")
    @JsonProperty("mi_cmpny_country")
    private String companyCountryCodeChar3;

    /**
     * 个人成本中心编码
     */
    @SerializedName("mi_cost_ct_fld")
    @JsonProperty("mi_cost_ct_fld")
    private String costCenter;

    /**
     * 个人成本中心名称
     */
    @SerializedName("descr100")
    @JsonProperty("descr100")
    private String costCenterDesc;

    /**
     * 部门成本中心编码
     */
    @SerializedName("mi_dept_cost_fld")
    @JsonProperty("mi_dept_cost_fld")
    private String deptCostCenter;

    /**
     * 部门成本中心名称
     */
    @SerializedName("mi_dept_cost_descr")
    @JsonProperty("mi_dept_cost_descr")
    private String deptCostCenterDesc;

    /**
     * 直属领导用户名
     */
    @SerializedName("leader_oprid")
    @JsonProperty("leader_oprid")
    private String leaderUserName;

    /**
     * 直属领导姓名
     */
    @SerializedName("leader_name")
    @JsonProperty("leader_name")
    private String leaderDisplayName;

    /**
     * 入职日期
     */
    @SerializedName("last_hire_dt")
    @JsonProperty("last_hire_dt")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date entryDate;
}