package com.mi.oa.asset.mobile.infra.dto.mall;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/10/10 9:33
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InnerPlanInfo {

    /**
     * 计划开始时间
     */
    private Date startTime;

    /**
     * 此次计划的上线数量
     */
    private int num;
}
