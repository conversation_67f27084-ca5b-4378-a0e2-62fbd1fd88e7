package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc: 订单状态
 * @Author: huy
 * @Date: 2022/8/1
 */
@Getter
public enum AssetOrderStatusEnum {

    WAIT_SELL("0", "待销售"),
    WAIT_PAY("1", "待支付"),
    COMPLETED("2", "已完成"),
    CLOSE("3", "已关闭"),
    ;

    private String state;
    private String desc;

    AssetOrderStatusEnum(String state, String desc) {
        this.state = state;
        this.desc = desc;
    }
}
