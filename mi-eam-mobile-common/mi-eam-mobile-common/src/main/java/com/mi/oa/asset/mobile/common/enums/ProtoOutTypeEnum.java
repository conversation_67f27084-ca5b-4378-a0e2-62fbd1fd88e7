package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/21 17:13
 */
@Getter
public enum ProtoOutTypeEnum {
    /**
     * 申请领取
     */
    APPLY_FOR_COLLECTION("0","申请领取"),
    /**
     * 采购退货
     */
    PURCHASE_RETURN("1","采购退货"),
    /**
     * 采购退货
     */
    INVENTORY_LOSS_DELIVERY("2","盘亏出库"),
    /**
     * 报废出库
     */
    SCRAP_ISSUE("3","报废出库"),
    /**
     * 入职领用
     */
    EMPLOYMENT_COLLECTION("4","入职领用"),
    /**
     * 线下领取
     */
    OFFLINE_COLLECTION("7","线下领取"),
    /**
     * 借用领取
     */
    TO_BORROW("9","借用领取"),
    ;
    private String code;
    private String desc;

    ProtoOutTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
