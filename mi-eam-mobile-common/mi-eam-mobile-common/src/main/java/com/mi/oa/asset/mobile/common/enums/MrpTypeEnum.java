package com.mi.oa.asset.mobile.common.enums;

import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Copyright (c) 2022 XiaoMi Inc.All Rights Reserved.
 * Description: your description
 *
 * <AUTHOR>
 * @date 2022/11/2 20:05
 */
@Getter
@AllArgsConstructor
public enum MrpTypeEnum {
    AFTER_SALE("after_sale", "售后内购机", "售后内购机"),
    MOBILE("mobile", "手机", "手机工程机"),
    TV("tv", "电视", "电视工程机"),
    WEARABLE("wearable", "可穿戴", "可穿戴"),
    ECOCHAIN("ecochain", "生态链", "生态链"),
    LAPTOP("laptop", "笔记本电脑", "笔记本电脑"),
    ROBOT("robot", "机器人", "机器人"),
    PROTOTYPE("prototype", "售后样机", "售后样机"),
    HEA_FACTORY("hea_factory", "大家电-空调", "大家电-空调"),
    MI_AUTOEACC("MI_AUTOEACC","汽车电子","汽车电子"),
    ;

    private String type;
    private String description;
    private String alias;

    public static MrpTypeEnum valuesOf(String type) {
        for (MrpTypeEnum mrpTypeEnum : values()) {
            if (mrpTypeEnum.getType().equals(type)) {
                return mrpTypeEnum;
            }
        }
        throw new BizException(BizCodeEnum.ENUM_ERROR, String.format("type = %s not exist.", type));
    }
}
