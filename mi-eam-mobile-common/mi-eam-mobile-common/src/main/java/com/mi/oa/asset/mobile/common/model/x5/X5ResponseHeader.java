package com.mi.oa.asset.mobile.common.model.x5;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/9/30 0:09
 */

@Data
@NoArgsConstructor
public class X5ResponseHeader {

    public static final String SUCCESS_CODE = "200";

    public static final String DEFAULT_FAILED_CODE = "-1";

    public static final String DEFAULT_SUCCESS_MESSAGE = "success";

    private String code;

    private String desc;

    private String msg;

    private String message;

    public String getMessage() {
        String output = "";
        if(null != desc && 0 != desc.length()) {
            output = desc;
        } else if(null != msg && 0 != msg.length()) {
            output = msg;
        } else if(null != message && 0 != message.length()) {
            output = message;
        }

        return output;
    }
}