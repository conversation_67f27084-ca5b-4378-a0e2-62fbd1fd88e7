package com.mi.oa.asset.mobile.utils;

/**
 * 来自jxstar
 *
 * <AUTHOR>
 * @date 2022/1/21 11:27
 */

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DateUtil {
    private DateUtil() {
    }

    public static String getDate(int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(6, day);
        return calendarToDate(calendar);
    }

    public static String getTodaySec() {
        return (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(new Date());
    }

    public static String getToday() {
        return (new SimpleDateFormat("yyyy-MM-dd")).format(new Date());
    }

    public static String getDateStr(Date date, String pattern) {
        return (new SimpleDateFormat(pattern)).format(date);
    }

    public static String getCurrYearMonth() {
        String today = getToday();
        return today.substring(0, today.length() - 3);
    }

    public static String[] getDateByMonth(String month) {
        String[] rtn = new String[]{month + "-01", null};
        rtn[1] = dateAddMonth(rtn[0], 1);
        return rtn;
    }

    public static String dateAddMonth(String date, int month) {
        Calendar calendar = strToCalendar(date);
        calendar.add(2, month);
        return calendarToDate(calendar);
    }

    public static String getAddMonth(String curMonth, int month) {
        String preDate = dateAddMonth(curMonth + "-01", month);
        String[] smonth = preDate.split("-");
        return smonth[0] + "-" + smonth[1];
    }

    public static Calendar strToCalendar(String strDate) {
        Calendar calendar = Calendar.getInstance();
        int h = 0;
        int m = 0;
        int s = 0;
        String[] sdt = strDate.split(" ");
        if (sdt.length > 1) {
            String[] st = sdt[1].split(":");
            if (st.length > 0) {
                h = Integer.parseInt(st[0]);
            }

            if (st.length > 1) {
                m = Integer.parseInt(st[1]);
            }

            if (st.length > 2) {
                s = Integer.parseInt(st[2]);
            }
        }

        int year = 0;
        int month = 0;
        int day = 0;
        String[] sd = sdt[0].split("-");
        if (sd.length > 0) {
            year = Integer.parseInt(sd[0]);
        }

        if (sd.length > 1) {
            month = Integer.parseInt(sd[1]) - 1;
        }

        if (sd.length > 2) {
            day = Integer.parseInt(sd[2]);
        }

        calendar.set(year, month, day, h, m, s);
        return calendar;
    }

    public static String calendarToDate(Calendar calendar) {
        return (new SimpleDateFormat("yyyy-MM-dd")).format(calendar.getTime());
    }

    public static String calendarToDateTime(Calendar calendar) {
        return (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(calendar.getTime());
    }

//    /**
//     * 两个日期相隔的天数
//     * @param fDate
//     * @param oDate
//     * @return
//     */
//    public static int dayDiff(Date fDate, Date oDate) {
//        Calendar aCalendar = Calendar.getInstance();
//        aCalendar.setTime(fDate);
//        int day1 = aCalendar.get(Calendar.DAY_OF_YEAR);
//        aCalendar.setTime(oDate);
//        int day2 = aCalendar.get(Calendar.DAY_OF_YEAR);
//        return day1 - day2;
//    }

    /**
     * 两个日期相隔的天数，传参没有先后，返回正整数
     * @param fDate
     * @param oDate
     * @return
     */
    public static int dayDiff(Date fDate, Date oDate) {
        int seconds = (int) (Math.abs((fDate.getTime() - oDate.getTime())) / 1000);
        return seconds / (60 * 60 * 24);
    }

    /**
     * 当前日期的日数大于等于上市当天的日数，计算逻辑：（当前年-上市年）*12+当前月-上市月+1。否则（当前年-上市年）*12+当前月-上市月
     * @param fDate
     * @param oDate
     * @return
     */
    public static int monthDiff(Date fDate, Date oDate) {
        Calendar beginCal = Calendar.getInstance();
        beginCal.setTime(fDate);
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(oDate);
        int addCount = (endCal.get(Calendar.DAY_OF_MONTH) - beginCal.get(Calendar.DAY_OF_MONTH) >= 0 ? 0 : 1);
        return (beginCal.get(Calendar.YEAR)-endCal.get(Calendar.YEAR))*12+(beginCal.get(Calendar.MONTH)-endCal.get(Calendar.MONTH))+addCount;
    }

    /**
     * 两个日期相隔的分钟
     * @param fDate
     * @param oDate
     * @return
     */
    public static Long minuteDiff(Date fDate, Date oDate) {
        return (fDate.getTime() - oDate.getTime())/1000/60;
    }

    public static Date getZeroDate(String dateStr, String format){
        Date result = null;
        try {
            result = new SimpleDateFormat(format).parse(dateStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(result);
            calendar.set(Calendar.HOUR, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            result = calendar.getTime();
        } catch (ParseException e) {
            return null;
        }
        return result;
    }


    public static Date getLastDate(String dateStr, String format){
        Date result = null;
        try {
            result = new SimpleDateFormat(format).parse(dateStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(result);
            calendar.set(Calendar.HOUR, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            result = calendar.getTime();
        } catch (ParseException e) {
            return null;
        }
        return result;
    }
}
