package com.mi.oa.asset.mobile.common.enums;


import lombok.Getter;

/**
 * <AUTHOR>
 * @description: sap返回值enum enum
 * @date 2021/12/7 14:38
 */
@Getter
public enum SapRetEnum {

    ERROR("E", "error"),
    SUCCESS("S", "success"),
    UNTREATED("N", "未处理");

    private String key;

    private String desc;

    SapRetEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
