package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/17
 */
@Getter
public enum InTypeEnum {

    NOT_ASN_DELIVERY("0", "无asn收货"),
    HAVE_ASN_DELIVERY("1", "有asn收货"),
    SECOND_HAND_DELIVERY("2", "二手收货"),
    ;

    private String type;
    private String desc;

    InTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
