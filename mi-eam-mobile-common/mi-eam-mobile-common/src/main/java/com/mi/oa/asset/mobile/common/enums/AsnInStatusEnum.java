package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/19
 */
@Getter
public enum AsnInStatusEnum {

    NOT_DELIVERY("0", "未收货"),
    SECTION_DELIVERY("2", "待收货"),
    HAVE_DELIVERY("3", "已收货"),
    CANCEL_DELIVERY("5", "已取消"),
    ;

    private String state;
    private String desc;

    AsnInStatusEnum(String state, String desc) {
        this.state = state;
        this.desc = desc;
    }
}
