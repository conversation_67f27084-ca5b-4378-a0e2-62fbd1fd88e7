package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/7/5
 */
@Getter
public enum UnReturnTaskStatusEnum {

    UN_COMMIT("0", "未提交"),
    COMPLETE("1", "已完成"),
    ;

    private String type;

    private String desc;

    UnReturnTaskStatusEnum(String type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDesc(String type){
        for(UnReturnTaskStatusEnum statusEnum: UnReturnTaskStatusEnum.values()){
            if(statusEnum.getType().equals(type)){
                return statusEnum.getDesc();
            }
        }
        return null;
    }
}
