package com.mi.oa.asset.mobile.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 消息模板
 */
@Getter
@AllArgsConstructor
public enum MessageTemplateEnum {

    /**
     * 待支付
     */
    PAY_AFTER_SALE("after_sale", "pay", "proto_order_online_notify_sale"),
    PAY_MOBILE("mobile", "pay", "proto_order_online_notify"),
    PAY_TV("tv", "pay", "proto_order_online_notify"),
    CANCEL_AFTER_SALE("after_sale", "cancel", "proto_order_cancel_after_sale"),
    CANCEL_MOBILE("mobile", "cancel", "proto_order_cancel"),
    CANCEL_TV("tv", "cancel", "proto_order_cancel"),
    ;

    private String code;

    private String type;

    private String name;

    public static String getTemplateName(String code, String type) {
        if (StringUtils.isBlank(code) || StringUtils.isBlank(type)) {
            return null;
        }
        for (MessageTemplateEnum value : MessageTemplateEnum.values()) {
            if (value.getCode().equals(code) && value.getType().equals(type)) {
                return value.getName();
            }
        }
        return null;
    }

}
