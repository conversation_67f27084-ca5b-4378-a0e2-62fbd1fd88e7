package com.mi.oa.asset.mobile.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/12/13
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ProtoMessageEnum {
    TRAN_SUCCESS_LARK("proto_tran_success_lark", "工程机转移审批完飞书"),
    TRAN_SUCCESS_EMAIL("proto_tran_success_email", "工程机转移审批完邮件"),
    TRAN_SUBMIT_LARK("proto_tran_submit_lark", "工程机转移提交飞书"),
    TRAN_SUBMIT_EMAIL("proto_tran_submit_email", "工程机转移提交邮件"),
    ;

    private String templateTag;
    private String desc;
}
