package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 *
 * <AUTHOR>
 * @date 2022/1/21 15:44
 */
@Getter
public enum ProtoInDetCheckStatusEnum {
    /**
     * 不合格
     */
    UNQUALIFIED("0","不合格"),
    /**
     * 不合格
     */
    QUALIFIED("1","合格"),
    ;
    private String code;
    private String desc;

    ProtoInDetCheckStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
