package com.mi.oa.asset.mobile.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/17
 */

public class ArrayUtil {

    public static String arrayToString(String[] astr) {
        return arrayToString(astr, ",");
    }

    public static String arrayToString(String[] astr, String fill) {
        if (astr != null && astr.length != 0) {
            if (fill == null) {
                fill = "";
            }

            StringBuilder sb = new StringBuilder();
            int i = 0;

            for(int n = astr.length; i < n; ++i) {
                sb.append(astr[i] + fill);
            }

            String tmps = "";
            if (sb.length() > 0) {
                tmps = sb.substring(0, sb.length() - 1);
            }

            return tmps;
        } else {
            return "";
        }
    }

    public static String[] arrayAddString(String[] amain, String str) {
        return arrayAddArray(amain, new String[]{str});
    }

    public static String[] arrayAddArray(String[] amain, String[] asub) {
        if (amain == null) {
            return asub == null ? new String[0] : asub;
        } else if (asub == null) {
            return amain;
        } else {
            String[] aret = new String[amain.length + asub.length];
            System.arraycopy(amain, 0, aret, 0, amain.length);
            System.arraycopy(asub, 0, aret, amain.length, asub.length);
            return aret;
        }
    }

    public static List<String> arrayToList(String[] astr) {
        List<String> lsRet = new ArrayList<>();
        if (astr == null) {
            return lsRet;
        } else {
            for(int i = 0; i < astr.length; ++i) {
                lsRet.add(astr[i]);
            }

            return lsRet;
        }
    }

    public static String listToString(List<String> ls, String fill) {
        String[] astr = listToArray(ls);
        return arrayToString(astr, fill);
    }

    public static String[] listToArray(List<String> ls) {
        if (ls == null) {
            return null;
        } else {
            int len = ls.size();
            String[] asRet = new String[len];
            asRet = (String[])ls.toArray(asRet);
            return asRet;
        }
    }
}
