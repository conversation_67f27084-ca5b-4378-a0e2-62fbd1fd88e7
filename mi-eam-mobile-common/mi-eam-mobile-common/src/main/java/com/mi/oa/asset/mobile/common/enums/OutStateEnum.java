package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/5
 */
@Getter
public enum OutStateEnum {

    IN_PREPARATION("0", "编制中"),
    TO_BE_CONFIRMED("1", "待确认"),
    ISSUED("2", "已出库"),
    DUMPED_SAP("3", "已抛SAP"),
    CANCELLATION("7", "注销"),
    ;

    private String state;
    private String desc;

    OutStateEnum(String state, String desc) {
        this.state = state;
        this.desc = desc;
    }

}
