package com.mi.oa.asset.mobile.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/1/25 9:49
 */
@Getter
@AllArgsConstructor
public enum MdmProjectBusinessLineEnum {
    MDM_PHONE_LINE1("mobile","MI_PHONE"),
    MDM_PHONE_LINE2("mobile","手机"),
    MDM_PHONE_LINE3("mobile","平板"),
    MDM_PHONE_LINE4("mobile","手机配件"),
    MDM_PHONE_LINE5("MI_AUTOEACC","汽车电子"),
    MDM_WEARABLE_LINE("wearable","可穿戴"),
    MDM_LAPTOP_LINE("laptop","笔记本"),
    MDM_ROBOT_LINE("robot","机器人"),
    ;
    private final String mrpType;
    private final String businessLine;

    public static MdmProjectBusinessLineEnum valuesOf(String businessLine) {
        for (MdmProjectBusinessLineEnum value : MdmProjectBusinessLineEnum.values()) {
            if (value.businessLine.equals(businessLine)) {
                return value;
            }
        }
        return null;
    }
}
