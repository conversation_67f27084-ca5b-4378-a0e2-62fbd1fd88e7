package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/7/5
 */
@Getter
public enum DisposalTypeEnum {

    SCRAP("0", "报废"),
    PRESENTED("1", "赠送"),
    LOSE("2", "丢失"),
    BUY_BACK("3", "回购"),
    ;

    private String type;

    private String desc;

    DisposalTypeEnum(String type, String desc){
        this.type = type;
        this.desc = desc;
    }

    public static String getDesc(String type){
        for(DisposalTypeEnum statusEnum: DisposalTypeEnum.values()){
            if(statusEnum.getType().equals(type)){
                return statusEnum.getDesc();
            }
        }
        return null;
    }
}
