package com.mi.oa.asset.mobile.common.enums;

import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MallOrderTypeEnum {
    GENERAL(0, "一般订单"),
    ALL_STAFF(2, "全员内购订单");

    private Integer type;

    private String description;

    public OrderTypeEnum valuesOf(Integer type) {
        for (OrderTypeEnum orderType : OrderTypeEnum.values()) {
            if (orderType.getType().equals(type)) {
                return orderType;
            }
        }
        throw new BizException(BizCodeEnum.ENUM_ERROR, "type=" + type + "对应的枚举不存在");
    }


}
