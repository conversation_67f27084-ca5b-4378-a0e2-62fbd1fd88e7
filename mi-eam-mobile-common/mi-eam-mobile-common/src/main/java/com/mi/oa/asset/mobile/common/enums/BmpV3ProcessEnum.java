package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/10/31 20:27
 */
@Getter
public enum BmpV3ProcessEnum {

    /**
     * 工程机处置
     */
    PROTO_TRAN_APPLY("bpmn_791355800398299136", "工程机转移申请(Application for Construction Machinery prototype) %s"),
    PROTO_BACK_APPLY("bpmn_792499222474780672", "工程机归还申请(Application for Return of Engineering Machine) %s"),
    PROTO_REMOVALL_COVER("bpmn_794230292912140288", "工程机保密套拆除(Removal of Confidential Cover) %s"),
    PROTO_BASELINE_APPLY("bpmn_890264921154117632", "工程机公共基线（%s）申请（Application for a common baseline of the Engineering machine）%s")
    ;

    private String modelCode;
    private String processInstanceName;

    BmpV3ProcessEnum(String modelCode,String processInstanceName){
        this.modelCode=modelCode;
        this.processInstanceName=processInstanceName;
    }

    public static BmpV3ProcessEnum getBpmV3ProcessEnum(String modelCode){
        for (BmpV3ProcessEnum process : BmpV3ProcessEnum.values()) {
            if(process.getModelCode().equalsIgnoreCase(modelCode)){
                return process;
            }
        }
        return null;
    }
}
