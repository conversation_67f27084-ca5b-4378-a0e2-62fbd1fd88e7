package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/3/1 16:48
 */
@Getter
public enum BaselineChangeTypeEnum {
    /**
     * 新增
     */
    INSERT("1", "新增"),
    /**
     * 变更
     */
    UPDATE("2", "变更"),
    /**
     * 删除
     */
    DELETE("3", "删除"),
    ;
    private String key;
    private String desc;

    BaselineChangeTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public static BaselineChangeTypeEnum getChangeType(String key){
        for(BaselineChangeTypeEnum type:BaselineChangeTypeEnum.values()){
            if(type.getKey().equals(key)){
                return type;
            }
        }
        return null;
    }
}
