package com.mi.oa.asset.mobile.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Desc: SRM 同步字段bsart
 * @Author: wangal
 * @Date: 2024/4/26
 */
@Getter
@AllArgsConstructor
public enum SrmPurchaseTypeEnum {
    /**
     * 采购类型 采购凭证类型 ZNB:原材料 ZWW:成品、ZPJ半成品 ZDB 米通模式
     */
    ZNB("ZNB", "原材料"),
    ZWW("ZWW", "成品"),
    ZPJ("ZPJ", "半成品"),
    ZUB("ZUB", "ZUB"),
    ZDB("ZDB", "米通模式");

    private final String code;

    private final String desc;

}
