package com.mi.oa.asset.mobile.common.enums;

/**
 * 转移单状态
 */
public enum TranAuditEnum {
    NOT_SUBMIT("0", "未提交"),
    SUBMITED("1", "已提交"),
    CHECKING("2", "审批中"),
    CHECK_OK("3", "已审批"),
    CHECK_VETO("4", "已否决"),
    RETURN_EDITOR("6", "已驳回"),
    CANCELLED("7", "已失效");

    private String key;

    private String desc;

    TranAuditEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }
}
