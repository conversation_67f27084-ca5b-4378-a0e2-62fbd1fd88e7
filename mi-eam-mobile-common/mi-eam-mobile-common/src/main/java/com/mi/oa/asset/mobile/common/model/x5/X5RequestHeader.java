package com.mi.oa.asset.mobile.common.model.x5;

import com.google.gson.annotations.SerializedName;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import lombok.Data;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/9/30 0:06
 */

@Data
public class X5RequestHeader {

    @SerializedName("appid")
    String appId;

    String sign;

    String method;

    String sign_type;

    public X5RequestHeader(String appId, String body, String appKey) {
        this.appId = appId;
        this.sign = DigestUtils.md5DigestAsHex((appId + body + appKey).getBytes(StandardCharsets.UTF_8)).toUpperCase();
    }

    public X5RequestHeader(String appId, String body,String appKey,String method, String sign_type) {
        this.appId = appId;
        this.method = method;
        this.sign = DigestUtils.md5DigestAsHex((appId + body + appKey).getBytes(StandardCharsets.UTF_8)).toUpperCase();
        this.sign_type = sign_type;
    }

    @Override
    public String toString() {
        return GsonUtil.toJsonString(this);
    }
}


