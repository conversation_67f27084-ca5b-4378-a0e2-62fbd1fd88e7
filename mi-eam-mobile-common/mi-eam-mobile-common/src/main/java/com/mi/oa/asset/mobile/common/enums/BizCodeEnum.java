package com.mi.oa.asset.mobile.common.enums;

import com.mi.oa.infra.oaucf.core.exception.ErrorCode;

/**
 * Copyright (c) 2022 XiaoMi Inc.All Rights Reserved.
 * Description: biz code enum
 *
 * <AUTHOR>
 * @date 2022/7/21 10:23
 */
public enum BizCodeEnum implements ErrorCode {
    OK(200, "success"),
    PARAMETER_IS_NULL(1001, "参数为空"),
    PARAMETER_ERROR(1002, "参数异常，%s"),
    ENUM_ERROR(1003, "枚举错误，%s"),
    MD5_ERROR(1004, "MD5异常，%s");

    /**
     * 具体错误码
     */
    private int errCode;
    /**
     * 描述
     */
    private String errDesc;

    @Override
    public int getBizCode() {
        return 0;
    }

    @Override
    public int getLayerCode() {
        return 0;
    }

    @Override
    public int getErrorCode() {
        return this.errCode;
    }

    @Override
    public String getErrDesc() {
        return this.errDesc;
    }

    /**
     * 构造方法
     *
     * @param errCode
     * @param errDesc
     */
    BizCodeEnum(int errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }
}


