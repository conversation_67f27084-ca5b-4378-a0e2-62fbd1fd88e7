package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/2/7
 */
@Getter
public enum IsOpenEnum {

    NOT_DISMANTLE("0", "未拆除"),
    HAVE_DISMANTLE("1", "已拆除"),
    DISMANTLE_ING("2", "拆除中"),
    ;

    private String type;
    private String desc;

    IsOpenEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
