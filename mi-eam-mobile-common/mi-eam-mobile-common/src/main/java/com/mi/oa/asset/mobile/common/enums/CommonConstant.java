package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @title: CommonConstant
 * @projectName eap-parent
 * @description:公共常量
 * @date 2021/8/4下午7:52
 **/
public final class CommonConstant {

    public static final Integer SUCCESS_CODE = 0;

    /**
     * 手机工程机 固定值  电视样机领用代工厂（供应商）
     */
    public static final String I_LIFNR = "M10001";

    public static final String FALSE = "false";

    public static final String TRUE = "true";

    public static final String PROTO_APPLY_SECOND_PM = "proto.apply_second_pm";


    public static final String KEY_ID = "keyid";

    public static final String DEFAULT_CODE_RULE = "code.rule.format";

    public static final String DEFAULT_CODE_RULE_SERIAL = "code.rule.serial";

    public static final String DEFAULT_CODE_RULE_FORMAT = "yyyyMM";

    public static final String SAVE_EG_EVENT = "save_eg";

    public static final String CREATE_EVENT = "create";

    public static final String UPDATE_EVENT = "save";

    public static final String COMMON_AUDIT = "audit";


    public static final String COST_CENTER_NOT_EXIST = "成本中心不存在";

    public static final Integer DEFAULT_PAGE_SIZE = 1000;

    public static final String AUDIT_VALUE = "auditvalue";

    public static final String SCAN_RESULT = "scan_result";

    /**
     * 转移申请人提醒邮件模板
     */
    public static final String PROTO_TRAN_OVER_TIME_APPLICANT = "proto_tran_over_time_applicant";

    /**
     * 转移申请人提醒飞书模板
     */
    public static final String PROTO_TRAN_OVER_TIME_APPLICANT_LARK = "proto_tran_over_time_01";

    /**
     * 转移接受人提醒邮件模板
     */
    public static final String PROTO_TRAN_OVER_TIME_RECEIVER = "proto_tran_over_time_receiver";

    /**
     * 转移接受人提醒飞书模板
     */
    public static final String PROTO_TRAN_OVER_TIME_RECEIVER_LARK = "proto_tran_over_time_02";

    /**
     * 线上通知消息
     */
    public static final String PROTO_ORDER_ONLINE_NOTIFY = "proto_order_online_notify";

    /**
     * 内购完成之后通知消息
     */
    public static final String PROTO_ORDER_COMPLETE = "proto_order_complete";

    /**
     * 线下通知消息去上传
     */
    public static final String PROTO_NOTIFY_UPLOAD = "proto_notify_upload";

    /**
     * 工程机内购上传完成之后给财务飞书提醒
     */
    public static final String PROTO_ORDER_UPLOAD_FINANCE = "proto_order_upload_finance";

    /**
     * 工程机内购上传完成之后给购买人飞书提醒
     */
    public static final String PROTO_ORDER_UPLOAD_USER = "proto_order_upload_user";

    /**
     * 工程机订单取消时消息提醒
     */
    public static final String PROTO_ORDER_CANCEL = "proto_order_cancel";

    /**
     * 工程机订单管理员取消提醒
     */
    public static final String PROTO_ORDER_ADMIN_CANCEL = "proto_order_admin_cancel";

    /**
     * 工程机内购验货飞书提醒
     */
    public static final String PROTO_ORDER_CHECK_BEIJING = "proto_order_check_beijing";
    public static final String PROTO_ORDER_CHECK_WUHAN = "proto_order_check_wuhan";
    public static final String PROTO_ORDER_CHECK_SHANGHAI = "proto_order_check_shanghai";
    public static final String PROTO_ORDER_CHECK_SHENZHEN = "proto_order_check_shenzhen";
    public static final String PROTO_ORDER_CHECK_NANJING = "proto_order_check_nanjing";

    /**
     * 售后内购机
     */
    public static final String PROTO_ORDER_CHECK_AFTER_SALE = "proto_order_check_after_sale";

    /**
     * 工程机内购验货飞书提醒（手动触发）
     */
    public static final String PROTO_ORDER_CHECK_MANUAL = "proto_order_check_manual";

    /**
     * 电视内购验货飞书提醒
     */
    public static final String PROTO_ORDER_CHECK_TV_BEIJING = "proto_order_check_tv_beijing";
    public static final String PROTO_ORDER_CHECK_TV_WUHAN = "proto_order_check_tv_wuhan";
    public static final String PROTO_ORDER_CHECK_TV_SHANGHAI = "proto_order_check_tv_shanghai";
    public static final String PROTO_ORDER_CHECK_TV_SHENZHEN = "proto_order_check_tv_shenzhen";
    public static final String PROTO_ORDER_CHECK_TV_NANJING = "proto_order_check_tv_nanjing";

    /**
     * 工程机接口人角色
     */
    public static final String INTERFACE_ROLE = "gcj_13";

    /**
     * 超级管理员
     */
    public static final String ADMIN_ROLE = "admin";

    /**
     * 工程机管理员
     */
    public static final String GCJ_ADMIN_ROLE = "gcj_admin";

    /**
     * 工程机部门权限
     */
    public static final String DEPT_CODE_FIELD = "dept_code";

    public static final String WHERE_SQL = "where_sql";

    public static final String WHERE_VALUE = "where_value";

    public static final String MOBILE_TYPE = "mobile";

    public static final String R_PLATFORM = "R";

    /**
     * Mdm PM标识
     */
    public static final String MDM_PM_ROLE_KEY = "PROJECT MANAGER";

    /**
     * 系统默认修改人
     */
    public static final String ADMIN_KEY = "admin";

    /**
     * 产品系列标识
     */
    public static final String PRODUCT_SERIES_KEY = "productSeries";

    /**
     * 用户名
     */
    public static final String USER_NAME = "user_name";

    public static final String FK_VALUE = "fkValue";

    /**
     * 盘点提前30分钟邮件提醒
     */
    public static final String PROTO_PLAN_FORWARD_EMAIL  = "proto_plan_forward_email";

    /**
     * 盘点提前30分钟飞书提醒
     */
    public static final String PROTO_PLAN_FORWARD_LARK  = "proto_plan_forward_lark";

    /**
     * 盘点提交时发送飞书、邮件
     */
    public static final String PROTO_PLAN_SUBMIT_EMAIL = "proto_plan_submit_email";

    /**
     * 盘点提交时发送飞书、邮件
     */
    public static final String PROTO_PLAN_SUBMIT_LARK = "proto_plan_submit_lark";

    /**
     * 工程机处置上传凭证提交完之后发送飞书提醒
     */
    public static final String PROTO_DIS_SUBMIT_LARK = "proto_dis_submit_lark";

    /**
     * 工程机处置上传凭证提交完之后给财务飞书提醒
     */
    public static final String PROTO_DIS_FINANCE_LARK = "proto_dis_finance_lark";

    /**
     * eam域名
     */
    public static final String EAM_URL = "asset.eam.url";

    public final static String PROTO_UNRETURN = "proto_unreturn";

    public final static String NO_BUTTON  = "no_button";

    public final static String MALL_BOT_TYPE = "mall";

    /**
     * 前端上传附件标识
     */
    public final static String FRONT_ATTACH_TYPE = "0";

    @Getter
    public enum QueryBpmLogEnum {

        KEY(0, "businessKey"),
        RECORD_NO(1, "recordStatus");

        private Integer type;

        private String desc;

        QueryBpmLogEnum(Integer type, String desc) {

            this.type = type;
            this.desc = desc;
        }
    }

    /**
     * 记录状态
     */
    @Getter
    public enum RecordStatus {
        UNCOMMITTED("0", "未提交"),
        COMMITTED("1", "已提交"),
        IN_APPROVAL("2", "审批中"),
        APPROVED("3", "已审批"),
        REJECTED("4", "已否决"),
        REBUT("6", "已驳回"),
        INVALID("7", "已失效");

        private String status;

        private String statusDes;

        RecordStatus(String status, String statusDes) {
            this.status = status;
            this.statusDes = statusDes;
        }
    }

    /**
     * TODO 领用方类型
     */
    @Getter
    public enum ApplyUserTypeEnum {
        INNER("inner", "小米内部员工"),
        ODM("odm", "ODM+EMS+黑鲨"),
        PROVIDER("provider","第三方供应商");
        private String value;
        private String desc;

        ApplyUserTypeEnum(String value, String desc) {
            this.value = value;
            this.desc = desc;
        }
    }

    /** proto_card:asset_newold
     * 新旧属性
     */
    @Getter
    public enum ProtoCardAssetNewOld {
        ONEHAND("1", "一手"),
        SECONDHAND("2", "二手"),
        ;

        private String type;
        private String desc;

        ProtoCardAssetNewOld(String type, String desc) {
            this.type = type;
            this.desc = desc;
        }
    }

    @Getter
    public enum SapBusiTypeEnum{
        RECEIPT_GOODS("YJRK", "收货标识"),
        PROVIDE("YJFF", "发放标识"),
        MRP_PROVIDE("YJFC", "第三方MRP发放标识"),
        TRANSFER("YJZY", "转移标识"),
        ;

        private String type;

        private String desc;

        SapBusiTypeEnum(String type, String desc) {
            this.type = type;
            this.desc = desc;
        }
    }

    /** proto_card:IsComplete
     * 是否良品
     */
    @Getter
    public enum ProtoCardIsComplete {
        DEFAULT("0", "默认"),
        GOOD("1", "良品"),
        NO_GOOD("2", "非良品"),
        TEMPORARY_GOOD_PRODUCT("3", "处于暂存的良品状态"),
        RETURN_NOT_ALLOWED("9", "不允许归还"),
        ;

        private String type;
        private String desc;

        ProtoCardIsComplete(String type, String desc) {
            this.type = type;
            this.desc = desc;
        }
    }

    /** proto_card:ApplyType
     * ApplyType
     */
    @Getter
    public enum ProtoCardApplyType {
        PHONE("手机", "手机"),
        ;

        private String type;
        private String desc;

        ProtoCardApplyType(String type, String desc) {
            this.type = type;
            this.desc = desc;
        }
    }

    /** proto_card:SrcType
     * SrcType
     */
    @Getter
    public enum ProtoCardSrcType {
        FIRSTHAND("10", "一手"),
        SECONDHAND("20", "二手"),
        FIRSTHANDDEMAND("30", "一手-需求"),
        SECONDHANDNONGOOD("40", "二手-非良品"),
        ;

        private String type;
        private String desc;

        ProtoCardSrcType(String type, String desc) {
            this.type = type;
            this.desc = desc;
        }
    }

    /** proto_card:ApplyStatus
     * ApplyStatus
     */
    @Getter
    public enum ProtoCardApplyStatus {
        INPREPARATION("0", "编制中"),
        SHIPPING("2", "发放中"),
        TOBECONFIRMED("3", "待确认"),
        SHIPMENTCOMPLETED("4", "已完成"),
        PARTIALLYCLOSED("8", "部分关闭"),
        CLOSE("9", "已关闭"),
        ;

        private String type;
        private String desc;

        ProtoCardApplyStatus(String type, String desc) {
            this.type = type;
            this.desc = desc;
        }
    }

    /** proto_card:IsOther
     * IsOther
     */
    @Getter
    public enum ProtoCardIsOther {
        NO("0", "no"),
        YES("1", "yes"),
        ;

        private String type;
        private String desc;

        ProtoCardIsOther(String type, String desc) {
            this.type = type;
            this.desc = desc;
        }
    }

    /**
     * OccupyApplyCode
     */
    @Getter
    public enum ProtoCardOccupyApplyCode {
        OCCUPY("1", "占用"),
        NOTOCCUPY("0", "非占用"),
        ;

        private String type;
        private String desc;

        ProtoCardOccupyApplyCode(String type, String desc) {
            this.type = type;
            this.desc = desc;
        }
    }
}
