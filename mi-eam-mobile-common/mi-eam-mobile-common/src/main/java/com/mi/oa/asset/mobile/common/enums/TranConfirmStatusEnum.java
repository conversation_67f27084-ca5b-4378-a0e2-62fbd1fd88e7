package com.mi.oa.asset.mobile.common.enums;

/**
 * <AUTHOR>
 * @description: 工程机转确认状态
 * @date 2021/12/7 14:38
 */
public enum TranConfirmStatusEnum {

    IN_PREPARATION("0", "编制中"),
    TO_BE_CONFIRMED("3", "待确认"),
    CONFIRMED("4", "已确认"),
    ;

    private String key;

    private String desc;

    TranConfirmStatusEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

}
