package com.mi.oa.asset.mobile.common.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @description: 台账状态 enum
 * @date 2021/12/7 14:38
 */
public enum CardStateEnum {

    IN_LIBRARY("1", "在库"),
    RETURN_CONFIRMATION_IN_PROGRESS("2", "归还确认中"),
    TO_BE_RETURNED("4", "待归还"),
    TRANSFERRING("6", "转移中"),
    HAVE_DEDUCT_MONEY("13", "已扣款"),
    SCRAPPING("90", "报废中"),
    SCRAP("92", "报废"),
    UNABLE_TO_RETURN_UNDER_APPROVAL("94", "处置中"),
    UNABLE_TO_RETURN("96", "已处置"),
    INVENTORY_LOSS("99", "盘亏"),
    PURCHASING("14", "内购中"),
    HAVE_PURCHASED("15", "已内购"),
    ;

    private String key;

    private String desc;

    CardStateEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    public static CardStateEnum convert(String key){
        CardStateEnum defaultEnum = IN_LIBRARY;
        if(StringUtils.isBlank(key)){
            return defaultEnum;
        }
        CardStateEnum[] cardStateEnums = CardStateEnum.values();
        for (CardStateEnum cardStateEnum:cardStateEnums) {
            if(key.equals(cardStateEnum.getKey())){
                return cardStateEnum;
            }
        }
        return defaultEnum;
    }
}
