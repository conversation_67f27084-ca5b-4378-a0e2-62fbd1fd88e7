package com.mi.oa.asset.mobile.common.enums;

import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 采购组织枚举/工程机管理-基础数据-试产工厂类型
 *
 * <AUTHOR>
 * @date 2023/8/15
 */
@Getter
@AllArgsConstructor
public enum OrganizationEnum {

    wearable_items2("111C","可穿戴项目"),
    box_chain("2110","盒子供应链"),
    tv_chain("111C","电视供应链"),
    mobile_terminal("111A","手持终端设备供应链"),
    mobile_chain("1110","手机供应链"),
    smart_hardware("2120","智能硬件"),
    wearable_items1("111J","可穿戴项目"),
    tv_chain1("1111","电视供应链"),
    eco_chain("1122","生态链供应链"),
    eco_smart_hardware("2120","智能硬件"),
    laptop_chain("111K","笔记本电脑供应链"),
    mobile_cp_factory("3880","手机昌平工厂"),
    mobile_cp2_factory("111Z","手机昌平二期工厂"),
    robot_chain("111H","机器人供应链"),
    hea_factory("1134","大家电-空调供应链"),
    hea_factory_2("1120","米科公司间采购"),
    MI_AUTOEACC("111N","汽车电子供应链"),
    ;

    private String type;
    private String description;


    public static OrganizationEnum valuesOf(String type) {
        for (OrganizationEnum organizationEnum : values()) {
            if (organizationEnum.getType().equals(type)) {
                return organizationEnum;
            }
        }
        throw new BizException(BizCodeEnum.ENUM_ERROR, String.format("type = %s not exist.", type));
    }

}
