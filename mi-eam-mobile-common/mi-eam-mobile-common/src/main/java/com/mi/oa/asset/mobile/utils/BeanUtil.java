package com.mi.oa.asset.mobile.utils;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/2/20 16:05
 */

public class BeanUtil {

    /**
     * 将一个bean转为另一个bean
     *
     * @param source
     * @param target
     */
    public static void copyProperties(Object source, Object target) {
        BeanUtils.copyProperties(source, target);
    }

    /**
     * 将一个bean转为另一个bean，并且可以忽略属性
     *
     * @param source
     * @param target
     * @param ignoreProperties
     */
    public static void copyProperties(Object source, Object target, String... ignoreProperties) {
        BeanUtils.copyProperties(source, target, ignoreProperties);
    }

    /**
     * 将一个List转为另一个List
     *
     * @param sourceList
     * @param targetClass
     * @return
     */
    public static List copyListProperties(List<? extends Object> sourceList, Class targetClass) {
        List targetList = Lists.newArrayList();
        Object target = null;
        for (Object source : sourceList) {
            target = getObject(targetClass, target);
            copyProperties(source, target);
            targetList.add(target);
        }
        return targetList;
    }

    /**
     * 将一个List转为另一个List，并且可以忽略属性
     *
     * @param sourceList
     * @param targetClass
     * @param ignoreProperties
     * @return
     */
    public static List copyListProperties(List<? extends Object> sourceList, Class targetClass, String... ignoreProperties) {
        List targetList = Lists.newArrayList();
        Object target = null;
        for (Object source : sourceList) {
            target = getObject(targetClass, target);
            copyProperties(source, target, ignoreProperties);
            targetList.add(target);
        }
        return targetList;
    }

    /**
     * 将Bean转为Map
     *
     * @param clz
     * @param <T>
     * @return
     */
    public static <T> Map beanToMap(T clz) {
        Map map = Maps.newHashMap();
        BeanWrapper beanWrapper = new BeanWrapperImpl(clz);
        PropertyDescriptor[] descriptor = beanWrapper.getPropertyDescriptors();
        for (int i = 0; i < descriptor.length; i++) {
            if ("class".equals(descriptor[i].getName())) {
                continue;
            }
            String propertyName = descriptor[i].getName();
            map.put(propertyName, beanWrapper.getPropertyValue(propertyName));
        }
        return map;
    }

    /**
     * 将map转为Bean
     *
     * @param map
     * @param clz
     * @param <T>
     */
    public static <T> void mapToBean(Map map, T clz) {
        BeanWrapper beanWrapper = new BeanWrapperImpl(clz);
        PropertyDescriptor[] propertyDescriptors = beanWrapper.getPropertyDescriptors();
        for (PropertyDescriptor property : propertyDescriptors) {
            String key = property.getName();
            if (map.containsKey(key)) {
                Object value = map.get(key);
                Method setter = property.getWriteMethod();
                try {
                    setter.invoke(clz, value);
                } catch (IllegalAccessException | InvocationTargetException e) {
                    e.printStackTrace();
                }
            }
        }

    }
    /**
     * @Description:
     * @Param: [targetClass, target]
     * @return: java.lang.Object
     * @Author: xak
     * @Date: 2019/12/12/0012
     */
    private static Object getObject(Class targetClass, Object target) {
        try {
            target = targetClass.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            e.printStackTrace();
        }
        return target;
    }
}
