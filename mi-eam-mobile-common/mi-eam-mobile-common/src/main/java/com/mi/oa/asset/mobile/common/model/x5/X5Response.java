package com.mi.oa.asset.mobile.common.model.x5;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/9/30 0:10
 */

@Data
@AllArgsConstructor
public class X5Response {

    private X5ResponseHeader header;

    private Object body;

    public static X5Response success() {
        X5ResponseHeader header = new X5ResponseHeader();
        header.setCode(X5ResponseHeader.SUCCESS_CODE);
        header.setDesc(X5ResponseHeader.DEFAULT_SUCCESS_MESSAGE);
        header.setMsg(X5ResponseHeader.DEFAULT_SUCCESS_MESSAGE);

        return new X5Response(header, null);
    }

    public static X5Response success(Object body) {
        X5ResponseHeader header = new X5ResponseHeader();
        header.setCode(X5ResponseHeader.SUCCESS_CODE);
        header.setDesc(X5ResponseHeader.DEFAULT_SUCCESS_MESSAGE);
        header.setMsg(X5ResponseHeader.DEFAULT_SUCCESS_MESSAGE);

        return new X5Response(header, body);
    }

    public static X5Response fail(String message) {
        X5ResponseHeader header = new X5ResponseHeader();
        header.setCode(X5ResponseHeader.DEFAULT_FAILED_CODE);
        header.setDesc(message);
        header.setMsg(message);

        return new X5Response(header, null);
    }

    public static X5Response fail(String code, String message) {
        X5ResponseHeader header = new X5ResponseHeader();
        header.setCode(code);
        header.setDesc(message);
        header.setMsg(message);

        return new X5Response(header, null);
    }
}