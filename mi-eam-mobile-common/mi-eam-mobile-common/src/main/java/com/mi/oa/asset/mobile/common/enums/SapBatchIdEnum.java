package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/5
 */
@Getter
public enum SapBatchIdEnum {

    ASN_RECEIVE_GOODS("D7", "ASN收货", 5),
    MACHINE_TRANSFER("YJZ", "工程机转移", 7),
    RECEIVE("YJF", "工程机领用", 7),
    YI_ZHUANG("F1M", "亦庄", 4),
    CHANG_PING("F3", "昌平", 5),
    CHANG_PING2("F3", "昌平二期", 5),
    ROBOT("F5", "机器人", 5),
    NO_ASN("OSN", "无SAN", 7),
    MACHINE_TRANSFER_M("YJZM", "工程机转移", 6),
    RECEIVE_M("YJFM", "工程机领用", 6),
    ;

    private String billType;
    private String billName;
    private Integer length;

    SapBatchIdEnum(String billType, String billName, Integer length) {
        this.billType = billType;
        this.billName = billName;
        this.length = length;
    }

    public static SapBatchIdEnum getSapBatchIdEnum(String billType){
        for (SapBatchIdEnum batchEnum : values()){
            if (batchEnum.getBillType().equals(billType)){
                return batchEnum;
            }
        }
        return null;
    }
}
