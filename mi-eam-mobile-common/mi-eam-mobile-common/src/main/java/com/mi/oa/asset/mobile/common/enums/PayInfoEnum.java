package com.mi.oa.asset.mobile.common.enums;

import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Copyright (c) 2022 XiaoMi Inc.All Rights Reserved.
 * Description: your description
 *
 * <AUTHOR>
 * @date 2022/11/2 20:05
 */
@Getter
@AllArgsConstructor
public enum PayInfoEnum {
    MOBILE_INNER_PURCHASE("mobile_inner_purchase", "手机内购", "11001018700053037406", "小米通讯技术有限公司", "中国建设银行北京市分行朝阳支行"),
    MOBILE_DISPOSAL("mobile_disposal", "手机处置", "110907530210901", "小米通讯技术有限公司", "招商银行股份有限公司北京首体支行"),
    TV_INNER_PURCHASE("tv_inner_purchase", "电视内购", "", "", ""),
    TV_INNER_DISPOSAL("tv_disposal", "电视处置", "110908326910902", "北京小米电子产品有限公司", "招商银行北京首体科技金融支行"),
    WEARABLE_INNER_PURCHASE("wearable_inner_purchase", "可穿戴内购", "", "", ""),
    WEARABLE_DISPOSAL("wearable_disposal", "可穿戴处置", "", "", ""),
    LAPTOP_INNER_PURCHASE("laptop_inner_purchase", "笔记本电脑内购", "", "", ""),
    LAPTOP_DISPOSAL("laptop_disposal", "笔记本电脑处置", "110907530210901", "小米通讯技术有限公司", "招商银行股份有限公司北京首体科技金融支行"),
    ROBOT_INNER_PURCHASE("robot_inner_purchase", "机器人内购", "", "", ""),
    ROBOT_DISPOSAL("robot_disposal", "机器人处置", "", "", ""),
    ECOCHAIN_DISPOSAL("ecochain_disposal", "生态链处置", "", "", ""),
    HEA_FACTORY_DISPOSAL("hea_factory_disposal", "大家电-空调处置", "", "", ""),
    MI_AUTOEACC_DISPOSAL("MI_AUTOEACC_disposal", "汽车电子处置", "", "", ""),
    ;

    private String type;

    private String description;

    private String accountBank;

    private String companyName;

    private String openBank;


    public static PayInfoEnum valuesOf(String type) {
        for (PayInfoEnum payInfoEnum : values()) {
            if (payInfoEnum.getType().equals(type)) {
                return payInfoEnum;
            }
        }
        throw new BizException(BizCodeEnum.ENUM_ERROR, String.format("type = %s not exist.", type));
    }
}
