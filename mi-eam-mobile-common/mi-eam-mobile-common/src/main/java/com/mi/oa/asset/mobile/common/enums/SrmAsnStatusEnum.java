package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/5
 */
@Getter
public enum SrmAsnStatusEnum {

    WAIT_SEND("S001", -1, "等待发送"),
    HAVE_SEND("S002", 0, "已发送(未收货)"),
    SECTION_RECEIVE("S003", 1, "部分入库"),
    HAVE_RECEIVE("S004", 2, "已入库(全部入库)"),
    HAVE_CANCEL("S005", 5, "已取消"),
    ;

    private String srmStatus;
    private Integer eamStatus;
    private String desc;
    SrmAsnStatusEnum(String srmStatus, Integer eamStatus, String desc) {
        this.srmStatus = srmStatus;
        this.eamStatus = eamStatus;
        this.desc = desc;
    }

    public static Integer getEamStatusByAsnStatus(String asnStatus){
        for (SrmAsnStatusEnum statusEnum : values()){
            if (statusEnum.getSrmStatus().equals(asnStatus)){
                return statusEnum.getEamStatus();
            }
        }
        return null;
    }
}
