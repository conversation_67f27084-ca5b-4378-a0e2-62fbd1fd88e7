package com.mi.oa.asset.mobile.utils;

import com.google.gson.stream.JsonWriter;
import org.apache.commons.codec.binary.Base64;

import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/17
 */

public class StringUtil {

    public static final String ENTER = "\r\n";

    public static boolean isEmpty(String str) {
        return str == null || str.length() == 0;
    }



    public static int hasCharsNum(String str) {
        int a = 0;
        int b = 0;
        int c = 0;
        int i = 0;

        for(int n = str.length(); i < n; ++i) {
            if (Character.isDigit(str.charAt(i))) {
                a = 1;
            } else if (Character.isLetter(str.charAt(i))) {
                b = 1;
            } else {
                c = 1;
            }
        }

        return a + b + c;
    }

    public static String addkf(String where) {
        return where != null && where.length() != 0 ? "(" + where.trim() + ")" : "";
    }

    public static double tonum(String value) {
        if (value != null && value.length() != 0) {
            double d = 0.0D;

            try {
                d = Double.parseDouble(value);
            } catch (Exception var4) {
                d = 0.0D;
            }

            return d;
        } else {
            return 0.0D;
        }
    }

    public static int toint(String value) {
        if (value != null && value.length() != 0) {
            boolean var1 = false;

            int d;
            try {
                d = Integer.parseInt(value);
            } catch (Exception var3) {
                d = 0;
            }

            return d;
        } else {
            return 0;
        }
    }

    public static boolean isNum(String str) {
        return str.matches("^[-+]?(([0-9]+)([.]([0-9]+))?|([.]([0-9]+))?)$");
    }

    public static String strForJson(String value) {
        return strForJson(value, false);
    }

    public static String strForJson(String value, boolean htmlSafe) {
        try {
            StringWriter stringWriter = new StringWriter();
            JsonWriter jsonWriter = new JsonWriter(stringWriter);
            jsonWriter.setHtmlSafe(htmlSafe);
            jsonWriter.setLenient(true);
            jsonWriter.value(value);
            String str = stringWriter.toString();
            if (str.length() >= 2) {
                str = str.substring(1, str.length() - 1);
            }

            jsonWriter.close();
            return str;
        } catch (IOException var5) {
            throw new AssertionError(var5);
        }
    }

    public static String[] split(String value, String regex) {
        if (value != null && value.length() != 0) {
            if (regex != null && regex.length() != 0) {
                int spos = value.length() - regex.length();
                if (value.substring(spos).equals(regex)) {
                    value = value + "tempnull";
                }

                String[] values = value.split(regex);
                int len = values.length;
                if (values[len - 1].equals("tempnull")) {
                    values[len - 1] = "";
                }

                return values;
            } else {
                return new String[]{value};
            }
        } else {
            return null;
        }
    }

    public static String convEncoding(byte[] value, String oldCharset, String newCharset) {
        OutputStreamWriter outWriter = null;
        ByteArrayInputStream byteIns = null;
        ByteArrayOutputStream byteOuts = new ByteArrayOutputStream();
        InputStreamReader inReader = null;
        char[] cbuf = new char[1024];
        boolean var8 = false;

        try {
            byteIns = new ByteArrayInputStream(value);
            inReader = new InputStreamReader(byteIns, oldCharset);
            outWriter = new OutputStreamWriter(byteOuts, newCharset);

            int retVal;
            while((retVal = inReader.read(cbuf)) != -1) {
                outWriter.write(cbuf, 0, retVal);
            }
        } catch (Exception var20) {
            var20.printStackTrace();
        } finally {
            try {
                if (inReader != null) {
                    inReader.close();
                }

                if (outWriter != null) {
                    outWriter.close();
                }
            } catch (Exception var18) {
                var18.printStackTrace();
            }

        }

        String temp = null;

        try {
            temp = new String(byteOuts.toByteArray(), newCharset);
        } catch (UnsupportedEncodingException var19) {
            var19.printStackTrace();
        }

        return temp;
    }

    public static String decodeURLParam(String astrParam) {
        String ret = "";

        try {
            ret = URLDecoder.decode(astrParam, "utf-8");
            return ret;
        } catch (UnsupportedEncodingException var3) {
            var3.printStackTrace();
            return ret;
        }
    }

    public static String encodeURLParam(String astrParam) {
        String ret = "";

        try {
            ret = URLEncoder.encode(astrParam, "utf-8");
            return ret;
        } catch (UnsupportedEncodingException var3) {
            var3.printStackTrace();
            return ret;
        }
    }

    public static String getNoTableCol(String colName) {
        if (colName == null) {
            return colName;
        } else {
            if (colName.indexOf(".") >= 0) {
                colName = colName.substring(colName.indexOf(".") + 1, colName.length());
            }

            return colName;
        }
    }

    public static String replaceURLCode(String strParamValue) {
        if (strParamValue == null) {
            return "";
        } else {
            strParamValue = strParamValue.replaceAll("\\%", "%25");
            strParamValue = strParamValue.replaceAll("\\'", "%27");
            strParamValue = strParamValue.replaceAll("\\&", "%26");
            return strParamValue;
        }
    }

    public static String getFillString(String fill, int num) {
        if (fill != null && fill.length() != 0) {
            if (num <= 0) {
                return "";
            } else {
                StringBuilder sb = new StringBuilder();

                for(int i = 0; i < num; ++i) {
                    sb.append(fill);
                }

                String tmp = sb.toString();
                return tmp.substring(0, tmp.length() - 1);
            }
        } else {
            return "";
        }
    }

    public static String byte2hex(byte[] bytes) {
        if (bytes != null && bytes.length != 0) {
            StringBuilder hex = new StringBuilder();

            for(int i = 0; i < bytes.length; ++i) {
                int val = bytes[i] & 255;
                if (val < 16) {
                    hex.append("0");
                }

                hex.append(Integer.toHexString(val));
            }

            return hex.toString().toUpperCase();
        } else {
            return "";
        }
    }

    public static boolean isEqual(String str1, String str2) {
        if (str1 == null) {
            str1 = "";
        }

        if (str2 == null) {
            str2 = "";
        }

        return str1.equals(str2);
    }

    /**
     * 解密
     * @param wheresql
     * @return
     */
    public static String decodeWhere(String wheresql) {
        int len = wheresql.length();
        if (len <= 5) {
            return "";
        }
        if (!wheresql.substring(0, 3).equals("TYH") || !wheresql.substring(len - 2, len).equals("A=")) {
            return "";
        }
        wheresql = wheresql.substring(3, wheresql.length() - 2);
        wheresql = wheresql.replace(" ", "+");
        wheresql = wheresql.replace("\n", "");
        try {
            wheresql = new String(Base64.decodeBase64(wheresql), "UTF-8");
        } catch (UnsupportedEncodingException var4) {
            var4.printStackTrace();
        }
        return wheresql;
    }
}
