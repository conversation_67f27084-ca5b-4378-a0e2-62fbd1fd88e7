package com.mi.oa.asset.mobile.utils;


import com.mi.oa.asset.x5.common.X5Request;
import com.mi.oa.asset.x5.common.X5RequestHeader;
import com.mi.oa.asset.x5.common.X5Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.ServiceUnavailableRetryStrategy;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.protocol.HttpContext;
import org.apache.http.util.EntityUtils;
import org.springframework.http.MediaType;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc HTTP 请求工具类
 * <AUTHOR>
 * @Date 2021/10/23 17:17
 */

@Slf4j
public class HttpClientUtil {
    private static final int CONNECTION_TIMEOUT = 60000;

    private static final int SO_TIMEOUT = 60000;

    private static final int MAX_RETRY_TIMES = 5; // 重试次数

    private static final long RETRY_INTERVAL = 200; // ms 重试间隔

    public static String doGet(String url, Map<String, String> params, Map<String, String> headers) {
        Assert.isTrue(url.startsWith("http://") || url.startsWith("https://"), "Invalid url");

        CloseableHttpResponse response = null;
        String result = "";

        try(CloseableHttpClient client = createClient()) {
            URIBuilder uriBuilder = new URIBuilder(url);

            if(!CollectionUtils.isEmpty(params)) {
                List<NameValuePair> paramList = new ArrayList<>();
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    paramList.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                }
                uriBuilder.setParameters(paramList);
            }

            HttpGet request = new HttpGet(uriBuilder.build());
            request.setConfig(getRequestConfig());

            if(!CollectionUtils.isEmpty(headers)) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    request.setHeader(entry.getKey(), entry.getValue());
                }
            }

            response = client.execute(request);
            int httpStatusCode = response.getStatusLine().getStatusCode();
            if(httpStatusCode == HttpStatus.SC_OK) {
                result = EntityUtils.toString(response.getEntity());
            } else {
                log.error("Do get error url {} code {} message {}", url, httpStatusCode, response.getStatusLine().getReasonPhrase());
            }
        } catch (Exception e) {
            log.error("Do get error, url {}", url, e);
        } finally {
            if(null != response) {
                try {
                    response.close();
                } catch (IOException ignored) {}
            }
        }

        return result;
    }

    public static String doPost(String url, Map<String, String> params, Map<String, String> headers) {
        Assert.isTrue(url.startsWith("http://") || url.startsWith("https://"), "Invalid url");

        log.info("Do post url: {}, params: {}", url, params);

        CloseableHttpResponse response = null;

        try(CloseableHttpClient client = createClient()) {
            URIBuilder uriBuilder = new URIBuilder(url);

            HttpPost request = new HttpPost(uriBuilder.build());
            request.setConfig(getRequestConfig());

            if(!CollectionUtils.isEmpty(headers)) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    request.setHeader(entry.getKey(), entry.getValue());
                }
            }

            if(!CollectionUtils.isEmpty(params)) {
                List<NameValuePair> paramList = new ArrayList<>();
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    paramList.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                }

                request.setEntity(new UrlEncodedFormEntity(paramList));
            }

            response = client.execute(request);
            int httpStatusCode = response.getStatusLine().getStatusCode();
            if(httpStatusCode == HttpStatus.SC_OK) {
                String result = EntityUtils.toString(response.getEntity());
                log.info("Do post url: {}, response: {}", url, result);

                return result;
            } else {
                log.error("Do post error url {} code {} message {}", url, httpStatusCode, response.getStatusLine().getReasonPhrase());
            }
        } catch (Exception e) {
            log.error("Do post error, url {}", url, e);
        } finally {
            if(null != response) {
                try {
                    response.close();
                } catch (IOException ignored) {}
            }
        }

        return null;
    }

    public static X5Response doX5Post(String url, String appId, String appKey, Object data, String method) {
        String bodyString = GsonUtil.toJsonString(data);
        log.info("Do x5 request url: {}, data: {}", url, bodyString);

        X5RequestHeader x5RequestHeader = new X5RequestHeader(appId, bodyString, appKey);
        x5RequestHeader.setMethod(method);
        X5Request x5Request = new X5Request(x5RequestHeader, bodyString);

        Map<String, String> params = new HashMap<>();
        params.put("data", x5Request.toString());

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE);

        String result = doPost(url, params, headers);
        if(StringUtils.isBlank(result)) {
            return X5Response.fail("Empty output");
        }

        return GsonUtil.toBean(result, X5Response.class);
    }

    public static String doSoapPost(String url, String soapMsg, String soapAction) {
        Assert.isTrue(url.startsWith("http://") || url.startsWith("https://"), "Invalid url");

        log.info("Do soap post url: {}, params: {}", url, soapMsg);

        CloseableHttpResponse response = null;

        try(CloseableHttpClient client = createClient()) {
            URIBuilder uriBuilder = new URIBuilder(url);

            HttpPost request = new HttpPost(uriBuilder.build());
            request.setConfig(getRequestConfig());

            request.setHeader("Content-Type", MediaType.TEXT_XML_VALUE);
            if(StringUtils.isNotBlank(soapAction)) {
                request.setHeader("SOAPAction", soapAction);
            }

            StringEntity entity = new StringEntity(soapMsg);
            request.setEntity(entity);

            response = client.execute(request);
            int httpStatusCode = response.getStatusLine().getStatusCode();
            if(httpStatusCode == HttpStatus.SC_OK) {
                String result = EntityUtils.toString(response.getEntity());
                log.info("Do soap post url: {}, response: {}", url, result);

                return result;
            } else {
                log.error("Do soap post error url {} code {} message {}", url, httpStatusCode, response.getStatusLine().getReasonPhrase());
            }
        } catch (Exception e) {
            log.error("Do soap post error, url {}", url, e);
        } finally {
            if(null != response) {
                try {
                    response.close();
                } catch (IOException ignored) {}
            }
        }

        return null;
    }

    private static CloseableHttpClient createClient() {
        return HttpClientBuilder.create()
                .setRetryHandler((e, i, httpContext) -> {
                    log.info("Exception cause, message: {},  retry: {}", e.getMessage(), i);

                    return i <= MAX_RETRY_TIMES;
                })
                .setServiceUnavailableRetryStrategy(new ServiceUnavailableRetryStrategy() {
                    @Override
                    public boolean retryRequest(HttpResponse httpResponse, int i, HttpContext httpContext) {
                        int statusCode = httpResponse.getStatusLine().getStatusCode();

                        return i <= MAX_RETRY_TIMES && (
                                statusCode == HttpStatus.SC_BAD_GATEWAY || // 502
                                        statusCode == HttpStatus.SC_SERVICE_UNAVAILABLE || // 503
                                        statusCode == HttpStatus.SC_GATEWAY_TIMEOUT
                        );
                    }

                    @Override
                    public long getRetryInterval() {
                        return RETRY_INTERVAL;
                    }
                })
                .build();
    }

    private static RequestConfig getRequestConfig() {
        RequestConfig.Builder builder = RequestConfig.custom().setConnectionRequestTimeout(CONNECTION_TIMEOUT).setSocketTimeout(SO_TIMEOUT);
        return builder.build();
    }
}
