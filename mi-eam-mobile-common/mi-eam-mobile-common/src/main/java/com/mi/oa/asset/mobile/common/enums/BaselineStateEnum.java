package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/3/1 16:53
 */
@Getter
public enum BaselineStateEnum {
    /**
     * 初始状态
     */
    WORKING("1","生效中"),
    /**
     * 变更中
     */
    UPDATING("2","变更中"),
    /**
     * 初始状态
     */
    DELETED("3","已删除"),
    ;

    private String key;
    private String desc;

    BaselineStateEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
