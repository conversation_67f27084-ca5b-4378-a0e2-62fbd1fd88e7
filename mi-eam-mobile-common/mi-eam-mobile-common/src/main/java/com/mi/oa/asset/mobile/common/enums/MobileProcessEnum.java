package com.mi.oa.asset.mobile.common.enums;

/**
 * <AUTHOR>
 * @description: 工程机流程key类型枚举
 * @date 2021/12/7 14:38
 */
public enum MobileProcessEnum {

    PROCESS_KEY_MOBILE_CLAIM("eam_mobile_claim", "工程机申请"),
    PROCESS_KEY_MOBILE_TRANSFER("eam_mobile_transfer", "工程机转移"),
    PROCESS_KEY_MOBILE_RETURN("eam_mobile_return", "工程机归还"),
    PROCESS_KEY_MOBILE_DELAY("eam_mobile_dReturn", "工程机延期归还申请"),
    PROCESS_KEY_MOBILE_NO_RETURN("eam_mobile_noReturn", "工程机无法归还"),
    PROCESS_KEY_MOBILE_SEC_REMOVE("eam_mobile_secRem", "工程机保密套拆除"),
    PROCESS_KEY_MOBILE_SCRAP("eam_mobile_scrap", "工程机报废"),
    PROCESS_KEY_MOBILE_ROLE_APPLY("eam_mobile_auth", "小米权限申请"),
    PROCESS_KEY_MOBILE_THIRD_USER_APPLY("eam_mobile_odmEms", "三方P账号申请");

    private String key;

    private String desc;

    MobileProcessEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

    //根据key查找枚举
    public static MobileProcessEnum getProcessEnum(String key) {
        MobileProcessEnum result = PROCESS_KEY_MOBILE_CLAIM;
        MobileProcessEnum[] processEnums = MobileProcessEnum.values();
        for (MobileProcessEnum processEnum : processEnums) {
            if (processEnum.getKey().equals(key)) {
                result = processEnum;
            }
        }
        return result;
    }
}
