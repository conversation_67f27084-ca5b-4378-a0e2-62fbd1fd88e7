package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/21
 */
@Getter
public enum SapFactoryEnum {

    IN_LAND("1110", "1110","1110-1110", "国内"),
    YI_ZHUANG("111B","P111B", "111B-1110", "亦庄"),
    CHANG_PING("3882","P3882", "3882-3880", "昌平"),
    CHANG_PING2("111Z","P111Z", "111Z-1110", "昌平二期"),
    YIN_NI("1400","1400", "1400-1400", "印尼"),
    ROBOT("8290","P8290", "8290-8290", "机器人"),
    HEA_FACTORY("8510","P8510", "8510-1700", "大家电-空调"),
    MI_AUTOEACC("3885","P3885", "3885-1110", "汽车电子"),
    MI_AUTOEACC2("111N","111N", "111N-1110", "汽车电子"),
    ;

    private String sapFactory;

    private String sapFactoryCode;

    private String makeCn;

    private String desc;

    SapFactoryEnum(String sapFactory, String sapFactoryCode, String makeCn, String desc){
        this.sapFactory = sapFactory;
        this.sapFactoryCode = sapFactoryCode;
        this.makeCn = makeCn;
        this.desc = desc;
    }
    public static SapFactoryEnum getEnum(String sapFactoryCode){
        for (SapFactoryEnum sapFactoryEnum : SapFactoryEnum.values()) {
            if (sapFactoryEnum.getSapFactoryCode().equals(sapFactoryCode)) {
                return sapFactoryEnum;
            }
        }
        return null;
    }
}
