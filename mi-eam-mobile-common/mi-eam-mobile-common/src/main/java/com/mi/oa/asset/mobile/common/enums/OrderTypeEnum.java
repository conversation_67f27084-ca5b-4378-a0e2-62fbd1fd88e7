package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/4
 */
@Getter
public enum OrderTypeEnum {

    SALE_ORDER("0", "销售订单"),
    SOLD_OUT("1", "下架订单"),
    REFUND("2", "退款订单"),
    ;

    private String type;
    private String desc;

    OrderTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
