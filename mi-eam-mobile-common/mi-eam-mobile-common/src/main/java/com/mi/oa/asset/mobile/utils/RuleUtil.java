package com.mi.oa.asset.mobile.utils;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/5/6
 */

public class RuleUtil {

    public static String parseConstant(String sql, Map<String, String> userInfo) {
        return parseConstant(sql, userInfo, true);
    }

    public static String parseConstant(String sql, Map<String, String> userInfo, boolean addChar) {
        String regex = "\\{[^}|^{]+\\}";
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(sql);
        StringBuffer sb = new StringBuffer();

        while(true) {
            while(m.find()) {
                String tag = m.group();
                String value = (String)getConstantParam(tag, userInfo);
                if (value.indexOf("{") >= 0) {
                    m.appendReplacement(sb, value);
                } else if (addChar && sql.indexOf("'" + tag) < 0 && sql.indexOf(tag + "'") < 0) {
                    m.appendReplacement(sb, addChar(value));
                } else {
                    m.appendReplacement(sb, value);
                }
            }

            m.appendTail(sb);
            return sb.toString();
        }
    }

    public static Object getConstantParam(String sParam, Map<String, String> userInfo) {
        if (sParam != null && sParam.length() != 0) {
            if (userInfo != null && !userInfo.isEmpty()) {
                if (sParam.equals("{CURDATE}")) {
                    return DateUtil.getToday();
                } else if (sParam.equals("{CURDATETIME}") || sParam.equals("{TODAY}")) {
                    return DateUtil.getTodaySec();
                } else if (sParam.equals("{CURUSER}")) {
                    return userInfo;
                } else if (sParam.equals("{CURUSERID}")) {
                    return MapUtil.getValue(userInfo, "user_id");
                } else if (sParam.equals("{CURUSERCODE}")) {
                    return MapUtil.getValue(userInfo, "user_code");
                } else if (sParam.equals("{CURUSERNAME}")) {
                    return MapUtil.getValue(userInfo, "user_name");
                } else if (sParam.equals("{CURDEPTID}")) {
                    return MapUtil.getValue(userInfo, "dept_id");
                } else if (sParam.equals("{CURDEPTCODE}")) {
                    return MapUtil.getValue(userInfo, "dept_code");
                } else if (sParam.equals("{CURDEPTNAME}")) {
                    return MapUtil.getValue(userInfo, "dept_name");
                } else if (sParam.equals("{CURROLEID}")) {
                    return MapUtil.getValue(userInfo, "role_id");
                } else if (sParam.equals("{CURTENANTID}")) {
                    return MapUtil.getValue(userInfo, "tenant_id");
                } else {
                    return sParam.equals("{CURLANG}") ? MapUtil.getValue(userInfo, "lang_type") : sParam;
                }
            } else {
                return sParam;
            }
        } else {
            return "";
        }
    }

    public static String parseParam(String sql, Map<String, String> params) {
        return parseParam(sql, params, true);
    }

    public static String parseParam(String sql, Map<String, String> params, boolean addChar) {
        String regex = "\\[[^]|^\\[]+\\]";
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(sql);

        StringBuffer sb;
        String value;
        for(sb = new StringBuffer(); m.find(); m.appendReplacement(sb, value)) {
            String tag = m.group();
            tag = tag.substring(1, tag.length() - 1);
            value = (String)params.get(tag);
            if (value == null) {
                value = "[no field]";
            } else if (addChar && sql.indexOf("'" + tag) < 0 && sql.indexOf(tag + "'") < 0) {
                value = addChar(value);
            }
        }

        m.appendTail(sb);
        return sb.toString();
    }

    public static String addChar(String str) {
        if (str == null) {
            str = "";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("'").append(str).append("'");
        return sb.toString();
    }

    /**
     * 获取位置
     * @param position
     * @return
     */
    public static int[] getPosition(String position) {
        int [] ret = new int[0];
        if (position == null || position.length() == 0) {
            return ret;
        }
        String[] strRet = position.split(",");
        if (strRet.length != 2) return ret;

        ret = new int[2];
        ret[0] = Integer.parseInt(strRet[0]);   //行
        ret[1] = Integer.parseInt(strRet[1]);   //列

        return ret;
    }

    /**
     * 解析SQL中的常量值。
     * @param sql
     * @param userInfo
     * @return
     */
    public static String parseImpConstant(String sql, Map<String,String> userInfo) {
        String regex = "\\{[^}|^{]+\\}";
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(sql);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            String tag = m.group();
            //取常量的值
            String value = (String) getConstantParam(tag, userInfo);
            //如果还含{，说明没有解析
            if (value.indexOf("{") >= 0) {
                m.appendReplacement(sb, value);
            } else {
                m.appendReplacement(sb, addChar(value));
            }
        }
        m.appendTail(sb);

        return sb.toString();
    }

    public static String parseAppField(String taskDesc, Map<String, Object> appData, boolean addChar) {
        Pattern p = Pattern.compile("\\[[^]|^\\[]+\\]");
        Matcher m = p.matcher(taskDesc);

        StringBuffer sb;
        String value;
        for(sb = new StringBuffer(); m.find(); m.appendReplacement(sb, value)) {
            String name = m.group();
            name = name.substring(1, name.length() - 1);
            value = (String)appData.get(name);
            if (value == null) {
                value = "[no field]";
            } else if (addChar) {
                value = addChar(value);
            }
        }

        m.appendTail(sb);
        return sb.toString();
    }

    public static String repDateValue(String value) {
        if (value == null || value.length() == 0) return "";

        value = value.replace('/', '-');
        String[] vs = value.split("-");
        if (vs.length == 2) {
            value = value + "-01";
        } else if (vs.length == 1) {
            value = value + "-01-01";
        }

        return value;
    }
}
