package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/3/14 16:14
 */
@Getter
public enum BaselineRecordAuditEnum {
    /**
     *
     */
    NOT_SUBMIT("0", "未提交"),
    SUBMIT("1", "已提交"),
    CHECKING("2", "审批中"),
    FINISHED("3", "已完成"),
    INVALID("7", "已失效"),
    CLOSED("8", "已关闭");

    private String key;

    private String desc;

    BaselineRecordAuditEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
