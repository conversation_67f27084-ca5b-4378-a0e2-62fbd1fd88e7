package com.mi.oa.asset.mobile.utils;

import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/17
 */

public class MapUtil {

    public MapUtil() {
    }

    public static double getDouble(Map mp, String param) {
        return getDouble(mp, param, "0.00");
    }

    public static double getDouble(Map mp, String param, String defval) {
        String value = getValue(mp, param, defval);
        return Double.parseDouble(value);
    }

    public static int getInt(Map mp, String param) {
        return getInt(mp, param, "0");
    }

    public static int getInt(Map mp, String param, String defval) {
        String value = getValue(mp, param, defval);
        return Integer.parseInt(value);
    }

    public static String getValue(Map mp, String param) {
        return getValue(mp, param, "");
    }

    public static String getValue(Map mp, String param, String defval) {
        if (mp == null) {
            return defval;
        } else if (param == null) {
            return defval;
        } else {
            Object obj = mp.get(param);
            if (obj == null) {
                return defval;
            } else {
                String s;
                if (obj instanceof String[]) {
                    s = ArrayUtil.arrayToString((String[])((String[])obj));
                } else {
                    s = (String)obj;
                    s = s.trim();
                }

                return s.length() == 0 ? defval : s;
            }
        }
    }

    public static String[] getValues(Map mp, String name) {
        String[] asRet = null;
        Object obj = mp.get(name);
        if (obj instanceof String) {
            asRet = new String[]{(String)obj};
        } else if (obj instanceof String[]) {
            asRet = (String[])((String[])obj);
        }else if(obj instanceof List){
            asRet = ArrayUtil.listToArray((List)obj);
        } else {
            asRet = new String[0];
        }

        return asRet;
    }

    public static String[] getParameterNames(Map mp) {
        if (mp != null && !mp.isEmpty()) {
            Set<String> key = mp.keySet();
            String[] asRet = (String[])key.toArray(new String[key.size()]);
            return asRet;
        } else {
            return new String[0];
        }
    }

    public static boolean hasRecord(Map<String, String> mp) {
        return hasRecodNum(mp) > 0;
    }

    public static int hasRecodNum(Map<String, String> mp) {
        if (mp != null && !mp.isEmpty()) {
            String sCnt = (String)mp.get("cnt");
            if (sCnt == null || sCnt.length() == 0) {
                sCnt = (String)mp.get(mp.keySet().iterator().next());
                if (sCnt == null || sCnt.length() == 0) {
                    sCnt = "0";
                }
            }

            return Integer.parseInt(sCnt);
        } else {
            return 0;
        }
    }

    public static String toString(Map mp) {
        return toString(mp, (StringBuilder)null);
    }

    private static String toString(Map mp, StringBuilder sb) {
        if (mp != null && !mp.isEmpty()) {
            if (sb == null) {
                sb = new StringBuilder();
            }

            Iterator itr = mp.keySet().iterator();

            while(true) {
                while(itr.hasNext()) {
                    String sName = (String)itr.next();
                    Object obj = mp.get(sName);
                    if (obj instanceof String) {
                        sb.append("\t" + sName + "=" + obj + "\r\n");
                    } else if (obj instanceof String[]) {
                        String[] objs = (String[])((String[])obj);
                        String[] var11 = objs;
                        int var12 = objs.length;

                        for(int var8 = 0; var8 < var12; ++var8) {
                            String val = var11[var8];
                            sb.append("\t" + sName + "=" + val + "\r\n");
                        }
                    } else if (obj instanceof Map) {
                        sb.append("\t<" + sName.toString() + ">\r\n");
                        toString((Map)obj, sb);
                        sb.append("\t</" + sName.toString() + ">\r\n");
                    } else if (obj instanceof List) {
                        List ls = (List)obj;

                        for(int i = 0; i < ls.size(); ++i) {
                            Object lsObj = ls.get(i);
                            if (lsObj instanceof Map) {
                                sb.append("\t<" + sName.toString() + "_ls>\r\n");
                                toString((Map)lsObj, sb);
                                sb.append("\t</" + sName.toString() + "_ls>\r\n");
                            } else {
                                sb.append("list value=" + obj.toString() + "\r\n");
                            }
                        }
                    }
                }

                return sb.toString();
            }
        } else {
            return "map is empty...";
        }
    }
}
