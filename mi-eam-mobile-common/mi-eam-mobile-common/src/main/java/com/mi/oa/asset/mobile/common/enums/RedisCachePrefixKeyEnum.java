package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/5
 */
@Getter
public enum RedisCachePrefixKeyEnum {

    PROJECT_CODE_CACHE("mobile:project:code:"),
    SKU_DETAIL_CODE("mobile:sku:detail:code:"),
    SN_DETAIL_ASN_NO("mobile:sn:detail:asnNo:"),
    PO_DETAIL_NO("mobile:po:detail:poNo:"),
    BATCH_ID_UNIQUE("eam:batchId:unique:"),
    PS_LEAVE_CHECK("ps:leave:check:empcode"),
    SRM_ASN_CACHE("mobile:srm:asn:cache:asnNo:"),
    THIRD_USER_CACHE("mobile:third:user:cache:userCode:"),
    SYS_DEPT_DEPT_ID("mobile:long:dept:deptId:"),
    TRAN_EMAIL_USER_CODE("mobile:email:userCode:"),
    IDM_CACHE_USER_CODE("mobile:idm:cache:userCode:"),
    FLOW_NUM_REDIS_KEY("mobile:flowNum:"),
    MDM_PROJECT_PROJECT_PM("mobile:pm:projectCode:"),
    PROTO_CARD_DEPTPRO_LOCK_KEY("mobile:card:deptpro:total:lock:key"),
    PROTO_CARD_DEPTPRO_UPDATE_TIME("mobile:card:deptpro:cache:updTime:"),
    PROTO_CARD_DEPT_UPDATE_TIME("mobile:card:dept:cache:updTime"),
    PROTO_CARD_DEPT_LOCK_KEY("mobile:card:dept:total:lock:key"),
    PROTO_CARD_PROJECT_UPDATE_TIME("mobile:card:project:cache:updTime:"),
    PROTO_CARD_PROJECT_LOCK_KEY("mobile:card:project:cache:lock:key"),
    PROTO_CARD_SKU_UPDATE_TIME("mobile:card:sku:cache:updateTime:"),
    PROTO_CARD_SKU_LOCK_KEY("mobile:card:sku:cache:lock:key"),
    PROTO_CARD_STORE_UPDATE_TIME("mobile:card:store:cache:update:key"),
    PROTO_CARD_STORE_LOCK_KEY("mobile:card:store:cache:lock:key"),
    PROTO_PLAN_MESSAGE_REMIND("mobile:plan:message:remind:planId:"),
    PROTO_PLAN_SUBMIT_MESSAGE_REMIND("mobile:plan:submit:message:remind:planId:"),
    PROTO_PLAN_GENERATE_PUR_ORDER("mobile:plan:create:pur:order:planId:"),
    PROTO_PLAN_PRE_ORDER_CREATE("mobile:plan:create:pre:order:md5:"),
    ;

    private String prefixKey;

    RedisCachePrefixKeyEnum(String prefixKey) {
        this.prefixKey = prefixKey;
    }
}
