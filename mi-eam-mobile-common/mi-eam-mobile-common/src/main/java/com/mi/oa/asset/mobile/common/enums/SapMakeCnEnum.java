package com.mi.oa.asset.mobile.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/10/25
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SapMakeCnEnum {

    INTERNAL("1110", "1110-1110"),
    YING_NI("1400", "1400-1400"),
    YI_ZHUANG("111B", "111B-1110"),
    CHANG_PING("3882", "3882-3880"),
    CHANG_PING2("111Z", "111Z-1110"),
    YUE_NAN("110R", "110R-1100"),
    MI_DIAN("1184", "1184-1110"),
    WEARABLE("111C", "111C-1110"),
    TV("1181", "1181-1180"),
    BOX("1182", "1182-1180"),
    ECO_MIDIAN("1185", "1185-1180"),
    ECO_MITONG("1112", "1112-1110"),
    LAPTOP_MITONGRMB("111K", "111K-1110"),
    LAPTOP_GANGMIUSD("110S", "110S-1100"),
    ROBOT("8290", "8290-8290"),
    HEA_FACTORY("8510", "8510-1700"),
    MI_AUTOEACC("111N", "111N-1110"),
    MI_AUTOEACC2("3885", "3885-1110"),
    ;

    private String sapFactory;

    private String makeCn;

    public static String getMakeCn(String sapFactory){
        for (SapMakeCnEnum cnEnum : values()){
            if (cnEnum.getSapFactory().equals(sapFactory)){
                return cnEnum.getMakeCn();
            }
        }
        return "";
    }
}
