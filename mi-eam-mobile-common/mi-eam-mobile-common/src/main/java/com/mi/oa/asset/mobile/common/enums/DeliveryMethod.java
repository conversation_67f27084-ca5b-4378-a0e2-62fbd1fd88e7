package com.mi.oa.asset.mobile.common.enums;

import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DeliveryMethod {

    SELF("self", "自提"),
    POST("post", "邮寄");

    private String type;

    private String name;

    public static DeliveryMethod valuesOf(String type) {
        for (DeliveryMethod deliveryMethod : DeliveryMethod.values()) {
            if (deliveryMethod.type.equals(type)) {
                return deliveryMethod;
            }
        }
        throw new BizException(BizCodeEnum.ENUM_ERROR, "type=" + type + "对应的枚举不存在");
    }


}
