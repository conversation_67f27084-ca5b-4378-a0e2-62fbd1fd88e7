package com.mi.oa.asset.mobile.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/14 17:12
 */
@AllArgsConstructor
@Getter
public enum ProtoProjectCategroyEnum {
    /**
     * 项目分类
     * 电视108
     * 空调623
     * 小米互联网音箱（智能硬件）306
     * 路由器（智能硬件）382
     * 翻译机863
     * 生态链674
     * 笔记本电脑98
     * 品商958
     * 印度电商940
     * 大家电762
     * 123盒子  308小米互联网音箱 845电视配件
     */
    TV_PROJECT(108, "电视", "tv"),
    TV_PROJECT_2(123, "123盒子", "tv"),
    MI_PROJECT_2(308, "小米互联网音箱", "tv"),
    ROUTER_PROJECT_2(845, "电视配件", "tv"),
    MI_PROJECT(306, "小米互联网音箱", "ecochain"),
    ROUTER_PROJECT(382, "路由器", "ecochain"),
    ECO_PROJECT(674, "生态链", "ecochain"),
    ;
    private final int code;
    private final String name;
    private final String mrpType;

    public static String getMrpTypeByCode(int code) {
        for (ProtoProjectCategroyEnum item : ProtoProjectCategroyEnum.values()) {
            if (item.code == code) {
                return item.getMrpType();
            }
        }
        return null;
    }

    public static List<Integer> getAllCode() {
        List<Integer> list = new ArrayList<>();
        for (ProtoProjectCategroyEnum item : ProtoProjectCategroyEnum.values()) {
            list.add(item.code);
        }
        return list;
    }
}
