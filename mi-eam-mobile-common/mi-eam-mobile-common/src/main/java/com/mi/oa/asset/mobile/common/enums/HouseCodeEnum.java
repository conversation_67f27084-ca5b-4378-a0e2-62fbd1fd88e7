package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/5/25 19:30
 */

@Getter
public enum HouseCodeEnum {
    BEIJING("2", "北京"),
    WUHAN("3", "武汉"),
    SH<PERSON>ZHEN("4", "深圳"),
    SHANGHAI("5", "上海"),
    NANJING("6", "南京"),
    ;

    private String code;
    private String desc;

    HouseCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
