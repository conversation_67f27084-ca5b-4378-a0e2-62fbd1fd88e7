package com.mi.oa.asset.mobile.common.exception;

import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletResponse;

/**
 * GlobalExceptionHandler
 *
 * <AUTHOR>
 * @date 2021/8/4 14:10
 */
@Slf4j
@RestControllerAdvice
class GlobalExceptionHandler {

    /**
     * 其余异常错误
     *
     * @param e
     * @param response
     * @return com.mi.oa.infra.oaucf.core.dto.BaseResp<java.lang.Object>
     * <AUTHOR>
     * @date 2021/8/4 14:08
     */
    @ExceptionHandler(value = {Exception.class})
    public BaseResp<Object> handleException(Exception e, HttpServletResponse response) {
        log.error("request_internal_error: ", e);
        //response.setStatus(500);
        return BaseResp.error(500, e.getMessage());
    }

    /**
     * 空指针异常
     *
     * @param e
     * @param response
     * @return com.mi.oa.infra.oaucf.core.dto.BaseResp<java.lang.Object>
     * <AUTHOR>
     * @date 2021/8/4 14:08
     */
    @ExceptionHandler(NullPointerException.class)
    public BaseResp<Object> handleNullPointerException(NullPointerException e, HttpServletResponse response) {
        String message = "空指针异常";
        if (e.getStackTrace().length > 0) {
            StackTraceElement first = e.getStackTrace()[0];
            message = String.format("%s [%s:%d]", message, first.getFileName(), first.getLineNumber());
        }
        log.error(message, e);
       // response.setStatus(500);
        return BaseResp.error(500, message);
    }

    /**
     * http not supported 异常
     *
     * @param e
     * @param response
     * @return com.mi.oa.infra.oaucf.core.dto.BaseResp<java.lang.Object>
     * <AUTHOR>
     * @date 2021/8/4 14:08
     */
    @ExceptionHandler(value = {HttpRequestMethodNotSupportedException.class})
    public BaseResp<Object> handleNotSupportedRequestException(HttpRequestMethodNotSupportedException e, HttpServletResponse response) {
        log.warn("request not found", e);
        //response.setStatus(404);
        return BaseResp.error(404, e.getMessage());
    }

    /**
     * 业务异常
     *
     * @param e
     * @param response
     * @return com.mi.oa.infra.oaucf.core.dto.BaseResp<java.lang.Object>
     * <AUTHOR>
     * @date 2021/8/4 14:08
     */
    @ExceptionHandler(value = {BizException.class})
    public BaseResp<Object> handleDomainException(BizException e, HttpServletResponse response) {
        log.warn("----- BizException [" + e.getCode() + "] message :" + e.getMessage());
        log.debug("request domain error:", e);
        //response.setStatus(400);
        return BaseResp.error(e.getCode(), e.getMessage());
    }

    /**
     * 参数valid校验异常处理
     *
     * @param e        参数异常类
     * @param response 返回值
     * @return
     */
    @ExceptionHandler(value = {MethodArgumentNotValidException.class})
    public BaseResp<Object> handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletResponse response) {
        log.warn("----- MethodArgumentNotValidException message :" + e.getMessage());
        FieldError fieldError = e.getBindingResult().getFieldError();
        String defaultMessage = "";
        if (fieldError != null) {
            defaultMessage = fieldError.getDefaultMessage();
        }
        return BaseResp.error(500, defaultMessage);
    }

}
