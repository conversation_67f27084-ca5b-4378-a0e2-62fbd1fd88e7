package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/23
 */
@Getter
public enum PlanStatusEnum {

    STAFFING("0", "编制中"),
    PLANING("2", "盘点中"),
    HAVE_PLANED("3", "盘点完成"),
    ;

    private String type;
    private String desc;

    PlanStatusEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

}
