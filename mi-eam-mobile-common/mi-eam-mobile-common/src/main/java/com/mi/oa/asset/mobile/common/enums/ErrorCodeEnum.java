package com.mi.oa.asset.mobile.common.enums;

import com.mi.oa.infra.oaucf.core.exception.InfraErrorCode;

/**
 * <AUTHOR>
 * @date 2021/8/16 18:21
 */

public enum ErrorCodeEnum implements InfraErrorCode {
    /**
     * 未知错误
     */
    UNKNOWN_ERROR(1, "未知错误"),
    /**
     * 参数为空
     */
    PARAM_IS_NULL_ERROR(1, "%s 参数为空"),
    /**
     * X5 未知错误
     */
    X5_UNKNOWN_ERROR(100, "X5 未知错误"),
    /**
     * X5 请求客户端初始化失败
     */
    X5_INIT_ERROR(101, "X5 请求客户端初始化失败"),
    /**
     * X5 appId 或 appKey 为空
     */
    X5_EMPTY_ID_OR_KEY(101, "X5 appId 或 appKey 为空"),
    /**
     * X5 sign 为空
     */
    X5_EMPTY_SIGN(102, "X5 sign 为空"),
    /**
     * X5 签名错误
     */
    X5_CHECK_SIGN_FAIL(102, "X5 签名错误"),
    /**
     * X5 返回错误: %s
     */
    X5_RESPONSE_ERROR(103, "X5 返回错误: %s"),
    /**
     * 不合法的 X5 appid
     */
    X5_INVALID_ID(104, "不合法的 X5 appid"),
    /**
     * 库存服务未知错误
     */
    STOCK_UNKNOWN_ERROR(200, "库存服务未知错误"),
    /**
     * %s 不能为空
     */
    STOCK_MISSING_PARAM_ERROR(201, "%s 不能为空"),
    /**
     * 调用三方返回失败
     */
    APPLICATION_THIRD_RESP_ERROR(301, "调用三方返回失败"),
    /**
     * IDM 接口返回错误
     */
    IDM_SERVICE_ERROR(401, "IDM 接口返回错误：%s"),
    /**
     * 用户信息不存在
     */
    IDM_USER_NOT_FOUNT(402, "用户信息不存在, %s"),
    /**
     * 寄件服务参数校验不通过
     */
    PR_SEND_EXPRESS_PARAM_ERROR(501, "寄件服务参数校验不通过,errorMsg: %s"),
    /**
     * 找不到接口定义
     */
    HROD_MISSING_API_DESCRIBE(601, "找不到接口定义: %s"),
    /**
     * 同步SRM数据错误
     */
    SRM_DATA_ERROR(601, "同步SRM数据错误:%s"),
    /**
     * 飞书或邮件创建数据异常
     */
    MESSAGE_CREATE_ERROR(701,"飞书或邮件创建数据异常"),

    NOT_FUNCTION(901, "{{{未找到功能信息}}}"),
    SAVE_NO_DATA(902, "{{{没有可以保存的数据}}}"),
    DATA_HAVE_REPEAT(903, "{{{字段}}}【%s】{{{有重复的值！}}}"),
    UPDATE_NO_KEY(904, "{{{找不到被保存记录的键值！}}}"),
    EVENT_CODE_ERROR(905, "{{{事件码错误}}}"),
    SUBMIT_NOT_VALUE(906, "{{{找不到提交记录的键值！}}}"),
    RECORD_HAVE_SUBMIT(907, "{{{选择的记录中有}}}%s{{{条已经提交，请刷新数据后重新选择提交！}}}"),
    SUB_NOT_DATA(907, "{{{子功能}}}【%s】{{{没有数据，必须填写！}}}"),
    ;

    private int errCode;

    private String errDesc;

    ErrorCodeEnum(int errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }

    @Override
    public int getBizCode() {
        return 0;
    }

    @Override
    public int getErrorCode() {
        return errCode;
    }

    @Override
    public String getErrDesc() {
        return errDesc;
    }
}
