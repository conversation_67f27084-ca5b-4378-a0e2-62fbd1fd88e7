package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description: 工程机流程类型枚举
 * @date 2021/12/7 14:38
 */
@Getter
public enum TranTypeEnum {

    INNER_TO_INNER("1", "小米内部转移"),
    ODM_TO_INNER("2", "ODM/EMS/黑鲨转小米内部"),
    INNER_TO_ODM("3", "小米内部转ODM/EMS/黑鲨");

    private String key;

    private String desc;

    TranTypeEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }
}
