package com.mi.oa.asset.mobile.common.enums;

import com.mi.oa.infra.oaucf.redis.key.OACacheKey;
import lombok.Getter;

/**
 * 缓存定义枚举
 *
 * <AUTHOR>
 * @date 2021/9/27 16:32
 */
@Getter
public enum OACacheKeyEnum implements OACacheKey {

    SAP_PROJECT_INFO_CACHE("SAP_PROJECT_INFO_CACHE_{0}", 60*60*2, "sap获取项目、阶段信息"),
    ;

    /* 缓存key */
    private String key;

    /* 缓存时间 */
    private int second;

    /* 缓存中文说明 */
    private String comment;

    OACacheKeyEnum(String key, int second, String comment) {
        this.key = key;
        this.second = second;
        this.comment = comment;
    }

    @Override
    public String getKey() {
        return key;
    }

    @Override
    public void setKey(String s) {
        this.key = s;
    }

    @Override
    public int getSecond() {
        return second;
    }

    @Override
    public void setSecond(int i) {
        this.second = i;
    }

    @Override
    public String getComment() {
        return comment;
    }

    @Override
    public void setComment(String s) {
        this.comment = s;
    }
}
