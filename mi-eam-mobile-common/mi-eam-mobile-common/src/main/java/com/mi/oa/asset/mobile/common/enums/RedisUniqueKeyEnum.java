package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/5
 */
@Getter
public enum RedisUniqueKeyEnum {
    PROTO_ASN_ORDER("proto_asn_order"),
    PROTO_ASN_ORDER_ITEM("proto_asn_order_item"),
    PROTO_ASN_ORDER_DET_ITEM("proto_asn_order_item"),
    PROTO_UNIQUE_SN("proto_unique_sn"),
    SAP_EXELOG_DET("sap_exelog_det"),
    SAP_EXELOG("sap_exelog"),
    PROTO_APPLY_SECOND("proto_apply_second"),
    PROTO_APPLY("proto_apply"),
    PROTO_APPLY_MAT("proto_apply_mat"),
    BPM_EXELOG("bpm_exelog"),
    PROTO_SECOND_CARD("proto_second_card"),
    PROTO_IN("proto_in"),
    SYS_USER_ROLE("sys_user_role"),
    PROTO_PROJECT_STAGE("proto_project_stage"),
    PROTO_IN_DET("proto_in_det"),
    PROTO_CARD("proto_card"),
    PROTO_SLEEVE("proto_sleeve"),
    PROTO_DELAY("proto_delay"),
    PROTO_SLEEVE_MAT("proto_sleeve_mat"),
    PROTO_BASELINE("proto_baseline"),
    PROTO_PROJECT_LIST("proto_project_list"),
    PROTO_PROJECT_FACTORY("proto_project_factory"),
    PROTO_PROJECT_CFG("proto_project_cfg"),
    SYS_USER_DATA("sys_user_data"),
    PROTO_AUTH_DATA_CFG("proto_auth_data_cfg"),
    PROTO_DISPOSE_RULE("proto_dispose_rule"),
    PROTO_ORDER("proto_order"),
    SYS_OPER_LOG("sys_oper_log"),
    ;

    private String tableName;

    RedisUniqueKeyEnum(String tableName) {
        this.tableName = tableName;
    }
}
