package com.mi.oa.asset.mobile.common.model.x5;

import com.mi.oa.asset.mobile.utils.GsonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/9/30 0:07
 */

@Data
@AllArgsConstructor
public class X5Request {

    private X5RequestHeader header;
    private String body;

    @Override
    public String toString() {
        String jsonStr = GsonUtil.toJsonString(this);
        return Base64.getEncoder().encodeToString(jsonStr.getBytes(StandardCharsets.UTF_8));
    }
}


