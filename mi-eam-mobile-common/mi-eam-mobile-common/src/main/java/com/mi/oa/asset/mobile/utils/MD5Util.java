package com.mi.oa.asset.mobile.utils;

import com.alibaba.nacos.common.utils.Md5Utils;
import com.mi.oa.asset.mobile.common.enums.BizCodeEnum;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;

/**
 * 来自jxstar
 *
 * <AUTHOR>
 * @date 2022/1/21 11:27
 */

public class MD5Util {

    public static final Logger log = LoggerFactory.getLogger(MD5Util.class);

    private MD5Util() {
    }

    public static String getMD5(String data) {
        if (StringUtils.isBlank(data)) {
            throw new BizException(BizCodeEnum.PARAMETER_IS_NULL);
        }
        try {
            return Md5Utils.getMD5(data.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("getMD5 error:{}", e.getMessage(), e);
            throw new BizException(BizCodeEnum.MD5_ERROR, e.getMessage());
        }
    }
}
