package com.mi.oa.asset.mobile.common.enums;

import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PreOrderTypeEnum {

    CREATE(1, "创建"),
    CANCEL(0, "取消");

    private Integer type;

    private String name;

    public static PreOrderTypeEnum valuesOf(Integer type) {
        for (PreOrderTypeEnum typeEnum : PreOrderTypeEnum.values()) {
            if (typeEnum.type.equals(type)) {
                return typeEnum;
            }
        }
        throw new BizException(BizCodeEnum.ENUM_ERROR, "type=" + type + "对应的枚举不存在");
    }


}
