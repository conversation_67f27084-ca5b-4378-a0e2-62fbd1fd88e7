package com.mi.oa.asset.mobile.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * description: your description
 *
 * <AUTHOR>
 * @date 2023/9/20
 */
@Getter
@AllArgsConstructor
public enum MdmX5Enum {
    // code = 200  创建成功
    // code = 0 成本中心编码校验不合规
    // code = 2 成本中心编码在系统中已存在
    // code = 3 成本中心编码在流程中存在
    S("200", "创建成功"),
    A("0", "成本中心编码校验不合规"),
    B("2", "成本中心编码在系统中已存在"),
    C("3", "成本中心编码在流程中存在"),
    E("E", "远程调用异常");
    private final String code;
    private final String desc;

    public static String valuesOf(String code) {
        for (MdmX5Enum mdmX5Enum : MdmX5Enum.values()) {
            if (mdmX5Enum.getCode().equals(code)) {
                return mdmX5Enum.getDesc();
            }
        }
        return MdmX5Enum.E.getDesc();
    }
}
