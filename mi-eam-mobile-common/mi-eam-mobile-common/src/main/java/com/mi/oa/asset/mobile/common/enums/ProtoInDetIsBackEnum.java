package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * 工程机归还状态
 *
 * <AUTHOR>
 * @date 2022/1/20 16:15
 */
@Getter
public enum ProtoInDetIsBackEnum {
    /**
     * 未确认
     */
    UNCONFIRMED("0","未确认"),
    /**
     * 确认中
     */
    RETURNING_CONFIRMATION("2","确认中"),
    /**
     * 已确认
     */
    CONFIRMED("3","已确认"),
    ;
    private String code;
    private String desc;

    ProtoInDetIsBackEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
