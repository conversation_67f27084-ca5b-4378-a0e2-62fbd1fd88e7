package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/17
 */
@Getter
public enum InStatusEnum {

    NOT_DELIVERY("0", "未收货"),
    HAVE_DELIVERY("1", "已收货"),
    DUMPED_SAP("3", "已抛SAP"),
    ;

    private String state;
    private String desc;

    InStatusEnum(String state, String desc) {
        this.state = state;
        this.desc = desc;
    }
}
