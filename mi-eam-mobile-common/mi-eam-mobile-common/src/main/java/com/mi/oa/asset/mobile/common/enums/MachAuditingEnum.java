package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/17
 */
@Getter
public enum MachAuditingEnum {

    NOT_MACH("0", "未匹配"),
    HANDLE_MACH("1", "手工匹配"),
    HAVE_MACH("3", "已匹配"),
    RECEIVING_POSTING("4", "收货过账"),
    HAVE_COMPLETE("5", "已完成"),
    FAIL_MACH("6", "匹配失败"),
    ;

    private String state;
    private String desc;

    MachAuditingEnum(String state, String desc) {
        this.state = state;
        this.desc = desc;
    }
}
