package com.mi.oa.asset.mobile.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/10/31 11:28
 */

@Getter
public enum PurchaseOrderTypeEnum {
    TRIAL("1", "试用内购"),
    ALL_STAFF("2", "全员内购"),
    SELF("3", "自留内购");

    private String type;
    private String desc;

    PurchaseOrderTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static PurchaseOrderTypeEnum getPurchaseOrderTypeEnum(String type) {
        for (PurchaseOrderTypeEnum orderTypeEnum : PurchaseOrderTypeEnum.values()) {
            if (orderTypeEnum.getDesc().equalsIgnoreCase(type)) {
                return orderTypeEnum;
            }
        }
        return null;
    }
}
