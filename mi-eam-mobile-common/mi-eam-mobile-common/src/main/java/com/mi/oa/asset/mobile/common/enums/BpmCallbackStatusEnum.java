package com.mi.oa.asset.mobile.common.enums;

import com.google.gson.annotations.SerializedName;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/10/6 15:25
 */

public enum BpmCallbackStatusEnum {

    @SerializedName("submit")
    SUBMIT("submit"),

    @SerializedName("agree")
    AGREE("agree"),

    @SerializedName("reject")
    REJECT("reject"),

    @SerializedName("sign")
    SIGN("sign"),

    @SerializedName("end")
    END("end"),
    ;

    private String code;

    BpmCallbackStatusEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
