package com.mi.oa.asset.mobile.application.dto.bpm;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/31 20:01
 */

@Data
@Builder
public class ProtoBackBizDetDTO {
    /**
     * 编号：code
     */
    @SerializedName("input_1666876318947")
    private String code;

    /**
     * 小米料号：sku_code
     */
    @SerializedName("input_1666876338059")
    private String skuCode;

    /**
     * 描述：sku_name
     */
    @SerializedName("textarea_1724039121018")
    private String skuName;

    /**
     * 设备编号SN device_code
     */
    @SerializedName("input_1666876362163")
    private String deviceCode;

    /**
     * 项目编号 project_code
     */
    @SerializedName("input_1666876371467")
    private String projectCode;

    /**
     * 试产阶段 stage_name
     */
    @SerializedName("input_1666876392099")
    private String stageName;

    /**
     * IMEI号 imei
     */
    @SerializedName("input_1666876411427")
    private String imei;

    /**
     * 镭雕号 laser_code
     */
    @SerializedName("input_1666876422283")
    private String laserCode;

    /**
     * 新旧属性(类型） asset_newold
     */
    @SerializedName("input_1666876439019")
    private String assetNewold;

    /**
     * 备注 remark
     */
    @SerializedName("textarea_1666949412884")
    private String remark;
}
