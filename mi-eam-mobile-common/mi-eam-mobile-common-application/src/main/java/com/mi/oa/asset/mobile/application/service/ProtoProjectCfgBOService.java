package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.application.dto.ProtoDisposeDTO;
import com.mi.oa.asset.mobile.application.dto.ProtoPurchaseDTO;
import com.mi.oa.asset.mobile.common.enums.PriceTypeEnum;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;

import java.util.List;

/**
 * 项目配置类
 *
 * <AUTHOR>
 * @date 2022/1/20 17:31
 */
public interface ProtoProjectCfgBOService {

    /**
     * 获取处置依据、参考金额
     * @param deviceCodeList
     * @return
     */
    List<ProtoDisposeDTO> getDisposeDamages(List<String> deviceCodeList);

    /**
     * 内购信息
     * @param purchaseList
     * @return
     */
    List<ProtoPurchaseDTO> getPurchaseDesc(List<ProtoPurchaseDTO> purchaseList);
}
