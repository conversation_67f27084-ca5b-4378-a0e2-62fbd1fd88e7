package com.mi.oa.asset.mobile.application.dto.bpm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2022/3/1 15:31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MobileBaselineBizDTO extends ProcessBaseDTO{
    /**
     * 申请单号 apply_code
     */
    private String applyCode;
    /**
     * 基线编码
     */
    private String baselineCode;

    /**
     * 申请人  示例：杨媛媛（yangyuanyuan3）
     */
    private String applyUser;

    /**
     * 申请人部门名称 dept_name 信息技术部-企业效率产品部-固资产品组
     */
    private String deptName;

    /**
     * 变更类型 change_type  示例：新增
     */
    private String changeType;

    /**
     * 基线备注 remark
     */
    private String remark;

    private List<Item> items;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Item {
        private String prop;
        private String updBefore;
        private String updAfter;
        private String updType;
    }
}
