package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.dto.bpm.*;
import com.mi.oa.asset.mobile.infra.remote.entity.BpmDTO;

import java.util.Map;

/**
 * <AUTHOR>
 * @description: bpm 相关处理类
 * @date 2021/12/7 14:40
 */
public interface BpmService {
    /**
     * 手机工程机结束流程
     *
     * @param interruptProcessDTO 流程参数
     */
    void interruptProcess(InterruptProcessDTO interruptProcessDTO);

    void interruptProcessV2(String processKey, String keyId, String startUser);

    /**
     * 工程机发起流程
     *
     * @param submitProcessDTO 流程参数
     * @return bpm 返回 businessKey
     */
    SubmitResultDTO submitProcess(SubmitProcessDTO submitProcessDTO);

    String bpmSubmitV2(String processKey, String startUser, String title, ProcessBaseDTO business, Map<String, Object> variable);

    void bpmCallback(BpmCallbackDTO param);

    BpmV3Service v3();
}
