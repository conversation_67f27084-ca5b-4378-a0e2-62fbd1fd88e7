package com.mi.oa.asset.mobile.application.dto.order;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/25
 */
@Data
public class PlanProgressDTO {

    /**
     * 待销售数量
     */
    private Integer waitSellNum;

    /**
     * 待销售金额
     */
    private BigDecimal waitSellAmount;

    /**
     * 待支付数量
     */
    private Integer waitPayNum;

    /**
     * 待支付金额
     */
    private BigDecimal waitPayAmount;

    /**
     * 已完成数量
     */
    private Integer completedNum;

    /**
     * 已完成金额
     */
    private BigDecimal completedAmount;

    /**
     * 已下架数量
     */
    private Integer soldOutNum;

    /**
     * 已下架金额
     */
    private BigDecimal soldOutAmount;

    /**
     * 已退款数量
     */
    private Integer refundNum;

    /**
     * 已退款金额
     */
    private BigDecimal refundAmount;
}
