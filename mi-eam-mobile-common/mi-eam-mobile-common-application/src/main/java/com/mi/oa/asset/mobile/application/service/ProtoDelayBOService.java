package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmCallbackDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.MobileProcessBaseBizDTO;
import com.mi.oa.asset.mobile.application.dto.delay.DelayDTO;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;

/**
 * 工程机延期归还
 *
 * <AUTHOR>
 * @date 2022/2/17 19:27
 */
public interface ProtoDelayBOService {

    String save(DelayDTO delayDTO, RequestContext request);

    String afterSave(DelayDTO delayDTO);

    String audit(String[] keyIds, RequestContext request);

    String pushBpm(String[] keyIds);

    String interruptBpm(String[] keyIds, UserInfo userInfo);

    boolean bpmCallback(BpmCallbackDTO<MobileProcessBaseBizDTO> bpmCallbackDTO);

    String delayBackScanQry(String scanResult);
}
