package com.mi.oa.asset.mobile.application.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/3/30
 */
public interface ProtoCardBOService {

    /**
     * 刷新1、apply_date 首次发放日期  2、入库时间
     */
    void refreshOldData();

    /**
     * 刷新全量部门信息
     */
    void refreshAllDeptName();

    /**
     * 刷新最近入库时间
     */
    void refreshEntryDate();

    /**
     * 获取入库时间优先级
     * 1、EAM取归还时间
     * 2、收货时间
     * 3、老系统的最新时间
     * @param snList
     * @return
     */
    Map<String, Date> getEntryDate(List<String> snList);

    /**
     * 刷所有初始部门编码
     */
    void refreshAllApplyDeptCode();
}
