package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.application.dto.message.EmailDTO;
import com.mi.oa.asset.mobile.application.dto.message.LarkDTO;
import com.mi.oa.asset.mobile.application.dto.message.TemplateMsgBaseDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @description: bpm 相关处理类
 * @date 2021/12/7 14:40
 */
public interface MessageService {

    /**
     * 发送email
     *
     * @param emailDTO
     */
    void sendEmail(EmailDTO emailDTO);

    /**
     * 发送飞书
     *
     * @param larkDTO
     */
    void sendLark(LarkDTO larkDTO);

    /**
     * @param templateTag 模板tag
     * @param userCodes   用户code
     * @param mat         额外参数
     * @param msgBaseDTO  参数dto
     */
    void sendLark(String templateTag, List<String> userCodes, String mat, TemplateMsgBaseDTO msgBaseDTO);

    /**
     * 发送邮件
     *
     * @param templateTag 模板tag
     * @param emails      用户Emails
     * @param mat         额外参数
     * @param msgBaseDTO  参数dto
     */
    void sendEmail(String templateTag, List<String> emails, String mat, TemplateMsgBaseDTO msgBaseDTO);

    /**
     * 发送短信
     *
     * @param tels      手机号
     * @param content  内容
     */
    void sendSMS(List<String> tels, String content);
}