package com.mi.oa.asset.mobile.application.dto.bpm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工程机延期归还物料明细清单
 *
 * <AUTHOR>
 * @date 2022/1/19 15:39
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProtoDelayItemBizDTO extends ProcessBaseDTO {
    //编号
    private String number;
    //小米料号
    private String skuCode;
    //描述
    private String skuName;
    //SN
    private String deviceCode;
    //IMEI号
    private String imei;
    //镭雕号
    private String laserCode;
    //项目
    private String projectCode;
    //试产阶段
    private String stageName;
    //新旧类型
    private String assetNewold;
    //当前归还时间
    private String endDate;
    //延期归还时间
    private String delayDate;

}
