package com.mi.oa.asset.mobile.application.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/7/27
 */
@Data
public class ProtoPurchaseDTO {

    /**
     * 业务类型
     */
    private String mrpType;

    /**
     * 物料描述
     */
    private String skuCode;

    /**
     * 定价配置码
     */
    private String priceCode;

    /**
     * 定价描述
     */
    private String priceDesc;

    /**
     * 上市价格  传过来的
     */
    private BigDecimal listMoney;

    /**
     * 市场价格
     */
    private BigDecimal marketPrice;

    /**
     * 参考赔偿金额
     */
    private BigDecimal referenceDamages;


    /**
     * 上市时间
     */
    private Date listDate;

    /**
     * 上市描述
     */
    private String listDesc;

    /**
     * 处置依据
     */
    private String disposalBasis;
}
