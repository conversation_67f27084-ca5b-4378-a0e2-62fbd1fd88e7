package com.mi.oa.asset.mobile.application.dto.bpm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: 手机工程机清单内容
 * @date 2021/11/3 19:28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MobileItemBizDTO extends ProcessBaseDTO {
    //编号（Serial number）
    private String sn;
    //小米料号（Xiaomi material number）
    private String miMaterialCode;
    //描述（Describe）
    private String desc;
    //领用单号（Requisition number）
    private String reqNumber;
    //SN（SN number）
    private String number;
    //IMEI号
    private String imei;
    //镭雕号(Radium Carving Number
    private String radiumCarvingNumber;
    //project
    private String project;
    //试产阶段（Trial production stage）
    private String stage;
    //类型
    private String type;
    //备注
    private String remark;
}
