package com.mi.oa.asset.mobile.application.dto.order;

import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * Copyright (c) 2022 XiaoMi Inc.All Rights Reserved.
 * Description: your description
 *
 * <AUTHOR>
 * @date 2022/8/1 16:47
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PreOrderDTO extends DTO {
    private static final long serialVersionUID = 5646648597538029685L;
    private Integer planId;
    private Long skuId;
    private String orderId;
    private String deviceCode;
    private Integer num;
    private String userCode;
    private String mrpType;
    private String deliveryMethod;
    private String skuCode;
    private String skuName;
    private String houseCode;
    private String houseName;
    private String priceCode;
    private String newPercent;
    private BigDecimal currPrice;

    /**
     * 自提地址
     */
    private String address;

    /**
     * 验货信息
     */
    private String checkInfo;

    /**
     * 预订单类型，1-创建，0-取消
     */
    private Integer preOrderType;

    /**
     * 审批简要描述
     */
    private String brief;
}
