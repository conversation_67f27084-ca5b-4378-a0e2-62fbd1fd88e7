package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.application.dto.bpm.ProtoBackBizDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.ProtoBaselineRecordBizDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.ProtoSleeveBizDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.ProtoTranBizDTO;

import java.util.Map;

/**
 * BPM 3.0 相关流程
 * <AUTHOR>
 * @date 2022/10/31 19:59
 */
public interface BpmV3Service {

    /**
     * 终止流程
     * @param businessKey
     * @param operator
     */
    void terminate(String businessKey, String operator);

    /**
     * 工程机转移申请流程
     *
     * @param business
     * @param startUser
     * @return
     */
    String submitProtoTranApply(ProtoTranBizDTO business, String startUser, Map<String, Object> variables);

    /**
     * 工程机归还申请流程
     *
     * @param business
     * @param startUser
     * @return
     */
    String submitProtoBackApply(ProtoBackBizDTO business, String startUser, Map<String, Object> variables);

    /**
     * 工程机保密套拆除流程
     *
     * @param business
     * @param startUser
     * @return
     */
    String submitProtoSleeveApply(ProtoSleeveBizDTO business, String startUser, Map<String, Object> variables);

    /**
     * 工程机公共基线（新增/删除/变更）申请流程
     *
     * @param business
     * @param startUser
     * @return
     */
    String submitBaselineApply(ProtoBaselineRecordBizDTO business, String startUser, Map<String, Object> variables);
}
