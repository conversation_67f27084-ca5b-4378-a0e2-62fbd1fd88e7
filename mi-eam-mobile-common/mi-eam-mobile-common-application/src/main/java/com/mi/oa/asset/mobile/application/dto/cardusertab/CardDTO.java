package com.mi.oa.asset.mobile.application.dto.cardusertab;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 工程机台账DTO
 *
 * <AUTHOR>
 * @date 2022/2/23 11:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CardDTO {
    private String proto_card__mrp_type;
    //private String mrpType;
    private String proto_card__device_id;
    //private String deviceId;
    private String proto_card__sku_code;
    //private String skuCode;
    private String proto_card__sku_name;
    //private String skuName;
    private String proto_card__device_code;
    //private String deviceCode;
    private String proto_card__laser_code;
    //private String laserCode;
    private String proto_card__imei;
    //private String imei;
    private String proto_card__device_type;
    //private String deviceType;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date proto_card__apply_date;
    //private String applyDate;
    private String proto_card__apply_code;
    //private String applyCode;
    private String proto_card__apply_itemid;
    //private String applyItemid;
    private String proto_card__apply_user_name;
    //private String applyUserName;
    private String proto_card__apply_user_code;
    //private String applyUserCode;
    private String proto_card__apply_emp_code;
    //private String  applyEmpCode;
    private String proto_card__project_name;
    //private String projectName;
    private String proto_card__project_code;
    //private String projectCode;
    private String proto_card__stage_name;
    //private String stageName;
    private String proto_card__make_cn;
    //private String makeCn;
    private String proto_card__use_state;
    //private String useState;
    private String proto_card__user_name;
    //private String userName;
    private String proto_card__user_code;
    //private String userCode;
    private String proto_card__emp_code;
    //private String empCode;
    private String proto_card__is_open;
    //private String isOpen;
    private String proto_card__open_user_name;
    //private String openUserName;
    private String proto_card__open_user_code;
    //private String openUserCode;
    private String proto_card__open_emp_code;
    //private String openEmpCode;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date proto_card__open_date;
    //private String openDate;
    private String proto_card__asset_newold;
    //private String assetNewold;
    private String proto_card__house_name;
    //private String houseName;
    private String proto_card__house_code;
    //private String houseCode;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date proto_card__last_apply_date;
    //private String lastApplyDate;
    private String proto_card__last_apply_code;
    //private String lastApplyCode;
    private String proto_card__last_apply_itemid;
    //private String lastApplyItemid;
    private String proto_card__last_apply_user_name;
    //private String lastApplyUserName;
    private String proto_card__last_apply_user_code;
    //private String lastApplyUserCode;
    private String proto_card__last_apply_emp_code;
    //private String lastApplyEmpCode;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date proto_card__out_date;
    //private String outDate;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date proto_card__last_date;
    //private String lastDate;
    private String proto_card__center_code;
    //private String centerCode;
    private String proto_card__center_name;
    //private String centerName;
    private String proto_card__comp_dept_code;
    //private String compDeptCode;
    private String proto_card__comp_dept_name;
    //private String compDeptName;
    private String proto_card__sap_factory;
    //private String sapFactory;
    private String proto_card__apply_user_type;
    //private String applyUserType;
    private String proto_card__occupy_apply_code;
    //private String occupyApplyCode;
    private String proto_card__is_complete;
    //private String isComplete;
    private String proto_card__in_code;
    //private String inCode;
    private String proto_card__out_code;
    //private String outCode;
    private String proto_card__order_code;
    //private String orderCode;
    private String proto_card__ori_project_code;
    //private String oriProjectCode;
    private String proto_card__ori_stage_name;
    //private String oriStageName;
    private String proto_card__syc_flag;
    //private String sycFlag;
    private String proto_card__new_project_code;
    //private String newProjectCode;
    private String proto_card__new_stage_name;
    //private String newStageName;
    private String proto_card__house_id;
    //private String houseId;
    private String proto_card__old_use_code;
    //private String oldUseCode;
    private String proto_card__old_use_name;
    //private String oldUseName;
    private String proto_card__remark;
    private String proto_card__dept_code;
    private String proto_card__long_dept_name;
    //private String remark;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date proto_card__modify_date;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date proto_card__end_date;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date proto_card__list_date;
    private String proto_card__sku_code_type;
}
