package com.mi.oa.asset.mobile.application.dto.bpm;

import com.mi.oa.asset.mobile.common.enums.MobileProcessEnum;
import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @description: TODO
 * @date 2021/12/8 10:52
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SubmitProcessDTO extends DTO {
    //发起人
    private String startUser;
    //流程枚举
    private MobileProcessEnum mobileProcessEnum;
    //基础数据字段
    private MobileProcessBaseBizDTO mobileProcessBaseBizDTO;
}
