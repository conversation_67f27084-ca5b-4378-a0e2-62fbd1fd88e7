package com.mi.oa.asset.mobile.application.dto.bpm;

import com.google.gson.annotations.SerializedName;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/10 11:13
 */
@Data
@Builder
public class ProtoSleeveBizDTO {
    /**
     * 申请单号 apply_code
     */
    @SerializedName("input_6b0159eb00af")
    private String applyCode;

    /**
     * 申请人 apply_user_name
     */
    @SerializedName("input_959723a13211")
    private String applyUserName;

    /**
     * 申请部门 dept_name
     */
    @SerializedName("input_bbb0188f4184")
    private String deptName;

    /**
     * 工程机类型 mrp_type
     */
    @SerializedName("input_04763b6f666b")
    private String mrpType;

    /**
     * 领用方类型 apply_user_type
     */
    @SerializedName("input_cea5a9d9fa9c")
    private String applyUserType;

    /**
     * 小米确认人 check_user_code
     */
    @SerializedName("user_f4a395cc9917")
    @Builder.Default
    private String checkUserCode = "";

    /**
     * PM pm_user_code
     */
    @SerializedName("user_d5bc3f2c141e")
    @Builder.Default
    private String pmUserCode = "";

    /**
     * 第一申领人 first_apply_user_code
     */
    @SerializedName("user_91d83ac8945a")
    @Builder.Default
    private String firstApplyUserCode = "";

    /**
     * 备注 remark
     */
    @SerializedName("textarea_66d224fbf54a")
    private String remark;

    /**
     * 拆除清单
     */
    @SerializedName("formTable_3bda19f7c077")
    private List<ProtoSleeveBizDetDTO> protoSleeveBizDetDTOList;

    public String getJson() {
        return GsonUtil.toJsonString(this);
    }
}
