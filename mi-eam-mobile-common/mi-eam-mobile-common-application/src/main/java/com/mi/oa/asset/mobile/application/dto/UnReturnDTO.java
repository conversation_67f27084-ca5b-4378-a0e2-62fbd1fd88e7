package com.mi.oa.asset.mobile.application.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/7/1
 */
@Data
@Builder
public class UnReturnDTO {

    /**
     * 主键ID
     */
    private String unreturnId;

    /**
     * 业务类型  手机/电视/可穿戴/生态链/笔记本电脑
     */
    private String mrpType;

    /**
     * 处置类型描述
     */
    private String disposalTypeDesc;

    /**
     * 单号
     */
    private String unreturnCode;

    /**
     * 应支付金额
     */
    private BigDecimal sumActualDamages;

    /**
     * 状态 0:待上传凭证 1:已完成
     */
    private String taskStatus;

    /**
     * 状态描述 0:待上传凭证 1:已完成
     */
    private String taskStatusDesc;

    /**
     * 支付信息
     */
    private PayInfo payInfo;

    @Data
    @Builder
    public static class PayInfo{

        /**
         * 开户名
         */
        private String companyName;

        /**
         * 银行账户
         */
        private String accountBank;

        /**
         * 开户
         */
        private String openBank;

        /**
         * 参考备注
         */
        private String referenceRemark;
    }
}
