package com.mi.oa.asset.mobile.application.dto.bpm;

import com.mi.oa.asset.mobile.common.enums.MobileProcessEnum;
import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 流程中断处理参数
 * @date 2021/12/7 14:35
 */
@Data
@Builder
public class InterruptProcessDTO extends DTO {
    private String businessKey;
    private String startUser;
    private MobileProcessEnum mobileProcessEnum;
}
