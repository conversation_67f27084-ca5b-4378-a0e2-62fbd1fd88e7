package com.mi.oa.asset.mobile.application.dto.tran;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TranSapSplitDTO {
    private String projectCode;
    private String centerCode;
    private String makeCn;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TranSapSplitDTO that = (TranSapSplitDTO) o;
        return Objects.equals(projectCode, that.projectCode) && Objects.equals(centerCode, that.centerCode) && Objects.equals(makeCn, that.makeCn);
    }

    @Override
    public int hashCode() {
        return Objects.hash(projectCode, centerCode, makeCn);
    }
}
