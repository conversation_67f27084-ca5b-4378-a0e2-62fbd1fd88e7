package com.mi.oa.asset.mobile.application.dto.order;

import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.*;

import java.util.List;

/**
 * 售后申请
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AfterSaleApplyResp extends DTO {

    private static final long serialVersionUID = -5056490746768192776L;
    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 售后类型：退货退款-1，换货-2
     */
    private Integer afterSaleType;

    /**
     * 处理状态：未申请-0，已提交-1，已确认-2
     */
    private Integer processStatus;

    /**
     * 售后原因
     */
    private String reason;

    /**
     * 附件列表JSON格式
     */
    private List<Attach> attaches;
}