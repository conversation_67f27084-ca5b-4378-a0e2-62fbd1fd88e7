package com.mi.oa.asset.mobile.application.dto.cardusertab;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/11/22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CardDeptDTO {

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 业务类型
     */
    private String mrpType;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CardDeptDTO that = (CardDeptDTO) o;
        return Objects.equals(deptCode, that.deptCode) && Objects.equals(mrpType, that.mrpType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(deptCode, mrpType);
    }
}
