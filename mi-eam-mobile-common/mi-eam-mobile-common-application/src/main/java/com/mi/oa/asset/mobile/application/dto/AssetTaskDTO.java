package com.mi.oa.asset.mobile.application.dto;

import lombok.*;

import javax.validation.constraints.NotNull;
import java.time.ZonedDateTime;

/**
 * 资产任务DTO
 *
 * <AUTHOR>
 * @date 2021/09/02/11:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class AssetTaskDTO {

    /**
     * 资产管理系统的task_id
     */
    private String taskId;

    /**
     * 执行任务员工用户名 executor_name
     */
    private String executorName;

    /**
     * 执行任务员工姓名 executor_display_name
     */
    private String executorDisplayName;

    /**
     * 执行任务员工邮箱 executor_email
     */
    private String executorEmail;

    /**
     * 执行任务的部门名 executor_dept_name
     */
    private String executorDeptName;

    /**
     * 下发任务的管理员 operator_name
     */
    private String operatorName;

    /**
     * 下发任务的部门名 operator_dept_name
     */
    private String operatorDeptName;

    /**
     * 1-盘点任务 2-领取任务 task_type
     */
    private Byte taskType;

    /**
     * 0-待完成 1-已完成 task_status
     */
    private Byte taskStatus;

    /**
     * 任务主题 task_theme
     */
    private String taskTheme;

    /**
     * 任务描述 remark
     */
    private String remark;

    /**
     * 任务开始时间 start_time
     *
     */
    @NotNull
    private ZonedDateTime startTime;

    /**
     * 任务结束时间 end_time
     */
    private ZonedDateTime endTime;

    /**
     * 消息通知国别
     */
    private String country;
}