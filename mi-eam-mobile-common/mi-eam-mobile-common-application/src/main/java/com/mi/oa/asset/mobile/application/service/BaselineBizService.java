package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.application.dto.bpm.BpmProcessCompletedDTO;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/3/1 16:10
 */
public interface BaselineBizService {
    /**
     * 提交
     *
     * @param keyId
     */
    void submit(String keyId);

    /**
     * 中断
     *
     * @param keyId     主键id
     * @param startUser 申请人
     */
    void interruptBpm(String keyId, String startUser);

    /**
     * bpmV3回调方法
     *
     * @param param
     * @return
     */
    boolean bpmCallbackV3(BpmProcessCompletedDTO param);

}
