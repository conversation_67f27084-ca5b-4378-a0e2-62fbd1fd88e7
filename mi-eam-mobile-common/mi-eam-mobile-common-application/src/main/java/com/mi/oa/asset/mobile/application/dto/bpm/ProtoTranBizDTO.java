package com.mi.oa.asset.mobile.application.dto.bpm;

import com.google.gson.annotations.SerializedName;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/31 17:45
 */

@Data
@Builder
public class ProtoTranBizDTO {
    /**
     * 转移单号 tran_code
     */
    @SerializedName("input_0e49a5eee52a")
    private String tranCode;

    /**
     * 申请人 user_name
     */
    @SerializedName("input_959723a13211")
    private String userName;

    /**
     * 申请部门 dept_name
     */
    @SerializedName("textarea_1d7d6a0ce8a6")
    private String deptName;

    /**
     * 接收人账号 acc_user_code
     */
    @SerializedName("user_0f58949f418a")
    @Builder.Default
    private String accUserCode = "";

    /**
     * 接收部门 in_dept_name
     */
    @SerializedName("textarea_0fbeb08da8ae")
    private String inDeptName;

    /**
     * 工程机类型 mrp_type
     */
    @SerializedName("input_fa7a3c7276be")
    private String mrpType;

    /**
     * 转移类型 tran_type
     */
    @SerializedName("input_af082c7d41a5")
    private String tranType;

    /**
     * 备注 remark
     */
    @SerializedName("textarea_2f7052ab39db")
    private String remark;


    /**
     * 转移清单
     */
    @SerializedName("formTable_7a6f8ace585f")
    private List<ProtoTranBizDetDTO> protoTranBizDetDTOList;

    public String getJson() {
        return GsonUtil.toJsonString(this);
    }
}
