package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.application.dto.bpm.BpmCallbackDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmCallbackFunctionEnum;
import com.mi.oa.asset.mobile.infra.remote.entity.BpmDTO;
import org.apache.commons.lang3.ObjectUtils;

/**
 *
 * <AUTHOR>
 * @date 2022/3/4 11:06
 */
public abstract class BpmCallbackService {
    /**
     * 基线回调
     * @param bpmCallbackDTO
     * @return
     */
    public void bpmCallback(BpmCallbackDTO bpmCallbackDTO){
        BpmCallbackFunctional callbackFunctional = BpmCallbackFunctionEnum.getBpmCallbackFunctionalByStatus(bpmCallbackDTO.getStatus());
        if(!ObjectUtils.isEmpty(callbackFunctional)){
            callbackFunctional.callback(this,bpmCallbackDTO);
        }
    }
    /**
     * 基线回调
     * @param bpmCallbackDTO
     * @return
     */
    public abstract void bpmEnd(BpmCallbackDTO bpmCallbackDTO);
    /**
     * 基线回调
     * @param bpmCallbackDTO
     * @return
     */
    public abstract void bpmReject(BpmCallbackDTO bpmCallbackDTO);

    /**
     *
     * @param keyId
     * @return 返回businessKey
     */
    public abstract String bpmInterrupt(String keyId);
}
