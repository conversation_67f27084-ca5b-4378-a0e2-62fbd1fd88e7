package com.mi.oa.asset.mobile.application.dto.bpm;

import com.mi.oa.asset.mobile.application.service.BpmCallbackFunctional;
import com.mi.oa.asset.mobile.application.service.BpmCallbackService;
import com.mi.oa.asset.mobile.common.enums.BpmCallbackStatusEnum;
import lombok.Getter;

import java.util.Arrays;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/3/4 11:10
 */
@Getter
public enum BpmCallbackFunctionEnum {
    /**
     *
     */
    END(BpmCallbackStatusEnum.END, BpmCallbackService::bpmEnd),
    /**
     *
     */
    REJECT(BpmCallbackStatusEnum.REJECT, BpmCallbackService::bpmReject),
    ;

    private BpmCallbackStatusEnum status;
    private BpmCallbackFunctional callbackFunctional;

    BpmCallbackFunctionEnum(BpmCallbackStatusEnum status, BpmCallbackFunctional callbackFunctional) {
        this.status = status;
        this.callbackFunctional = callbackFunctional;
    }

    public static BpmCallbackFunctional getBpmCallbackFunctionalByStatus(BpmCallbackStatusEnum status){
        for (BpmCallbackFunctionEnum item:values()){
            if(item.getStatus().equals(status)){
                return item.getCallbackFunctional();
            }
        }
        return null;
    }
}
