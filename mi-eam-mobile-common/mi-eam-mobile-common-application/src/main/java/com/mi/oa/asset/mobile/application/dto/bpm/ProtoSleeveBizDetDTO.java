package com.mi.oa.asset.mobile.application.dto.bpm;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/11/10 11:14
 */

@Data
@Builder
public class ProtoSleeveBizDetDTO {
    /**
     * 编号：code
     */
    @SerializedName("input_1667288984012")
    private String code;

    /**
     * 小米料号：sku_code
     */
    @SerializedName("input_1667289003044")
    private String skuCode;

    /**
     * 描述：sku_name
     */
    @SerializedName("textarea_1724039698395")
    private String skuName;

    /**
     * 设备编号SN device_code
     */
    @SerializedName("input_1667289027412")
    private String deviceCode;

    /**
     * 项目编号 project_code
     */
    @SerializedName("input_1667289037740")
    private String projectCode;

    /**
     * 试产阶段 stage_name
     */
    @SerializedName("input_1667289048124")
    private String stageName;

    /**
     * IMEI号 imei
     */
    @SerializedName("input_1667289061564")
    private String imei;

    /**
     * 镭雕号 laser_code
     */
    @SerializedName("input_1667289071580")
    private String laserCode;

    /**
     * 新旧属性(类型） asset_newold
     */
    @SerializedName("input_1667304618584")
    private String assetNewold;

    /**
     * 备注 remark
     */
    @SerializedName("textarea_1667289092331")
    private String remark;
}
