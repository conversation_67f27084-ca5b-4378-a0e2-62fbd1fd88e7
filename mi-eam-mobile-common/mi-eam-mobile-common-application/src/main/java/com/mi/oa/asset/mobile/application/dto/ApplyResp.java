package com.mi.oa.asset.mobile.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.google.gson.annotations.SerializedName;
import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/5 15:52
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplyResp extends DTO {
    private String success;
    @JsonProperty("target_id")
    private String targetId;
}
