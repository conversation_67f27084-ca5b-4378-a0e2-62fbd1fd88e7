package com.mi.oa.asset.mobile.application.dto.bpm;

import com.mi.oa.infra.oaucf.bpm.enums.ProcessInstanceStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/10/31 20:43
 */

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class BpmProcessCompletedDTO {
    private String taskId;
    private String taskName;
    private String delegationUser;
    private String comment;
    private String taskDefinitionKey;
    private Map<String, Object> operator;
    private Map<String, Object> assignee;
    private String processInstanceId;
    private String processDefinitionId;
    private String starTaskName;
    private String startTaskId;
    private String startTaskDefinitionKey;
    private Map<String, Object> variables;
    private String initiator;
    private String initiatorUser;
    private String currentDate;
    private String processDefinitionName;
    private String title;
    private String modelCode;
    private String businessKey;
    private String processDefinitionVersion;
    private ProcessInstanceStatus processInstanceStatus;
    private String status;
    private Map<String, Object> formData;
}
