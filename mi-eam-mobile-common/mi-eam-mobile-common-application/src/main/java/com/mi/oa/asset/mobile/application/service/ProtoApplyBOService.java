package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyPO;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2021/12/31 11:29
 */
public interface ProtoApplyBOService {

    /**
     * 领用申请单抛送SAP
     * @param applyPO
     */
    void pushSap(ProtoApplyPO applyPO);

    /**
     * 设置样机生产地
     * @param applyPO
     */
    void searchAndSetMakeCn(ProtoApplyPO applyPO);
}
