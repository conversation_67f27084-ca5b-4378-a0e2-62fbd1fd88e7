package com.mi.oa.asset.mobile.application.dto.delay;

import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工程机延期归还
 *
 * <AUTHOR>
 * @date 2022/2/18 14:38
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DelayDTO {
    private String keyId;
    private String projectCode;
    private String delayCode;
    private String applyUserCode;
    private UserInfo userInfo;
}
