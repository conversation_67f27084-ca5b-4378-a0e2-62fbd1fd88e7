package com.mi.oa.asset.mobile.application.dto.bpm;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Description: 工程机工共基线bpmV3DTO类
 *
 * <AUTHOR>
 * @date 2023/7/24
 */
@Data
@Builder
public class ProtoBaselineRecordBizDTO {

    /**
     * 申请单号 apply_code
     */
    @SerializedName("input_67cc07251357")
    private String applyCode;

    /**
     * 基线编号
     */
    @SerializedName("input_51459bf4aef6")
    private String baselineCode;
    /**
     * 申请人  示例：杨媛媛（yangyuanyuan3）
     */
    @SerializedName("input_959723a13211")
    private String applyUser;

    /**
     * 申请人部门名称 dept_name 信息技术部-企业效率产品部-固资产品组
     */
    @SerializedName("input_aa91faa1c772")
    private String deptName;

    /**
     * 变更类型 change_type  示例：新增
     */
    @SerializedName("input_07216d69c5e9")
    private String changeType;

    /**
     * 基线备注 remark
     */
    @SerializedName("input_9676cb0cfadd")
    private String remark;

    @SerializedName("formTable_3e34fe46ee78")
    private List<ProtoBaselineRecordBizDTO.Item> items;


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Item {
        @SerializedName("input_1690187943873")
        private String prop;
        @SerializedName("input_1690188031889")
        private String updBefore;
        @SerializedName("input_1690188109769")
        private String updAfter;
        @SerializedName("input_1690188154786")
        private String updType;
    }
}
