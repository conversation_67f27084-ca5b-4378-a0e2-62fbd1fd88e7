package com.mi.oa.asset.mobile.application.dto.statusnotify;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工程机单据明细信息
 *
 * <AUTHOR>
 * @date 2022/4/13 15:53
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProtoListDetDTO {
    //小米料号
    private String skuCode;
    //描述
    private String skuName;
    //项目编号
    private String projectCode;
    //阶段
    private String stageName;
    //配置
    private String deviceType;
    //镭雕
    private String laserCode;
    //SN
    private String deviceCode;
    //IMEI
    private String imei;
    //归还状态：不推不允许归还的数据
    private String isComplete;
}
