package com.mi.oa.asset.mobile.application.dto.order;

import com.mi.oa.infra.oaucf.core.dto.Req;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
* 订单表
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MallOrderReq extends Req {

    /**
     * 业务线
     */
    private String mrpType;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
    * 业务类型 0:工程机内购
    */
    private Integer bizType;

    /**
     * 计划订单类型
     */
    private String planOrderType;

    /**
    * 订单类型
    */
    private Integer orderType;

    /**
    * 订单id
    */
    private String orderId;

    /**
     * 订单描述
     */
    private String orderDesc;

    /**
    * 订单状态, 1-待通知，2-已通知待支付，3-已支付，4-取消
    */
    private Integer orderState;

    /**
    * 订单原价（总价）
    */
    private BigDecimal totalAmount;

    /**
    * 支付方式, 1-线上，2-线下
    */
    private Integer payWay;

    /**
    * 支付链接
    */
    private String jumpPayLink;

    /**
    * 所属用户
    */
    private String owner;

    /**
     * 订单详情
     */
    private List<OrderDetailReq> orderDetailList;
}