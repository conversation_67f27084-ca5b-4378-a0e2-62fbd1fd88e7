package com.mi.oa.asset.mobile.application.dto.bpm;

import com.mi.oa.asset.mobile.application.service.BaselineBizService;
import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/3/4 14:30
 */
@Getter
public enum BpmProcessEnum {
    /**
     * 工程机公共基线（新增/删除/变更）申请
     */
    EAM_MOBILE_PUBBASE("eam_mobile_pubBase","工程机公共基线（%s）申请"
            ,MobileBaselineBizDTO.class, BaselineBizService.class),
    ;

    private String processKey;
    private String title;
    private Class bizClazz;
    private Class callbackClazz;

    BpmProcessEnum(String processKey, String title, Class bizClazz, Class callbackClazz) {
        this.processKey = processKey;
        this.title = title;
        this.bizClazz = bizClazz;
        this.callbackClazz = callbackClazz;
    }

    public static BpmProcessEnum getBpmProcessEnum(String processKey){
        for (BpmProcessEnum item:values()){
            if(item.getProcessKey().equals(processKey)){
                return item;
            }
        }
        return null;
    }

}
