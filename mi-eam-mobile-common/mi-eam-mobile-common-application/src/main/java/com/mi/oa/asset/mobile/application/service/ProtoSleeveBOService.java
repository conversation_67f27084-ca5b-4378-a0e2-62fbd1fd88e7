package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmCallbackDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.MobileProcessBaseBizDTO;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;

/**
 * 工程机保密套拆除
 *
 * <AUTHOR>
 * @date 2022/1/28 14:55
 */
public interface ProtoSleeveBOService {

    /**
     * 提交前重新分析领用方类型，并判断确认人是否需要填写
     *
     * @param keyIds
     * @return
     */
    String audit(String[] keyIds, UserInfo userInfo, RequestContext request);

    /**
     * 推送BPM
     *
     * @return
     */
    String pushBpm(String[] keyIds);

    /**
     * 撤回BPM流程具体逻辑
     * @param keyIds
     * @param userInfo
     * @return
     */
    String interruptBpm(String[] keyIds, UserInfo userInfo);

    /**
     * bpm回调
     * @param bpmCallbackDTO
     * @return
     */
    boolean bpmCallback(BpmCallbackDTO<MobileProcessBaseBizDTO> bpmCallbackDTO);

    void sleeveDetScanConfirm(String sleeveId,String keyword,UserInfo userInfo);

}
