package com.mi.oa.asset.mobile.application.dto.tran;

import com.google.gson.annotations.SerializedName;
import com.mi.oa.asset.mobile.application.dto.message.TemplateMsgBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * {user_name}({user_code})，您好：<br/>{acc_user_name}({acc_user_code})在{today}
 * 同意了您设备转移({tran_code})的请求转移设备信息，见如下表格：<br/>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TranSuccessEmailDTO extends TemplateMsgBaseDTO {
    @SerializedName("acc_user_name")
    private String accUserName;
    @SerializedName("acc_user_code")
    private String accUserCode;
    @SerializedName("user_name")
    private String userName;
    @SerializedName("user_code")
    private String userCode;
    @SerializedName("today")
    private String today;
    @SerializedName("tran_code")
    private String tranCode;
}
