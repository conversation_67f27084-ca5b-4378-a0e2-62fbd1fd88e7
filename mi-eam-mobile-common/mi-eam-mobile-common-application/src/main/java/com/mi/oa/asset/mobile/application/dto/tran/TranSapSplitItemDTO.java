package com.mi.oa.asset.mobile.application.dto.tran;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TranSapSplitItemDTO {
    private String skuCode;
    private String applyItemid;
    private String applyCode;
    private String outCode;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TranSapSplitItemDTO that = (TranSapSplitItemDTO) o;
        return Objects.equals(skuCode, that.skuCode) && Objects.equals(applyItemid, that.applyItemid) && Objects.equals(applyCode, that.applyCode) && Objects.equals(outCode, that.outCode);
    }

    @Override
    public int hashCode() {
        return Objects.hash(skuCode, applyItemid, applyCode, outCode);
    }
}
