package com.mi.oa.asset.mobile.application.dto.bpm;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 工程机转移申请字段
 * @date 2021/11/3 10:36
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MobileScrapProcessBizDTO extends ProcessBaseDTO{
    //预计报废日期
    private String scrapDate;
    //报废数量
    private Integer scrapNum;
    //报废人
    private String scrapUser;
    //报废人电话
    private String scrapUserPhone;
    //报废人公司
    private String scrapCompany;
    //报废人地址
    private String scrapAddress;
    //报废照片
    private List<String> scrapPhoto;
    //下载明细
    private List<String> detail;
    //备注
    private String remark;
    //申请清单
    @Singular
    private List<MobileScrapDetailDTO> items;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MobileScrapDetailDTO{
        //编号
        private String sn;
        //项目
        private String project;
        //数量
        private Integer number;
        //单机重量（g）
        private String weight;
        //总重量（kg）
        private String allWeight;
    }

}
