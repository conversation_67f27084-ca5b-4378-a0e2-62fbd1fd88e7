package com.mi.oa.asset.mobile.application.dto.order;

import com.mi.oa.infra.oaucf.core.dto.Req;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
* 订单表
*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderPostSaveDTO extends Req {

    private static final long serialVersionUID = -426446802593411452L;
    @NotBlank(message = "订单ID不能为空")
    private String orderId;

    @NotNull(message = "收货人不能为空")
    private String consignee;

    @NotNull(message = "收货人手机号不能为空")
    private String consigneeMobile;

    @NotNull(message = "收货人地址不能为空")
    private String consigneeAddress;
}