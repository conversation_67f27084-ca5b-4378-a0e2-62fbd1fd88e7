package com.mi.oa.asset.mobile.application.dto.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Copyright (c) 2022 XiaoMi Inc.All Rights Reserved.
 * Description: your description
 *
 * <AUTHOR>
 * @date 2022/8/25 18:43
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayBillResp {

    /**
     * 交易号
     */
    private String tradeId;

    /**
     * 手续费
     */
    private BigDecimal fee;

    /**
     * 渠道商户号
     */
    private String channelMerchantNo;

}
