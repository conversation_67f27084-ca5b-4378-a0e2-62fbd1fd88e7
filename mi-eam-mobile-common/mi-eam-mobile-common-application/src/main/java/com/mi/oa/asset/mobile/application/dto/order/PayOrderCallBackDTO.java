package com.mi.oa.asset.mobile.application.dto.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * Copyright (c) 2022 XiaoMi Inc.All Rights Reserved.
 * Description: your description
 *
 * <AUTHOR>
 * @date 2022/8/1 16:47
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PayOrderCallBackDTO implements Serializable {
    /**
     * orderId
     */
    private String outOrderId;
    private Date payTime;
    private String tradeId;
    private String tradeNo;
    /**
     * TradeStateEnum
     */
    private String tradeStatus;
    /**
     * 支付类型 1:小米钱包 24:支付宝
     */
    private String payType;

    /**
     * 订单类型 2-全员，0-一般
     */
    private String orderType;

    /**
     * 创建预订单信息
     */
    private PreOrderDTO preOrderDTO;

    /**
     * 预订单类型，1-创建，0-取消
     */
    private Integer preOrderType;
}
