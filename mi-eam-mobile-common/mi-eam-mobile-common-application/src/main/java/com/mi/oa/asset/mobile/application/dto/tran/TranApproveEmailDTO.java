package com.mi.oa.asset.mobile.application.dto.tran;

import com.google.gson.annotations.SerializedName;
import com.mi.oa.asset.mobile.application.dto.message.TemplateMsgBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 {acc_user_name}({acc_user_code})，您好：<br>
 {user_name}({user_code})在{today}发起了设备转移，
 转移申请{tran_code}已审批完成，
 请您及时点击<a href="{eam_url}{acc_user_code}{suffix_param}">&lt;确认&gt;</a>这里，
 操作确认转移设备信息，见如下表格：<br>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TranApproveEmailDTO extends TemplateMsgBaseDTO {
    @SerializedName("acc_user_name")
    private String accUserName;
    @SerializedName("acc_user_code")
    private String accUserCode;
    @SerializedName("user_name")
    private String userName;
    @SerializedName("user_code")
    private String userCode;
    @SerializedName("today")
    private String today;
    @SerializedName("tran_code")
    private String tranCode;
    @SerializedName("eam_url")
    private String eamUrl;
    @SerializedName("suffix_param")
    private String suffixParam;

}
