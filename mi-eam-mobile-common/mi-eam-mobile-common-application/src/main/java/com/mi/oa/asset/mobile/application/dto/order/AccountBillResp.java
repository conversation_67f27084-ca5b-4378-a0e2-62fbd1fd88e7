package com.mi.oa.asset.mobile.application.dto.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Copyright (c) 2022 XiaoMi Inc.All Rights Reserved.
 * Description: your description
 *
 * <AUTHOR>
 * @date 2022/8/25 18:43
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountBillResp {

    /**
     * 提现日期
     */
    private String cashWithdrawDate;

    /**
     * 提现金额
     */
    private BigDecimal cashWithdrawAmount;

    /**
     * 交易手续费净额
     */
    private BigDecimal netCommissionCharge;

    /**
     * 入款总流水
     */
    private BigDecimal totalInflow;

    /**
     * 出款总流水
     */
    private BigDecimal totalOutflow;

    /**
     * 支付渠道 小米钱包:1 支付宝:2
     */
    private String payType;

    /**
     * 渠道商户号
     */
    private String channelMerchantNo;
}
