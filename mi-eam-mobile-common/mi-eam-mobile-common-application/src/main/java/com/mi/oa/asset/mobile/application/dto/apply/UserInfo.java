package com.mi.oa.asset.mobile.application.dto.apply;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/5 11:15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfo {
    @JsonProperty("user_code")
    private String userCode;
    @JsonProperty("user_name")
    private String userName;
    @JsonProperty("emp_code")
    private String empCode;
    @JsonProperty("user_id")
    private String userId;
    @JsonProperty("dept_code")
    private String deptCode;
    @JsonProperty("dept_name")
    private String deptName;
    @JsonProperty("tree_dept_code")
    private String treeDeptCode;
}
