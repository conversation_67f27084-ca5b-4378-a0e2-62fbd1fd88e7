package com.mi.oa.asset.mobile.application.dto.bpm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 三方账号申请
 *
 * <AUTHOR>
 * @date 2022/1/7 15:08
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProtoThirdUserApplyBizDTO extends ProcessBaseDTO {
    //申请人
    private String applyUser;
    //申请部门
    private String deptName;
    //申请系统
    private String applySystemName;
    //备注
    private String remark;
    //申请账号
    private String thirdUserCode;
    //申请账号名称
    private String thirdUserName;
    //供应商编号
    private String providerCode;
    //供应商名称
    private String providerName;
    //邮箱
    private String email;
    //特别说明
    private String specialExplain;
}
