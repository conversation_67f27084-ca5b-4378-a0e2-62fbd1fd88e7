package com.mi.oa.asset.mobile.application.dto.bpm;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/10/31 17:45
 */

@Data
@Builder
public class ProtoTranBizDetDTO {
    /**
     * 编号：code
     */
    @SerializedName("input_1666604362232")
    private String code;

    /**
     * 小米料号：sku_code
     */
    @SerializedName("input_1666604383896")
    private String skuCode;

    /**
     * 描述：sku_name
     */
    @SerializedName("textarea_1724039517071")
    private String skuName;

    /**
     * 设备编号SN device_code
     */
    @SerializedName("input_1666604431208")
    private String deviceCode;

    /**
     * 项目编号 project_code
     */
    @SerializedName("input_1666604452544")
    private String projectCode;

    /**
     * 试产阶段 stage_name
     */
    @SerializedName("input_1666604471184")
    private String stageName;

    /**
     * IMEI号 imei
     */
    @SerializedName("input_1666604482336")
    private String imei;

    /**
     * 镭雕号 laser_code
     */
    @SerializedName("input_1666604495681")
    private String laserCode;

    /**
     * 新旧属性(类型） asset_newold
     */
    @SerializedName("input_1666604512768")
    private String assetNewold;

    /**
     * 备注 remark
     */
    @SerializedName("textarea_1666604534264")
    private String remark;
}
