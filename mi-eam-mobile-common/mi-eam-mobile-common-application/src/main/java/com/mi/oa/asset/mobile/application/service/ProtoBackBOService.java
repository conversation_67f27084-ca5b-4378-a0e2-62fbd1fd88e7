package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmCallbackDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmProcessCompletedDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.MobileProcessBaseBizDTO;

/**
 * 工程机归还
 *
 * <AUTHOR>
 * @date 2022/1/21 10:20
 */
public interface ProtoBackBOService {
    /**
     * 更新归还状态和台账管理状态
     */
    void updateInStatusAndProtoCard(String inId, String inStatus, String isBack, String useState);

    /**
     * 提交后事件
     *
     * @return
     */
    String afterAudit(String[] keyIds);

    /**
     * 推送BPM
     *
     * @return
     */
    String pushBpm(String keyId);

    /**
     * 撤回BPM流程
     *
     * @param keyIds
     * @param userInfo
     * @return
     */
    String interruptBpm(String[] keyIds, UserInfo userInfo);

    /**
     * bpm回调方法
     *
     * @param bpmCallbackDTO
     * @return
     */
    boolean bpmCallback(BpmCallbackDTO<MobileProcessBaseBizDTO> bpmCallbackDTO);

    /**
     * bpmV3回调方法
     * @param param
     * @return
     */
    boolean bpmCallbackV3(BpmProcessCompletedDTO param);

    /**
     * 撤回
     *
     * @param keyIds
     * @param userInfo
     * @return
     */
    String withDraw(String[] keyIds, UserInfo userInfo);

    /**
     * 修复小米内部员工工号为空的数据
     *
     * @return
     */
    String repairEmpCode();

    /**
     * 业务类型修改清空明细
     * @param keyId
     * @param mrpType
     */
    void clearDet(String keyId, String mrpType);
}
