package com.mi.oa.asset.mobile.application.dto.cardusertab;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CardSpiltDTO {

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 项目编码
     */
    private String projectCode;

    /**
     * 类别
     */
    private String skuCodeType;

    /**
     * 业务类型
     */
    private String mrpType;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CardSpiltDTO that = (CardSpiltDTO) o;
        return Objects.equals(deptCode, that.deptCode) && Objects.equals(projectCode, that.projectCode)
                && Objects.equals(skuCodeType, that.skuCodeType) && Objects.equals(mrpType, that.mrpType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(deptCode, projectCode, skuCodeType, mrpType);
    }
}
