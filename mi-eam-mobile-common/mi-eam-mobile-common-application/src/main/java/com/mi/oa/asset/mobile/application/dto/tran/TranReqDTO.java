package com.mi.oa.asset.mobile.application.dto.tran;

import com.mi.oa.asset.mobile.application.dto.CommonConditionDTO;
import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 工程机转移
 *
 * <AUTHOR>
 * @date 2022/4/7 15:24
 */
@Data
@SuperBuilder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class TranReqDTO extends CommonConditionDTO {

    private String userCode;
}
