package com.mi.oa.asset.mobile.application.config;

import com.mi.oa.asset.mobile.application.service.ProtoDisposeRuleBOService;
import com.mi.oa.asset.mobile.application.service.ProtoPriceBOService;
import com.mi.oa.asset.mobile.application.service.ProtoReportService;
import lombok.Getter;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/4/20 17:26
 */
@Getter
public enum FunEventEnum {

    CARD_STORE_UPDATE("proto_card_store","updrep", ProtoReportService.class,"updateReportInfo"),
    CARD_STORE_UPDATE_TIME("proto_card_store","selupddate", ProtoReportService.class,"getUpdateTimeInfo"),
    CARD_PROJECT_TOTAL_UPDATE("proto_card_project_total", "updrep", ProtoReportService.class, "updateReportInfo"),
    CARD_PROJECT_TOTAL_UPDATE_TIME("proto_card_project_total", "selupddate", ProtoReportService.class, "getUpdateTimeInfo"),
    CARD_SKU_TOTAL_UPDATE("proto_card_sku_total", "updrep", ProtoReportService.class, "updateReportInfo"),
    CARD_SKU_TOTAL_UPDATE_TIME("proto_card_sku_total", "selupddate", ProtoReportService.class, "getUpdateTimeInfo"),
    CARD_DEPT_TOTAL_UPDATE("proto_card_dept_total", "updrep", ProtoReportService.class, "updateReportInfo"),
    CARD_DEPT_TOTAL_UPDATE_TIME("proto_card_dept_total", "selupddate", ProtoReportService.class, "getUpdateTimeInfo"),
    CARD_DEPT_PRO_TOTAL_UPDATE("proto_card_deptpro_total", "updrep", ProtoReportService.class, "updateReportInfo"),
    CARD_DEPT_PRO_TOTAL_UPDATE_TIME("proto_card_deptpro_total", "selupddate", ProtoReportService.class, "getUpdateTimeInfo"),
    PROTO_PRICE_SAVE("proto_price", "save", ProtoPriceBOService.class, "saveOrSubmit"),
    PROTO_PRICE_SUBMIT("proto_price", "audit", ProtoPriceBOService.class, "saveOrSubmit"),
    PROTO_DISPOSE_SAVE("proto_dispose_rule", "save_eg", ProtoDisposeRuleBOService.class, "saveOrSubmit"),
    PROTO_DISPOSE_SUBMIT("proto_dispose_rule", "audit", ProtoDisposeRuleBOService.class, "saveOrSubmit"),
    ;
    private String funId;
    private String eventCode;
    private Class bizClazz;
    private String method;

    FunEventEnum(String funId, String eventCode, Class bizClazz, String method) {
        this.funId = funId;
        this.eventCode = eventCode;
        this.bizClazz = bizClazz;
        this.method = method;
    }

    public static FunEventEnum getFunEventEnum(String funId,String eventCode){
        for (FunEventEnum item:values()){
            if(item.getFunId().equalsIgnoreCase(funId)&&item.getEventCode().equalsIgnoreCase(eventCode)){
                return item;
            }
        }
        throw new RuntimeException("can not find FunEventEnum");
    }
}
