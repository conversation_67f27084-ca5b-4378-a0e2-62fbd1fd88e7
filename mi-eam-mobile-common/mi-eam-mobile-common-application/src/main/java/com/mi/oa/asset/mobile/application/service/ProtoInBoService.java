package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderPO;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/2/24 10:07
 */
public interface ProtoInBoService {
    String inScanQry(String scanResult);

    /**
     * 查询样机成产底
     * @param applyCode
     * @return
     */
    String getMakeCn(String applyCode);

    /**
     * 根据申请单号获取ASN相同料号的采购订单类型信息（匹配第一条ASN单）
     * 米通模式下：取SAP成功状态的采购订单类型
     * @param applyCode
     * @return
     */
    ProtoAsnOrderPO getSapSuccessAsnOrder(String applyCode);
}
