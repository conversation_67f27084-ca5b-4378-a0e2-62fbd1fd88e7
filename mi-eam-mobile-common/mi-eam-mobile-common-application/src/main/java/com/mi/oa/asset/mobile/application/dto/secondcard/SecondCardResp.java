package com.mi.oa.asset.mobile.application.dto.secondcard;

import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/4 18:36
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SecondCardResp extends DTO {
    private String projectCode;
    private String stageName;
    private String skuCode;
    private String skuName;
    private String beijingNum;
    private String shanghaiNum;
    private String shanghaiOdmNum;
    private String shenzhenNum;
    private String xianNum;
    private String yinduNum;
    private String yinniNum;
    private String yuenanNum;
    private String allNum;
}
