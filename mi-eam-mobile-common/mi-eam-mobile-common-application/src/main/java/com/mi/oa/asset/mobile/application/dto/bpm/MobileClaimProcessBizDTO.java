package com.mi.oa.asset.mobile.application.dto.bpm;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 手机工程机领用申请业务字段
 * @date 2021/11/3 10:36
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MobileClaimProcessBizDTO extends ProcessBaseDTO {
    //是否是MRP第三方使用 是 否
    private String isMrp;
    //使用人(User)
    private String userInfo;
    //项目(Project)
    private String project;
    //试产阶段(Trial production stage)
    private String stage;
    //预研项目编号
    private String projectNo;
    //备注
    private String remark;
    //领用方类型
    private String applyUserType;
    //单据类型
    private String srcType;
    //使用人部门
    private String deptCode;
    //扣款接受主体
    private String debitOnwer;
    //申请清单
    @Singular
    private List<Item> items;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Item {
        //编号（Serial number）
        private String sn;
        //小米料号（Xiaomi material number）
        private String miMaterialCode;
        //描述（Describe）
        private String desc;
        //申请数量（Number of applications）
        private Integer applyCount;
        //保密套拆除数量
        private Integer removeShellCount;
        //备注（Remarks）
        private String remark;
    }

}
