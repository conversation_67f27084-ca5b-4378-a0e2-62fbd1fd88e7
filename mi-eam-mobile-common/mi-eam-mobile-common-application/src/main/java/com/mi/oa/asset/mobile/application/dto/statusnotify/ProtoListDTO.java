package com.mi.oa.asset.mobile.application.dto.statusnotify;

import lombok.*;

import java.util.List;

/**
 * 工程机单据
 *
 * <AUTHOR>
 * @date 2022/4/12 18:35
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProtoListDTO {
    //发放单号
    private String outCode;
    //领用单号
    private String applyCode;
    //转移单号
    private String tranCode;
    //归还单号
    private String inCode;
    //无法归还单号
    private String unreturnCode;
    //申请人
    private String applyUserCode;
    //发放时间
    private String outDate;
    //接收人
    private String accUserCode;
    //接受时间
    private String applyDate;
    //确认归还时间
    private String inDate;
    //单据审批完成时间
    private String confirmDate;
    @Singular
    private List<ProtoListDetDTO> items;

    //发放单号、领用单号、领用单申请人、发放时间、发放明细信息（小米料号、描述、项目编号、阶段、配置、镭雕、SN、IMEI）
    //转移单号、申请人、接收人、接受时间、转移明细信息（小米料号、描述、项目编号、阶段、配置、镭雕、SN、IMEI）
    //归还单号、申请人、确认归还时间、归还明细信息（小米料号、描述、项目编号、阶段、配置、镭雕、SN、IMEI、归还状态：不推不允许归还的数据）
    //无法归还单号、申请人、单据审批完成时间、归还明细信息（小米料号、描述、项目编号、阶段、配置、镭雕、SN、IMEI）

}
