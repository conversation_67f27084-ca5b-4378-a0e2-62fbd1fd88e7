package com.mi.oa.asset.mobile.application.dto;

import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/4/20 15:26
 */
public class EamApiResp {


    public static final String RESULT = "result";
    public static final String RETURN_DATA = "_returnData";
    public static final String MESSAGE = "_message";

    public static Map<String, String> getEamApiResp(boolean result, String msg, Object data){
        HashMap<String, String> map = new HashMap<>();
        if(!StringUtils.isEmpty(msg)){
            map.put(MESSAGE,msg);
        }
        if(!ObjectUtils.isEmpty(data)){
            map.put(RETURN_DATA, JacksonUtils.bean2Json(data));
        }
        map.put(RESULT,result?"true":"false");
        return map;
    }

    public static Map<String, String> success(){
        return getEamApiResp(true,null,null);
    }

    public static Map<String, String> success(Object data){
        return getEamApiResp(true,null,data);
    }

    public static Map<String, String> failed(String msg){
        return getEamApiResp(false,msg,null);
    }
}
