package com.mi.oa.asset.mobile.application.dto.bpm;

import com.mi.oa.asset.mobile.common.enums.MobileProcessEnum;
import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.*;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description: 手机工程机流程基础字段
 * @date 2021/11/4 9:45
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MobileProcessBaseBizDTO {

    //工程机的申请单的标题
    public static final String MOBILE_CLAIM_TITLE = "工程机领用申请(Application for Construction Machinery prototype) %s";
    //工程机的转移的标题
    public static final String MOBILE_TRANSFER_TITLE = "工程机转移申请(Application for Construction Machinery prototype) %s";
    //手机工程机归还
    public static final String MOBILE_RETURN_TITLE = "工程机归还申请 %s";
    //工程机延期归还申请
    public static final String MOBILE_DELAY_TITLE = "工程机延期归还申请(Application for Machinery delay return) %s";
    //手机工程机无法归还
    public static final String MOBILE_NO_RETURN_TITLE = "工程机无法归还申请(Unable to return application form for approval) %s";
    //手机工程机保密套拆除
    public static final String MOBILE_SEC_REM_TITLE = "保密套拆除(Removal of Confidential Cover) %s";
    //手机工程机报废
    public static final String MOBILE_SCRAP_TITLE = "工程样机报废申请(Application for Construction Machinery prototype) %s";
    //小米权限申请
    public static final String MOBILE_ROLE_APPLY = "小米权限申请(Application for Construction Machinery role apply) %s";
    //三方P账号申请
    public static final String MOBILE_THIRD_USER_APPLY = "三方P账号申请(Application for Construction Machinery third user apply) %s";

    /**
     * 子流程
     */
    @NotNull
    private SubProcessEnum subProcess;
    //业务id
    private String bizId = "";
    //手机工程机申请
    private MobileClaimProcessBizDTO mobileClaimProcessBizDTO;
    //手机工程机转移
    private MobileTransferProcessBizDTO mobileTransferProcessBizDTO;
    //手机工程机归还
    private MobileReturnProcessBizDTO mobileReturnProcessBizDTO;
    //手机工程机无法归还
    private MobileNoReturnProcessBizDTO mobileNoReturnProcessBizDTO;
    //手机工程机保密套拆除
    private MobileSecRemProcessBizDTO mobileSecRemProcessBizDTO;
    //手机工程机报废
    private MobileScrapProcessBizDTO mobileScrapProcessBizDTO;
    //小米权限申请
    private ProtoRoleApplyBizDTO protoRoleApplyBizDTO;
    //三方P账号申请
    private ProtoThirdUserApplyBizDTO protoThirdUserApplyBizDTO;
    //工程机延期归还申请
    private ProtoDelayBizDTO protoDelayBizDTO;

    //bpm varaibles 字段
    private VaraibleDTO varaibleDTO;

    /**
     * bpm流程文档：https://xiaomi.f.mioffice.cn/docs/dock4yI0StxsnLmV9jxwM4kr87d#
     * claim:
     * 内部领用/MRP第三方领用：申请人-->部门直属领导-->PM   subProcessKey：1
     * ODM/EMS/黑鲨领用：申请人-->需求确认人-->需求确认人直属领导-->PM  subProcessKey：2
     * transfer:
     * 小米内部转移：申请人-->接收人 subProcessKey：1
     * ODM/EMS/黑鲨转入小米内部：申请人-->接收人直属领导-->接收人 subProcessKey：2
     * return:
     * ODM/EMS/黑鲨:申请人-->需求确认人 subProcessKey：1
     * noReturn:
     * 小米内部无法归还：申请人-->申请人直属领导-->内审 subProcessKey：1
     * ODM/EMS/黑鲨无法归还：申请人-->需求确认人-->需求确认人直属领导-->商务-->财务-->内审 subProcessKey：2
     * secRem:
     * 小米内部保密套拆除：申请人-->第一申领人-->PM subProcessKey：1
     * ODM/EMS/黑鲨保密套拆除：申请人-->需求确认人-->PM subProcessKey：1
     * scrap:
     * 申请人-->PM负责人-->项目开发管理部-->硬件研发部-->财务-->监察-->公司领导 subProcessKey：1
     */
    @Getter
    @AllArgsConstructor
    public enum SubProcessEnum {
        //多个个枚举值,注意名字并不是构造方法名
        PROCESS_ONE(1, "子流程1"),
        PROCESS_TWO(2, "子流程2"),
        PROCESS_THREE(3, "子流程3");

        //枚举值所包含的属性
        private Integer value;
        private String label;

        //根据value查找枚举
        public static SubProcessEnum convertValueToEnum(Integer value) {
            SubProcessEnum result = PROCESS_ONE;
            for (SubProcessEnum subProcessEnum : SubProcessEnum.values()) {
                if (subProcessEnum.getValue().intValue() == value.intValue()) {
                    result = subProcessEnum;
                }
            }
            return result;
        }
    }
}
