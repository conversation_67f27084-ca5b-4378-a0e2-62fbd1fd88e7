package com.mi.oa.asset.mobile.application.dto.apply;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/5 11:47
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplyDTO {
    private String keyId;
    private String projectCode;
    private String stageName;
    private String isOther;
    private String userCode;
    private String applyUserCode;
    private UserInfo userInfo;
}
