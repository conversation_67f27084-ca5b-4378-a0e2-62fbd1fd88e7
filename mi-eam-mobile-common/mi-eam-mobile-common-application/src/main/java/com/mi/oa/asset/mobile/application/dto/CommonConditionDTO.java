package com.mi.oa.asset.mobile.application.dto;

import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder(toBuilder = true)
public class CommonConditionDTO{
    private String fieldNames;
    private String fieldConds;
    private String fieldValues;
    private Integer start;
    private Integer limit;
    private String whereSql;
}
