package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.application.dto.ApplyResp;
import com.mi.oa.asset.mobile.application.dto.AssetCommonDTO;
import com.mi.oa.asset.mobile.application.dto.CommonConditionDTO;
import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.dto.secondcard.ApplySecondDTO;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/4 14:13
 */
public interface ProtoSecondCardBOService {

    AssetCommonDTO query(CommonConditionDTO requestParam, String userId, String funId);

    ApplyResp createApplySecond(ApplySecondDTO dto, UserInfo userInfo);

    /**
     *  生成二手不良品库清单记录
     */
    void createData(ApplySecondDTO applySecondDto, UserInfo userInfo);
    /**
     *  删除不良品记录
     * @return
     */
    void deleteData(List<ApplySecondDTO> applySecondDtoList, UserInfo userInfo);
}
