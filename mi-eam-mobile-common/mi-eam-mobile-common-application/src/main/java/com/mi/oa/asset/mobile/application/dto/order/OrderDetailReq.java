package com.mi.oa.asset.mobile.application.dto.order;

import com.mi.oa.infra.oaucf.core.dto.Req;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Copyright (c) 2022 XiaoMi Inc.All Rights Reserved.
 * Description: your description
 *
 * <AUTHOR>
 * @date 2022/7/29 10:16
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderDetailReq extends Req {

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 计划ID
     */
    private Integer planId;

    /**
     * 计划编码
     */
    private String planCode;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品描述
     */
    private String productDesc;

    /**
     * 商品价格
     */
    private BigDecimal productPrice;

    /**
     * 商品数量
     */
    private Integer productNum;
}
