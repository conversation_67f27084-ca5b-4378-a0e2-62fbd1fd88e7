package com.mi.oa.asset.mobile.application.dto.secondcard;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/5 11:47
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplySecondDTO {
    private String mrpType;
    private String projectCode;
    private String stageName;
    private String skuCode;
    private String skuName;
    private String houseCode;
    private String houseName;
    private Integer num;
    private String isComplete;
    private String goodsId;
}
