package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoUnReturnPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysAttachPO;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2022/2/25 14:46
 */
public interface ProtoUnReturnBOService {
    /**
     *
     *扫码查询订单
     * @param scanResult
     * @return
     */
    String unReturnScanQry(String scanResult);

    /**
     * 删除附件
     * @param dataId
     * @param attachPath
     */
    void deletedFile(String dataId, String attachPath, String tableName);

    /**
     * 删除附件
     * @param dataId
     */
    void deleteAttach(String dataId);

    /**
     * 上传附件
     * @param attachList
     */
    void uploadFile(List<SysAttachPO> attachList);

    /**
     * copy 附件
     * @param attachList
     */
    void copyAttach(List<SysAttachPO> attachList, List<ProtoUnReturnPO> unreturnList);

    /**
     * copy 附件
     * @param attachList
     */
    void copyAttach(List<SysAttachPO> attachList);

    /**
     * 前端提交
     * @param unreturnCode
     */
    void unreturnSubmit(String unreturnCode);
}
