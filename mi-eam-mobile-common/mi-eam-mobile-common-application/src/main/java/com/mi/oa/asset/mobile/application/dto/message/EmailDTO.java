package com.mi.oa.asset.mobile.application.dto.message;


import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmailDTO extends DTO {
    //email 收件人
    private List<String> emails;
    //标题
    private String title;
    //内容
    private String content;
    //抄送人
    private List<String> ccEmails;
}
