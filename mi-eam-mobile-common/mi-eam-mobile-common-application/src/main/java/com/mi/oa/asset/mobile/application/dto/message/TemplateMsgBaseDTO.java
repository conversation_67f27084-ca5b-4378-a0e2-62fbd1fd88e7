package com.mi.oa.asset.mobile.application.dto.message;

import com.google.gson.annotations.SerializedName;
import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TemplateMsgBaseDTO extends DTO {
    //最后eamil中采用 如果不为空用于配置跳转
    @SerializedName("business_key")
    private String businessKey;
    @SerializedName("fun_id")
    private String funId;
    @SerializedName("data_id")
    private String dataId;
    //是否包含跳转url 默认false
    private boolean containUrl;
}
