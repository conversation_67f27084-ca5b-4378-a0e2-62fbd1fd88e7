package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.application.dto.AssetCommonDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmCallbackDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmProcessCompletedDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.MobileProcessBaseBizDTO;
import com.mi.oa.asset.mobile.application.dto.tran.TranReqDTO;
import com.mi.oa.asset.mobile.application.dto.tran.TranSapSplitDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranPO;

import java.util.List;

public interface TranService {

    /**
     * 转移提交
     *
     * @param tranIds
     * @param userCode
     */
    void tranCommit(String tranId, String userCode);

    String tranScanQry(String scanResult);

    /**
     * bpm回调处理
     */
    void bpmCallBack(BpmCallbackDTO<MobileProcessBaseBizDTO> bpmCallbackDTO);


    /**
     * bpm回调处理3.0
     */
    void bpmCallBackV3(BpmProcessCompletedDTO param);

    /**
     * 抛SAP转移接口
     * @param tranPO
     * @param tranDetPOList
     * @param isCommit
     */
    void pushSap(ProtoTranPO tranPO, List<ProtoTranDetPO> tranDetPOList, boolean isCommit);

    /**
     * 抛SAP 107接口
     * @param tranPO
     * @param sapSplitDTO
     * @param tranDetPOS
     */
    void pushSap107(ProtoTranPO tranPO, TranSapSplitDTO sapSplitDTO, List<ProtoTranDetPO> tranDetPOS);

    /**
     * 工程机转移查询
     *
     * @param requestParam
     * @return
     */
    AssetCommonDTO query(TranReqDTO requestParam);

    /**
     * 业务类型修改清空明细
     * @param keyId
     * @param mrpType
     * @return
     */
    void tranClearDet(String keyId, String mrpType);

    /**
     * 发生通知
     * @param tranPO
     * @param tranDetPOList
     */
    void sendSuccessMessage(ProtoTranPO tranPO, List<ProtoTranDetPO> tranDetPOList);

    /**
     * 提交时邮件消息通知
     * @param tranPO
     * @param tranDetPOList
     * @param businessKey
     */
    void sendAuditMessage(ProtoTranPO tranPO, List<ProtoTranDetPO> tranDetPOList,
                                 String businessKey);
}
