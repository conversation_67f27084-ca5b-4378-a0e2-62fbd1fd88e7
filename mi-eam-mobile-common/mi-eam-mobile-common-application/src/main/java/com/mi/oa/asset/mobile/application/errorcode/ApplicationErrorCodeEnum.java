package com.mi.oa.asset.mobile.application.errorcode;

import com.mi.oa.infra.oaucf.core.exception.ApplicationErrorCode;

/**
 * create by roger
 */
public enum ApplicationErrorCodeEnum implements ApplicationErrorCode {

    /**
     * 应用层未知错误
     */
    APPLICATION_UNKNOWN_ERROR(1, "{{{应用层未知错误}}}"),
    /**
     * cannot find by tranId
     */
    NOT_FIND_BY_TRAN_ID(2, "cannot find by tranId"),
    /**
     * 数据库找不到记录错误
     */
    DATA_NOT_FOUND_ERROR(3, "{{{data not find :table:%s,uid:%s}}}"),
    /**
     * EAM前端错误提示
     */

    EAM_BIZ_ERROR(4, "{{{%s}}}"),
    BPM_REMOTE_ERROR(5, "call BPM failed: %s"),
    //You can only submit the documents you apply for!
    ERR_TRAN_NOT_SELF_APPLY(6, "{{{只能提交自己申请的单据}}}"),
    //Please fill out the detail form first!
    ERR_TRAN_WRITE_DET(7,"{{{请先填写明细表}}}"),
    // Unqualified items exist in the list, please check!
    ERR_TRAN_CHECK_DET(8,"{{{明细表存在不合格项，请检查}}}"),
    //User p cannot be transferred to user P
    ERR_TRAN_P_TO_P(9,"{{{p账号用户不允许转移到p账号用户}}}"),
    //The recipient is P account user, which must be maintained in the third-party configuration before transfer is allowed
    ERR_TRAN_USER_CKECK(10,"{{{接收人为P账号用户，必须在第三方配置里维护才允许转移}}}"),
    //The following equipment serial SN has changed in the ledger, which does not meet the import conditions, please check
    ERR_TRAN_CARD(11,"{{{以下设备编号SN在台账中出现异动，不满足导入条件，请检查}}} %s"),
    //The following distribution No. has not been confirmed, please check!
    ERR_TRAN_CHECK_OUT_CODE(12,"{{{以下发放单号尚未确认，请检查}}} %s"),
    //The cost center for the recipient could not be found
    ERR_TRAN_NOT_COST_CENTER(13,"{{{找不到接收人的成本中心}}}"),

    ERR_TRAN_OUT_NOT_PUSH_SAP(14,"{{{存在尚未抛送SAP的发放记录}}} %s"),
    ERR_TRAN_DEPT_CODE(700,"{{{申请人或接收人部门编码不存在}}}"),
    /**
     * 参数检查错误
     */
    PARAM_INVALID_ERROR(15, "{{{param check error: }}}: %s"),
    /**
     * 参数检查错误
     */
    UPDATE_DATA_ERROR(16, "{{{update error:}}} %s"),
    PROTO_BACK_CONFIRM_006(17, "{{{扫描的SN号未找到对应的归还确认单}}}【%s】"),
    PROTO_BACK_CONFIRM_007(18, "{{{更新归还确认人失败}}}"),
    PROTO_BACK_CONFIRM_005(19, "{{{更新明细是否良品失败}}}"),
    PROTO_BACK_003(20, "{{{只允许确认归还[归还中]状态的单据}}}"),
    PROTO_BACK_012(21, "{{{存在未确认归还的明细，请先进行确认}}}"),
    PROTO_BACK_022(22, "{{{存在默认归还状态的明细，请您重新确认}}}"),
    PROTO_BACK_013(23, "{{{更新操作时间失败}}}"),
    PROTO_BACK_011(24, "{{{更新工程机台账失败}}}"),
    PROTO_BACK_014(25, "{{{更新明细是否已归还信息失败}}}"),
    PROTO_BACK_CONFIRM_002(26, "{{{扫描的SN号}}}: %s {{{不存在归还单}}}:【%s】"),
    PROTO_BACK_CONFIRM_003(27, "{{{扫描的SN号}}}:【%s】 {{{已确认，是否良品}}}:【%s】"),
    PROTO_BACK_CONFIRM_004(28, "{{{扫描的SN号}}}:【%s】 {{{进行预确认失败}}}"),
    PROTO_BACK_CONFIRM_001(29, "{{{该归还单已完成确认归还，不允许重复操作}}}"),
    PROTO_APPLY_003(30, "{{{查找不到项目编号下的试产阶段信息}}}【%s】"),
    PROTO_APPLY_004(31, "{{{插入物料明细表失败，请联系系统管理员}}}"),
    PROTO_COLLECT_PLAN_004(32, "{{{申请人为p账号用户，MRP第三方领用必须为否}}}"),
    PROTO_APPLY_029(33, "{{{只有未提交的单据才允许提交}}}"),
    PROTO_APPLY_011(34, "{{{申请人为p账号用户，MRP第三方领用必须为否}}}"),
    PROTO_COLLECT_PLAN_002(35, "{{{领用方类型为ODM/EMS/黑鲨领用，必须填写确认人}}}"),
    PROTO_COLLECT_PLAN_003(36, "{{{领用方类型非ODM/EMS/黑鲨领用，无需填写确认}}}"),
    PROTO_APPLY_015(37, "{{{最少需要一条明细的申请数量大于0}}}"),
    PROTO_APPLY_010(38, "{{{料号}}}:【%s】,{{{申请数量}}} %s {{{大于}}} {{{库存数量}}}: %s "),
    PROTO_APPLY_017(39, "{{{更新成本中心失败}}}"),
    PROTO_APPLY_019(40, "{{{更新已申请待发放数量失败}}}"),
    PROTO_APPLY_018(41, "{{{p账号用户必须在第三方人员配置表里维护才能选择}}}"),
    PROTO_APPLY_005(42, "{{{更新领用方类型失败，请联系系统管理员}}}"),
    PROTO_APPLY_012(43, "{{{无法获取SAP推送数据，请检查单据完整性}}}"),
    PROTO_APPLY_016(44, "{{{更新领用申请单失败}}}"),
    PROTO_APPLY_014(45, "{{{抛送SAP失败}}}：%s"),
    PROTO_BPM_001(46, "{{{抛送BPM失败}}}"),
    PROTO_BPM_002(47, "{{{保存BPM流程信息失败}}}"),
    PROTO_BPM_003(48, "{{{只能撤回本人发起的单据}}}"),
    PROTO_BPM_004(49, "{{{更新工程机台账状态失败}}}"),
    PROTO_BPM_005(50, "{{{单据未发起Bpm流程，无需撤回}}}"),
    PROTO_BPM_006(51, "{{{只能撤回审批中的单据}}}"),
    PROTO_BPM_007(52, "{{{撤回Bpm流程失败}}}"),
    PROTO_BPM_008(53, "{{{更新记录状态为未提交失败}}}"),
    PROTO_APPLY_030(54, "{{{该单据尚未提交，无需关闭}}}"),
    PROTO_APPLY_031(55, "{{{请撤回该单据，并删除即可}}}"),
    PROTO_APPLY_032(56, "{{{当前领用单存在待确认的发放记录，请【确认收货】后再【关闭单据】}}}"),
    PROTO_APPLY_033(57, "{{{该单据已关闭，无需重复操作}}}"),
    PROTO_APPLY_034(58, "{{{只有已审批的单据可以使用}}}"),
    PROTO_APPLY_037(59, "{{{当前操作员不是单据申请人，不允许确认收货}}}"),
    PROTO_APPLY_006(60, "{{{占用库存记录失败}}}"),
    PROTO_APPLY_007(61, "{{{释放库存记录失败}}}"),
    PROTO_APPLY_013(62, "{{{更新领用申请单-物料明细SAP行号失败}}}"),
    PROTO_APPLY_026(63, "{{{找不到当前操作人信息，请联系系统管理员}}}"),
    PROTO_SECOND_CARD_002(64, "{{{不良品维护数量}}} %s {{{大于}}} {{{库存数量}}} %s"),
    PROTO_SECOND_CARD_001(65, "{{{不良品维护失败，请联系系统管理员}}}"),
    PROTO_SECOND_CARD_003(66, "{{{只能删除是否良品为【非良品】的记录}}}"),
    PROTO_SECOND_CARD_005(67, "{{{您不是当前记录维护人}}},%s"),
    PROTO_APPLY_027(68, "{{{生成二手领用申请单失败，请联系系统管理员}}}"),
    PROTO_APPLY_028(69, "{{{生成二手领用物料明细失败，请联系系统管理员}}}"),
    PROTO_ASN_ORDER_001(70, "{{{未找到ASN订单数据, ASN订单号:}}} %s"),
    PROTO_ASN_ORDER_002(71, "{{{ASN单已完成收货，不允许继续收货！}}}"),
    PROTO_ASN_ORDER_003(72, "{{{ASN单【}}}%s{{{】已失效，无法进行收货！}}}"),
    PROTO_ASN_ORDER_004(73, "{{{收货记录明细不允许为空，请先进行扫码预收货后再点击收货按钮！}}}"),
    PROTO_IN_001(74, "{{{扫描的SN号【}}}%s{{{】已被收货单【}}}%s{{{】收货，您不能进行收货！}}}"),
    PROTO_IN_002(75, "{{{收货数量不能大于发货数量！}}}"),
    PROTO_IN_003(76, "{{{只有收货状态为“已收货”的单据才允许抛送SAP！}}}"),
    PROTO_IN_004(77, "{{{ASN单号不能为空，如是无ASN收货，先先等待匹配完成后再抛送SAP！}}}"),
    PROTO_SAP_001(78, "{{{抛送SAP，107失败：}}}%s"),
    PROTO_SLEEVE_001(79, "{{{必须填写确认人！}}}"),
    PROTO_SLEEVE_002(80, "{{{无需填写确认人！}}}"),
    PROTO_SLEEVE_003(81, "{{{明细表存在不合格项，请检查！}}}"),
    PROTO_SLEEVE_004(82, "{{{以下设备编号SN在台账中出现异动，请检查！%s}}}"),
    PROTO_SLEEVE_005(83, "{{{以下设备编号SN尚未【确认收货】，请确认收货后，再次尝试提交！%s}}}"),
    PROTO_SLEEVE_006(84, "{{{更新领用方类型失败！}}}"),
    PROTO_SLEEVE_007(85, "{{{第一申领人【}}}%s{{{】和PM【}}}%s{{{】非在职状态，不允许发起流程，请联系管理员处理}}}"),
    PROTO_SLEEVE_008(86, "{{{更新工程机台账保密套是否拆除字段失败！}}}"),
    PROTO_PROJECT_CFG_001(87, "{{{同一个项目只能配置同一个PM，请自行检查！}}}"),
    PROTO_PROJECT_CFG_002(88, "{{{已经存在项目配置记录应用了该项目、PM与试产工厂，请修改！}}}"),
    PROTO_PROJECT_CFG_003(89, "{{{更新最后修改人失败，请联系系统管理员！}}}"),
    PROTO_PROJECT_CFG_004(90, "{{{更新项目目录表PM信息失败！}}}"),
    PROTO_PROJECT_CFG_005(91, "{{{批量修改PM失败，请联系系统管理员！}}}"),
    PROTO_PROJECT_CFG_006(92, "{{{更新项目目录表失败，请联系系统管理员！}}}"),
    PROTO_BACK_004(93, "{{{只有已审批的单据才可以撤回！}}}"),
    PROTO_BACK_005(94, "{{{只有未确认的单据才可以撤回！}}}"),
    PROTO_BACK_006(95, "{{{只有申请人才可以撤回！}}}"),
    PROTO_BACK_007(96, "{{{更新记录状态失败！}}}"),
    PROTO_BACK_008(97, "{{{更新明细表归还状态失败！}}}"),
    PROTO_BACK_009(110, "{{{只有审批中的单据才可以撤回！}}}"),
    PROTO_DELAY_001(98, "{{{p账号用户必须在第三方人员配置表里维护才能选择！}}}"),
    PROTO_DELAY_002(99, "{{{领用方类型为小米员工，无需填写确认人！}}}"),
    PROTO_DELAY_003(100, "{{{领用方类型为ODM+EMS+黑鲨，必须填写确认人！}}}"),
    PROTO_CARD_USER_TAB_001(101, "{{{查询条件不能都为空！}}}"),
    PROTO_CARD_USER_TAB_002(102, "{{{单次仅能追溯一条SN记录！当前查询条件下，有多条符合要求数据，请细化检索条件或仅用SN查询！}}}"),
    PROTO_CARD_USER_TAB_003(103, "{{{没有查询到数据！}}}"),
    MAIL_TEMPLATE_001(104, "{{{未找到模板}}}"),
    DATA_IS_UPDATING_001(105, "数据正在更新中，请稍后操作!"),
    PROTO_AUTH_DATA_CFG_001(106, "不能提交重复数据!"),
    PROTO_SLEEVE_009(107, "{{{更新拆除数量失败！}}}"),
    PROTO_SLEEVE_010(108, "{{{明细不能为空！}}}"),
    PROTO_SLEEVE_011(109, "{{{电视没有保密套拆除操作，请核实业务类型！}}}"),

    FUN_EVENT_NOSUCHMETHODEXCEPTION_ERROR(300, "NoSuchMethodException"),
    FUN_EVENT_INVOCATIONTARGETEXCEPTION_ERROR(301, "%s"),
    FUN_EVENT_ILLEGALACCESSEXCEPTION_ERROR(304, "IllegalAccessException"),

    PROTO_PRICE_CODE_SAME(401, "定价配置编码重复"),

    PROTO_PLAN_ID_IS_NULL(501, "内购订单主键为空"),
    PROTO_PLAN_IS_NULL(502, "未找到订单信息"),
    PROTO_NOT_WAIT_SELL(503, "不是待销售状态,设备SN【%s】"),
    PROTO_ORDER_IS_NULL(504, "预订单ID为空"),
    PROTO_ORDER_IS_USER(505, "没有购买人,设备SN【%s】"),

    PROTO_ORDER_IS_ZERO(506, "内购金额为0,设备SN【%s】"),
    PROTO_CREATE_ORDER_ERROR(507, "创建订单失败,【%s】"),
    PROTO_NOT_FIND_USER(508, "未找到用户信息，账号【%s】"),
    PROTO_NOT_WAIT_PAY(509, "不是待销售和待支付状态,设备SN【%s】"),
    PROTO_PRE_ORDER_CREATE_NO_DATA(509, "创建预订单失败,无数据【%s】"),

    ERR_INCONSISTENCY_TYPE(510,"{{{主表与子表的业务类型不一致，不允许提交！}}}"),
    ERR_NULL_TYPE(511,"{{{子表业务类型不能为空，请重新导入数据！}}}"),

    PROTO_ORDER_NOT_BIND_DEVICE_CODE(512, "没有指定SN,订单ID【%s】"),
    PROTO_ORDER_IS_POST(513, "邮寄方式无需通知验货【%s】"),

    PROTO_INNER_PURCHASE_PULL_DATA_ERROR(530, "内购计划拉取数据错误【%s】"),
    PROTO_INNER_PURCHASE_DB_NO_DATA_ERROR(531, "内购计划单不存在【%s】"),
    PROTO_INNER_PURCHASE_DB_NOT_APPROVED_ERROR(532, "内购计划单未审批完成【%s】"),
    PROTO_ROLE_NO(601, "{{{未配置角色【%s】,请联系管理员}}}"),
    ;

    /**
     * 具体错误码
     */
    private int errCode;
    /**
     * 描述
     */
    private String errDesc;

    @Override
    public int getBizCode() {
        return 0;
    }

    @Override
    public int getErrorCode() {
        return errCode;
    }

    @Override
    public String getErrDesc() {
        return this.errDesc;
    }

    /**
     * 构造方法
     *
     * @param errCode
     * @param errDesc
     */
    ApplicationErrorCodeEnum(int errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }


}
