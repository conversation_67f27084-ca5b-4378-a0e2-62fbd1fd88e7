package com.mi.oa.asset.mobile.application.dto.bpm;

import com.google.gson.annotations.SerializedName;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/31 20:00
 */

@Data
@Builder
public class ProtoBackBizDTO {
    /**
     * 归还单号 in_code
     */
    @SerializedName("input_7abdd02766f5")
    private String inCode;

    /**
     * 申请人 apply_user_name
     */
    @SerializedName("input_959723a13211")
    private String applyUserName;

    /**
     * 申请部门 dept_name
     */
    @SerializedName("input_5622927e2618")
    private String deptName;

    /**
     * 工程机类型 mrp_type
     */
    @SerializedName("input_341aa945a79c")
    private String mrpType;

    /**
     * 备注 in_memo
     */
    @SerializedName("textarea_ee5f690821c0")
    private String inMemo;

    /**
     * 小米确认人 user_code
     */
    @SerializedName("user_5c119331baaf")
    @Builder.Default
    private String userCode= "";

    /**
     * 归还清单
     */
    @SerializedName("formTable_0fd5022582fb")
    private List<ProtoBackBizDetDTO> protoBackBizDetDTOList;

    public String getJson() {
        return GsonUtil.toJsonString(this);
    }
}
