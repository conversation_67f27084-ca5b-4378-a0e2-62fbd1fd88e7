package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/20 15:10
 */
public interface ProtoBackConfirmBOService {
    /**
     * 扫码确认
     *
     * @return inId
     */
    public String scanConfirm(String scanResult, UserInfo userInfo);
    /**
     * 提交后事件
     *
     * @return
     */
    public String afterAudit(String[] keyIds, UserInfo userInfo);
    public String audit(String[] keyIds, UserInfo userInfo, RequestContext request);
}
