package com.mi.oa.asset.mobile.application.dto.bpm;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description: BPM 流程里需要通过varaible传递的字段
 * @date 2021/11/8 14:33
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VaraibleDTO {
    //PM 多人用,分割
    private String projectManager;
    //需求确认人
    private String confirmor;
    //接收人
    private String receiver;
    //第一申领人
    private String firstStarter;
    /**
     * 角色 items中有工程机接口人角色就设置  否则就设置任意一角色
     */
    private String roleNo;
}
