package com.mi.oa.asset.mobile.application.service;

import java.time.LocalDateTime;

public interface ProtoDataSyncService {

    /**
     * 同步电视和生态链项目信息
     *
     * @param dataTime 同步开始时间
     */
    void syncEcoAndTvProjectInfo(LocalDateTime dataTime);

    /**
     * 同步SAP生态链半成品物料
     *
     * @param dataTime 同步开始时间
     */
    void syncSapEcoSkuData(LocalDateTime dataTime);

    void dealProject(String projectCode,String mrpType);

}
