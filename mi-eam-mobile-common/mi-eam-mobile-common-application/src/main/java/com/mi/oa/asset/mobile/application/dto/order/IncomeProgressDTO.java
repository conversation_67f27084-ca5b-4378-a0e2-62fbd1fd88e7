package com.mi.oa.asset.mobile.application.dto.order;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/25
 */
@Data
public class IncomeProgressDTO {

    /**
     * 支付方式  1-线上，2-线下
     */
    private String payWay;

    /**
     * 支付渠道 1:小米钱包 2:支付宝
     */
    private String payOrderType;

    /**
     * 收款金额
     */
    private BigDecimal receiptAmount;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 交易手续费
     */
    private BigDecimal chargeAmount;

    /**
     * 净收益
     */
    private BigDecimal netEarning;
}
