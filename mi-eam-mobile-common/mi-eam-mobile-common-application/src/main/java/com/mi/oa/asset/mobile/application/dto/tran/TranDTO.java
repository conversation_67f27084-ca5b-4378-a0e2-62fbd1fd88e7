package com.mi.oa.asset.mobile.application.dto.tran;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 工程机转移
 *
 * <AUTHOR>
 * @date 2022/4/7 16:07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TranDTO {

    //private String tranId;
    private String proto_tran__tran_id;

    //private String tranCode;
    private String proto_tran__tran_code;

    //private String auditing;
    private String proto_tran__auditing;

    //private String userName;
    private String proto_tran__user_name;

    //private String userCode;
    private String proto_tran__user_code;

    //private String empCode;
    private String proto_tran__emp_code;

    //private String accUserName;
    private String proto_tran__acc_user_name;

    //private String accUserCode;
    private String proto_tran__acc_user_code;

    //private String accEmpCode;
    private String proto_tran__acc_emp_code;

    //private Date applyDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date proto_tran__apply_date;

    //private String deptName;
    private String proto_tran__dept_name;

    //private String deptCode;
    private String proto_tran__dept_code;

    //private String inDeptName;
    private String proto_tran__in_dept_name;

    //private String inDeptCode;
    private String proto_tran__in_dept_code;

    //private String addUserid;
    private String proto_tran__add_userid;

    //private Date addDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date proto_tran__add_date;

    //private String modifyUserid;
    private String proto_tran__modify_userid;

    //private Date modifyDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date proto_tran__modify_date;

    //private String tenantId;
    private String proto_tran__tenant_id;

    //private String remark;
    private String proto_tran__remark;

    //private String businessKey;
    private String proto_tran__business_key;

    //private String applyUserType;
    private String proto_tran__apply_user_type;

    //private String accCenterCode;
    private String proto_tran__acc_center_code;

    //private String centerCode;
    private String proto_tran__center_code;

    //private String outCompDeptCode;
    private String proto_tran__out_comp_dept_code;

    //private String inCompDeptCode;
    private String proto_tran__in_comp_dept_code;

    //private String makeCn;
    private String proto_tran__make_cn;

    //private String tranType;
    private String proto_tran__tran_type;

    //private String assetNewold;
    private String proto_tran__asset_newold;

    //private String sapRetType;
    private String proto_tran__sap_ret_type;

    //private String batchId;
    private String proto_tran__batch_id;

    //private String confirmStatus;
    private String proto_tran__confirm_status;
}
