package com.mi.oa.asset.mobile.application.dto.message;

import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LarkDTO extends DTO {
    //收件人
    private List<String> userNames;
    //标题
    private String title;
    //内容
    private String content;
    //跳转
    private String url;
    // 按钮名称
    private String buttonName;
    // 按钮类型
    private String botType;
}
