package com.mi.oa.asset.mobile.application.dto.order;

import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Copyright (c) 2024 XiaoMi Inc.All Rights Reserved.
 * Description: your description
 *
 * <AUTHOR>
 * @date 2024/9/24 17:33
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AfterSaleApplyDTO extends DTO {
    private static final long serialVersionUID = 2560353805888637457L;

    private String orderId;

    private Integer processStatus;

    private Integer afterSaleType;

    private String reason;

    private List<Attach> attaches;

    private String userCode;
}
