package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.application.dto.order.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOrderPO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/1
 */
public interface ProtoOrderBOService {

    void generatePurOrder(PurOrderDTO purOrderDTO);

    /**
     * 创建内购平台预订单
     *
     * @param dto
     */
    void preOrderCreate(PreOrderDTO dto);

    /**
     * 通知付款
     * @param orderDTO
     */
    void notifyPay(ProtoOrderDTO orderDTO);

    /**
     * 通知验机
     * @param orderDTO
     */
    void notifyCheck(ProtoOrderDTO orderDTO);

    /**
     * 下架
     * @param orderDTO
     */
    void soldOut(ProtoOrderDTO orderDTO);

    /**
     * 完成时发送邮件
     * @param salesOrder
     */
    void sendCompleteLark(String salesOrder);

    /**
     * 关闭订单
     * @param orderPO
     */
    void close(ProtoOrderPO orderPO);

    /**
     * 完成订单
     * @param orderPO
     */
    void complete(ProtoOrderPO orderPO);

    /**
     * 取消订单
     * @param orderDTO
     */
    void cancelOrder(ProtoOrderDTO orderDTO);

    /**
     * 取消内购平台预订单
     *
     * @param req
     */
    void preOrderCancel(PreOrderDTO req);

    /**
     * 资金对账 yyyy-MM-dd
     */
    void checkAccount(String dateStr, String mrpType);

    /**
     * 查询内购核算进度
     * @param planCode
     * @return
     */
    List<PlanProgressListDTO> planProgress(String planCode);

    /**
     * 查询内购收益详情
     * @param planCode
     * @return
     */
    List<IncomeProgressDTO> incomeProgress(String planCode);

    /**
     * 同步手续费
     * @param dateStr
     */
    void synChargeAmount(String mrpType, String dateStr);

    /**
     * 保存邮寄信息
     *
     * @param dto
     */
    void saveAssigneeInfo(OrderPostSaveDTO dto);

    /**
     * 更新售后处理状态
     * @param dto
     */
    void submitAfterSaleApply(AfterSaleApplyDTO dto);
}
