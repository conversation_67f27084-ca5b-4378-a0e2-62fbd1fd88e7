package com.mi.oa.asset.mobile.application.service;

import java.util.List;

/**
 * 项目目录
 *
 * <AUTHOR>
 * @date 2022/1/27 9:55
 */
public interface ProtoProjectListBOService {


    /**
     * 根据时间同步项目
     * @param updateTime
     */
    void synProjectListByTime(String updateTime);

    /**
     * 根据项目编码同步项目
     * @param projectCode
     */
    void synProjectListByProjectCode(List<String> projectCode);
    /**
     * 根据项目编码同步项目
     * @param projectCode
     */
    void synEcoAndTvProjectListByProjectCode(List<String> projectCode,String mrpType);
    /**
     * 从一级项目中获取PM
     * @param projectCode
     * @return
     */
    String getPmProjectCode(String projectCode);
}
