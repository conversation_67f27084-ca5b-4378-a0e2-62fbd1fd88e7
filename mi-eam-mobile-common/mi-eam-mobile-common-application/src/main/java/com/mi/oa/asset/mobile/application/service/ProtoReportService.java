package com.mi.oa.asset.mobile.application.service;

import com.mi.oa.asset.mobile.common.enums.RedisCachePrefixKeyEnum;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;

import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * @Desc: 报表
 * @Author: huy
 * @Date: 2022/5/17
 */
public interface ProtoReportService {


    /**
     * 获取更新时间
     */
    String getUpdateTime(RedisCachePrefixKeyEnum prefixKeyEnum);


    void updateReport(RedisCachePrefixKeyEnum lockKey, RedisCachePrefixKeyEnum updateTimeKey, Supplier consumer);

    /**
     * 更新报表
     * @param requestContext
     */
    void updateReportInfo(RequestContext requestContext);

    /**
     * 查询更新时间
     * @param requestContext
     * @return
     */
    Map<String, String> getUpdateTimeInfo(RequestContext requestContext);
}
