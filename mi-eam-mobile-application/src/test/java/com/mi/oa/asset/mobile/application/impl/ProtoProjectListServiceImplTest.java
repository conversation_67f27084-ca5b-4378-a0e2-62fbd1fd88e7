package com.mi.oa.asset.mobile.application.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmProjectVO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectListPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectCfgService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl.ProtoProjectListServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProtoProjectListServiceImplTest {

    @InjectMocks
    private ProtoProjectListServiceImpl protoProjectListService;

    @Mock
    private CommonService commonService;

    @Mock
    private ProtoProjectCfgService protoProjectCfgService;
    @Test
    void getByProjectCode_WhenProjectExists_ShouldReturnProject() {
        // 准备测试数据
        String projectCode = "TEST_PROJECT";
        ProtoProjectListPO expectedProject = new ProtoProjectListPO();
        expectedProject.setProjectCode(projectCode);


        // 由于使用了mybatis-plus的ServiceImpl，我们需要mock父类的getOne方法
        ProtoProjectListServiceImpl spyService = spy(protoProjectListService);
        doReturn(expectedProject).when(spyService).getOne(any(LambdaQueryWrapper.class), eq(false));

        // 执行测试
        ProtoProjectListPO result = spyService.getByProjectCode(projectCode);

        // 验证结果
        assertNotNull(result);
        assertEquals(projectCode, result.getProjectCode());
        verify(spyService).getOne(any(LambdaQueryWrapper.class), eq(false));
    }

    @Test
    void getByProjectCode_WhenProjectNotExists_ShouldReturnNull() {
        // 准备测试数据
        String projectCode = "NON_EXISTENT_PROJECT";

        // mock父类的getOne方法返回null
        ProtoProjectListServiceImpl spyService = spy(protoProjectListService);
        doReturn(null).when(spyService).getOne(any(LambdaQueryWrapper.class), eq(false));

        // 执行测试
        ProtoProjectListPO result = spyService.getByProjectCode(projectCode);

        // 验证结果
        assertNull(result);
        verify(spyService).getOne(any(LambdaQueryWrapper.class), eq(false));
    }

    @Test
    void saveOrUpdateProject_WhenProjectExists_ShouldUpdate2() {
        // 准备测试数据
        String projectCode = "TEST_PROJECT";
        String existingProjectId = "EXISTING_ID";

        ProtoProjectListPO projectListPO = new ProtoProjectListPO();
        projectListPO.setProjectCode(projectCode);

        ProtoProjectListPO existingProject = new ProtoProjectListPO();
        existingProject.setProjectListId(existingProjectId);

        List<MdmProjectVO.TrialProductionFactoryDTO> factoryList = new ArrayList<>();

        // mock getByProjectCodeAndMrpType返回已存在的项目
        ProtoProjectListServiceImpl spyService = spy(protoProjectListService);
        doReturn(existingProject).when(spyService).getByProjectCode(projectCode);
        doReturn(true).when(spyService).saveOrUpdate(any(ProtoProjectListPO.class));

        // 执行测试
        spyService.saveOrUpdateProject(projectListPO, factoryList);

        // 验证结果
        assertEquals(existingProjectId, projectListPO.getProjectListId());
        assertNotNull(projectListPO.getModifyDate());
        verify(spyService).saveOrUpdate(projectListPO);
        verify(protoProjectCfgService).saveOrUpdateCfg(projectListPO, factoryList);
    }

    @Test
    void saveOrUpdateProject_WhenProjectNotExists_ShouldCreate2() {
        // 准备测试数据
        String projectCode = "NEW_PROJECT";
        String newProjectId = "NEW_ID";

        ProtoProjectListPO projectListPO = new ProtoProjectListPO();
        projectListPO.setProjectCode(projectCode);

        List<MdmProjectVO.TrialProductionFactoryDTO> factoryList = new ArrayList<>();

        // mock相关方法
        ProtoProjectListServiceImpl spyService = spy(protoProjectListService);
        doReturn(null).when(spyService).getByProjectCode(projectCode);
        when(commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_PROJECT_LIST)).thenReturn(newProjectId);
        doReturn(true).when(spyService).saveOrUpdate(any(ProtoProjectListPO.class));

        // 执行测试
        spyService.saveOrUpdateProject(projectListPO, factoryList);

        // 验证结果
        assertEquals(newProjectId, projectListPO.getProjectListId());
        assertNotNull(projectListPO.getAddDate());
        assertNotNull(projectListPO.getModifyDate());
        verify(spyService).saveOrUpdate(projectListPO);
        verify(commonService).getUniqueId(RedisUniqueKeyEnum.PROTO_PROJECT_LIST);
        verify(protoProjectCfgService).saveOrUpdateCfg(projectListPO, factoryList);
    }


    @Test
    void getByProjectCodeAndMrpType_WhenProjectExists_ShouldReturnProject() {
        // 准备测试数据
        String projectCode = "TEST_PROJECT";
        String mrpType = "tv";
        ProtoProjectListPO expectedProject = new ProtoProjectListPO();
        expectedProject.setProjectCode(projectCode);
        expectedProject.setMrpType(mrpType);

        // 由于使用了mybatis-plus的ServiceImpl，我们需要mock父类的getOne方法
        ProtoProjectListServiceImpl spyService = spy(protoProjectListService);
        doReturn(expectedProject).when(spyService).getOne(any(LambdaQueryWrapper.class), eq(false));

        // 执行测试
        ProtoProjectListPO result = spyService.getByProjectCodeAndMrpType(projectCode, mrpType);

        // 验证结果
        assertNotNull(result);
        assertEquals(projectCode, result.getProjectCode());
        assertEquals(mrpType, result.getMrpType());
        verify(spyService).getOne(any(LambdaQueryWrapper.class), eq(false));
    }

    @Test
    void getByProjectCodeAndMrpType_WhenProjectNotExists_ShouldReturnNull() {
        // 准备测试数据
        String projectCode = "NON_EXISTENT_PROJECT";
        String mrpType = "tv";

        // mock父类的getOne方法返回null
        ProtoProjectListServiceImpl spyService = spy(protoProjectListService);
        doReturn(null).when(spyService).getOne(any(LambdaQueryWrapper.class), eq(false));

        // 执行测试
        ProtoProjectListPO result = spyService.getByProjectCodeAndMrpType(projectCode, mrpType);

        // 验证结果
        assertNull(result);
        verify(spyService).getOne(any(LambdaQueryWrapper.class), eq(false));
    }
} 