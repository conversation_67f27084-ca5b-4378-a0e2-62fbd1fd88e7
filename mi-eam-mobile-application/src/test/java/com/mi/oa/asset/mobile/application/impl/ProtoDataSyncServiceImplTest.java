package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.infra.dto.upc.UpcProjectDTO;
import com.mi.oa.asset.mobile.infra.dto.upc.UpcProjectDetailDTO;
import com.mi.oa.asset.mobile.infra.remote.sdk.UpcClient;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ProtoDataSyncServiceImplTest {

    @InjectMocks
    private ProtoDataSyncServiceImpl protoDataSyncService;

    @Mock
    private UpcClient upcClient;

    @Test
    void getUpcInfoByProjectCode_Success() {
        // 准备测试数据
        int page = 1;
        String projectCode = "TEST001";
        LocalDateTime dataTime = LocalDateTime.now();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.COMMON_PATTERN);
        
        // 准备模拟返回数据
        UpcProjectDTO expectedResponse = new UpcProjectDTO();
        expectedResponse.setPage(1);
        expectedResponse.setPageSize(500);
        expectedResponse.setTotal(1L);
        List<UpcProjectDetailDTO> detailList = new ArrayList<>();
        UpcProjectDetailDTO detail = new UpcProjectDetailDTO();
        detail.setProjectCode(projectCode);
        detail.setNameCn("测试项目");
        detailList.add(detail);
        expectedResponse.setData(detailList);

        // 设置mock行为
        when(upcClient.skuProjectQuery(any())).thenReturn(expectedResponse);

        // 执行测试
        UpcProjectDTO result = protoDataSyncService.getUpcInfoByProjectCode(page, projectCode, dataTime);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getPage());
        assertEquals(500, result.getPageSize());
        assertEquals(1L, result.getTotal());
        assertEquals(1, result.getData().size());
        assertEquals(projectCode, result.getData().get(0).getProjectCode());
        assertEquals("测试项目", result.getData().get(0).getNameCn());

        // 验证mock调用
        verify(upcClient, times(1)).skuProjectQuery(any());
    }

    @Test
    void getUpcInfoByProjectCode_EmptyResponse() {
        // 准备测试数据
        int page = 1;
        String projectCode = "TEST002";
        LocalDateTime dataTime = LocalDateTime.now();

        // 准备模拟返回数据
        UpcProjectDTO expectedResponse = new UpcProjectDTO();
        expectedResponse.setPage(1);
        expectedResponse.setPageSize(500);
        expectedResponse.setTotal(0L);
        expectedResponse.setData(new ArrayList<>());

        // 设置mock行为
        when(upcClient.skuProjectQuery(any())).thenReturn(expectedResponse);

        // 执行测试
        UpcProjectDTO result = protoDataSyncService.getUpcInfoByProjectCode(page, projectCode, dataTime);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getPage());
        assertEquals(500, result.getPageSize());
        assertEquals(0L, result.getTotal());
        assertEquals(0, result.getData().size());

        // 验证mock调用
        verify(upcClient, times(1)).skuProjectQuery(any());
    }
} 