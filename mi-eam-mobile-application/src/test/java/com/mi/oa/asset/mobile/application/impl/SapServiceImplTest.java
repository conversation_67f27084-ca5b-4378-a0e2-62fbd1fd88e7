package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.MrpTypeEnum;
import com.mi.oa.asset.mobile.common.enums.SapRetEnum;
import com.mi.oa.asset.mobile.infra.dto.sap.*;
import com.mi.oa.asset.mobile.infra.remote.sdk.SapClient;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.MdmService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoSapExeLogService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl.SapServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SapServiceImplTest {

    @InjectMocks
    private SapServiceImpl sapService;

    @Mock
    private SapClient sapClient;

    @Mock
    private ProtoSapExeLogService protoSapExeLogService;

    @Mock
    private MdmService mdmService;

    @Test
    void enginePhoneTransfer_WhenMrpTypeIsMiAutoEacc_ShouldSetCorrectOrganizationAndFactory() {
        // 准备测试数据
        SapPhoneTransferDTO request = new SapPhoneTransferDTO();
        request.setMidocNum("TEST_DOC_001");
        List<SapPhoneTransferDTO.Item> items = new ArrayList<>();
        SapPhoneTransferDTO.Item item = new SapPhoneTransferDTO.Item();
        item.setMaterialOne("TEST_MATERIAL");
        item.setQuantityOne("1");
        items.add(item);
        request.setItems(items);

        String funId = "TEST_FUN";
        String orderId = "TEST_ORDER";
        String orderCode = "TEST_CODE";
        String orderName = "TEST_NAME";
        String doUser = "TEST_USER";
        String mrpType = MrpTypeEnum.MI_AUTOEACC.getType();

        // 模拟返回结果
        when(protoSapExeLogService.insertLog(any(), eq(funId), eq(orderId), eq(orderCode), eq(orderName), eq(doUser)))
                .thenReturn("TEST_LOG_ID");
        
        SapCustomVO mockResponse = new SapCustomVO();
        mockResponse.setSuccess(true);
        mockResponse.setMessage("Success");
        when(sapClient.enginePhoneTransfer(any())).thenReturn(mockResponse);

        // 执行测试
        SapCustomVO result = sapService.enginePhoneTransfer(request, funId, orderId, orderCode, orderName, doUser, mrpType);

        // 验证结果
        verify(sapClient).enginePhoneTransfer(request);
        assertEquals(true, result.getSuccess());
        assertEquals("Success", result.getMessage());
        assertEquals(CommonConstant.I_LIFNR, request.getFoundryCode());
        assertEquals("111N", request.getSapFactory());

        // 验证日志记录
        verify(protoSapExeLogService).insertLog(any(), eq(funId), eq(orderId), eq(orderCode), eq(orderName), eq(doUser));
        verify(protoSapExeLogService).updateLog(eq("TEST_LOG_ID"), eq(SapRetEnum.SUCCESS.getKey()), eq("Success"));
    }

    @Test
    void getProjectInfo_ShouldFilterAndSortResults() {
        // 准备测试数据
        String project = "TEST_PROJECT";
        SapProjectDTO request = new SapProjectDTO();
        request.setProject(project);

        List<SapProjectInfoDTO> mockData = new ArrayList<>();
        
        // 添加应该被保留的数据
        SapProjectInfoDTO item1 = new SapProjectInfoDTO();
        item1.setMatnr("90TEST1");
        item1.setMaktx("Test Desc 1");
        mockData.add(item1);

        SapProjectInfoDTO item2 = new SapProjectInfoDTO();
        item2.setMatnr("52TEST2");
        item2.setMaktx("Test Desc 2"); 
        mockData.add(item2);

        // 添加应该被过滤掉的数据
        SapProjectInfoDTO item3 = new SapProjectInfoDTO();
        item3.setMatnr("88TEST3");
        item3.setMaktx("Test Desc 3");
        mockData.add(item3);

        // 添加95开头的数据(需要特殊处理)
        SapProjectInfoDTO item4 = new SapProjectInfoDTO();
        item4.setMatnr("95TEST4");
        item4.setSkucode("SKU_TEST4");
        item4.setSmaktx("SKU Desc 4");
        mockData.add(item4);

        SapBaseVO<SapProjectInfoDTO> mockResponse = new SapBaseVO<>();
        mockResponse.setSuccess(true);
        mockResponse.setData(mockData);

        when(sapClient.getProjectInfo(any())).thenReturn(mockResponse);

        // 执行测试
        List<SapProjectInfoDTO> result = sapService.getProjectInfo(project);

        // 验证结果
        assertEquals(3, result.size()); // 应该只返回90,52,95开头的数据
        assertEquals("SKU_TEST4", result.get(0).getMatnr()); // 验证排序(90应该在最前)
        assertEquals("52TEST2", result.get(2).getMatnr());

        // 验证调用
        verify(sapClient).getProjectInfo(any(SapProjectDTO.class));
    }

    @Test
    void getProjectInfo_WhenSapCallFails_ShouldReturnEmptyList() {
        // 准备测试数据
        String project = "TEST_PROJECT";
        
        SapBaseVO<SapProjectInfoDTO> mockResponse = new SapBaseVO<>();
        mockResponse.setSuccess(false);
        
        when(sapClient.getProjectInfo(any())).thenReturn(mockResponse);

        // 执行测试
        List<SapProjectInfoDTO> result = sapService.getProjectInfo(project);

        // 验证结果
        assertTrue(result.isEmpty());
        verify(sapClient).getProjectInfo(any(SapProjectDTO.class));
    }
} 