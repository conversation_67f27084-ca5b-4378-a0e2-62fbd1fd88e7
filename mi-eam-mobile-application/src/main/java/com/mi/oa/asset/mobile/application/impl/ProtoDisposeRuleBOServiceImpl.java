package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.service.ProtoDisposeRuleBOService;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDisposeRulePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoDisposeRuleService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.test.annotation.Rollback;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/15
 */
@Service
public class ProtoDisposeRuleBOServiceImpl implements ProtoDisposeRuleBOService {

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProtoDisposeRuleService protoDisposeRuleService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrSubmit(RequestContext requestContext) {
        String keyIds = commonService.commonSave(requestContext);
        List<ProtoDisposeRulePO> ruleList = protoDisposeRuleService.listByIds(Arrays.asList(keyIds.split(",")));
        ruleList  = ruleList.stream().filter(o -> StringUtils.isBlank(o.getOrderCode())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(ruleList)){
            for (ProtoDisposeRulePO rulePO : ruleList){
                String orderCode = "A"+commonService.getAutoIncId(RedisUniqueKeyEnum.PROTO_DISPOSE_RULE,3);
                rulePO.setOrderCode(orderCode);
            }
            protoDisposeRuleService.updateBatchById(ruleList);
        }
    }
}
