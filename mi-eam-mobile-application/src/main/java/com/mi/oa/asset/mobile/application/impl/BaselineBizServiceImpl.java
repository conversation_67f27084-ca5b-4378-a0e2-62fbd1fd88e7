package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.dto.bpm.BpmCallbackDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmProcessCompletedDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmProcessEnum;
import com.mi.oa.asset.mobile.application.dto.bpm.MobileBaselineBizDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.ProtoBaselineRecordBizDTO;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.BaselineBizService;
import com.mi.oa.asset.mobile.application.service.BpmCallbackService;
import com.mi.oa.asset.mobile.application.service.BpmService;
import com.mi.oa.asset.mobile.application.service.BpmV3Service;
import com.mi.oa.asset.mobile.common.enums.BaselineChangeTypeEnum;
import com.mi.oa.asset.mobile.common.enums.BaselineRecordAuditEnum;
import com.mi.oa.asset.mobile.common.enums.BaselineStateEnum;
import com.mi.oa.asset.mobile.common.enums.BmpV3ProcessEnum;
import com.mi.oa.asset.mobile.common.enums.CardStateEnum;
import com.mi.oa.asset.mobile.common.enums.ErrorCodeEnum;
import com.mi.oa.asset.mobile.common.enums.InStatusEnum;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.common.enums.TranAuditEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoBaselinePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoBaselineRecordDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoBaselineRecordPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.BpmExeLogService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoBaselineRecordDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoBaselineRecordService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoBaselineService;
import com.mi.oa.infra.oaucf.bpm.enums.ProcessInstanceStatus;
import com.mi.oa.infra.oaucf.bpm.enums.UserTaskOperation;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2022/3/1 16:12
 */
@Slf4j
@Service
public class BaselineBizServiceImpl extends BpmCallbackService implements BaselineBizService {
    private static final BigDecimal BASELINE_CODE_SERNO = new BigDecimal(1);
    @Autowired
    private ProtoBaselineRecordService protoBaselineRecordService;
    @Autowired
    private ProtoBaselineRecordDetService protoBaselineRecordDetService;
    @Autowired
    private ProtoBaselineService protoBaselineService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private BpmService bpmService;
    @Autowired
    private BpmV3Service bpmV3Service;
    @Autowired
    private BpmExeLogService bpmExeLogService;

    private static final int CODE_MAX_LENGTH = 9;
    private static final String FUNID = "proto_baseline_record";

    private static final String ORDER_NAME = "工程机公共基线-BPM回调";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submit(String keyId) {
        ProtoBaselineRecordPO recordPO = protoBaselineRecordService.getById(keyId);
        if(ObjectUtils.isEmpty(recordPO)){
            throw new BizException(ApplicationErrorCodeEnum.APPLICATION_UNKNOWN_ERROR);
        }
        if(!BaselineRecordAuditEnum.NOT_SUBMIT.getKey().equals(recordPO.getAuditing())){
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR,"只能提交‘未提交’状态的单");
        }
        List<ProtoBaselineRecordDetPO> dets = protoBaselineRecordDetService.findListByParam(ProtoBaselineRecordDetPO.builder().baselineRecordId(recordPO.getBaselineRecordId()).build());
        //校验变更明细清单数据格式
        valSaveRecordDet(dets);
        Optional<ProtoBaselineRecordDetPO> codeCol = dets.stream().filter(det->BASELINE_CODE_SERNO.compareTo(det.getBaselineTypeSerno())==0).findFirst();
        if(!codeCol.isPresent()){
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR,"找不到明细数据");
        }
        String baselineCode =  codeCol.get().getBaselineUpdAfter();
        if(!BaselineChangeTypeEnum.INSERT.getKey().equals(recordPO.getChangeType())){
            ProtoBaselinePO baseline = protoBaselineService.findOneByParam(ProtoBaselinePO.builder().baselineCode(baselineCode).build());
            if(ObjectUtils.isEmpty(baseline)||!BaselineStateEnum.WORKING.getKey().equals(baseline.getState())){
                throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR,"找不到基线数据或者状态不是生效中");
            }
            if(BaselineRecordDetSernoEnum.isChanged(dets, baseline)){
                throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR,"当前基线信息已发生变化");
            }
        }
        log.info("pushing bpm");
        // 推送BPM
        String businessKey = pushBpmV3(dets, recordPO);

        //修改为单据审批中，
        log.info("update record state");
        recordPO.setAuditing(BaselineRecordAuditEnum.CHECKING.getKey());
        recordPO.setModifyDate(new Date());
        recordPO.setApplyDate(new Date());
        recordPO.setBusinessKey(businessKey);
        protoBaselineRecordService.updateById(recordPO);

        log.info("update baseline state");
        if(!BaselineChangeTypeEnum.INSERT.getKey().equals(recordPO.getChangeType())){
            protoBaselineService.updateBaselineStateByCode(baselineCode, BaselineStateEnum.UPDATING);

            log.info("invalidate other change order");
            invalidateOldChangeOrder(baselineCode);
        }

    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void interruptBpm(String keyId,String startCode) {
        ProtoBaselineRecordPO record = protoBaselineRecordService.findOneByParam(ProtoBaselineRecordPO.builder().baselineRecordId(keyId).build());
        if(ObjectUtils.isEmpty(record)){
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR,"找不到基线变更记录， baselineRecordId:"+keyId);
        }
        if(!BaselineRecordAuditEnum.CHECKING.getKey().equals(record.getAuditing())){
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR,"仅审批中的单可以撤回");
        }
        // 撤回Bpm流程
        log.info("撤回Bpm流程...");
        try {
            bpmV3Service.terminate(record.getBusinessKey(), startCode);
        } catch (Exception e) {
            log.error("撤回Bpm流程失败！");
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_007);
        }
        updateAfterInterrupt(record, BaselineStateEnum.WORKING, BaselineRecordAuditEnum.NOT_SUBMIT);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bpmCallbackV3(BpmProcessCompletedDTO param) {
        if (StringUtils.isBlank(param.getBusinessKey())) {
            log.error("回调businessKey为空！");
            throw new BizException(ErrorCodeEnum.PARAM_IS_NULL_ERROR, "回调businessKey为空！");
        }
        if (Objects.isNull(param.getFormData())) {
            log.warn(BmpV3ProcessEnum.PROTO_BASELINE_APPLY.getProcessInstanceName(),"回调无表单数据");
            bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, null, null,
                    null, false, "param参数为空", param);
        }
        // 获取business_key
        String businessKey = param.getBusinessKey();
        ProtoBaselineRecordPO record = protoBaselineRecordService.findOneByParam(ProtoBaselineRecordPO.builder().businessKey(businessKey).build());
        if (ObjectUtils.isEmpty(record)) {
            bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, null, null,
                    null, false, "业务数据找不到business_key【" + businessKey + "】", param);
            return true;
        }

        // 获取业务参数
        String keyId = record.getBaselineRecordId();
        String code = record.getBaselineCode();
        String applyUserName = record.getApplyUserName();

        // 流程结束事件
        if (param.getProcessInstanceStatus() == ProcessInstanceStatus.COMPLETED) {
            String auditing = record.getAuditing();
            // 检测流程是否已审批
            if (TranAuditEnum.CHECK_OK.getKey().equals(auditing)) {
                return true;
            }
            if(BaselineChangeTypeEnum.INSERT.getKey().equals(record.getChangeType())){
                ProtoBaselinePO baselinePO = createBaseline(record.getBaselineRecordId(),record.getAddUserid());
                if(!ObjectUtils.isEmpty(baselinePO)){
                    record.setBaselineCode(baselinePO.getBaselineCode());
                }
            }else if(BaselineChangeTypeEnum.DELETE.getKey().equals(record.getChangeType())){
                updateBaselineWithDet(record, BaselineStateEnum.DELETED);
            } else if(BaselineChangeTypeEnum.UPDATE.getKey().equals(record.getChangeType())){
                updateBaselineWithDet(record, BaselineStateEnum.WORKING);
            }
            record.setModifyDate(new Date());
            record.setAuditing(BaselineRecordAuditEnum.FINISHED.getKey());
            protoBaselineRecordService.updateById(record);
            bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, keyId, code, applyUserName, true, "工程机公共基线BPM审批完成！", param);
        }

        // 流程终止（撤回）
        if (param.getProcessInstanceStatus() == ProcessInstanceStatus.TERMINATED) {
            updateAfterInterrupt(record, BaselineStateEnum.WORKING, BaselineRecordAuditEnum.NOT_SUBMIT);
            bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, keyId, code, applyUserName, true, "工程机公共基线BPM审批被终止（撤回）！", param);
        }

        // 流程审批事件
        String status = param.getVariables().get("status").toString();
        if (StringUtils.isNotBlank(status)) {
            UserTaskOperation userTaskOperation = UserTaskOperation.findByCode(status);
            switch (userTaskOperation) {
                case REJECT:
                    updateAfterInterrupt(record, BaselineStateEnum.WORKING, BaselineRecordAuditEnum.INVALID);
                    bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, keyId, code, applyUserName, true, "工程机公共基线BPM审批被拒绝！", param);
                    break;
                default:
                    log.debug("其他状态不处理...");
            }
        }

        log.debug("========处理工程机公共基线BPM回调事件结束");
        return true;
    }

    private void invalidateOldChangeOrder(String baselineCode) {
        if(StringUtils.isEmpty(baselineCode)){
            return;
        }
        List<ProtoBaselineRecordPO> recordList = protoBaselineRecordService.findAllByParam(ProtoBaselineRecordPO.builder()
                .baselineCode(baselineCode).auditing(BaselineRecordAuditEnum.NOT_SUBMIT.getKey()).build());
        recordList.forEach(t->{
            t.setAuditing(BaselineRecordAuditEnum.CLOSED.getKey());
            t.setModifyDate(new Date());
        });
        protoBaselineRecordService.updateBatchById(recordList);
    }


    private String pushBpmV3(List<ProtoBaselineRecordDetPO> dets, ProtoBaselineRecordPO recordPO){
        // 推送BPM
        List<ProtoBaselineRecordBizDTO.Item> bpmItems = dets.stream().map(det -> {
            BaselineChangeTypeEnum typeT = BaselineChangeTypeEnum.getChangeType(det.getBaselineUpdType());
            return ProtoBaselineRecordBizDTO.Item.builder()
                    .prop(BaselineRecordDetSernoEnum.getBaselineRecordDetSernoEnum(det.getBaselineTypeSerno().intValue()).getPropName())
                    .updAfter(det.getBaselineUpdAfter())
                    .updBefore(det.getBaselineUpdBefore())
                    .updType(typeT != null ? typeT.getDesc() : "")
                    .build();
        }).collect(Collectors.toList());
        BaselineChangeTypeEnum typeEnum = BaselineChangeTypeEnum.getChangeType(recordPO.getChangeType());
        //构建bpmV3 DTO对象
        ProtoBaselineRecordBizDTO bpmDto = ProtoBaselineRecordBizDTO.builder()
                .baselineCode(getDetVal(dets,BaselineRecordDetSernoEnum.BASELINE_CODE,false))
                .applyCode(recordPO.getApplyCode())
                .applyUser(recordPO.getApplyUserName()+"("+recordPO.getApplyUserCode()+")")
                .deptName(recordPO.getDeptName())
                .changeType(typeEnum!=null?typeEnum.getDesc():"")
                .remark(getDetVal(dets,BaselineRecordDetSernoEnum.REMARK,false))
                .items(bpmItems)
                .build();
        //业务系统传入 流程控制参数
        Map<String, Object> variable = new HashMap<>();
        variable.put("gm", recordPO.getApplyUserManagerCode());

        return bpmV3Service.submitBaselineApply(bpmDto,recordPO.getApplyUserCode(),variable);
    }

    @Deprecated
    private String pushBpm(List<ProtoBaselineRecordDetPO> dets, ProtoBaselineRecordPO recordPO){
        // 推送BPM
        List<MobileBaselineBizDTO.Item> bpmItems = dets.stream().map(det-> {
            BaselineChangeTypeEnum typeT = BaselineChangeTypeEnum.getChangeType(det.getBaselineUpdType());
            return MobileBaselineBizDTO.Item.builder()
                .prop(BaselineRecordDetSernoEnum.getBaselineRecordDetSernoEnum(det.getBaselineTypeSerno().intValue()).getPropName())
                .updAfter(det.getBaselineUpdAfter())
                .updBefore(det.getBaselineUpdBefore())
                .updType(typeT!=null?typeT.getDesc():"")
                .build();
        }).collect(Collectors.toList());
        BaselineChangeTypeEnum typeEnum = BaselineChangeTypeEnum.getChangeType(recordPO.getChangeType());
        MobileBaselineBizDTO bpmDto = MobileBaselineBizDTO.builder().baselineCode(getDetVal(dets,BaselineRecordDetSernoEnum.BASELINE_CODE,false)).applyCode(recordPO.getApplyCode())
                .applyUser(recordPO.getApplyUserName()+"("+recordPO.getApplyUserCode()+")")
                .deptName(recordPO.getDeptName()).changeType(typeEnum!=null?typeEnum.getDesc():"")
                .remark(getDetVal(dets,BaselineRecordDetSernoEnum.REMARK,false)).items(bpmItems)
                .build();
        Map<String, Object> variable = new HashMap<>();
        variable.put("gm", recordPO.getApplyUserManagerCode());
        return bpmService.bpmSubmitV2(BpmProcessEnum.EAM_MOBILE_PUBBASE.getProcessKey(), recordPO.getApplyUserCode()
                ,String.format(BpmProcessEnum.EAM_MOBILE_PUBBASE.getTitle(), typeEnum!=null?typeEnum.getDesc():""),bpmDto,variable);
    }

    private String getDetVal(List<ProtoBaselineRecordDetPO> detList, BaselineRecordDetSernoEnum sernoEnum, boolean isBefore){
        if(!CollectionUtils.isEmpty(detList)){
            return detList.stream().filter(det-> sernoEnum
                            .equals(BaselineRecordDetSernoEnum.getBaselineRecordDetSernoEnum(det.getBaselineTypeSerno().intValue())))
                    .findFirst().map(isBefore?ProtoBaselineRecordDetPO::getBaselineUpdBefore:ProtoBaselineRecordDetPO::getBaselineUpdAfter).orElse(null);
        }else {
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bpmEnd(BpmCallbackDTO bpmCallbackDTO) {
        String businessKey = bpmCallbackDTO.getBusinessKey();
        log.info("bpmEnd---------------businessKey: {}",businessKey);
        ProtoBaselineRecordPO record = protoBaselineRecordService.findOneByParam(ProtoBaselineRecordPO.builder().businessKey(businessKey).build());
        if(ObjectUtils.isEmpty(record)){
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR,"cannot find record by businessKey:"+businessKey);
        }
        if(BaselineChangeTypeEnum.INSERT.getKey().equals(record.getChangeType())){
            ProtoBaselinePO baselinePO = createBaseline(record.getBaselineRecordId(),record.getAddUserid());
            if(!ObjectUtils.isEmpty(baselinePO)){
                record.setBaselineCode(baselinePO.getBaselineCode());
            }
        }else if(BaselineChangeTypeEnum.DELETE.getKey().equals(record.getChangeType())){
            updateBaselineWithDet(record, BaselineStateEnum.DELETED);
        } else if(BaselineChangeTypeEnum.UPDATE.getKey().equals(record.getChangeType())){
            updateBaselineWithDet(record, BaselineStateEnum.WORKING);
        }

        record.setModifyDate(new Date());
        record.setAuditing(BaselineRecordAuditEnum.FINISHED.getKey());
        protoBaselineRecordService.updateById(record);

    }

    private void updateBaselineWithDet(ProtoBaselineRecordPO record, BaselineStateEnum stateEnum){
        ProtoBaselinePO baseline = protoBaselineService.findOneByParam(ProtoBaselinePO.builder().baselineCode(record.getBaselineCode()).build());
        List<ProtoBaselineRecordDetPO> detList = protoBaselineRecordDetService.findListByParam(ProtoBaselineRecordDetPO.builder()
                .baselineRecordId(record.getBaselineRecordId()).build());
        detList.forEach(det->{
            BaselineRecordDetSernoEnum.setBaselinePropVal(det, baseline);
        });
        baseline.setState(stateEnum.getKey());
        baseline.setModifyDate(new Date());
        protoBaselineService.updateById(baseline);
    }

    public ProtoBaselinePO createBaseline(String recordId,String addUserId) {
        List<ProtoBaselineRecordDetPO> detList = protoBaselineRecordDetService.findListByParam(ProtoBaselineRecordDetPO.builder()
                .baselineRecordId(recordId).build());
        if(!CollectionUtils.isEmpty(detList)){
            String uqCode = "JXM"+commonService.getAutoIncId(RedisUniqueKeyEnum.PROTO_BASELINE,CODE_MAX_LENGTH);
            ProtoBaselinePO baselinePO = ProtoBaselinePO.builder().addDate(new Date()).addUserid(addUserId).build();
            detList.forEach(det->{
                BaselineRecordDetSernoEnum.setBaselinePropVal(det, baselinePO);
            });
            baselinePO.setBaselineCode(uqCode);
            baselinePO.setBaselineId(uqCode);
            baselinePO.setState(BaselineStateEnum.WORKING.getKey());
            protoBaselineService.save(baselinePO);
            return baselinePO;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bpmReject(BpmCallbackDTO bpmCallbackDTO) {
        String businessKey = bpmCallbackDTO.getBusinessKey();
        log.info("bpmReject---------------businessKey: {}",businessKey);
        ProtoBaselineRecordPO record = protoBaselineRecordService.findOneByParam(ProtoBaselineRecordPO.builder().businessKey(businessKey).build());
        if(ObjectUtils.isEmpty(record)){
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR,"cannot find record by businessKey:"+businessKey);
        }
        updateAfterInterrupt(record, BaselineStateEnum.WORKING, BaselineRecordAuditEnum.INVALID);
    }

    private void updateAfterInterrupt(ProtoBaselineRecordPO record, BaselineStateEnum baselineStateEnum, BaselineRecordAuditEnum auditEnum){
        if(!BaselineChangeTypeEnum.INSERT.getKey().equals(record.getChangeType())){
            protoBaselineService.updateBaselineStateByRecordId(record.getBaselineRecordId(), baselineStateEnum);
        }

        record.setModifyDate(new Date());
        record.setAuditing(auditEnum.getKey());
        protoBaselineRecordService.updateById(record);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String bpmInterrupt(String recordId) {
        log.info("bpmInterrupt---------------baselineRecordId: {}",recordId);
        ProtoBaselineRecordPO record = protoBaselineRecordService.findOneByParam(ProtoBaselineRecordPO.builder().baselineRecordId(recordId).build());
        if(ObjectUtils.isEmpty(record)){
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR,"找不到基线变更记录， baselineRecordId:"+recordId);
        }
        if(!BaselineRecordAuditEnum.CHECKING.getKey().equals(record.getAuditing())){
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR,"仅审批中的单可以撤回");
        }
        updateAfterInterrupt(record, BaselineStateEnum.WORKING, BaselineRecordAuditEnum.NOT_SUBMIT);
        return record.getBusinessKey();
    }

    /**
     * TODO
     *
     * <AUTHOR>
     * @date 2022/3/7 14:55
     */
    @Getter
    public enum BaselineRecordDetSernoEnum {
        /**
         *
         */
        BASELINE_CODE(1, "基线编号", (t,u)->u.setBaselineCode(t.getBaselineUpdAfter()), (t,u)->u.getBaselineCode().equals(t.getBaselineUpdBefore())),
        BASELINE_SCOPE(2,"基线范围", (t,u)->u.setScope(t.getBaselineUpdAfter()),(t,u)->u.getScope().equals(t.getBaselineUpdBefore())),
        PROJECT_TYPE(3,"项目类型", (t,u)->u.setProjectType(t.getBaselineUpdAfter()),(t,u)->u.getProjectType().equals(t.getBaselineUpdBefore())),
        SERIES(4,"系列", (t,u)->u.setSeries(t.getBaselineUpdAfter()),(t,u)->u.getSeries().equals(t.getBaselineUpdBefore())),
        REMARK(5,"基线备注", (t,u)->u.setRemark(t.getBaselineUpdAfter()),(t,u)->u.getRemark().equals(t.getBaselineUpdBefore())),
        CP_NAME(6,"基线接口人", (t,u)->{u.setCpName(t.getBaselineUpdAfter());u.setCpCode(t.getBaselineAssist());},(t,u)->u.getCpName().equals(t.getBaselineUpdBefore())),
        CP_DEPT_NAME(7,"部门全名称", (t,u)->{u.setCpDeptName(t.getBaselineUpdAfter());u.setCpDeptCode(t.getBaselineAssist());},(t,u)->u.getCpDeptName().equals(t.getBaselineUpdBefore())),
        P00(8,"P00", (t,u)->u.setP00(new BigDecimal(t.getBaselineUpdAfter())),(t,u)->u.getP00().compareTo(new BigDecimal(t.getBaselineUpdBefore()))==0),
        P10(9,"P10", (t,u)->u.setP10(new BigDecimal(t.getBaselineUpdAfter())),(t,u)->u.getP10().compareTo(new BigDecimal(t.getBaselineUpdBefore()))==0),
        P20(10,"P20", (t,u)->u.setP20(new BigDecimal(t.getBaselineUpdAfter())),(t,u)->u.getP20().compareTo(new BigDecimal(t.getBaselineUpdBefore()))==0),
        P30(11,"P30", (t,u)->u.setP30(new BigDecimal(t.getBaselineUpdAfter())),(t,u)->u.getP30().compareTo(new BigDecimal(t.getBaselineUpdBefore()))==0),
        TOTAL(12,"总数量", (t,u)->u.setTotal(new BigDecimal(t.getBaselineUpdAfter())),(t,u)->u.getTotal().compareTo(new BigDecimal(t.getBaselineUpdBefore()))==0),
        ;
        private Integer serno;
        private String propName;
        private BiConsumer<ProtoBaselineRecordDetPO,ProtoBaselinePO> baselineProp;
        private BiFunction<ProtoBaselineRecordDetPO,ProtoBaselinePO,Boolean> eqFunc;

        BaselineRecordDetSernoEnum(int serno, String propName, BiConsumer<ProtoBaselineRecordDetPO,ProtoBaselinePO> baselineProp,
                                   BiFunction<ProtoBaselineRecordDetPO,ProtoBaselinePO,Boolean> eqFunc) {
            this.serno = serno;
            this.propName = propName;
            this.baselineProp = baselineProp;
            this.eqFunc = eqFunc;
        }

        public static BaselineRecordDetSernoEnum getBaselineRecordDetSernoEnum(Integer serno){
            for (BaselineRecordDetSernoEnum sernoEnum : BaselineRecordDetSernoEnum.values()){
                if(sernoEnum.getSerno().equals(serno)){
                    return sernoEnum;
                }
            }
            return null;
        }

        public static void setBaselinePropVal(ProtoBaselineRecordDetPO detPO, ProtoBaselinePO baselinePO){
            for (BaselineRecordDetSernoEnum sernoEnum : BaselineRecordDetSernoEnum.values()){
                if(sernoEnum.getSerno()==detPO.getBaselineTypeSerno().intValue()){
                    sernoEnum.getBaselineProp().accept(detPO, baselinePO);
                }
            }
        }

        public static boolean isChanged(@NotNull List<ProtoBaselineRecordDetPO> detList, @NotNull ProtoBaselinePO baselinePO){
            for(ProtoBaselineRecordDetPO detPO:detList){
                for (BaselineRecordDetSernoEnum sernoEnum : BaselineRecordDetSernoEnum.values()){
                    if(sernoEnum.getSerno()==detPO.getBaselineTypeSerno().intValue() && !sernoEnum.getEqFunc().apply(detPO,baselinePO)){
                        return true;
                    }
                }
            }
            return false;
        }
    }

    //校验变更明细清单数据格式
    public void valSaveRecordDet(List<ProtoBaselineRecordDetPO> recDetList) {
        if (!CollectionUtils.isEmpty(recDetList)) {
            for (ProtoBaselineRecordDetPO detPo : recDetList) {
                int serno = Integer.parseInt(detPo.getBaselineTypeSerno().toString());
                String baselineType = detPo.getBaselineType();
                String baselineUpdAfter = detPo.getBaselineUpdAfter();
                if (serno != 1) {
                    if (StringUtils.isBlank(baselineUpdAfter)) {
                        throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, baselineType + "调整后的内容不能空!");
                    }

                    if (serno > 7) {
                        String reg = "^\\+?[1-9][0-9]*$";
                        if (!"0".equals(baselineUpdAfter) && !baselineUpdAfter.matches(reg)) {
                            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, baselineType + "调整后的内容不是有效数字!");
                        }
                        if (serno == 12) {
                            if ("0".equals(baselineUpdAfter) && "1".equals(detPo.getBaselineUpdType())) {
                                throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, "新增基线总数量不能为0!");
                            }
                        }
                    }
                }
            }
        }
    }


}
