package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.service.ProtoDataSyncService;
import com.mi.oa.asset.mobile.application.service.ProtoProjectListBOService;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.MdmProjectBusinessLineEnum;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmBaseVO;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmProjectVO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectListPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.MdmService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectListService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.UserInfoService;
import com.mi.oa.infra.oaucf.idm.api.rep.UserBaseInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 项目目录
 *
 * <AUTHOR>
 * @date 2022/1/27 9:57
 */
@Service
@Slf4j
public class ProtoProjectListBOServiceImpl implements ProtoProjectListBOService {

    @Autowired
    private MdmService mdmService;

    @Autowired
    private ProtoProjectListService protoProjectListService;

    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private ProtoDataSyncService protoDataSyncService;

    @Override
    public void synProjectListByTime(String updateTime) {
        Integer pageNum = 1;
        while (true) {
            MdmBaseVO<MdmProjectVO> project = mdmService.getProject(null, updateTime, pageNum, 200);
            log.info("synProjectListByTime response:{}", project);
            this.dealProject(project);
            if (project.getList().size() < 200) {
                return;
            }
            pageNum++;
        }

    }

    @Override
    @Async
    public void synProjectListByProjectCode(List<String> projectCodeList) {
        for (String projectCode : projectCodeList){
            if (StringUtils.isEmpty(projectCode)) {
                continue;
            }
            MdmBaseVO<MdmProjectVO> project = mdmService.getProject(projectCode, null, 1, 200);
            log.info("synProjectListByProjectCode response:{}", project);
            this.dealProject(project);
        }
    }

    @Override
    public void synEcoAndTvProjectListByProjectCode(List<String> projectCodeList,String mrpType) {
        for (String projectCode : projectCodeList){
            if (StringUtils.isEmpty(projectCode)) {
                continue;
            }
            protoDataSyncService.dealProject(projectCode,mrpType);
        }
    }

    @Override
    public String getPmProjectCode(String projectCode) {
        if (StringUtils.isEmpty(projectCode)) {
            return null;
        }
        // 去掉缓存
//        String cacheKey = RedisCachePrefixKeyEnum.MDM_PROJECT_PROJECT_PM.getPrefixKey()+projectCode;
//        Object obj = RedisUtils.get(cacheKey);
//        if (null != obj){
//            return String.valueOf(obj);
//        }
        MdmBaseVO<MdmProjectVO> project = mdmService.getProject(projectCode, null, 1, 200);
        String result = "";
        for (MdmProjectVO projectVO : project.getList()) {
            List<MdmProjectVO.TeamDTO> mdmTeamList = projectVO.getMdmTeam();
            if (!CollectionUtils.isEmpty(mdmTeamList)) {
                for (MdmProjectVO.TeamDTO teamDTO : mdmTeamList) {
                    String roleName = teamDTO.getRoleKey();
                    if (CommonConstant.MDM_PM_ROLE_KEY.equals(roleName)) {
                        String principals = teamDTO.getPrincipals();
                        if (!StringUtils.isEmpty(principals)) {
                            result = principals.split(",")[0];
//                            RedisUtils.setEx(cacheKey, result, 30, TimeUnit.MINUTES);
                            break;
                        }
                    }
                }
            }
        }
        return result;
    }

    private void dealProject(MdmBaseVO<MdmProjectVO> project) {
        List<MdmProjectVO> list = project.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        for (MdmProjectVO projectVO : list) {
            if (StringUtils.isEmpty(projectVO.getProjectName())) {
                continue;
            }
            log.info("deal_project_info project_code:{}, projectVO:{}", projectVO.getProjectName(), projectVO);
            MdmProjectBusinessLineEnum lineEnum = MdmProjectBusinessLineEnum.valuesOf(projectVO.getBusinessLine());
            if (lineEnum == null) {
                continue;
            }
            String mrpType = lineEnum.getMrpType();
            // 只同步R平台数据
            if (!CommonConstant.R_PLATFORM.equals(projectVO.getProjectSourceSystem())) {
                continue;
            }
            // 只同步二级项目
            if (StringUtils.isEmpty(projectVO.getParentProjectID())) {
                continue;
            }
            // 从父级项目中获取PM账号
            String pmUser = this.getPmProjectCode(projectVO.getParentProjectName());
            UserBaseInfoDto userInfo = userInfoService.getNullCacheUserInfoByUserName(pmUser);
            // 产品系列
            String productSeries = "";
            List<MdmProjectVO.ProductParameterDTO> productParameters = projectVO.getProductParameters();
            if (!CollectionUtils.isEmpty(productParameters)) {
                for (MdmProjectVO.ProductParameterDTO parameterDTO : productParameters) {
                    MdmProjectVO.ParameterDTO parameters = parameterDTO.getProductParameters();
                    if (null != parameters && CommonConstant.PRODUCT_SERIES_KEY.equals(parameters.getCode())) {
                        productSeries = parameterDTO.getParameterDesc();
                        break;
                    }
                }
            }
            ProtoProjectListPO projectListPO = new ProtoProjectListPO();
            projectListPO.setProjectCode(projectVO.getProjectName());
            projectListPO.setProjectName(projectVO.getProjectNameCN());
            projectListPO.setProjectType(mrpType);
            projectListPO.setMrpType(mrpType);
            projectListPO.setAuditing(CommonConstant.RecordStatus.COMMITTED.getStatus());
            projectListPO.setOriProjectId(projectVO.getProjectOID());
            projectListPO.setType(projectVO.getProjectType());
            projectListPO.setProjectLevel(projectVO.getProjectLevel());
            projectListPO.setParentCode(projectVO.getParentProjectName());
            projectListPO.setType(projectVO.getProjectType());
            projectListPO.setProjectLevel(projectVO.getProjectLevel());
            projectListPO.setProductType(projectVO.getProductType());
            projectListPO.setProductSeries(productSeries);
            projectListPO.setPmUserCode(pmUser);
            projectListPO.setPmUserName(userInfo.getName());
            projectListPO.setPmEmpCode(userInfo.getPersonId());
            protoProjectListService.saveOrUpdateProject(projectListPO, projectVO.getTrialProductionFactory());
        }
    }

}
