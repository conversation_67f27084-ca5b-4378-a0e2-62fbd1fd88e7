package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.dto.ApplyResp;
import com.mi.oa.asset.mobile.application.dto.AssetCommonDTO;
import com.mi.oa.asset.mobile.application.dto.CommonConditionDTO;
import com.mi.oa.asset.mobile.application.dto.ConditionDTO;
import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.dto.secondcard.ApplySecondDTO;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.ProtoSecondCardBOService;
import com.mi.oa.asset.mobile.common.enums.CardStateEnum;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.MrpTypeEnum;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.common.enums.TranAuditEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.asset.mobile.utils.MapUtil;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.idm.api.rep.UserBaseInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/4 14:13
 */
@Service
@Slf4j
public class ProtoSecondCardBOServiceImpl implements ProtoSecondCardBOService {

    @Autowired
    private ProtoCardService protoCardService;
    @Autowired
    private ProtoSecondCardService protoSecondCardService;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private ProtoApplyService protoApplyService;
    @Autowired
    private ProtoApplyMatService applyMatService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private SysUserRoleService sysUserRoleService;
    @Autowired
    private SysDeptService sysDeptService;
    @Autowired
    private ProtoHouseService protoHouseService;

    private static final String APPLY_SECOND_PRFIX = "SQ";
    private static final String HOUSE_CODE = "house_code";
    private static final String HOUSE_NAME = "house_name";


    /**
     * 返回二手库清单到前端
     *
     * @return
     */
    @Override
    public AssetCommonDTO query(CommonConditionDTO requestParam, String userId, String funId) {
        List<ConditionDTO> conditionList = commonService.getConditionList(requestParam);
        List<String> mrpTypeList = sysUserRoleService.getMrpType(userId, funId);
        Integer start = requestParam.getStart();
        Integer limit = requestParam.getLimit();
        List<Map<String, Object>> oneList = protoCardService.findSecondNumList(conditionList, mrpTypeList);
        // proto_second_card表没有goods_id字段，过滤掉goods_id查询条件
        List<ConditionDTO> collect = conditionList.stream().filter(conditionDTO -> !"goods_id".equals(conditionDTO.getNames())).collect(Collectors.toList());
        List<Map<String, Object>> secList = protoSecondCardService.findSecondNoGoodNumList(collect, mrpTypeList);
        List<Map<String, Object>> allList = new ArrayList<>(oneList.size() + secList.size());
        allList.addAll(oneList);
        allList.addAll(secList);
        //过滤手机良品数据 二手库清单生成逻辑优化
        allList = allList.stream().filter(o -> !(MrpTypeEnum.MOBILE.getType().equals(MapUtil.getValue(o,"mrp_type")) && CommonConstant.ProtoCardIsComplete.GOOD.getType().equals(MapUtil.getValue(o,"is_complete")))).collect(Collectors.toList());
        List<String> houseCodeList = allList.stream().map(o -> String.valueOf(o.get(HOUSE_CODE))).distinct().collect(Collectors.toList());
        List<ProtoHousePO> houseList = protoHouseService.listByHouseCode(houseCodeList);
        Map<String, String> houseMap = houseList.stream().collect(Collectors.toMap(ProtoHousePO::getHouseCode, ProtoHousePO::getHouseName));
        for (Map<String, Object> tempMap : allList) {
            String houseName = houseMap.get(tempMap.get(HOUSE_CODE));
            if (StringUtils.isNotBlank(houseName)) {
                tempMap.put(HOUSE_NAME, houseName);
            }
        }
        if (allList.size() >= (start + limit)) {
            return AssetCommonDTO.builder().total((long) allList.size()).root(allList.subList(start, start + limit)).build();
        } else if (!CollectionUtils.isEmpty(allList) && allList.size() > start) {
            return AssetCommonDTO.builder().total((long) allList.size()).root(allList.subList(start, allList.size())).build();
        } else {
            return AssetCommonDTO.builder().total(0L).root(new ArrayList<>()).build();
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ApplyResp createApplySecond(ApplySecondDTO applySecondDto, UserInfo userInfo) {
        UserBaseInfoDto info = userInfoService.getCacheUserInfoByUserName(userInfo.getUserCode());
        if (null == info) {
            //找不到当前操作人信息，请联系系统管理员！  ,JsMessage.getValue("proto_apply.ht_text026")
            throw new BizException(ApplicationErrorCodeEnum.PROTO_APPLY_026);
        }
        if (StringUtils.isBlank(applySecondDto.getHouseName())
                && StringUtils.isNotBlank(applySecondDto.getProjectCode())) {
            //
            applySecondDto.setHouseName(protoHouseService.getHoseNameByCode(applySecondDto.getHouseCode()));
        }

        userInfo.setEmpCode(info.getPersonId());
        userInfo.setDeptName(info.getDeptDescr());
        SysDeptPO sysDeptPO = sysDeptService.getCacheByDeptId(info.getDeptId());
        if (null != sysDeptPO) {
            userInfo.setDeptName(sysDeptPO.getLongDeptName());
            userInfo.setTreeDeptCode(sysDeptPO.getTreeCode());
        }

        String applyId = createApplySecond(userInfo, applySecondDto.getProjectCode()
                , applySecondDto.getStageName(), applySecondDto.getSkuCode(), applySecondDto.getSkuName(),
                applySecondDto.getIsComplete(), applySecondDto.getMrpType(), applySecondDto.getHouseCode()
                , applySecondDto.getHouseName(), applySecondDto.getGoodsId());

        return new ApplyResp(CommonConstant.TRUE, applyId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createData(ApplySecondDTO applySecondDto, UserInfo userInfo) {
        String projectCode = applySecondDto.getProjectCode();
        String skuCode = applySecondDto.getSkuCode();
        String stageName = (StringUtils.isBlank(applySecondDto.getStageName()) ? null : applySecondDto.getStageName());
        String houseCode = applySecondDto.getHouseCode();

        ProtoCardPO po = ProtoCardPO.builder().stageName(stageName)
                .skuCode(skuCode).houseCode(houseCode).useState(CardStateEnum.IN_LIBRARY.getKey())
                .assetNewold(CommonConstant.ProtoCardAssetNewOld.SECONDHAND.getType())
                .isComplete(CommonConstant.ProtoCardIsComplete.NO_GOOD.getType()).build();

        if (StringUtils.isNotBlank(projectCode)) {
            //电视类型物料的项目信息可能为空
            po.setProjectCode(projectCode);
        }
        int cnt = protoCardService.qryCardCount(po);
        //如果维护数量大于库存数量
        if (applySecondDto.getNum() > cnt) {
            throw new BizException(ApplicationErrorCodeEnum.PROTO_SECOND_CARD_002, String.valueOf(applySecondDto.getNum()), String.valueOf(cnt));
        }
        //检查二手不良品库清单是否已存在该不良品记录
        ProtoSecondCardPO secondCardPO = protoSecondCardService.findOneByParam(ProtoSecondCardPO.builder()
                .projectCode(projectCode).stageName(stageName)
                .skuCode(skuCode).houseCode(houseCode).build());

        if (null != secondCardPO) {
            secondCardPO.setNum(BigDecimal.valueOf(applySecondDto.getNum()));
            secondCardPO.setModifyUserid(userInfo.getUserId());
            secondCardPO.setModifyDate(new Date());
            protoSecondCardService.updateById(secondCardPO);
            return;
        }

        ProtoCardPO cardPO = protoCardService.getOneBySkuCode(skuCode, projectCode);
        applySecondDto.setMrpType(cardPO.getMrpType());

        ProtoSecondCardPO addPo = new ProtoSecondCardPO();
        BeanUtils.copyProperties(applySecondDto, addPo);
        addPo.setDeviceId(commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_SECOND_CARD));
        addPo.setNum(BigDecimal.valueOf(applySecondDto.getNum()));
        addPo.setAddUserid(userInfo.getUserId());
        addPo.setAddDate(new Date());
        addPo.setModifyUserid(userInfo.getUserId());
        addPo.setModifyDate(new Date());
        protoSecondCardService.save(addPo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteData(List<ApplySecondDTO> applySecondDtoList, UserInfo userInfo) {
        for (ApplySecondDTO applySecondDTO : applySecondDtoList) {
            boolean flag = false;
            //维护人集合
            HashSet<String> maintainer = new HashSet<>();
            if (!CommonConstant.ProtoCardIsComplete.NO_GOOD.getType().equals(applySecondDTO.getIsComplete())) {
                //只能删除是否良品为【非良品】的记录  ,JsMessage.getValue("proto_second_card.ht_text003")
                throw new BizException(ApplicationErrorCodeEnum.PROTO_SECOND_CARD_003);
            }
            ProtoSecondCardPO queryParam = ProtoSecondCardPO.builder()
                    .mrpType(applySecondDTO.getMrpType()).projectCode(applySecondDTO.getProjectCode())
                    .stageName(applySecondDTO.getStageName()).skuCode(applySecondDTO.getSkuCode()).build();
            List<ProtoSecondCardPO> secondCardList = protoSecondCardService.findListByParam(queryParam);
            if (CollectionUtils.isEmpty(secondCardList)) {
                continue;
            }
            for (ProtoSecondCardPO secondCardMap : secondCardList) {
                String addUserId = secondCardMap.getAddUserid();
                String modifyUserId = secondCardMap.getModifyUserid();
                //当前用户如果为维护人，可删除该不良品记录
                if (userInfo.getUserId().equals(addUserId) || userInfo.getUserId().equals(modifyUserId)) {
                    flag = true;
                    break;
                }
                maintainer.add(addUserId);
                if (!StringUtils.isBlank(modifyUserId)) {
                    maintainer.add(modifyUserId);
                }
            }
            //将不良品记录清零
            if (flag) {
                secondCardList.forEach(t -> t.setNum(BigDecimal.valueOf(0)));
                protoSecondCardService.updateBatchById(secondCardList);
            } else {
                String str = String.join(",", maintainer);
                throw new BizException(ApplicationErrorCodeEnum.PROTO_SECOND_CARD_005, str);
            }
        }
    }


    /**
     * 创建二手领用单
     *
     * @param userInfo
     * @param projectCode
     * @param stageName
     * @param skuCode
     * @param skuName
     * @return
     */
    private String createApplySecond(UserInfo userInfo, String projectCode, String stageName, String skuCode,
                                     String skuName, String isComplete, String mrpType, String houseCode,
                                     String houseName, String goodsId) {

        String applyId = commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_APPLY);
        String applyCode = APPLY_SECOND_PRFIX + commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_APPLY_SECOND);
        Date date = new Date();

        ProtoApplyPO protoApplyPO = new ProtoApplyPO();
        protoApplyPO.setApplyId(applyId);
        protoApplyPO.setApplyCode(applyCode);
        protoApplyPO.setAuditing(TranAuditEnum.NOT_SUBMIT.getKey());
        protoApplyPO.setProjectCode(projectCode);
        protoApplyPO.setStageName(stageName);
        protoApplyPO.setDeptCode(userInfo.getDeptCode());
        protoApplyPO.setDeptName(userInfo.getDeptName());
        protoApplyPO.setApplyUserName(userInfo.getUserName());
        protoApplyPO.setApplyEmpCode(userInfo.getEmpCode());
        protoApplyPO.setApplyUserCode(userInfo.getUserCode());
        protoApplyPO.setUserName(userInfo.getUserName());
        protoApplyPO.setEmpCode(userInfo.getEmpCode());
        protoApplyPO.setUserCode(userInfo.getUserCode());
        protoApplyPO.setHouseCode(houseCode);
        protoApplyPO.setHouseName(houseName);
        protoApplyPO.setMrpType(mrpType);
        protoApplyPO.setTreeDeptCode(userInfo.getTreeDeptCode());
        protoApplyPO.setApplyType(CommonConstant.ProtoCardApplyType.PHONE.getType());
        if (CommonConstant.ProtoCardIsComplete.NO_GOOD.getType().equals(isComplete)) {
            protoApplyPO.setSrcType(CommonConstant.ProtoCardSrcType.SECONDHANDNONGOOD.getType());
        } else {
            protoApplyPO.setSrcType(CommonConstant.ProtoCardSrcType.SECONDHAND.getType());
        }
        protoApplyPO.setApplyStatus(CommonConstant.ProtoCardApplyStatus.INPREPARATION.getType());
        protoApplyPO.setIsOther(CommonConstant.ProtoCardIsOther.NO.getType());
        protoApplyPO.setAddUserid(userInfo.getUserId());
        protoApplyPO.setAddDate(date);
        protoApplyService.save(protoApplyPO);

        String applyMatId = commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_APPLY_MAT);
        ProtoApplyMatPO applyMatPO = ProtoApplyMatPO.builder().applyMatId(applyMatId)
                .applyId(applyId).skuCode(skuCode).skuName(skuName).mrpType(mrpType)
                .addUserid(userInfo.getUserId()).addDate(new Date())
                .goodsId(goodsId)
                .build();
        applyMatService.save(applyMatPO);
        return applyId;
    }
}
