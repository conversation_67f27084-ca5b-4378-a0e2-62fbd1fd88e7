package com.mi.oa.asset.mobile.application.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.dto.bpm.*;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.BpmCallbackService;
import com.mi.oa.asset.mobile.application.service.BpmService;
import com.mi.oa.asset.mobile.application.service.BpmV3Service;
import com.mi.oa.asset.mobile.common.enums.MobileProcessEnum;
import com.mi.oa.asset.mobile.common.exception.MobileParamException;
import com.mi.oa.asset.mobile.infra.remote.entity.BpmDTO;
import com.mi.oa.asset.mobile.infra.remote.sdk.BpmClient;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.BpmExeLogPO;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import com.mi.oa.asset.mobile.utils.SpringContextUtil;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description: bpm发起和中断
 * @date 2021/12/7 14:44
 */
@Slf4j
@Service("BpmService")
public class BpmServiceImpl implements BpmService {

    private static final String PROCESS_SUBMIT = "submit";

    private static final String PROCESS_INTERRUPT = "interrupt";

    private static final String PROCESS_RSP_BUSINESS_KEY = "businessKey";

    public static final String PROCESS_KEY = "processKey";
    //子流程key
    public static final String SUB_PROCESS_KEY = "subProcessKey";

    @Autowired
    private BpmClient bpmClient;

    @Autowired
    private BpmV3Service bpmV3Service;

    @Override
    public void interruptProcess(InterruptProcessDTO interruptProcessDTO) {
        BpmDTO param = BpmDTO.builder()
                .processKey(interruptProcessDTO.getMobileProcessEnum().getKey())
                .status(PROCESS_INTERRUPT)
                .startUser(interruptProcessDTO.getStartUser())
                .businessKey(interruptProcessDTO.getBusinessKey())
                .build();
        interruptProcess(param);
    }

    private void interruptProcess(BpmDTO param) {
        try {
            bpmClient.createCommon(param);
        } catch (Exception e) {
            log.error("Interrupt asset check process [{}] failed, {}", param.getBusinessKey(), e.getMessage(), e);
            throw new BizException(ApplicationErrorCodeEnum.BPM_REMOTE_ERROR,e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void interruptProcessV2(String processKey, String keyId, String startUser) {
        BpmDTO param = BpmDTO.builder()
                .processKey(processKey)
                .status(PROCESS_INTERRUPT)
                .startUser(startUser)
                .build();
        BpmProcessEnum processEnum = BpmProcessEnum.getBpmProcessEnum(processKey);
        if(null == processEnum){
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR,"BpmProcessEnum找不到对应流程配置枚举");
        }else {
            BpmCallbackService bpmCallbackService = (BpmCallbackService) SpringContextUtil.getBean(processEnum.getCallbackClazz());
            param.setBusinessKey(bpmCallbackService.bpmInterrupt(keyId));
        }

        interruptProcess(param);
    }

    @Override
    public SubmitResultDTO submitProcess(SubmitProcessDTO submitProcessDTO) {
        MobileProcessEnum mobileProcessEnum = submitProcessDTO.getMobileProcessEnum();
        String startUser = submitProcessDTO.getStartUser();
        MobileProcessBaseBizDTO mobileProcessBaseBizDTO = submitProcessDTO.getMobileProcessBaseBizDTO();

        //参数校验
        paramCheck(mobileProcessEnum, startUser, mobileProcessBaseBizDTO);
        //返回值
        String businessKey = "";
        Map<String, Object> map = addCommonVariables(mobileProcessEnum.getKey(), mobileProcessBaseBizDTO);
        switch (mobileProcessEnum) {
            case PROCESS_KEY_MOBILE_CLAIM:
                businessKey = bpmProcess(mobileProcessEnum,
                        startUser,
                        String.format(MobileProcessBaseBizDTO.MOBILE_CLAIM_TITLE, mobileProcessBaseBizDTO.getBizId()),
                        mobileProcessBaseBizDTO.getMobileClaimProcessBizDTO(),
                        map);
                break;
            case PROCESS_KEY_MOBILE_TRANSFER:
                businessKey = bpmProcess(mobileProcessEnum,
                        startUser,
                        String.format(MobileProcessBaseBizDTO.MOBILE_TRANSFER_TITLE, mobileProcessBaseBizDTO.getBizId()),
                        mobileProcessBaseBizDTO.getMobileTransferProcessBizDTO(),
                        map);
                break;
            case PROCESS_KEY_MOBILE_SEC_REMOVE:
                businessKey = bpmProcess(mobileProcessEnum,
                        startUser,
                        String.format(MobileProcessBaseBizDTO.MOBILE_SEC_REM_TITLE, mobileProcessBaseBizDTO.getBizId()),
                        mobileProcessBaseBizDTO.getMobileSecRemProcessBizDTO(),
                        map);
                break;
            case PROCESS_KEY_MOBILE_NO_RETURN:
                businessKey = bpmProcess(mobileProcessEnum,
                        startUser,
                        String.format(MobileProcessBaseBizDTO.MOBILE_NO_RETURN_TITLE, mobileProcessBaseBizDTO.getBizId()),
                        mobileProcessBaseBizDTO.getMobileNoReturnProcessBizDTO(),
                        map);
                break;
            case PROCESS_KEY_MOBILE_RETURN:
                businessKey = bpmProcess(mobileProcessEnum,
                        startUser,
                        String.format(MobileProcessBaseBizDTO.MOBILE_RETURN_TITLE, mobileProcessBaseBizDTO.getBizId()),
                        mobileProcessBaseBizDTO.getMobileReturnProcessBizDTO(),
                        map);
                break;
            case PROCESS_KEY_MOBILE_SCRAP:
                businessKey = bpmProcess(mobileProcessEnum,
                        startUser,
                        String.format(MobileProcessBaseBizDTO.MOBILE_SCRAP_TITLE, mobileProcessBaseBizDTO.getBizId()),
                        mobileProcessBaseBizDTO.getMobileScrapProcessBizDTO(),
                        map);
                break;
            case PROCESS_KEY_MOBILE_ROLE_APPLY:
                businessKey = bpmProcess(mobileProcessEnum,
                        startUser,
                        String.format(MobileProcessBaseBizDTO.MOBILE_ROLE_APPLY, mobileProcessBaseBizDTO.getBizId()),
                        mobileProcessBaseBizDTO.getProtoRoleApplyBizDTO(),
                        map);
                break;
            case PROCESS_KEY_MOBILE_THIRD_USER_APPLY:
                businessKey = bpmProcess(mobileProcessEnum,
                        startUser,
                        String.format(MobileProcessBaseBizDTO.MOBILE_THIRD_USER_APPLY, mobileProcessBaseBizDTO.getBizId()),
                        mobileProcessBaseBizDTO.getProtoThirdUserApplyBizDTO(),
                        map);
                break;
            case PROCESS_KEY_MOBILE_DELAY:
                businessKey = bpmProcess(mobileProcessEnum,
                        startUser,
                        String.format(MobileProcessBaseBizDTO.MOBILE_DELAY_TITLE, mobileProcessBaseBizDTO.getBizId()),
                        mobileProcessBaseBizDTO.getProtoDelayBizDTO(),
                        map);
                break;
            default:
                break;
        }
        return SubmitResultDTO.builder().businessKey(businessKey).build();
    }

    @Override
    public void bpmCallback(BpmCallbackDTO param) {
        Map<String, Object> variables = param.getVariables();
        if(variables==null||!variables.containsKey(PROCESS_KEY)){
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR,"param no processKey");
        }
        String processKey = (String) variables.get(PROCESS_KEY);
        BpmProcessEnum processEnum = BpmProcessEnum.getBpmProcessEnum(processKey);
        if(null == processEnum){
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR,"BpmProcessEnum找不到对应流程配置枚举");
        }else {
            BpmCallbackService bpmCallbackService = (BpmCallbackService) SpringContextUtil.getBean(processEnum.getCallbackClazz());
            bpmCallbackService.bpmCallback(param);
        }
    }

    @Override
    public BpmV3Service v3() {
        return bpmV3Service;
    }

    /**
     * @param processKey 手机工程机流程类型
     * @param startUser         发起人
     * @param title             流程标题
     * @param business          业务数据
     * @param variable          附加数据
     * @return
     */
    @Override
    public String bpmSubmitV2(String processKey, String startUser, String title, ProcessBaseDTO business, Map<String, Object> variable) {
        BpmDTO param = BpmDTO.builder()
                .processKey(processKey)
                .status(PROCESS_SUBMIT)
                .processTitle(title)
                .startUser(startUser)
                .business(business)
                .build();
        param.getVariables().putAll(variable);
        param.getVariables().put(PROCESS_KEY, processKey);
        log.info("bpm req {}", param);
        Map<String, String> rst = bpmClient.createCommon(param);
        log.info("bpm rsp:{}", rst);
        return rst.get(PROCESS_RSP_BUSINESS_KEY);
    }


    /**
     * @param mobileProcessEnum 手机工程机流程类型
     * @param startUser         发起人
     * @param title             流程标题
     * @param business          业务数据
     * @param variable          附加数据
     * @return
     */
    private String bpmProcess(MobileProcessEnum mobileProcessEnum, String startUser, String title, Object business, Map<String, Object> variable) {
        BpmDTO param = BpmDTO.builder()
                .processKey(mobileProcessEnum.getKey())
                .status(PROCESS_SUBMIT)
                .processTitle(title)
                .startUser(startUser)
                .business(business)
                .build();
        param.getVariables().putAll(variable);
        try {
            log.info("bpm req {}", param);
            Map<String, String> rst = bpmClient.createCommon(param);
            log.info("bpm rsp:{}", rst);
            return rst.get(PROCESS_RSP_BUSINESS_KEY);
        } catch (Exception e) {
            log.error("Submit asset check process failed", e);
            return null;
        }
    }

    private Map<String, Object> addCommonVariables(String processKey, MobileProcessBaseBizDTO baseBizDTO) {
        Map<String, Object> map = new HashMap<>();
        if (!StringUtils.isBlank(processKey)) {
            map.put(PROCESS_KEY, processKey);
        }
        if (baseBizDTO.getSubProcess() != null) {
            map.put(SUB_PROCESS_KEY, baseBizDTO.getSubProcess().getValue());
        }
        if (baseBizDTO.getVaraibleDTO() != null) {
            map.putAll(GsonUtil.toMap(baseBizDTO.getVaraibleDTO()));
        }
        return map;
    }

    /**
     * 对参数做检查 防止出现参数异常传递的情况
     */
    private void paramCheck(MobileProcessEnum mobileProcessEnum, String startUser, MobileProcessBaseBizDTO baseBizDTO) {
        if (mobileProcessEnum == null || StringUtils.isBlank(startUser) || baseBizDTO == null || baseBizDTO.getSubProcess() == null) {
            throw new MobileParamException();
        }
        //参数是否正常
        boolean checkParam = true;
        switch (mobileProcessEnum) {
            case PROCESS_KEY_MOBILE_CLAIM:
                if (baseBizDTO.getSubProcess() == MobileProcessBaseBizDTO.SubProcessEnum.PROCESS_ONE &&
                        (baseBizDTO.getVaraibleDTO() == null || StringUtils.isBlank(baseBizDTO.getVaraibleDTO().getProjectManager()))) {
                    checkParam = false;
                }
                if (baseBizDTO.getSubProcess() == MobileProcessBaseBizDTO.SubProcessEnum.PROCESS_TWO &&
                        (baseBizDTO.getVaraibleDTO() == null || StringUtils.isBlank(baseBizDTO.getVaraibleDTO().getProjectManager())
                                || StringUtils.isBlank(baseBizDTO.getVaraibleDTO().getConfirmor()))) {
                    checkParam = false;
                }
                break;
            case PROCESS_KEY_MOBILE_TRANSFER:
                if (baseBizDTO.getVaraibleDTO() == null || StringUtils.isBlank(baseBizDTO.getVaraibleDTO().getReceiver())) {
                    checkParam = false;
                }
                break;
            case PROCESS_KEY_MOBILE_RETURN:
                if (baseBizDTO.getVaraibleDTO() == null || StringUtils.isBlank(baseBizDTO.getVaraibleDTO().getConfirmor())) {
                    checkParam = false;
                }
                break;
            case PROCESS_KEY_MOBILE_NO_RETURN:
                if (baseBizDTO.getSubProcess() == MobileProcessBaseBizDTO.SubProcessEnum.PROCESS_TWO &&
                        (baseBizDTO.getVaraibleDTO() == null || StringUtils.isBlank(baseBizDTO.getVaraibleDTO().getConfirmor()))) {
                    checkParam = false;
                }
                break;
            case PROCESS_KEY_MOBILE_SEC_REMOVE:
                if (baseBizDTO.getSubProcess() == MobileProcessBaseBizDTO.SubProcessEnum.PROCESS_ONE &&
                        (baseBizDTO.getVaraibleDTO() == null || StringUtils.isBlank(baseBizDTO.getVaraibleDTO().getFirstStarter())
                                || StringUtils.isBlank(baseBizDTO.getVaraibleDTO().getProjectManager()))) {
                    checkParam = false;
                }
                if (baseBizDTO.getSubProcess() == MobileProcessBaseBizDTO.SubProcessEnum.PROCESS_TWO &&
                        (baseBizDTO.getVaraibleDTO() == null || StringUtils.isBlank(baseBizDTO.getVaraibleDTO().getConfirmor())
                                || StringUtils.isBlank(baseBizDTO.getVaraibleDTO().getProjectManager()))) {
                    checkParam = false;
                }
                break;
            case PROCESS_KEY_MOBILE_SCRAP:
                if (StringUtils.isBlank(baseBizDTO.getVaraibleDTO().getProjectManager())) {
                    checkParam = false;
                }
                break;
            case PROCESS_KEY_MOBILE_ROLE_APPLY:
                if (StringUtils.isBlank(baseBizDTO.getProtoRoleApplyBizDTO().getApplyUser())) {
                    checkParam = false;
                }
                break;
            case PROCESS_KEY_MOBILE_THIRD_USER_APPLY:
                if (StringUtils.isBlank(baseBizDTO.getProtoThirdUserApplyBizDTO().getApplyUser())) {
                    checkParam = false;
                }
                break;
            case PROCESS_KEY_MOBILE_DELAY:
                if (baseBizDTO.getSubProcess() == MobileProcessBaseBizDTO.SubProcessEnum.PROCESS_ONE &&
                        (baseBizDTO.getVaraibleDTO() == null || StringUtils.isBlank(baseBizDTO.getVaraibleDTO().getProjectManager()))) {
                    checkParam = false;
                }
                if (baseBizDTO.getSubProcess() == MobileProcessBaseBizDTO.SubProcessEnum.PROCESS_TWO &&
                        (baseBizDTO.getVaraibleDTO() == null || StringUtils.isBlank(baseBizDTO.getVaraibleDTO().getConfirmor())
                                || StringUtils.isBlank(baseBizDTO.getVaraibleDTO().getProjectManager()))) {
                    checkParam = false;
                }
                break;
            default:
                break;
        }
        if (!checkParam) {
            throw new MobileParamException();
        }
    }
}
