package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.service.ProtoTvProjectService;
import com.mi.oa.asset.mobile.common.enums.MrpTypeEnum;
import com.mi.oa.asset.mobile.infra.dto.upc.SkuReq;
import com.mi.oa.asset.mobile.infra.dto.upc.TvSkuDTO;
import com.mi.oa.asset.mobile.infra.remote.sdk.SkuClient;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectCfgPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectListPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectCfgService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Copyright (c) 2022 XiaoMi Inc.All Rights Reserved.
 * Description: your description
 *
 * <AUTHOR>
 * @date 2022/11/2 17:35
 */
@Slf4j
@Service
public class ProtoTvProjectServiceImpl implements ProtoTvProjectService {

    private static final List<String> PROJECT_TYPE_LIST = Arrays.asList("1", "2");

    @Autowired
    private SkuClient skuClient;

    @Autowired
    private ProtoProjectListService protoProjectListService;

    @Autowired
    private ProtoProjectCfgService protoProjectCfgService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncTvProjectInfo() {
        log.info("syncTvProject start at: {}", new Date());
        //1 拉取接口数据 一级和二级
        List<TvSkuDTO> tvOneList = skuClient.getTvProjectInfo(SkuReq.builder().level(SkuReq.LEVEL_ONE).build());
        List<TvSkuDTO> tvTwoList = skuClient.getTvProjectInfo(SkuReq.builder().level(SkuReq.LEVEL_TWO).build());
        log.info("tvOneList size:{},tvTwoList size:{}", tvOneList.size(), tvTwoList.size());
        Map<Integer, String> oneLevelMap = tvOneList.stream()
                .filter(item -> PROJECT_TYPE_LIST.contains(item.getProjectType()))
                .collect(Collectors.toMap(TvSkuDTO::getId, TvSkuDTO::getProjectName));
        List<ProtoProjectListPO> projectList = new ArrayList<>(tvTwoList.size());
        // 项目信息初始化
        tvTwoList.stream().filter(item -> PROJECT_TYPE_LIST.contains(item.getProjectType()))
                .forEach(item -> {
                    // 二级项目父项目名称初始化
                    initParentProjectInfo(item, oneLevelMap);
                    // 获取并封装项目列表
                    projectList.add(assembleProject(item));
                });

        // 项目配置信息初始化
        List<ProtoProjectCfgPO> projectCfgList = new ArrayList<>(projectList.size());
        projectList.forEach(item -> {
            // 获取并初始化项目配置项信息
            ProtoProjectCfgPO cfgPO = protoProjectCfgService.getAndInitProtoProjectCfgPO(item);
            // 获取并封装项目配置列表
            projectCfgList.add(cfgPO);
        });

        // 批量入库项目配置
        protoProjectListService.saveOrUpdateBatch(projectList);
        // 批量入库项目配置
        protoProjectCfgService.saveOrUpdateBatch(projectCfgList);
    }

    /**
     * 二级项目父项目名称初始化
     *
     * @param tvSkuDTO
     * @param oneLevelMap
     */
    private void initParentProjectInfo(TvSkuDTO tvSkuDTO, Map<Integer, String> oneLevelMap) {
        if (tvSkuDTO.getPid() <= 0) {
            tvSkuDTO.setParentProject("");
        }
        String parentProject = oneLevelMap.get(tvSkuDTO.getPid());
        tvSkuDTO.setParentProject(StringUtils.isBlank(parentProject) ? "" : parentProject);
    }

    /**
     * 获取并封装项目列表
     *
     * @param tvSkuDTO
     * @return
     */
    private ProtoProjectListPO assembleProject(TvSkuDTO tvSkuDTO) {
        //2 获取本地数据库数据
        ProtoProjectListPO protoProject = protoProjectListService.getByProjectCode(tvSkuDTO.getProjectName());
        //3 插入or更新
        if (protoProject == null) {
            protoProject = new ProtoProjectListPO();
            protoProject.setProjectListId(String.valueOf(tvSkuDTO.getId()));
        }
        protoProject.setProjectName(tvSkuDTO.getProjectRealName() != null ? tvSkuDTO.getProjectRealName() : tvSkuDTO.getProjectName());
        protoProject.setProjectCode(tvSkuDTO.getProjectName());
        protoProject.setParentCode(tvSkuDTO.getParentProject());
        protoProject.setIsOld(tvSkuDTO.getPid() == -1 ? new BigDecimal(1) : new BigDecimal(0));
        protoProject.setModifyDate(new Date());
        protoProject.setAddDate(new Date());
        protoProject.setProjectType(MrpTypeEnum.TV.getType());
        protoProject.setAuditing("1");
        protoProject.setMrpType(MrpTypeEnum.TV.getType());
        return protoProject;
    }
}
