package com.mi.oa.asset.mobile.application.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mi.oa.asset.mobile.application.service.ProtoCardFixDataBOService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyMatPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoApplyMatService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 工程机数据数据修复
 *
 * <AUTHOR>
 * @date 2022/4/1 11:29
 */
@Slf4j
@Service
public class ProtoCardFixDataBOServiceImpl implements ProtoCardFixDataBOService {

    @Autowired
    ProtoCardService protoCardService;

    @Autowired
    ProtoApplyMatService protoApplyMatService;

    @Override
    public void fixProtoCardSkuName() {
        // skuName为空台账数据
        List<ProtoCardPO> protoCardList = protoCardService.queryProtoCardList(new LambdaQueryWrapper<ProtoCardPO>()
                .isNull(Boolean.TRUE, ProtoCardPO::getSkuName)
                .or(protoCardPOLambdaQueryWrapper -> protoCardPOLambdaQueryWrapper.eq(ProtoCardPO::getSkuName, "")));
        if (CollectionUtils.isEmpty(protoCardList)) {
            return;
        }
        protoCardList.forEach(protoCard -> {
            ProtoApplyMatPO protoApplyMatPO = protoApplyMatService.queryOneProtoApplyMat(new LambdaQueryWrapper<ProtoApplyMatPO>()
                    .eq(ProtoApplyMatPO::getSkuCode, protoCard.getSkuCode())
                    .ne(ProtoApplyMatPO::getSkuName, "")
                    .isNotNull(ProtoApplyMatPO::getSkuName));
            // 如果领用物料明细没有，通过工程机台账表修补
            if (Objects.isNull(protoApplyMatPO)) {
                ProtoCardPO po = protoCardService.queryOneProtoCard(new LambdaQueryWrapper<ProtoCardPO>()
                        .eq(ProtoCardPO::getSkuCode, protoCard.getSkuCode())
                        .ne(ProtoCardPO::getSkuCode, "")
                        .isNotNull(Boolean.TRUE, ProtoCardPO::getSkuName));
                protoCard.setSkuName(Objects.nonNull(po)? po.getSkuName(): "");
            } else {
                protoCard.setSkuName(protoApplyMatPO.getSkuName());
            }
        });
        protoCardService.saveOrUpdateBatch(protoCardList);
    }
}
