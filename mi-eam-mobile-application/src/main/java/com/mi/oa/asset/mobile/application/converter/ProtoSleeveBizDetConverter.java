package com.mi.oa.asset.mobile.application.converter;

import com.mi.oa.asset.mobile.application.dto.bpm.ProtoSleeveBizDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.ProtoSleeveBizDetDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleeveMatPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/10 11:16
 */
@Mapper(componentModel = "spring")
public interface ProtoSleeveBizDetConverter {
    ProtoSleeveBizDTO protoSleeveDetPOToDTO(ProtoSleeveMatPO protoSleeveDetPO);

    List<ProtoSleeveBizDetDTO> protoSleeveDetPOToDTOList(List<ProtoSleeveMatPO> protoSleeveDetPOList);
}
