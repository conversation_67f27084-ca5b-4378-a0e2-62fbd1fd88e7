package com.mi.oa.asset.mobile.application.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.mobile.application.dto.message.EmailDTO;
import com.mi.oa.asset.mobile.application.dto.message.LarkDTO;
import com.mi.oa.asset.mobile.application.dto.message.TemplateDTO;
import com.mi.oa.asset.mobile.application.dto.message.TemplateMsgBaseDTO;
import com.mi.oa.asset.mobile.application.service.MessageService;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.config.message.MessageBizConfig;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MailTemplatePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MailTemplateParamPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.MailTemplateParamService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.MailTemplateService;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import com.mioffice.ums.open.common.constats.MessageChannelEnum;
import com.mioffice.ums.open.common.http.UmsHttpStub;
import com.mioffice.ums.open.common.http.UmsResponse;
import com.mioffice.ums.open.common.message.BatchMessage;
import com.mioffice.ums.open.common.message.MsgResult;
import com.mioffice.ums.open.common.message.MsgUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import rx.Single;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MessageServiceImpl implements MessageService {

    private final static int SUCCESS_CODE = 200;

    private final static String AMP = "amp;";

    @Value("${bpm.approvalUrlPrefix}")
    private String approvalUrlPrefix;

    @Value("${asset.host}")
    private String assetHost;

    @Autowired
    private UmsHttpStub umsHttpStub;

    @Autowired
    private MessageBizConfig mobileEmailMessageBizConfig;

    @Autowired
    private MessageBizConfig mobileLarkMessageBizConfig;

    @Autowired
    private MessageBizConfig mobileLarkButtonMessageConfig;

    @Autowired
    private MessageBizConfig mobileSmsMessageBizConfig;

    @Autowired
    private MessageBizConfig mobileLarkNotButtonMessageConfig;

    @Autowired
    private MessageBizConfig mallLarkSingleButtonMessageConfig;

    @Autowired
    private MessageBizConfig mallLarkNoButtonMessageConfig;

    @Autowired
    private MailTemplateService mailTemplateService;

    @Autowired
    private MailTemplateParamService mailTemplateParamService;

    @Override
    @Async
    public void sendEmail(EmailDTO emailDTO) {
        // 去重
        List<String> ccEmails = emailDTO.getCcEmails();
        if (null != ccEmails){
            ccEmails = ccEmails.stream().distinct().collect(Collectors.toList());
        }
        List<String> emails = emailDTO.getEmails().stream().distinct().collect(Collectors.toList());

        String botId = mobileEmailMessageBizConfig.getBotBizId();
        String templateId = mobileEmailMessageBizConfig.getTemplateBizId();

        BatchMessage message = new BatchMessage();
        message.setBotBizId(botId);
        message.setChannel(MessageChannelEnum.EMAIL.getType());
        message.setTemplateBizId(templateId);
        List<MsgUser> msgUsers = new ArrayList<>();
        Map<String, Object> params = new HashMap<>(2);
        params.put("content", emailDTO.getContent());
        params.put("title", emailDTO.getTitle());
        for (String email : emails) {
            MsgUser msgUser = new MsgUser();
            msgUser.setParams(params);
            msgUser.setEmail(email);
            msgUser.setParams(params);
            msgUser.setCcEmails(ccEmails);
            msgUsers.add(msgUser);
        }
        message.setUserList(msgUsers);

        try {
            UmsResponse<MsgResult> rst = umsHttpStub.sendBatch(message);
            if (SUCCESS_CODE != rst.getCode()) {
                log.error("send message to {} failed error: {}", emailDTO, rst.getMsg());
            }
        } catch (Exception e) {
            log.error("send message to {} failed error: {}", emailDTO, e.getMessage(), e);
        }
    }

    @Override
    @Async
    public void sendLark(LarkDTO larkDTO) {
        List<String> userNames = larkDTO.getUserNames().stream().distinct().collect(Collectors.toList());

        String botId = mobileLarkMessageBizConfig.getBotBizId();
        String templateId = mobileLarkMessageBizConfig.getTemplateBizId();
        if (Objects.equals(CommonConstant.MALL_BOT_TYPE, larkDTO.getBotType())) {
            // 内购机器人无按钮
            if (CommonConstant.NO_BUTTON.equals(larkDTO.getButtonName())) {
                botId = mallLarkNoButtonMessageConfig.getBotBizId();
                templateId = mallLarkNoButtonMessageConfig.getTemplateBizId();
            }
            // 内购机器人单按钮
            if (StringUtils.isNotBlank(larkDTO.getButtonName()) && !CommonConstant.NO_BUTTON.equals(larkDTO.getButtonName())) {
                botId = mallLarkSingleButtonMessageConfig.getBotBizId();
                templateId = mallLarkSingleButtonMessageConfig.getTemplateBizId();
            }
        } else {
            if (StringUtils.isNotBlank(larkDTO.getButtonName())) {
                botId = mobileLarkButtonMessageConfig.getBotBizId();
                templateId = mobileLarkButtonMessageConfig.getTemplateBizId();
            }
            if (CommonConstant.NO_BUTTON.equals(larkDTO.getButtonName())){
                botId = mobileLarkNotButtonMessageConfig.getBotBizId();
                templateId = mobileLarkNotButtonMessageConfig.getTemplateBizId();
            }
        }
        BatchMessage batchMessage = new BatchMessage();
        batchMessage.setBotBizId(botId);
        batchMessage.setTemplateBizId(templateId);
        batchMessage.setChannel(MessageChannelEnum.MI_WORK.getType());
        List<MsgUser> msgUsers = new ArrayList<>(userNames.size());
        for (String userName : userNames) {
            MsgUser msgUser = new MsgUser();
            msgUser.setUsername(userName);
            msgUsers.add(msgUser);

            Map<String, Object> params = new HashMap<>(4);
            params.put("title", larkDTO.getTitle());
            params.put("content", larkDTO.getContent());
            params.put("url", larkDTO.getUrl());
            if (StringUtils.isNotBlank(larkDTO.getButtonName())){
                params.put("buttonContent", larkDTO.getButtonName());
            }
            msgUser.setParams(params);
        }
        batchMessage.setUserList(msgUsers);

        try {
            UmsResponse<MsgResult> rst = umsHttpStub.sendBatch(batchMessage);
            if (SUCCESS_CODE != rst.getCode()) {
                log.error("send flyLetter message to {} failed error: {}", larkDTO, rst.getMsg());
            }
        } catch (Exception e) {
            log.error("send flyLetter message to {} failed error: {}", larkDTO, e.getMessage(), e);
        }
    }

    @Override
    @Async
    public void sendLark(String templateTag,
                         List<String> userCodes, String mat, TemplateMsgBaseDTO msgBaseDTO) {
        TemplateDTO templateDTO = this.getTemplateMsgByTag(templateTag, msgBaseDTO);
        String content = templateDTO.getContent();
        if (content.contains(AMP)) {
            content = content.replaceAll(AMP, "");
        }
        content += mat;
        content = content.replaceAll("<br/>", "\\\\r\\\\n");
        content = content.replaceAll("<br>", "\\\\r\\\\n");
        templateDTO.setContent(content);
        for (String userCode : userCodes) {
            String url = "";
            if (msgBaseDTO.isContainUrl()) {
                url = assetHost;
                url += "?usercode=" + userCode + "&nodeid=" + msgBaseDTO.getFunId() + "&keyid=" + msgBaseDTO.getDataId();
            }
            this.sendLark(LarkDTO.builder()
                    .userNames(Collections.singletonList(userCode))
                    .url(url)
                    .content(templateDTO.getContent())
                    .title(templateDTO.getTitle())
                    .build());
        }
    }

    @Override
    @Async
    public void sendEmail(String templateTag, List<String> emails, String mat, TemplateMsgBaseDTO msgBaseDTO) {
        if(ObjectUtils.isEmpty(emails)){
            return;
        }
        if(!StringUtils.isBlank(msgBaseDTO.getBusinessKey())){
            msgBaseDTO.setBusinessKey(approvalUrlPrefix + msgBaseDTO.getBusinessKey());
        }
        TemplateDTO templateDTO = this.getTemplateMsgByTag(templateTag, msgBaseDTO);
        String content = templateDTO.getContent();
        if (content.contains(AMP)) {
            content = content.replaceAll(AMP, "");
        }
        content += mat;
        this.sendEmail(EmailDTO.builder()
                .emails(emails)
                .content(content)
                .title(templateDTO.getTitle())
                .build());
    }

    @Override
    public void sendSMS(List<String> tels, String content) {
        try {
            BatchMessage msg = new BatchMessage();
            msg.setBotBizId(mobileSmsMessageBizConfig.getBotBizId());
            msg.setTemplateBizId(mobileSmsMessageBizConfig.getTemplateBizId());
            msg.setChannel(MessageChannelEnum.SMS.getType());
            Map<String, Object> params = new HashMap<>(1);
            params.put("content", content);
            List<MsgUser> msgUsers = new ArrayList<>(tels.size());
            for (String tel : tels) {
                MsgUser msgUser = new MsgUser();
                msgUser.setPhone(tel);
                msgUser.setParams(params);
                msgUsers.add(msgUser);
            }
            msg.setUserList(msgUsers);
            UmsResponse<MsgResult> rst = umsHttpStub.sendBatch(msg);
            if (SUCCESS_CODE != rst.getCode()) {
                log.error("send sms message to {} failed error: {}", tels, rst.getMsg());
            }
        } catch (Exception e) {
            log.error("send sms message to {} failed error: {}", tels, e.getMessage(), e);
        }
    }

    /**
     * 根据tag 和参数组装模板
     *
     * @param templateTag 模板tag
     * @param msgBaseDTO  参数 --使用这个基类的实现类
     * @return
     */
    private TemplateDTO getTemplateMsgByTag(String templateTag, TemplateMsgBaseDTO msgBaseDTO) {
        Map<String, Object> busDataMap = GsonUtil.toMap(msgBaseDTO);
        TemplateDTO templateDTO = new TemplateDTO();
        String content = "";
        MailTemplatePO templatePO = mailTemplateService.getOne(Wrappers
                .<MailTemplatePO>lambdaQuery()
                .eq(MailTemplatePO::getTemplateTag, templateTag), false);
        if (templatePO == null) {
            log.error("getTemplateMsgById err not find templateMsg:{}", templatePO);
            return templateDTO;
        }
        content = templatePO.getTemplateCont();
        List<MailTemplateParamPO> templateParamPOS = mailTemplateParamService.list(Wrappers.<MailTemplateParamPO>lambdaQuery().eq(MailTemplateParamPO::getTemplateId, templatePO.getTemplateId()));
        for (MailTemplateParamPO mailTemplateParamPO : templateParamPOS) {
            String paramName = mailTemplateParamPO.getParamName();
            Object paramObject = busDataMap.get(paramName);
            String paramValue = "";
            if (paramObject != null) {
                paramValue = String.valueOf(paramObject);
            }
            content = content.replace("{" + paramName + "}", paramValue);
        }

        templateDTO.setTitle(templatePO.getTemplateName());
        templateDTO.setContent(content);
        return templateDTO;
    }

}
