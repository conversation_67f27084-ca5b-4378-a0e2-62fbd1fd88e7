package com.mi.oa.asset.mobile.application.converter;

import com.mi.oa.asset.mobile.application.dto.bpm.ProtoSleeveBizDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleevePO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @date 2022/11/10 11:12
 */
@Mapper(componentModel = "spring")
public interface ProtoSleeveBizConverter {
    ProtoSleeveBizDTO protoSleeveBizPOToDTO(ProtoSleevePO protoSleevePO);
}
