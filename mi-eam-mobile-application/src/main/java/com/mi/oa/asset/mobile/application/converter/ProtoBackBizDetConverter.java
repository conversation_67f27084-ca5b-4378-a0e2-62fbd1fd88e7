package com.mi.oa.asset.mobile.application.converter;

import com.mi.oa.asset.mobile.application.dto.bpm.ProtoBackBizDetDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInDetPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/2 14:35
 */

@Mapper(componentModel = "spring")
public interface ProtoBackBizDetConverter {
    ProtoBackBizDetDTO protoBackDetPOToDTO(ProtoInDetPO protoInDetPO);

    List<ProtoBackBizDetDTO> protoBackDetPOToDTOList(List<ProtoInDetPO> po);
}
