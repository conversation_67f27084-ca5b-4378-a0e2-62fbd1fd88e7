package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.ProtoPriceBOService;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPricePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoPriceService;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/14
 */
@Service
public class ProtoPriceBOServiceImpl implements ProtoPriceBOService {

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProtoPriceService protoPriceService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrSubmit(RequestContext requestContext) {
        String keyId = commonService.commonSave(requestContext);
        Map<String, String> userInfo = requestContext.getUserInfo();
        ProtoPricePO pricePO = protoPriceService.getById(keyId);
        if (protoPriceService.havePriceCodeSame(pricePO)){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_PRICE_CODE_SAME);
        }
        pricePO.setModifyName(userInfo.get(CommonConstant.USER_NAME));
        protoPriceService.updateById(pricePO);
    }
}
