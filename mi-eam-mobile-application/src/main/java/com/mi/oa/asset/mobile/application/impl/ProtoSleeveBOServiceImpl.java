package com.mi.oa.asset.mobile.application.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.mi.oa.asset.mobile.application.converter.ProtoSleeveBizConverter;
import com.mi.oa.asset.mobile.application.converter.ProtoSleeveBizDetConverter;
import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.dto.bpm.*;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.BpmV3Service;
import com.mi.oa.asset.mobile.application.service.ProtoSleeveBOService;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.dto.BpmExeLogDTO;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.dto.hrod.EmployeeInfo;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.asset.mobile.utils.DateUtil;
import com.mi.oa.asset.mobile.utils.StringUtil;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 工程机保密套拆除
 *
 * <AUTHOR>
 * @date 2022/1/28 14:57
 */
@Service
@Slf4j
public class ProtoSleeveBOServiceImpl implements ProtoSleeveBOService {

    public static final String TABLE_PROTO_SLEEVE = "proto_sleeve";
    private static final Gson GSON = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();

    @Autowired
    private ProtoService protoService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProtoSleeveService protoSleeveService;

    @Autowired
    private ProtoThirdUserService protoThirdUserService;

    @Autowired
    private ProtoSleeveMatService protoSleeveMatService;

    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private FunAllControlService funAllControlService;

    @Autowired
    private ProtoOutDetService protoOutDetService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private BpmExeLogService bpmExeLogService;

    @Autowired
    private BpmV3Service bpmV3Service;

    @Autowired
    private ProtoSleeveBizConverter protoSleeveBizConverter;

    @Autowired
    private ProtoSleeveBizDetConverter protoSleeveBizDetConverter;

    /**
     * 提交前重新分析领用方类型，并判断确认人是否需要填写
     *
     * @param keyIds
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String audit(String[] keyIds, UserInfo userInfo, RequestContext request) {
        String rs = this.beforeAudit(keyIds, userInfo);
        commonService.commonAudit(request);
//        this.pushBpm(keyIds);  // eam-api在pushBpm时会重新调用
        return rs;
    }

    /**
     * 提交前重新分析领用方类型，并判断确认人是否需要填写
     *
     * @param keyIds
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String beforeAudit(String[] keyIds, UserInfo userInfo) {
        //获取状态的显示值
        List<FunAllControlPO> useStateList = funAllControlService.getComboValue("proto_use_state");
        List<FunAllControlPO> isOpenList = funAllControlService.getComboValue("proto_is_open");

        for (String keyId : keyIds) {
            ProtoSleevePO protoSleevePO = protoSleeveService.findProtoSleeve(keyId);
            if (ObjectUtils.isEmpty(protoSleevePO)) {
                throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR, RedisUniqueKeyEnum.PROTO_SLEEVE.getTableName(), keyId);
            }
            String applyUserCode = protoSleevePO.getApplyUserCode();
            String checkUserCode = protoSleevePO.getCheckUserCode();
            String applyUserType = protoSleevePO.getApplyUserType();
            String mainMrpType = protoSleevePO.getMrpType();

            if (mainMrpType.equals("tv")) {
                // 电视没有保密套拆除操作，请核实业务类型
                throw new BizException(ApplicationErrorCodeEnum.PROTO_SLEEVE_011);
            }

            String type = null;
            ProtoThirdUserPO protoThirdUserPO = protoThirdUserService.findUserInfo(applyUserCode);
            if (null != protoThirdUserPO) {
                type = CommonConstant.ApplyUserTypeEnum.ODM.getValue();
                if (StringUtils.isEmpty(checkUserCode)) {
                    //必须填写确认人！
                    //setMessage(JsMessage.getValue("proto_sleeve.ht_text001"));
                    throw new BizException(ApplicationErrorCodeEnum.PROTO_SLEEVE_001);
                }
            } else {
                type = CommonConstant.ApplyUserTypeEnum.INNER.getValue();
                if (!StringUtils.isEmpty(checkUserCode)) {
                    //无需填写确认人！
                    //setMessage(JsMessage.getValue("proto_sleeve.ht_text002"));
                    throw new BizException(ApplicationErrorCodeEnum.PROTO_SLEEVE_002);
                }
            }

            // 检查明细表是否合格，类型是否一致
            List<ProtoSleeveMatPO> protoSleeveDetPOList = protoSleeveMatService.list(Wrappers.<ProtoSleeveMatPO>lambdaQuery()
                    .eq(ProtoSleeveMatPO::getSleeveId, keyId));
            if (CollectionUtils.isEmpty(protoSleeveDetPOList)) {
                throw new BizException(ApplicationErrorCodeEnum.PROTO_SLEEVE_010);
            }
            for (ProtoSleeveMatPO protoSleeveMatPO : protoSleeveDetPOList) {
                if ("0".equals(protoSleeveMatPO.getCheckStatus()) || !protoSleeveMatPO.getMrpType().equals(mainMrpType)) {
                    //明细表存在不合格项，请检查！
                    throw new BizException(ApplicationErrorCodeEnum.PROTO_SLEEVE_003);
                }
            }

            //校验明细在台账中是否出现异动
            List<ProtoCardPO> changeList = isChange(keyId);
            if (null != changeList && !changeList.isEmpty()) {
                StringBuilder msgBuilder = new StringBuilder();
                for (ProtoCardPO protoCardPO : changeList) {
                    String deviceCode = protoCardPO.getDeviceCode();
                    String userCode = protoCardPO.getUserCode();
                    String projectCode = protoCardPO.getProjectCode();
                    String useStateDisplay = displayValue(useStateList, protoCardPO.getUseState());
                    String isOpenDisplay = displayValue(isOpenList, protoCardPO.getIsOpen());
                    //设备编号SN【{0}】-当前使用人【{1}】-项目【{2}】-状态【{3}】-保密套是否拆除【{4}】
                    //str += JsMessage.getValue("proto_sleeve.ht_text008",deviceCode,userCode,projectCode,useStateDisplay,isOpenDisplay) + "<br/>";
                    msgBuilder.append(String.format("设备编号SN【%s】-当前使用人【%s】-项目【%s】-状态【%s】-保密套是否拆除【%s】%n", deviceCode, userCode, projectCode, useStateDisplay, isOpenDisplay));
                }
                //以下设备编号SN在台账中出现异动，请检查！
                //setMessage(JsMessage.getValue("proto_sleeve.ht_text005")+"<br/>"+str);
                throw new BizException(ApplicationErrorCodeEnum.PROTO_SLEEVE_004, "/n" + msgBuilder.toString());
            }
            //校验明细的SN是否已出库
            qryProtoOut(keyId);

            // 如果领用方类型发生变动，则更新
            if (null == applyUserType || !applyUserType.equals(type)) {
                protoSleevePO.setApplyUserType(type);
                if (!protoSleeveService.updateById(protoSleevePO)) {
                    throw new BizException(ApplicationErrorCodeEnum.PROTO_SLEEVE_006);
                }
            }
            protoSleevePO.setNum(protoSleeveDetPOList.size());
            if (!protoSleeveService.updateById(protoSleevePO)) {
                throw new BizException(ApplicationErrorCodeEnum.PROTO_SLEEVE_006);
            }

            //如果领用方类型发生变动，则更新
            if (null == applyUserType || !applyUserType.equals(type)) {
                if (!protoSleeveService.updateType(type, keyId, userInfo.getUserId())) {
                    //更新领用方类型失败！
                    //setMessage(JsMessage.getValue("proto_sleeve.ht_text003"));
                    throw new BizException(ApplicationErrorCodeEnum.PROTO_SLEEVE_006);
                }
            }
        }
        return CommonConstant.TRUE;
    }

    private String displayValue(List<FunAllControlPO> comboList, String val) {
        if (CollectionUtils.isEmpty(comboList)) {
            return val;
        } else {
            Optional<FunAllControlPO> res = comboList.stream().filter(t -> StringUtils.equals(val, t.getValueData())).findFirst();
            if (res.isPresent()) {
                return res.get().getDisplayData();
            } else {
                return val;
            }
        }
    }

    private List<ProtoCardPO> isChange(String keyId) {
        List<ProtoSleeveInfoPO> list = protoSleeveService.selectSleeveInfoById(keyId);
        if (list == null || list.isEmpty()) {
            return null;
        }
        String applyUserCode = list.get(0).getApplyUserCode();
        String projectCode = list.get(0).getProjectCode();
        List<String> deviceCodes = list.stream().map(ProtoSleeveInfoPO::getDeviceCode).collect(Collectors.toList());
        List<ProtoCardPO> cardList = protoCardService.findCardList(deviceCodes, applyUserCode, CardStateEnum.TO_BE_RETURNED.getKey(), "0", projectCode);
        return cardList;
    }

    public void qryProtoOut(String keyId) {
        List<ProtoSleeveMatPO> sleeveMatList = protoSleeveMatService.qryMatDeviceCodes(keyId);
        if (null != sleeveMatList && !sleeveMatList.isEmpty()) {
            List<String> deviceCodes = sleeveMatList.stream().map(ProtoSleeveMatPO::getDeviceCode).collect(Collectors.toList());
            List<String> protoOutDeviceCodeList = protoOutDetService.selectProtoOutDetDeviceCodes(deviceCodes);
            if (!CollectionUtils.isEmpty(protoOutDeviceCodeList)) {
                //以下设备编号SN尚未【确认收货】，请确认收货后，再次尝试提交！
                StringBuilder sb = new StringBuilder();
                protoOutDeviceCodeList.forEach(t -> sb.append(",").append(t));
                throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, "以下设备编号SN尚未【确认收货】，请确认收货后，再次尝试提交！" + sb.substring(1));
            }
        }
    }

    /**
     * 推送BPM
     *
     * @param keyIds
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String pushBpm(String[] keyIds) {
        for (String keyId : keyIds) {
            if (!StringUtils.isBlank(keyId)) {
                pushBpm(keyId);
            }
        }
        return CommonConstant.TRUE;
    }

    public String pushBpm(String keyId) {
        log.info("========正在处理工程机保密套拆除推送Bpm事件");
        log.info("申请单ID：【{}】", keyId);
        String funId = TABLE_PROTO_SLEEVE;
        // 查询主表信息
        ProtoSleevePO mainData = protoSleeveService.findProtoSleeve(keyId);

        if (ObjectUtils.isEmpty(mainData)) {
            throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR, RedisUniqueKeyEnum.PROTO_SLEEVE.getTableName(), keyId);
        }
        String mainMrpType = mainData.getMrpType();
        String applyCode = mainData.getApplyCode();
        //申请人
        String userCode = mainData.getApplyUserCode();
        String applyUserName = mainData.getApplyUserName();
        //PM
        String pmUserName = setValue(mainData.getPmUserName());
        String pmUserCode = setValue(mainData.getPmUserCode());
        String pmEmpCode = setValue(mainData.getPmEmpCode());
        String applyUserType = setValue(mainData.getApplyUserType());

        // 查询子表信息
        List<ProtoSleeveMatPO> matData = protoSleeveMatService.qrySleeveMatList(keyId);
        for (ProtoSleeveMatPO protoSleeveMatPO : matData) {
            if (null == protoSleeveMatPO.getMrpType() || "".equals(protoSleeveMatPO.getMrpType())) {
                // 明细表业务类型不能为空，请重新导入
                throw new BizException(ApplicationErrorCodeEnum.ERR_NULL_TYPE, "子表业务类型不能为空，请重新导入数据！");
            }
            // 判断主表和明细表业务类型是否一致
            if (!protoSleeveMatPO.getMrpType().equals(mainMrpType)) {
                // 主表和明细表业务类型一致才可提交
                throw new BizException(ApplicationErrorCodeEnum.ERR_INCONSISTENCY_TYPE);
            }
        }

        log.info("申请单信息：apply_code【{}】，sleeve_id【{}】，流程发起用户：user_code【{}】", applyCode, keyId, userCode);
        log.info("------------------------------------------------------------------------");
        // 创建Bpm主表
        log.info("创建Bpm工程机保密套拆除主表...");
        ProtoSleeveBizDTO protoSleeveBizDTO = protoSleeveBizConverter.protoSleeveBizPOToDTO(mainData);

        //创建Bpm物料明细表
        log.info("创建Bpm保密套拆除物料明细子表...");
        List<ProtoSleeveBizDetDTO> protoSleeveBizDetDTOList = protoSleeveBizDetConverter.protoSleeveDetPOToDTOList(matData);

        int code = 1;
        for (ProtoSleeveBizDetDTO protoSleeveBizDetDTO : protoSleeveBizDetDTOList) {
            protoSleeveBizDetDTO.setCode(String.valueOf(code++));
            protoSleeveBizDetDTO.setAssetNewold(funAllControlService.getComboValue("asset_newold", setValue(protoSleeveBizDetDTO.getAssetNewold())));
        }

        String firstStarter = "";
        if (CommonConstant.ApplyUserTypeEnum.INNER.getValue().equals(applyUserType)) {
            //获取第一申领人
            firstStarter = getFirstStarter(keyId, pmUserCode, pmEmpCode);
            if (firstStarter == null) {
                //反馈第一申领人与PM信息到前台
                List<ProtoSleeveMatPO> list = protoSleeveMatService.qrySleeveMatListGroupByApplyUserCode(keyId);
                StringBuffer sbf = new StringBuffer();
                for (int i = 0; i < list.size(); i++) {
                    ProtoSleeveMatPO map = list.get(i);
                    sbf.append(map.getApplyUserName())
                            .append("(")
                            .append(map.getApplyUserCode())
                            .append(")");
                    if (i != list.size() - 1) {
                        sbf.append("、");
                    }
                }
                String pm = pmUserName + "(" + pmUserCode + ")";
                //第一申领人【{0}】和PM【{1}】非在职状态，不允许发起流程，请联系管理员处理
                //setMessage(JsMessage.getValue("proto_sleeve.ht_text009",sbf.toString(),pm));
                throw new BizException(ApplicationErrorCodeEnum.PROTO_SLEEVE_007, sbf.toString(), pm);
            }
        }

        // 工程机类型
        String mrpType = setValue(mainData.getMrpType());
        protoSleeveBizDTO.setMrpType(funAllControlService.getComboValue("mrp_type", mrpType));
        protoSleeveBizDTO.setCheckUserCode(Objects.isNull(mainData.getCheckUserCode()) ? "" : mainData.getCheckUserCode());  // 小米确认人
        protoSleeveBizDTO.setFirstApplyUserCode(firstStarter);
        protoSleeveBizDTO.setProtoSleeveBizDetDTOList(protoSleeveBizDetDTOList);

        Map<String, Object> variables = new HashMap<>();
        variables.put("apply_user_type", applyUserType);  // 拆除类型
        variables.put("mrp_type", mrpType);  // 工程机类型

        //更新工程机台账对应记录保密套是否拆除为拆除中
        log.info("更新保密套是否拆除为：拆除中...");
        if (!updProtoCardInfo(keyId, "2")) {
            log.info("更新工程机台账保密套是否拆除字段失败！");
            insertlog(funId, "工程机保密套拆除", keyId, applyCode, applyUserName, false, "更新工程机台账保密套是否拆除字段失败！", protoSleeveBizDTO);
            //setMessage(JsMessage.getValue("proto_bpm.ht_text009"));
            throw new BizException(ApplicationErrorCodeEnum.PROTO_SLEEVE_008);
        }
        // 创建Bpm流程
        log.info("创建Bpm流程...");
        String businessKey = bpmV3Service.submitProtoSleeveApply(protoSleeveBizDTO, userCode, variables);

        log.info("创建Bpm流程返回：businessKey【{}】", businessKey);
        if (StringUtils.isEmpty(businessKey)) {
            log.info("抛送BPM失败！");
            // 抛送BPM失败！
            insertlog(funId, "工程机保密套拆除", keyId, applyCode, applyUserName, false, "抛送BPM失败！", protoSleeveBizDTO);
            //setMessage(JsMessage.getValue("proto_bpm.ht_text001"));
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_001);
        }
        // 保存流程信息
        log.info("保存流程信息...");
        try {
            protoSleeveService.updProtoSleeveStatusBySleeveId(keyId, TranAuditEnum.CHECKING.getKey(), DateUtil.getTodaySec(), businessKey);
        } catch (Exception e) {
            log.error("保存流程信息失败 error: {}", e.getMessage(), e);
            insertlog(funId, "工程机保密套拆除", keyId, applyCode, applyUserName, false, "保存流程信息失败！", protoSleeveBizDTO);
            // 保存BPM流程信息失败！ proto_bpm.ht_text002
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_002);
        }

        insertlog(funId, "工程机保密套拆除", keyId, applyCode, applyUserName, true, businessKey, protoSleeveBizDTO);
        log.info("========处理工程机保密套拆除推送Bpm事件结束");
        return CommonConstant.TRUE;
    }

    /**
     * 撤回BPM流程具体逻辑
     *
     * @param keyIds
     * @param userInfo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String interruptBpm(String[] keyIds, UserInfo userInfo) {
        for (String keyId : keyIds) {
            if (!StringUtils.isBlank(keyId) && null != userInfo) {
                interruptBpm(keyId, userInfo.getUserCode());
            }
        }
        return CommonConstant.TRUE;
    }

    public String interruptBpm(String keyId, String userCode) {
        log.info("========正在处理工程机保密套拆除撤回Bpm流程事件");
        log.info("申请单ID：【{}】", keyId);
        // 查询主表信息
        ProtoSleevePO mainData = protoSleeveService.findProtoSleeve(keyId);
        if (ObjectUtils.isEmpty(mainData)) {
            throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR, RedisUniqueKeyEnum.PROTO_SLEEVE.getTableName(), keyId);
        }
        String applyCode = mainData.getApplyCode();
        //申请人
        String applyUserCode = mainData.getApplyUserCode();
        String auditing = mainData.getAuditing();
        String businessKey = mainData.getBusinessKey();

        if (!StringUtils.equals(applyUserCode, userCode)) {
            //只能撤回本人发起的单据！JsMessage.getValue("proto_bpm.ht_text003")
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_003);
        }

        log.info("申请单信息：apply_code【{}】，sleeve_id【{}】，操作流程撤回用户：user_code【{}】，auditing【{}】，business_key【{}】", applyCode, keyId, userCode, auditing, businessKey);
        log.info("------------------------------------------------------------------------");
        // 检测参数
        if (StringUtils.isEmpty(businessKey)) {
            //setMessage(JsMessage.getValue("proto_bpm.ht_text005"));
            log.info("单据未发起Bpm流程，无需撤回！");
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_005);
        }
        if (!TranAuditEnum.CHECKING.getKey().equals(auditing)) {
            //setMessage(JsMessage.getValue("proto_bpm.ht_text006"));
            log.info("只能撤回审批中的单据！");
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_006);
        }
        // 撤回Bpm流程
        log.info("撤回Bpm流程...");
        try {
            bpmV3Service.terminate(businessKey, userCode);
        } catch (Exception e) {
            log.error("撤回Bpm流程失败！");
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_007);
        }

        log.info("更新记录状态为：未提交...");
        protoSleeveService.updProtoSleeveStatusBySleeveId(keyId, TranAuditEnum.NOT_SUBMIT.getKey(), DateUtil.getTodaySec());
        log.info("更新保密套是否拆除为：否...");
        //更新工程机台账对应记录保密套是否拆除为否
        if (!updProtoCardInfo(keyId, "0")) {
            log.info("更新工程机台账保密套是否拆除字段失败！");
            //setMessage(JsMessage.getValue("proto_bpm.ht_text009"));
            throw new BizException(ApplicationErrorCodeEnum.UPDATE_DATA_ERROR, "更新工程机台账保密套是否拆除字段失败！");
        }
        log.info("成功撤回Bpm流程business_key【{}】", businessKey);
        log.info("========处理工程机保密套拆除撤回Bpm流程事件结束");
        return CommonConstant.TRUE;
    }

    /**
     * bpm回调方法
     *
     * @param bpmCallbackDTO
     * @return
     */
    @Deprecated
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bpmCallback(BpmCallbackDTO<MobileProcessBaseBizDTO> bpmCallbackDTO) {
        log.info("========正在处理工程机保密套拆除Bpm回调事件");
        String funId = TABLE_PROTO_SLEEVE;
        if (bpmCallbackDTO == null) {
            log.info("bpmCallbackDTO参数为空");
            insertlog(funId, "工程机保密套拆除-BPM回调", null, null, null, false, "bpmCallbackDTO参数为空", bpmCallbackDTO);
            throw new BizException(ApplicationErrorCodeEnum.PARAM_INVALID_ERROR, "bpmCallbackDTO参数为空");
        }
        // 获取business_key
        String businessKey = bpmCallbackDTO.getBusinessKey();
        log.info("获取business_key【{}】", businessKey);
        ProtoSleevePO mainData = protoSleeveService.findByBusinessKey(businessKey);
        if (ObjectUtils.isEmpty(mainData)) {
            log.info("业务数据找不到business_key【{}】", businessKey);
            insertlog(funId, "工程机保密套拆除-BPM回调", null, null, null, false, "业务数据找不到business_key【" + businessKey + "】", bpmCallbackDTO);
            return true;
        }
        // 获取业务参数
        String keyId = mainData.getSleeveId();
        String applyCode = mainData.getApplyCode();
        String applyUserName = mainData.getApplyUserName();
        String operator = bpmCallbackDTO.getOperator();
        log.info("业务参数sleeve_id【{}】，operator【{}】", keyId, operator);
        //获取审批状态
        BpmCallbackStatusEnum status = bpmCallbackDTO.getStatus();
        log.info("审批状态status【{}】", status);
        // 驳回
        if (BpmCallbackStatusEnum.REJECT.equals(status)) {
            log.info("单据被驳回...");

            // 更新记录状态
            // 已驳回
            try {
                protoSleeveService.updProtoSleeveStatusBySleeveId(keyId, TranAuditEnum.CANCELLED.getKey(), DateUtil.getTodaySec());
            } catch (Exception e) {
                log.info("更新记录状态失败！");
                insertlog(funId, "工程机保密套拆除-BPM回调", keyId, applyCode, applyUserName, false, "更新记录状态失败！", bpmCallbackDTO);
                throw new BizException(ApplicationErrorCodeEnum.UPDATE_DATA_ERROR, "更新记录状态失败");
            }
            log.info("更新保密套是否拆除为：否...");
            //更新工程机台账对应记录保密套是否拆除为否
            if (!updProtoCardInfo(keyId, "0")) {
                log.info("更新工程机台账保密套是否拆除字段失败！");
                insertlog(funId, "工程机保密套拆除-BPM回调", keyId, applyCode, applyUserName, false, "更新工程机台账保密套是否拆除字段失败！", bpmCallbackDTO);
                throw new BizException(ApplicationErrorCodeEnum.UPDATE_DATA_ERROR, "更新工程机台账保密套是否拆除字段失败！");
            }
        }
        // 审批完成
        else if (BpmCallbackStatusEnum.END.equals(status)) {
            log.info("单据审批完成...");

            String auditing = mainData.getAuditing();
            // 检测流程是否已审批
            if ("3".equals(auditing)) {
                log.info("单据已审批完成，无需再审...");
                return true;
            }
            // 更新记录状态
            // 已审批
            protoSleeveService.updProtoSleeveStatusBySleeveId(keyId, TranAuditEnum.CHECK_OK.getKey(), DateUtil.getTodaySec());
            log.info("更新保密套是否拆除为：是...");
            //更新工程机台账保密套拆除信息
            if (!updProtoCardInfo(keyId, "1", mainData.getApplyUserName(), mainData.getApplyUserCode(), mainData.getApplyEmpCode())) {
                log.info("更新工程机台账保密套拆除信息失败！");
                insertlog(funId, "工程机保密套拆除-BPM回调", keyId, applyCode, applyUserName, false, "更新工程机台账保密套拆除信息失败！", bpmCallbackDTO);
                throw new BizException(ApplicationErrorCodeEnum.UPDATE_DATA_ERROR, "更新工程机台账保密套拆除信息失败！");
            }
        } else {
            log.info("其他状态不处理...");
        }
        // 执行成功
        if (BpmCallbackStatusEnum.REJECT.equals(status) || BpmCallbackStatusEnum.END.equals(status)) {
            insertlog(funId, "工程机保密套拆除-BPM回调", keyId, applyCode, applyUserName, true, "工程机保密套拆除Bpm审批完成！", bpmCallbackDTO);
        }
        log.info("========处理工程机保密套拆除Bpm回调事件结束");
        return true;
    }

    @Override
    public void sleeveDetScanConfirm(String sleeveId, String keyword, UserInfo userInfo) {
        // 获取设备编号SN
        ProtoSleevePO protoSleevePO = protoSleeveService.findProtoSleeve(sleeveId);
        if (ObjectUtils.isEmpty(protoSleevePO)) {
            throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR, RedisUniqueKeyEnum.PROTO_SLEEVE.getTableName(), sleeveId);
        }
        Map<String, String> scanResultMap = protoService.tranScanResult(keyword);
        String deviceCode = scanResultMap.get("device_code");
        List<ProtoSleeveMatPO> matPOList = protoSleeveMatService.qrySleeveMatList(sleeveId);
        if (matPOList.stream().anyMatch(t -> StringUtils.equals(deviceCode, t.getDeviceCode()))) {
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, "the device has been added");
        }
        ProtoCardPO cardParam = ProtoCardPO.builder().deviceCode(deviceCode).userCode(protoSleevePO.getApplyUserCode())
                .useState("4").isOpen("0").projectCode(protoSleevePO.getProjectCode()).mrpType(protoSleevePO.getMrpType()).build();
        ProtoCardPO protoCardPO = protoCardService.findOneByParam(cardParam);
        if (ObjectUtils.isEmpty(protoCardPO)) {
            log.error("can not find card, sn:{}", deviceCode);
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, "can not find device card, sn:" + deviceCode);
        }
        ProtoSleeveMatPO matPO = ProtoSleeveMatPO.builder()
                .sleeveMatId(commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_SLEEVE_MAT))
                .sleeveId(sleeveId).skuCode(protoCardPO.getSkuCode()).skuName(protoCardPO.getSkuName()).deviceCode(protoCardPO.getDeviceCode())
                .applyCode(protoCardPO.getApplyCode()).laserCode(protoCardPO.getLaserCode()).imei(protoCardPO.getImei()).deviceType(protoCardPO.getDeviceType())
                .assetNewold(protoCardPO.getAssetNewold()).applyUserCode(protoCardPO.getApplyUserCode()).applyUserName(protoCardPO.getApplyUserName())
                .applyEmpCode(protoCardPO.getApplyEmpCode()).lastApplyUserName(protoCardPO.getLastApplyUserName())
                .lastApplyUserCode(protoCardPO.getLastApplyUserCode()).lastApplyEmpCode(protoCardPO.getLastApplyEmpCode())
                .projectCode(protoCardPO.getProjectCode()).stageName(protoCardPO.getStageName())
                .checkStatus("1").addUserid(userInfo.getUserId()).addDate(new Date())
                .mrpType(protoCardPO.getMrpType())
                .build();
        protoSleeveMatService.getBaseMapper().insert(matPO);

        // 更新主表拆除数量
        matPOList = protoSleeveMatService.qrySleeveMatList(sleeveId);
        protoSleevePO.setNum(matPOList.size());
        protoSleeveService.updateById(protoSleevePO);
    }

    /**
     * 获取BPM明细表
     **/
    private List<MobileItemBizDTO> getBpmItems(List<ProtoSleeveMatPO> matData) {
        List<MobileItemBizDTO> items = new ArrayList<>();
        int index = 0;
        for (ProtoSleeveMatPO matMap : matData) {
            index++;
            MobileItemBizDTO item = MobileItemBizDTO.builder()
                    .imei(matMap.getImei())
                    .desc(matMap.getSkuName())
                    .number(matMap.getDeviceCode())
                    .radiumCarvingNumber(matMap.getLaserCode())
                    .project(matMap.getProjectCode())
                    .sn(String.valueOf(index))
                    .miMaterialCode(matMap.getSkuCode())
                    .reqNumber(matMap.getApplyCode())
                    .stage(matMap.getStageName())
                    .type(funAllControlService.getComboValue("asset_newold", matMap.getAssetNewold()))
                    .remark(matMap.getRemark())
                    .build();
            items.add(item);
        }
        return items;
    }

    /**
     * 获取第一申领人
     *
     * @param sleeveId
     * @param pmUserCode
     * @param pmEmpCode
     * @return
     */
    public String getFirstStarter(String sleeveId, String pmUserCode, String pmEmpCode) {
        //获取所有第一申领人
        List<ProtoSleeveMatPO> firstStarterList = protoSleeveMatService.qrySleeveMatListGroupByApplyUserCode(sleeveId);
        if (null == firstStarterList || firstStarterList.isEmpty()) {
            return null;
        }
        //存储在职人员信息
        List<ProtoSleeveMatPO> userList = new ArrayList<>();
        for (ProtoSleeveMatPO firstStarter : firstStarterList) {
            String applyEmpCode = firstStarter.getApplyEmpCode();
            if ("".equals(applyEmpCode)) {
                continue;
            }
            EmployeeInfo empInfo = userInfoService.getEmpInfoByEmpId(applyEmpCode);
            if (empInfo == null) {
                continue;
            }
            //已离职
            if ("I".equals(empInfo.getHrStatus())) {
                continue;
            }
            userList.add(firstStarter);
        }
        //如果在职人员集合为空，说明第一申领人皆为p账号或已离职
        if (null == userList || userList.isEmpty()) {
            //检查PM是否已离职
            EmployeeInfo empInfo = userInfoService.getEmpInfoByEmpId(pmEmpCode);
            if (empInfo == null || "I".equals(empInfo.getHrStatus())) {
                //如果离职返回null
                return null;
            } else {
                //否则返回PM账号
                return pmUserCode;
            }
        } else {
            String firstStarters = "";
            StringBuilder sb = new StringBuilder();
            userList.forEach(t -> sb.append(",").append(t.getApplyUserCode()));
            firstStarters = sb.substring(1);
            return firstStarters;
        }
    }

    /**
     * 修改台账状态
     *
     * @param fKeyId 报废ID
     * @param isOpen 保密套是否拆除
     * @return
     */
    public boolean updProtoCardInfo(String fKeyId, String isOpen) {
        List<ProtoSleeveMatPO> matList = protoSleeveMatService.qrySleeveMatList(fKeyId);
        if (null != matList && !matList.isEmpty()) {
            List<String> deviceCodes = matList.stream().map(ProtoSleeveMatPO::getDeviceCode).collect(Collectors.toList());
            return protoCardService.updProtoCardOpenStatus(isOpen, deviceCodes);
        }
        return true;
    }

    /**
     * 修改台账状态
     *
     * @param fKeyId 报废ID
     * @param isOpen 保密套是否拆除
     * @return
     */
    public boolean updProtoCardInfo(String fKeyId, String isOpen, String userName, String userCode, String empCode) {
        List<ProtoSleeveMatPO> matList = protoSleeveMatService.qrySleeveMatList(fKeyId);
        if (null != matList && !matList.isEmpty()) {
            List<String> deviceCodes = matList.stream().map(ProtoSleeveMatPO::getDeviceCode).collect(Collectors.toList());
            return protoCardService.updProtoCardOpenStatus(isOpen, userName, userCode, empCode, deviceCodes);
        }
        return true;
    }

    /**
     * 插入BPM日志
     *
     * @param funId     功能ID
     * @param orderName 单据名称
     * @param keyId     单据主键
     * @param code      单据号
     * @param userName  执行人
     * @param success   执行代号
     * @param desc      执行结果
     * @param body      执行报文
     * @return
     */
    public void insertlog(String funId, String orderName, String keyId, String code, String userName, boolean success, String desc, Object body) {
        BpmExeLogDTO protoBpmExeLogDTO = BpmExeLogDTO.builder()
                .funId(funId)
                .orderId(keyId)
                .orderCode(code)
                .orderName(orderName)
                .doUser(userName)
                .doDate(DateUtils.getFormatDate(DateUtils.COMMON_PATTERN))
                .exeCode(success)
                .exeDesc(desc)
                .exeBody(body)
                .build();
        bpmExeLogService.save(protoBpmExeLogDTO);
    }

    /**
     * 判断为null 则返回空字符串
     *
     * @param value
     * @return
     */
    private String setValue(String value) {
        if (StringUtil.isEmpty(value)) {
            value = "";
        }
        return value;
    }

}
