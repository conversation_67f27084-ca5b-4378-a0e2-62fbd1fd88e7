package com.mi.oa.asset.mobile.application.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.mi.oa.asset.mobile.application.converter.ProtoTranBizConverter;
import com.mi.oa.asset.mobile.application.converter.ProtoTranBizDetConverter;
import com.mi.oa.asset.mobile.application.converter.TranVOConverter;
import com.mi.oa.asset.mobile.application.dto.AssetCommonDTO;
import com.mi.oa.asset.mobile.application.dto.ConditionDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.*;
import com.mi.oa.asset.mobile.application.dto.message.EmailDTO;
import com.mi.oa.asset.mobile.application.dto.message.LarkDTO;
import com.mi.oa.asset.mobile.application.dto.message.TemplateMsgBaseDTO;
import com.mi.oa.asset.mobile.application.dto.statusnotify.ProtoListDTO;
import com.mi.oa.asset.mobile.application.dto.statusnotify.ProtoListDetDTO;
import com.mi.oa.asset.mobile.application.dto.tran.*;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.BpmV3Service;
import com.mi.oa.asset.mobile.application.service.MessageService;
import com.mi.oa.asset.mobile.application.service.TranService;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.dto.BpmExeLogDTO;
import com.mi.oa.asset.mobile.infra.dto.CostCenter;
import com.mi.oa.asset.mobile.infra.dto.hrod.EmployeeInfo;
import com.mi.oa.asset.mobile.infra.dto.sap.Sap107JsonDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapCustomVO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapPhoneTransferDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoTranMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.asset.mobile.infra.service.ProtoListStatusNotifyService;
import com.mi.oa.asset.mobile.utils.BeanUtil;
import com.mi.oa.asset.mobile.utils.StringUtil;
import com.mi.oa.asset.mobile.utils.UserUtil;
import com.mi.oa.infra.oaucf.bpm.enums.ProcessInstanceStatus;
import com.mi.oa.infra.oaucf.bpm.enums.UserTaskOperation;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TranServiceImpl extends ServiceImpl<ProtoTranMapper, ProtoTranPO> implements TranService {

    public static final String FUNCTION_ID = "proto_tran";

    public static final String FUNCTION_NAME = "工程机转移";

    public static final String CALLBACK_FUNCTION_NAME = "工程机转移-BPM回调";

    public static final String FAIL_LOG = "更新工程机台账状态失败";

    public static final String TRAN_AUDIT_EMAIL_TAG = "tran_audit";

    public static final String TRAN_AUDIT_LARK_TAG = "tran_audit_lark";

    public static final String TRAN_AUDIT_SUCCESS_TAG = "tran_success";

    public static final String TRAN_AUDIT_APPROVE_TAG = "tran_approve";

    private static final Gson GSON = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();


    public static final String SAP_MARK = "Y";


    @Autowired
    private ProtoTranService protoTranService;

    @Autowired
    private ProtoTranDetService protoTranDetService;

    @Autowired
    private BpmV3Service bpmV3Service;

    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private BpmExeLogService bpmExeLogService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private ProtoThirdUserService thirdUserService;

    @Autowired
    private ProtoOutService protoOutService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProtoSapExeLogService protoSapExeLogService;

    @Autowired
    private ProtoService protoService;

    @Autowired
    private SapService sapService;

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired
    private SysUserDataService sysUserDataService;

    @Autowired
    private ProtoListStatusNotifyService protoListStatusNotifyService;

    @Autowired
    private MailTemplateService mailTemplateService;

    @Value("${asset.host}")
    private String assetHost;

    @Autowired
    private TranVOConverter tranVOConverter;

    @Autowired
    private ProtoTranBizConverter protoTranBizConverter;

    @Autowired
    private ProtoTranBizDetConverter protoTranBizDetConverter;

    @Autowired
    private FunAllControlService funAllControlService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void tranCommit(String tranId, String userCode) {
        //参数验证
        audit(tranId, userCode);
        //提交
        commit(tranId);
    }

    @Override
    public String tranScanQry(String scanResult) {
        // 解析扫码结果
        Map<String, String> scanResultMap = protoService.tranScanResult(scanResult);
        String deviceCode = scanResultMap.get("device_code");
        List<ProtoTranDetPO> detList = protoTranDetService.findListByParam(ProtoTranDetPO.builder().deviceCode(deviceCode).build());
        return detList.stream().map(ProtoTranDetPO::getTranId).collect(Collectors.joining(","));
    }

    @Override
    public void bpmCallBack(BpmCallbackDTO<MobileProcessBaseBizDTO> bpmCallbackDTO) {
        String businessKey = bpmCallbackDTO.getBusinessKey();
        BpmCallbackStatusEnum status = bpmCallbackDTO.getStatus();
        //1、查询主表及明细数据
        ProtoTranPO tranPO = protoTranService.getByBusinessKey(businessKey);
        List<ProtoTranDetPO> tranDetPOList = null;
        if (tranPO != null) {
            tranDetPOList = protoTranDetService.getByTranId(tranPO.getTranId());
        }
        log.info("tran bpm callback callbackDTO:{} tranPO:{} tranDetPOList:{}", bpmCallbackDTO, tranPO, tranDetPOList);
        if (tranPO == null || ObjectUtils.isEmpty(tranDetPOList)) {
            bpmExeLogService.save(BpmExeLogDTO.builder()
                    .orderName(CALLBACK_FUNCTION_NAME)
                    .exeCode(false)
                    .exeDesc("业务找不到businessKey:" + businessKey)
                    .exeBody(bpmCallbackDTO)
                    .build());
            return;
        }
        List<String> deviceCodeList = tranDetPOList.stream().map(ProtoTranDetPO::getDeviceCode)
                .collect(Collectors.toList());
        // 驳回
        if (BpmCallbackStatusEnum.REJECT.equals(status)) {
            tranPO.setAuditing(TranAuditEnum.CANCELLED.getKey());
            tranPO.setModifyDate(new Date());
            tranPO.setConfirmStatus(TranConfirmStatusEnum.IN_PREPARATION.getKey());
            //更新确认状态为编制中
            protoTranService.updateById(tranPO);
            //更新工程机台账对应记录状态为原来的状态
            protoCardService.updateState(deviceCodeList, CardStateEnum.TO_BE_RETURNED, tranPO.getMrpType());
            //审批完成
        } else if (BpmCallbackStatusEnum.END.equals(status)) {
            if (TranAuditEnum.CHECK_OK.getKey().equals(tranPO.getAuditing())) {
                log.info("单据已审批完成，无需再审 tranPO:{}", tranPO);
                return;
            }
            //更新确认状态
            tranPO.setModifyDate(new Date());
            tranPO.setAuditing(TranAuditEnum.CHECK_OK.getKey());
            String tranType = tranPO.getTranType();
            tranPO.setModifyDate(new Date());
            tranPO.setConfirmStatus(TranConfirmStatusEnum.CONFIRMED.getKey());
            //修改台账使用人、状态、成本中心、公司代码、领用方类型
            updateCard(tranPO, tranDetPOList);
            sendSuccessMessage(tranPO, tranDetPOList);
            protoTranDetService.updateBatchById(tranDetPOList);

            protoTranService.updateById(tranPO);

            //审批通过发送消息通知
            auditPassNotify(tranPO.getTranId());
        }
        // 执行成功
        if (BpmCallbackStatusEnum.REJECT.equals(status) || BpmCallbackStatusEnum.END.equals(status)) {
            BpmExeLogDTO bpmExeLogDTO = BpmExeLogDTO.builder()
                    .funId(FUNCTION_ID)
                    .orderId(tranPO.getTranId())
                    .orderCode(tranPO.getTranCode())
                    .orderName(CALLBACK_FUNCTION_NAME)
                    .doUser(tranPO.getUserName())
                    .exeCode(true)
                    .exeDesc("工程机转移Bpm审批完成！")
                    .exeBody(bpmCallbackDTO)
                    .build();
            bpmExeLogService.save(bpmExeLogDTO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bpmCallBackV3(BpmProcessCompletedDTO param) {
        if (StringUtils.isBlank(param.getBusinessKey())) {
            log.error("回调businessKey为空！");
            throw new BizException(ErrorCodeEnum.PARAM_IS_NULL_ERROR, "回调businessKey");
        }

        // 1、查询主表及明细数据
        ProtoTranPO tranPO = this.getOne(new LambdaQueryWrapper<ProtoTranPO>().eq(ProtoTranPO::getBusinessKey, param.getBusinessKey()));
        List<ProtoTranDetPO> tranDetPOList = null;
        if (tranPO != null) {
            tranDetPOList = protoTranDetService.getByTranId(tranPO.getTranId());
        }
        log.info("tran bpm callback callbackDTO:{} tranPO:{} tranDetPOList:{}", param, tranPO, tranDetPOList);
        if (tranPO == null || ObjectUtils.isEmpty(tranDetPOList)) {
            bpmExeLogService.save(BpmExeLogDTO.builder()
                    .orderName(CALLBACK_FUNCTION_NAME)
                    .exeCode(false)
                    .exeDesc("业务找不到businessKey:" + null)
                    .exeBody(param)
                    .build());
            return;
        }

        List<String> deviceCodeList = tranDetPOList.stream().map(ProtoTranDetPO::getDeviceCode)
                .collect(Collectors.toList());

        // 流程结束
        ProcessInstanceStatus processStatus = param.getProcessInstanceStatus();
        if (processStatus == ProcessInstanceStatus.COMPLETED) {
            doTranProcessCompleted(tranPO, tranDetPOList);
            BpmExeLogDTO bpmExeLogDTO = BpmExeLogDTO.builder()
                    .funId(FUNCTION_ID)
                    .orderId(tranPO.getTranId())
                    .orderCode(tranPO.getTranCode())
                    .orderName(CALLBACK_FUNCTION_NAME)
                    .doUser(tranPO.getUserName())
                    .exeCode(true)
                    .exeDesc("工程机转移Bpm审批完成！")
                    .exeBody(param)
                    .build();
            bpmExeLogService.save(bpmExeLogDTO);
        }

        // 流程终止(撤回）
        if (processStatus == ProcessInstanceStatus.TERMINATED) {
            doTranProcessRejectedOrTerminated(tranPO, deviceCodeList, "TERMINATED");
            BpmExeLogDTO bpmExeLogDTO = BpmExeLogDTO.builder()
                    .funId(FUNCTION_ID)
                    .orderId(tranPO.getTranId())
                    .orderCode(tranPO.getTranCode())
                    .orderName(CALLBACK_FUNCTION_NAME)
                    .doUser(tranPO.getUserName())
                    .exeCode(true)
                    .exeDesc("工程机转移Bpm审批被终止(撤回）！")
                    .exeBody(param)
                    .build();
            bpmExeLogService.save(bpmExeLogDTO);
        }

        // 流程审批事件
        String status = param.getVariables().get("status").toString();
        if (StringUtils.isNotBlank(status)) {
            UserTaskOperation userTaskOperation = UserTaskOperation.findByCode(status);
            switch (userTaskOperation) {
                case REJECT:
                    doTranProcessRejectedOrTerminated(tranPO, deviceCodeList, "REJECT");
                    BpmExeLogDTO bpmExeLogDTO = BpmExeLogDTO.builder()
                            .funId(FUNCTION_ID)
                            .orderId(tranPO.getTranId())
                            .orderCode(tranPO.getTranCode())
                            .orderName(CALLBACK_FUNCTION_NAME)
                            .doUser(tranPO.getUserName())
                            .exeCode(true)
                            .exeDesc("工程机转移Bpm审批被拒绝！")
                            .exeBody(param)
                            .build();
                    bpmExeLogService.save(bpmExeLogDTO);
                    break;
                default:
                    log.debug("其他状态不处理...");
            }
        }

        log.debug("========处理工程机转移Bpm回调事件结束");
    }

    /**
     * 工程机转移流程结束
     *
     * @param tranPO
     * @param tranDetPOList
     */
    private void doTranProcessCompleted(ProtoTranPO tranPO, List<ProtoTranDetPO> tranDetPOList) {
        if (TranAuditEnum.CHECK_OK.getKey().equals(tranPO.getAuditing())) {
            log.info("单据已审批完成，无需再审 tranPO:{}", tranPO);
            return;
        }
        //更新确认状态
        tranPO.setModifyDate(new Date());
        tranPO.setAuditing(TranAuditEnum.CHECK_OK.getKey());
        String tranType = tranPO.getTranType();
        tranPO.setModifyDate(new Date());
        tranPO.setConfirmStatus(TranConfirmStatusEnum.CONFIRMED.getKey());

        //修改台账使用人、状态、成本中心、公司代码、领用方类型
        updateCard(tranPO, tranDetPOList);
        sendSuccessMessage(tranPO, tranDetPOList);
//        //推送SAP
//        pushSap(tranPO, tranDetPOList, false);
//        protoTranDetService.updateBatchById(tranDetPOList);
        protoTranService.updateById(tranPO);

        //审批通过发送消息通知
        auditPassNotify(tranPO.getTranId());
    }

    /**
     * 工程机转移流程拒绝或终止(撤回）
     *
     * @param tranPO
     * @param deviceCodeList
     */
    private void doTranProcessRejectedOrTerminated(ProtoTranPO tranPO, List<String> deviceCodeList, String status) {

        if(status.equals("TERMINATED")){
            // 终止(撤回）Bpm流程
            log.debug("终止(撤回）Bpm流程...");
            log.debug("更新记录状态为未提交，更新确认状态为编制中，更新工程机台账对应记录状态为待归还");
            tranPO.setAuditing(TranAuditEnum.NOT_SUBMIT.getKey());
        } else if(status.equals("REJECT")){
            // Bpm流程拒绝
            log.debug("拒绝Bpm流程...");
            log.debug("更新记录状态为已失效，更新确认状态为编制中，更新工程机台账对应记录状态为待归还");
            tranPO.setAuditing(TranAuditEnum.CANCELLED.getKey());
        }

        tranPO.setModifyDate(new Date());
        tranPO.setConfirmStatus(TranConfirmStatusEnum.IN_PREPARATION.getKey());
        protoTranService.updateById(tranPO);
        String mrpType = tranPO.getMrpType();
        // 售后样机，剔除掉可能已出库处置的资产台账
        if (MrpTypeEnum.PROTOTYPE.getType().equals(mrpType)) {
            List<ProtoCardPO> protoCardPOList = protoCardService.getByDeviceCodeAndExcludeUseState(deviceCodeList, CardStateEnum.UNABLE_TO_RETURN.getKey());
            deviceCodeList = protoCardPOList.stream().map(ProtoCardPO::getDeviceCode).collect(Collectors.toList());
        }
        //更新工程机台账对应记录状态为原来的状态
        protoCardService.updateState(deviceCodeList, CardStateEnum.TO_BE_RETURNED, mrpType);

    }

    // 更新 card
    private void updateCard(ProtoTranPO tranPO, List<ProtoTranDetPO> tranDetPOList) {
        // 业务类型
        String mrpType = tranPO.getMrpType();
        EmployeeInfo employeeInfo = userInfoService.getEmpInfoByUserName(tranPO.getAccUserCode());
        List<String> deviceCodeList = tranDetPOList.stream().map(ProtoTranDetPO::getDeviceCode)
                .collect(Collectors.toList());
        List<ProtoCardPO> protoCardPOS = protoCardService.getByDeviceCode(deviceCodeList);
        log.info("get_update_card_info:{}", protoCardPOS);
        //处理成本中心
        for (ProtoTranDetPO detPO : tranDetPOList) {
            //只处理一手
            if (CommonConstant.ProtoCardAssetNewOld.SECONDHAND.getType().equals(detPO.getAssetNewold())) {
                continue;
            }
            for (ProtoCardPO cardPO : protoCardPOS) {
                if (detPO.getDeviceCode().equalsIgnoreCase(cardPO.getDeviceCode()) &&
                        detPO.getMakeCn().equalsIgnoreCase(cardPO.getMakeCn())) {
                    cardPO.setCenterCode(detPO.getInCenterCode());
                    cardPO.setCompDeptCode(detPO.getInCompDeptCode());
                }
            }
        }
        for (ProtoTranDetPO detPO : tranDetPOList) {
            for (ProtoCardPO cardPO : protoCardPOS) {
                if (detPO.getDeviceCode().equalsIgnoreCase(cardPO.getDeviceCode())) {
                    // 售后样机已处置状态的台账状态不变
                    if (MrpTypeEnum.PROTOTYPE.getType().equals(mrpType) && CardStateEnum.UNABLE_TO_RETURN.getKey().equals(cardPO.getUseState())) {
                        cardPO.setUseState(CardStateEnum.UNABLE_TO_RETURN.getKey());
                    } else {
                        cardPO.setUseState(CardStateEnum.TO_BE_RETURNED.getKey());
                    }
                    cardPO.setUserName(tranPO.getAccUserName());
                    cardPO.setUserCode(tranPO.getAccUserCode());
                    cardPO.setEmpCode(tranPO.getAccEmpCode() != null ? tranPO.getAccEmpCode() : "");
                    cardPO.setApplyUserType(tranPO.getApplyUserType());
                    cardPO.setDeptCode(employeeInfo != null ? employeeInfo.getDeptId() : "");
                    cardPO.setLongDeptName(employeeInfo != null ? employeeInfo.getDeptName() : "");
                    cardPO.setCountry(employeeInfo != null ? employeeInfo.getCompanyCountryCodeChar3() : "");
                    cardPO.setModifyDate(new Date());
                }
            }
        }
        log.info("update_card_detail_info:{}", protoCardPOS);
        protoCardService.updateBatchById(protoCardPOS);
    }

    public void audit(String tranId, String userCode) {
        //1、查询主表及明细数据
        ProtoTranPO tranPO = protoTranService.getByTranId(tranId);
        if (tranPO == null) {
            throw new BizException(ApplicationErrorCodeEnum.NOT_FIND_BY_TRAN_ID);
        }
        String mainMrpType = tranPO.getMrpType();
        List<ProtoTranDetPO> tranDetPOList = protoTranDetService.list(Wrappers.<ProtoTranDetPO>lambdaQuery()
                .eq(ProtoTranDetPO::getTranId, tranId));
        if (ObjectUtils.isEmpty(tranDetPOList)) {
            throw new BizException(ApplicationErrorCodeEnum.ERR_TRAN_WRITE_DET);
        }
        //2、查询申请人和接收人身份--是否第三方
        ProtoThirdUserPO userInfo = thirdUserService.findUserInfo(tranPO.getUserCode());
        ProtoThirdUserPO accUserInfo = thirdUserService.findUserInfo(tranPO.getAccUserCode());
        boolean applyFlag = userInfo != null;
        boolean accFlag = accUserInfo != null;

        if (!userCode.equals(tranPO.getUserCode())) {
            //只能提交自己申请的单据！
            throw new BizException(ApplicationErrorCodeEnum.ERR_TRAN_NOT_SELF_APPLY);
        }
        if (UserUtil.isPAccount(userCode) && UserUtil.isPAccount(tranPO.getAccUserCode())) {
            //p账号用户不允许转移到p账号用户
            throw new BizException(ApplicationErrorCodeEnum.ERR_TRAN_P_TO_P);
        }
        //如果接收人为p账号，且没有在第三方配置里维护，不允许提交
        if (UserUtil.isPAccount(tranPO.getAccUserCode()) && !accFlag) {
            throw new BizException(ApplicationErrorCodeEnum.ERR_TRAN_USER_CKECK);
        }
        for (ProtoTranDetPO tranDetPO : tranDetPOList) {
            //明细表存在不合格项，请检查！
            if (TranCheckStatusEnum.UNQUALIFIED.getKey().equals(tranDetPO.getCheckStatus())) {
                throw new BizException(ApplicationErrorCodeEnum.ERR_TRAN_CHECK_DET);
            }
            if (null == tranDetPO.getMrpType() || "".equals(tranDetPO.getMrpType())) {
                // 明细表业务类型不能为空，请重新导入
                throw new BizException(ApplicationErrorCodeEnum.ERR_NULL_TYPE);
            }
            // 判断主表和明细表业务类型是否一致
            if (!tranDetPO.getMrpType().equals(mainMrpType)) {
                // 主表和明细表业务类型一致才可提交
                throw new BizException(ApplicationErrorCodeEnum.ERR_INCONSISTENCY_TYPE);
            }
        }

        //3、检查明细数据在台账中是否出现异动（即不满足导入条件）
        List<String> deviceCodes = tranDetPOList
                .stream()
                .map(ProtoTranDetPO::getDeviceCode)
                .collect(Collectors.toList());
        List<ProtoCardPO> protoCardPOS = protoCardService.findNotSelfCard(deviceCodes, tranPO.getUserCode(), null);
        StringBuilder errMessage = new StringBuilder();
        if (ObjectUtils.isNotEmpty(protoCardPOS)) {
            protoCardPOS.forEach(protoCardPO -> errMessage.append(" SN:").append(protoCardPO.getDeviceCode())
                    .append(" userCode:").append(protoCardPO.getUserCode() == null ? "" : protoCardPO.getUserCode())
                    .append(" state:").append(CardStateEnum.convert(protoCardPO.getUseState()).getDesc()));
            throw new BizException(ApplicationErrorCodeEnum.ERR_TRAN_CARD, errMessage.toString());
        }

        //分析转移类型
        String tranType = "";
        String applyUserType = "";
        if (!applyFlag && !accFlag) {
            //小米内部转移
            tranType = TranTypeEnum.INNER_TO_INNER.getKey();
            applyUserType = CommonConstant.ApplyUserTypeEnum.INNER.getValue();
        } else if (!applyFlag && accFlag) {
            //小米内部转外部
            tranType = TranTypeEnum.INNER_TO_ODM.getKey();
            applyUserType = CommonConstant.ApplyUserTypeEnum.ODM.getValue();
        } else if (applyFlag && !accFlag) {
            //小米外部转内部
            tranType = TranTypeEnum.ODM_TO_INNER.getKey();
            applyUserType = CommonConstant.ApplyUserTypeEnum.INNER.getValue();
        }

        //4、提交前校验，一二手都要校验,找出所有待确认的记录
        List<String> getOutCode = tranDetPOList
                .stream()
                .map(ProtoTranDetPO::getOutCode)
                .collect(Collectors.toList());
        List<ProtoOutPO> protoOutPOS = protoOutService.getOutPO(getOutCode, OutStateEnum.TO_BE_CONFIRMED);
        if (ObjectUtils.isNotEmpty(protoOutPOS)) {
            //以下发放单号尚未确认，请检查！
            String message = protoOutPOS.stream()
                    .map(ProtoOutPO::getOutCode)
                    .distinct()
                    .collect(Collectors.joining(","));
            throw new BizException(ApplicationErrorCodeEnum.ERR_TRAN_CHECK_OUT_CODE, message);
        }

        //5、分析成本中心与公司代码
        updateCenterCode(tranPO.getAccUserCode(), tranType, tranDetPOList);

        //6、更新转移类型、领用方类型、确认状态
        tranPO.setTranType(tranType);
        tranPO.setApplyUserType(applyUserType);
        tranPO.setConfirmStatus(TranConfirmStatusEnum.TO_BE_CONFIRMED.getKey());
        protoTranService.updateById(tranPO);

        //7、批量更新
        protoTranDetService.updateBatchById(tranDetPOList);
    }

    /**
     * 推送给sap
     *
     * @param tranPO
     * @param tranDetPOList
     * @param isCommit      转移提交 true 回调 false
     */
    public void pushSap(ProtoTranPO tranPO, List<ProtoTranDetPO> tranDetPOList, boolean isCommit) {
        //需要循环抛送SAP的次数（根据项目、成本中心、样机生产地拆单）
        Map<TranSapSplitDTO, List<ProtoTranDetPO>> splitTranDetMap = protoTranDetService.splitTranDetMap(tranDetPOList);
        log.info("according_to_project_cost_center :{}", splitTranDetMap);
        for (Map.Entry<TranSapSplitDTO, List<ProtoTranDetPO>> entry : splitTranDetMap.entrySet()) {
            TranSapSplitDTO key = entry.getKey();
            List<ProtoTranDetPO> tranDetPOS = entry.getValue();
            //获取当前记录项目
            String projectCode = key.getProjectCode();
            //获取当前记录成本中心
            String centerCode = key.getCenterCode();
            //获取当前样机生产地
            String makeCn = key.getMakeCn();
            // 设置批次号和BatchId
            this.setSapTranCodeBatchId(tranDetPOS, makeCn);
            // 校验是否存在尚未抛送SAP的发放记录
            List<String> outCodeList = tranDetPOS.stream().map(ProtoTranDetPO::getOutCode).collect(Collectors.toList());
            List<ProtoOutPO> protoOutPOS = protoOutService.getOutPO(outCodeList, OutStateEnum.ISSUED).stream()
                    .filter(protoOutPO -> SapRetEnum.ERROR.getKey().equals(protoOutPO.getSapRetType())
                            || SapRetEnum.UNTREATED.getKey().equals(protoOutPO.getSapRetType())
                            || StringUtils.isBlank(protoOutPO.getSapRetType())).collect(Collectors.toList());
            if (ObjectUtils.isNotEmpty(protoOutPOS)) {
                List<String> collect = protoOutPOS.stream().map(ProtoOutPO::getOutCode).collect(Collectors.toList());
                String exeBody = "以下发放单号尚未抛送SAP，请检查！ 【" + String.join(",", collect) + "】";
                protoSapExeLogService.insertLog(exeBody, "proto_tran", tranPO.getTranId(), tranPO.getTranCode(), "工程机转移", tranPO.getUserName(), "W");
                continue;
            }

            //获取成本中心--同个样机生产地同个成本中心，故取第一条记录
            String inCenterCode = tranDetPOS.get(0).getInCenterCode();
            //创立sap主表
            SapPhoneTransferDTO sapMain = new SapPhoneTransferDTO();
            // 设置单据号
            sapMain.setMidocNum(tranDetPOS.get(0).getSapTranCode());
            sapMain.setBillType(SapBatchIdEnum.MACHINE_TRANSFER.getBillType());
            sapMain.setBusinessType(CommonConstant.SapBusiTypeEnum.TRANSFER.getType());
            sapMain.setCreateUserName(tranPO.getAddUserid());
            sapMain.setRemark(tranPO.getRemark());
            sapMain.setKostl(inCenterCode);
            sapMain.setApplyDept(tranPO.getDeptCode());
            sapMain.setApplyPerson(tranPO.getUserCode());
            sapMain.setUsePerson(tranPO.getAccUserCode());
            sapMain.setMark(SAP_MARK);
            // 创建时传"Y"，Bpm回调时传空""
            if (!isCommit) {
                sapMain.setMark("");
            }
            //取自当前循环记录的成本中心、项目、样机生产地
            sapMain.setZzKostl(centerCode);
            sapMain.setProject(projectCode);
            if (StringUtils.isNotBlank(makeCn)) {
                sapMain.setSapFactory(makeCn.substring(0, 4));
            }
            // 添加SAP明细 --进一步拆分
            Map<TranSapSplitItemDTO, List<ProtoTranDetPO>> splitTranDetItemMap = new HashMap<>();
            for (ProtoTranDetPO detPO : tranDetPOS) {
                TranSapSplitItemDTO itemKey = new TranSapSplitItemDTO(detPO.getSkuCode(), detPO.getApplyItemid(), detPO.getApplyCode(), detPO.getOutCode());
                List<ProtoTranDetPO> splitTranDetItemPOList = splitTranDetItemMap.get(itemKey);
                if (ObjectUtils.isEmpty(splitTranDetItemPOList)) {
                    splitTranDetItemPOList = new ArrayList<>();
                }
                splitTranDetItemPOList.add(detPO);
                splitTranDetItemMap.put(itemKey, splitTranDetItemPOList);
            }
            log.info("SAP_detail_excrete_bill：{}", splitTranDetItemMap);
            int midocRow = 10;
            List<SapPhoneTransferDTO.Item> sapItems = new ArrayList<>();
            for (Map.Entry<TranSapSplitItemDTO, List<ProtoTranDetPO>> itemEntry : splitTranDetItemMap.entrySet()) {
                TranSapSplitItemDTO itemEntryKey = itemEntry.getKey();
                List<ProtoTranDetPO> itemEntryValue = itemEntry.getValue();
                String skuCode = itemEntryKey.getSkuCode();
                String applyCode = itemEntryKey.getApplyCode();
                String applyItemid = itemEntryKey.getApplyItemid();
                String outCode = itemEntryKey.getOutCode();

                SapPhoneTransferDTO.Item sapItem = new SapPhoneTransferDTO.Item();
                sapItem.setMidocRow(String.valueOf(midocRow));
                sapItem.setMaterialOne(skuCode);
                sapItem.setQuantityOne(String.valueOf(itemEntryValue.size()));
                sapItem.setApplyNo(applyCode);
                sapItem.setApplyNoRow(applyItemid);
                sapItem.setOutOrderCode(outCode);
                sapItems.add(sapItem);
                for (ProtoTranDetPO detPO : itemEntryValue) {
                    detPO.setSapItemRowNo(String.valueOf(midocRow));
                }
                //更新行号
                midocRow += 10;
            }
            sapMain.setItems(sapItems);

            // 推送SAP，调用转移接口
            String orderName = "";
            if (isCommit) {
                orderName = "工程机转移-校验";
            } else {
                orderName = "工程机转移";
            }

            SapCustomVO sapCustomVO = sapService.enginePhoneTransfer(sapMain, FUNCTION_ID,
                    tranPO.getTranId(), tranPO.getTranCode(), orderName, tranPO.getUserName(), tranPO.getMrpType());
            // 抛送SAP失败 或者 不是转移校验
            if (!sapCustomVO.getSuccess() || !isCommit) {
                //更新表状态
                tranDetPOS.forEach(protoTranDetPO -> protoTranDetPO.setSapRetType(sapCustomVO.getSuccess() ?
                        SapRetEnum.SUCCESS.getKey() : SapRetEnum.ERROR.getKey()));
            }
            //BMP成功回调 还需要调用107接口
            if (sapCustomVO.getSuccess() && !isCommit) {
                pushSap107(tranPO, key, tranDetPOS);
            }

        }
    }

    //推送SAP，调用107接口
    @Override
    public void pushSap107(ProtoTranPO tranPO, TranSapSplitDTO sapSplitDTO, List<ProtoTranDetPO> tranDetPOS) {
        String makeCn = sapSplitDTO.getMakeCn();
        String projectCode = sapSplitDTO.getProjectCode();
        // 设置批次号和BatchId
        this.setSapTranCodeBatchId(tranDetPOS, makeCn);

        String batchId = tranDetPOS.get(0).getBatchId();
        String sapTranCode = tranDetPOS.get(0).getSapTranCode();


        Sap107JsonDTO jsonDTO = new Sap107JsonDTO();
        jsonDTO.setBusiType(CommonConstant.SapBusiTypeEnum.TRANSFER.getType());
        if (StringUtils.isNotBlank(makeCn)) {
            jsonDTO.setSapFactory(makeCn.substring(0, 4));
        }
        jsonDTO.setBatchId(batchId);
        jsonDTO.setMidocNum(sapTranCode);
        jsonDTO.setWareType("TP");
        // 按照 skuCode, applyItemid, applyCode, outCode分组
        Map<String, List<ProtoTranDetPO>> tranDetMap = new HashMap<>();
        for (ProtoTranDetPO detPO : tranDetPOS) {
            String mapKey = detPO.getSkuCode() + "_" + detPO.getApplyItemid() + "_" + detPO.getApplyCode() + "_" + detPO.getOutCode();
            List<ProtoTranDetPO> tranList = tranDetMap.get(mapKey);
            if (null == tranList) {
                tranList = new ArrayList<>();
            }
            tranList.add(detPO);
            tranDetMap.put(mapKey, tranList);
        }
        List<Sap107JsonDTO.SapItemDTO> list = new ArrayList<>();
        for (Map.Entry<String, List<ProtoTranDetPO>> itemEntry : tranDetMap.entrySet()) {
            Sap107JsonDTO.SapItemDTO itemDTO = new Sap107JsonDTO.SapItemDTO();
            List<ProtoTranDetPO> tempTranDetList = itemEntry.getValue();
            ProtoTranDetPO detPO = tempTranDetList.get(0);
            //保持业务单据WEB端接收接口与107接口行号一致
            String sapItemRowNo = detPO.getSapItemRowNo();
            itemDTO.setBatchRow(sapItemRowNo);
            itemDTO.setMidocRow(sapItemRowNo);
            itemDTO.setMatDescOne(detPO.getSkuName());
            itemDTO.setMaterialOne(detPO.getSkuCode());
            itemDTO.setMiProjOne(projectCode);
            itemDTO.setCommItm(detPO.getOutCode());
            itemDTO.setVndocNum(detPO.getApplyCode());
            itemDTO.setVndocRow(detPO.getApplyItemid());
            itemDTO.setQuantityOne(String.valueOf(tempTranDetList.size()));
            list.add(itemDTO);
            // 设置行号
            for (ProtoTranDetPO tempTranDetPO : tempTranDetList) {
                tempTranDetPO.setSap107ItemRowNo(detPO.getSapItemRowNo());
            }
        }
        jsonDTO.setItemDTOList(list);
        SapCustomVO sapCustomVO = sapService.sap107Json(jsonDTO, FUNCTION_ID, tranPO.getTranId(),
                tranPO.getTranCode(), "工程机转移-107", tranPO.getUserName(), tranPO.getMrpType());
        for (ProtoTranDetPO detPO : tranDetPOS) {
            detPO.setIsSuccess(sapCustomVO.getSuccess() ? SapRetEnum.SUCCESS.getKey() : SapRetEnum.ERROR.getKey());
        }
    }

    /**
     * 更新成本中心
     *
     * @param accUserCode
     * @param tranType
     * @return
     */
    private void updateCenterCode(String accUserCode,
                                  String tranType, List<ProtoTranDetPO> tranDetPOList) {
        //如果是小米转odm，成本中心不发生变化
        if (TranTypeEnum.INNER_TO_ODM.getKey().equals(tranType)) {
            tranDetPOList.forEach(protoTranDetPO -> {
                protoTranDetPO.setInCenterCode(protoTranDetPO.getCenterCode());
                protoTranDetPO.setInCompDeptCode(protoTranDetPO.getInCompDeptCode());
            });
            protoTranDetService.updateBatchById(tranDetPOList);
            return;
        }
        //否则取接收人本身成本中心
        for (ProtoTranDetPO detPO : tranDetPOList) {
            if (CommonConstant.ProtoCardAssetNewOld.ONEHAND.getType()
                    .equals(detPO.getAssetNewold())) {
                CostCenter centerCode = protoService.getSAPCenterCode(accUserCode, CommonConstant.ApplyUserTypeEnum.INNER.getValue(),
                        detPO.getMakeCn());
                if (centerCode == null) {
                    throw new BizException(ApplicationErrorCodeEnum.ERR_TRAN_NOT_COST_CENTER);
                }
                detPO.setInCenterCode(centerCode.getCenterCode());
                detPO.setInCompDeptCode(centerCode.getCompDeptCode());
            }
        }
        protoTranDetService.updateBatchById(tranDetPOList);
    }

    public void commit(String tranId) {
        // 1、查询主表及明细数据
        ProtoTranPO tranPO = this.getById(tranId);
        if (tranPO == null) {
            throw new BizException(ApplicationErrorCodeEnum.NOT_FIND_BY_TRAN_ID);
        }
        List<ProtoTranDetPO> tranDetPOList = protoTranDetService.getByTranId(tranPO.getTranId());
        log.info("commit tran tranId:{} tranPO:{} tranDetPOList:{}", tranId, tranPO, tranDetPOList);

        ProtoTranBizDTO protoTranBizDTO = protoTranBizConverter.protoTranBizPOToDTO(tranPO);
        List<ProtoTranBizDetDTO> protoTranBizDetDTOList = protoTranBizDetConverter.protoTranDetPOToDTOList(tranDetPOList);

        //2、更新工程机台账对应记录状态为转移中
        List<String> deviceCodeList = tranDetPOList.stream()
                .map(ProtoTranDetPO::getDeviceCode)
                .collect(Collectors.toList());
        // 售后样机
        if (MrpTypeEnum.PROTOTYPE.getType().equals(tranPO.getMrpType())) {
            List<ProtoCardPO> protoCardPOList = protoCardService.getByDeviceCodeAndExcludeUseState(deviceCodeList, CardStateEnum.UNABLE_TO_RETURN.getKey());
            deviceCodeList = protoCardPOList.stream().filter(protoCard -> !CardStateEnum.UNABLE_TO_RETURN.getKey().equals(protoCard.getUseState()))
                    .map(ProtoCardPO::getDeviceCode).collect(Collectors.toList());
        }
        protoCardService.updateState(deviceCodeList, CardStateEnum.TRANSFERRING, tranPO.getMrpType());

        int code = 1;
        for (ProtoTranBizDetDTO protoTranBizDetDTO : protoTranBizDetDTOList) {
            protoTranBizDetDTO.setCode(String.valueOf(code++));
            protoTranBizDetDTO.setAssetNewold(funAllControlService.getComboValue("asset_newold", setValue(protoTranBizDetDTO.getAssetNewold())));
        }
        String tranType = setValue(tranPO.getTranType());
        String mrpType = setValue(tranPO.getMrpType());
        String applyUserCode = setValue(tranPO.getApplyUserType());

        protoTranBizDTO.setTranType(funAllControlService.getComboValue("proto_tran_type", tranType));
        protoTranBizDTO.setMrpType(funAllControlService.getComboValue("mrp_type", tranPO.getMrpType()));
        protoTranBizDTO.setProtoTranBizDetDTOList(protoTranBizDetDTOList);

        Map<String, Object> variables = new HashMap<>();
        variables.put("tran_type", tranType);
        variables.put("dept_code", "");
        variables.put("mrp_type", mrpType);  // 工程机类型

        if (MrpTypeEnum.MOBILE.getType().equals(tranPO.getMrpType()) || MrpTypeEnum.TV.getType().equals(tranPO.getMrpType())) {
            if (TranTypeEnum.INNER_TO_INNER.getKey().equals(tranType)) {
                if (StringUtils.isAnyBlank(tranPO.getDeptCode(), tranPO.getInDeptCode())) {
                    throw new BizException(ApplicationErrorCodeEnum.ERR_TRAN_DEPT_CODE);
                }
                variables.put("cross_dept", tranPO.getDeptCode().substring(0, 2).equals(tranPO.getInDeptCode().substring(0, 2)) ? "0" : "1"); // 是否跨部门
            } else {
                variables.put("cross_dept", "0");
            }
        }


        // 非三方借用需要部门编码信息，判断是否中国区
        if (!applyUserCode.equals(CommonConstant.ApplyUserTypeEnum.ODM.getValue())) {
            // 查询接收人员工信息，为了获取员工一级部门编码
            EmployeeInfo employeeInfo = userInfoService.getEmpInfoByUserName(applyUserCode);
            if (null != employeeInfo && null != employeeInfo.getDeptIdLv1()) {
                variables.put("dept_code", employeeInfo.getDeptIdLv1());
            }
        }

        //3、创建Bpm流程
        String businessKey = bpmV3Service.submitProtoTranApply(protoTranBizDTO, tranPO.getUserCode(), variables);
        if (StringUtils.isBlank(businessKey)) {
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_001);
        }

        //4、保存流程信息
        tranPO.setAuditing(TranAuditEnum.CHECKING.getKey());
        tranPO.setModifyDate(new Date());
        tranPO.setApplyDate(new Date());
        tranPO.setBusinessKey(businessKey);
        protoTranService.updateById(tranPO);

        //6、保存bpm 日志
        BpmExeLogDTO logDTO = BpmExeLogDTO.builder()
                .funId(FUNCTION_ID)
                .orderId(tranId)
                .orderCode(tranPO.getTranCode())
                .orderName(FUNCTION_NAME)
                .doUser(tranPO.getUserName())
                .exeCode(true)
                .exeDesc(businessKey)
                .exeBody(protoTranBizDTO)
                .build();
        bpmExeLogService.save(logDTO);
    }

    @Override
    public void sendAuditMessage(ProtoTranPO tranPO, List<ProtoTranDetPO> tranDetPOList,
                                 String businessKey) {

        List<ProtoCardPO> cardList = BeanUtil.copyListProperties(tranDetPOList, ProtoCardPO.class);
        String title = "【工程机】转移申请待确认";
        Map<String, String> valueMap = new HashMap<>();
        valueMap.put("user_name", tranPO.getUserName());
        valueMap.put("user_code", tranPO.getUserCode());
        valueMap.put("acc_user_code", tranPO.getAccUserCode());
        valueMap.put("acc_user_name", tranPO.getAccUserName());
        valueMap.put("todayStr", DateUtils.getFormatDate(DateUtils.COMMON_PATTERN));
        valueMap.put("emailCn", protoCardService.getEmailTableContent(cardList, true));
        valueMap.put("emailEn", protoCardService.getEmailTableContent(cardList, false));
        valueMap.put("larkTableCn", protoCardService.getLarkTableContent(cardList, true));
        valueMap.put("larkTableEn", protoCardService.getLarkTableContent(cardList, false));
        String linkUrl = protoService.getBpmUrl(tranPO.getBusinessKey(), tranPO.getAccUserCode());
        valueMap.put("linkUrl", linkUrl);
        String messageContent = mailTemplateService.getMailContent(ProtoMessageEnum.TRAN_SUBMIT_LARK.getTemplateTag(), valueMap);
        messageService.sendLark(LarkDTO.builder()
                .userNames(Collections.singletonList(tranPO.getAccUserCode()))
                .title(title)
                .url(linkUrl)
                .content(messageContent)
                .buttonName("去确认")
                .build());

        // 发送邮件通知
        String emailContent = mailTemplateService.getMailContent(ProtoMessageEnum.TRAN_SUBMIT_EMAIL.getTemplateTag(), valueMap);
        String email = userInfoService.getEmailByName(tranPO.getAccUserCode());
        messageService.sendEmail(EmailDTO.builder()
                .emails(Collections.singletonList(email))
                .title(title)
                .content(emailContent)
                .build());

    }

    @Override
    public void sendSuccessMessage(ProtoTranPO tranPO, List<ProtoTranDetPO> tranDetPOList) {
        String title = "【工程机】转移申请已确认";
        String url = assetHost + "?usercode=" + tranPO.getUserCode() + "&nodeid=" + FUNCTION_ID + "&keyid=" + tranPO.getTranId();
        List<ProtoCardPO> cardList = BeanUtil.copyListProperties(tranDetPOList, ProtoCardPO.class);
        Map<String, String> valueMap = new HashMap<>();
        valueMap.put("user_name", tranPO.getUserName());
        valueMap.put("user_code", tranPO.getUserCode());
        valueMap.put("acc_user_code", tranPO.getAccUserCode());
        valueMap.put("acc_user_name", tranPO.getAccUserName());
        valueMap.put("todayStr", DateUtils.getFormatDate(DateUtils.COMMON_PATTERN));
        valueMap.put("emailCn", protoCardService.getEmailTableContent(cardList, true));
        valueMap.put("emailEn", protoCardService.getEmailTableContent(cardList, false));
        valueMap.put("larkTableCn", protoCardService.getLarkTableContent(cardList, true));
        valueMap.put("larkTableEn", protoCardService.getLarkTableContent(cardList, false));

        String messageContent = mailTemplateService.getMailContent(ProtoMessageEnum.TRAN_SUCCESS_LARK.getTemplateTag(), valueMap);
        messageService.sendLark(LarkDTO.builder()
                .userNames(Collections.singletonList(tranPO.getUserCode()))
                .title(title)
                .url(url)
                .content(messageContent)
                .buttonName("去查看")
                .build());

        // 发送邮件通知
        String emailContent = mailTemplateService.getMailContent(ProtoMessageEnum.TRAN_SUCCESS_EMAIL.getTemplateTag(), valueMap);
        String email = userInfoService.getEmailByName(tranPO.getUserCode());
        messageService.sendEmail(EmailDTO.builder()
                .emails(Collections.singletonList(email))
                .title(title)
                .content(emailContent)
                .build());
    }

    @Deprecated
    private void sendApproveMessage(ProtoTranPO tranPO, List<ProtoTranDetPO> tranDetPOList) {
        TemplateMsgBaseDTO baseDTO = TranApproveEmailDTO.builder()
                .tranCode(tranPO.getTranCode())
                .accUserName(tranPO.getAccUserName())
                .accUserCode(tranPO.getAccUserCode())
                .userCode(tranPO.getUserCode())
                .userName(tranPO.getUserName())
                .eamUrl(assetHost)
                .suffixParam("&amp;nodeid=proto_tran&amp;pagetype=grid&amp;iswarn=1")
                .today(DateUtils.getFormatDate(DateUtils.COMMON_PATTERN))
                .build();
        baseDTO.setContainUrl(true);
        baseDTO.setDataId(tranPO.getTranId());
        baseDTO.setFunId(FUNCTION_ID);

        // p账号不发送飞书通知
        if (!UserUtil.isPAccount(tranPO.getAccUserCode())) {
            messageService.sendLark(TRAN_AUDIT_SUCCESS_TAG,
                    Collections.singletonList(tranPO.getAccUserCode()),
                    getLarkMat(tranDetPOList),
                    baseDTO);
        }

        // 发送邮件通知
        String email = userInfoService.getEmailByName(tranPO.getAccUserCode());
        messageService.sendEmail(TRAN_AUDIT_SUCCESS_TAG,
                Collections.singletonList(email),
                getEmailMat(tranDetPOList), baseDTO);
    }

    /**
     * 组装bpm接口参数
     *
     * @param tranPO        转移主单
     * @param tranDetPOList 转移明细单
     * @return
     */
    private SubmitProcessDTO setBpmParam(ProtoTranPO tranPO, List<ProtoTranDetPO> tranDetPOList) {
        MobileTransferProcessBizDTO mobileTransferProcessBizDTO = MobileTransferProcessBizDTO.builder()
                .receiver(tranPO.getAccUserName() + "(" + tranPO.getAccUserCode() + ")")
                .remark(tranPO.getRemark())
                .build();
        VaraibleDTO varaibleDTO = VaraibleDTO
                .builder()
                .receiver(tranPO.getAccUserCode())
                .build();
        MobileProcessBaseBizDTO baseBizDTO = MobileProcessBaseBizDTO.builder()
                .mobileTransferProcessBizDTO(mobileTransferProcessBizDTO)
                .varaibleDTO(varaibleDTO)
                .bizId(tranPO.getTranCode())
                .subProcess(MobileProcessBaseBizDTO
                        .SubProcessEnum
                        .convertValueToEnum(Integer.valueOf(tranPO.getTranType())))
                .build();
        //明细
        List<MobileItemBizDTO> items = new ArrayList<>();
        for (int index = 0; index < tranDetPOList.size(); index++) {
            ProtoTranDetPO protoTranDetPO = tranDetPOList.get(index);
            MobileItemBizDTO mobileItemBizDTO = MobileItemBizDTO.builder()
                    .imei(protoTranDetPO.getImei())
                    .desc(protoTranDetPO.getSkuName())
                    .number(protoTranDetPO.getDeviceCode())
                    .radiumCarvingNumber(protoTranDetPO.getLaserCode())
                    .project(protoTranDetPO.getProjectCode())
                    .sn(String.valueOf(index + 1))
                    .miMaterialCode(protoTranDetPO.getSkuCode())
                    .reqNumber(protoTranDetPO.getApplyCode())
                    .stage(protoTranDetPO.getStageName())
                    .type(protoTranDetPO.getAssetNewold())
                    .remark(protoTranDetPO.getRemark())
                    .build();
            items.add(mobileItemBizDTO);
        }
        mobileTransferProcessBizDTO.setItems(items);
        return SubmitProcessDTO
                .builder()
                .mobileProcessBaseBizDTO(baseBizDTO)
                .startUser(tranPO.getUserCode())
                .mobileProcessEnum(MobileProcessEnum.PROCESS_KEY_MOBILE_TRANSFER)
                .build();
    }

    private String getEmailMat(List<ProtoTranDetPO> tranDetPOS) {
        StringBuilder sbf = new StringBuilder();
        int index = 0;
        sbf.append("<table border=\"1px\" bordercolor=\"#000000\" cellspacing=\"0px\" style=\"border-collapse:collapse;text-align: center;width: 820px;\">")
                .append("<tr>")
                .append("<td width=\"40px\">序号</td>")
                .append("<td width=\"130px\">申请单号</td>")
                .append("<td width=\"80px\">项目名</td>")
                .append("<td width=\"50px\">阶段</td>")
                .append("<td width=\"130px\">料号</td>")
                .append("<td width=\"50px\">配置</td>")
                .append("<td width=\"80px\">镭雕号</td>")
                .append("<td width=\"130px\">IMEI号</td>")
                .append("<td width=\"130px\">SN号</td>")
                .append("</tr>");
        for (ProtoTranDetPO tranDetPO : tranDetPOS) {
            index++;
            sbf.append("<tr>")
                    .append("<td>").append(index).append("</td>")
                    .append("<td>").append(tranDetPO.getApplyCode()).append("</td>")
                    .append("<td>").append(tranDetPO.getProjectCode()).append("</td>")
                    .append("<td>").append(tranDetPO.getStageName()).append("</td>")
                    .append("<td>").append(tranDetPO.getSkuCode()).append("</td>")
                    .append("<td></td>")
                    .append("<td>").append(tranDetPO.getLaserCode()).append("</td>")
                    .append("<td>").append(tranDetPO.getImei()).append("</td>")
                    .append("<td>").append(tranDetPO.getDeviceCode()).append("</td>")
                    .append("</tr>");
        }
        sbf.append("</table>");
        return sbf.toString();
    }

    private String getLarkMat(List<ProtoTranDetPO> tranDetPOS) {
        StringBuilder sbf = new StringBuilder();
        sbf.append("  小米料号         领用单号         SN<br/>");
        for (ProtoTranDetPO tranDetPO : tranDetPOS) {
            sbf.append(tranDetPO.getSkuCode() + "   " +
                    tranDetPO.getApplyCode() + "   " +
                    tranDetPO.getDeviceCode() + "<br/>");
        }
        return sbf.toString();
    }

    /**
     * 工程机转移查询
     *
     * @return
     */
    @Override
    public AssetCommonDTO query(TranReqDTO requestParam) {
        int start = requestParam.getStart();
        int limit = requestParam.getLimit();
        //按条件查询台账信息
        List<ConditionDTO> conditionList = commonService.getConditionList(requestParam);
        String userCode = requestParam.getUserCode();
        // 是否拥有工程机管理员或者超级管理员角色
        Integer count = sysUserRoleService.countByRoleNoAndUserCode(CommonConstant.ADMIN_ROLE, userCode)
                + sysUserRoleService.countByRoleNoAndUserCode(CommonConstant.GCJ_ADMIN_ROLE, userCode);
        // 部门权限
        List<String> deptCodeList = null;
        // 没有管理员权限
        if (count == 0) {
            List<SysUserDataPO> userDataList = sysUserDataService.getByUserCodeAndDataType(userCode, CommonConstant.DEPT_CODE_FIELD);
            deptCodeList = userDataList.stream().map(SysUserDataPO::getDtypeData).collect(Collectors.toList());
        }
        List<ProtoTranPO> list = protoTranService.getTranList(requestParam.getUserCode(), conditionList, start, limit, count, deptCodeList, requestParam.getWhereSql());
        // 查询总数
        Integer total = protoTranService.countTranList(requestParam.getUserCode(), conditionList, count, deptCodeList, requestParam.getWhereSql());
        List<TranDTO> tranPOtoDTOs = tranVOConverter.tranPOtoDTOs(list);
        return AssetCommonDTO.builder().total((long) total).root(tranPOtoDTOs).build();
    }

    @Override
    public void tranClearDet(String keyId, String mrpType) {
        // 删除业务类型不同的数据
        protoTranDetService.remove(new LambdaQueryWrapper<ProtoTranDetPO>().eq(ProtoTranDetPO::getTranId, keyId).ne(ProtoTranDetPO::getMrpType, mrpType));
        int num = protoTranDetService.list(new LambdaQueryWrapper<ProtoTranDetPO>().eq(ProtoTranDetPO::getTranId, keyId)).size();
        protoTranService.update(null, new LambdaUpdateWrapper<ProtoTranPO>().set(ProtoTranPO::getNum, num).eq(ProtoTranPO::getTranId, keyId));
    }

    /**
     * 审批通过发送消息通知
     */
    public void auditPassNotify(String tranId) {
        String tag = "proto_tran";
        ProtoTranPO protoTranPO = protoTranService.getByTranId(tranId);
        if (!CommonConstant.MOBILE_TYPE.equals(protoTranPO.getMrpType())) {
            return;
        }
        //转移单号
        String tranCode = protoTranPO.getTranCode();
        //申请人
        String userCode = protoTranPO.getUserCode();
        //接收人
        String accUserCode = protoTranPO.getAccUserCode();
        //接收时间
        String applyDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(protoTranPO.getApplyDate());
        //明细信息
        List<ProtoListDetDTO> detList = getDets(tranId);
        ProtoListDTO protoListDTO = ProtoListDTO.builder()
                .tranCode(tranCode)
                .applyUserCode(userCode)
                .accUserCode(accUserCode)
                .applyDate(applyDate)
                .items(detList)
                .build();
        protoListStatusNotifyService.sendMessage(tag, protoListDTO);
    }

    /**
     * 设置SAP转移单号和batchID
     *
     * @param tranDetPOS
     * @param makeCn
     */
    private void setSapTranCodeBatchId(List<ProtoTranDetPO> tranDetPOS, String makeCn) {
        //获取批次号
        String batchId = "";
        //sap转移单号
        String sapTranCode = tranDetPOS.get(0).getSapTranCode();
        if (StringUtils.isNotBlank(sapTranCode)) {
            return;
        }
        //SAP转移单号生成
        sapTranCode = commonService.getBatchIdUnique(SapBatchIdEnum.MACHINE_TRANSFER_M);
        if (SapFactoryEnum.YI_ZHUANG.getMakeCn().equals(makeCn) || SapFactoryEnum.YI_ZHUANG.getSapFactory().equals(makeCn)) {
            batchId = commonService.getBatchIdUnique(SapBatchIdEnum.YI_ZHUANG);
        } else if (SapFactoryEnum.CHANG_PING.getMakeCn().equals(makeCn) || SapFactoryEnum.CHANG_PING.getSapFactory().equals(makeCn)) {
            batchId = commonService.getBatchIdUnique(SapBatchIdEnum.CHANG_PING);
        } else if (SapFactoryEnum.CHANG_PING2.getMakeCn().equals(makeCn) || SapFactoryEnum.CHANG_PING2.getSapFactory().equals(makeCn)) {
            batchId = commonService.getBatchIdUnique(SapBatchIdEnum.CHANG_PING2);
        } else if (SapFactoryEnum.ROBOT.getMakeCn().equals(makeCn) || SapFactoryEnum.ROBOT.getSapFactory().equals(makeCn)) {
            batchId = commonService.getBatchIdUnique(SapBatchIdEnum.ROBOT);
        } else {
            batchId = commonService.getBatchIdUnique(SapBatchIdEnum.ASN_RECEIVE_GOODS);
        }
        // 更新ProtoTranDetPO
        for (ProtoTranDetPO po : tranDetPOS) {
            po.setSapTranCode(sapTranCode);
            po.setBatchId(batchId);
        }
    }

    private List<ProtoListDetDTO> getDets(String fKeyId) {
        //查询子表信息
        List<ProtoTranDetPO> protoTranDetList = protoTranDetService.getByTranId(fKeyId);
        List<ProtoListDetDTO> items = new ArrayList<>();
        if (!protoTranDetList.isEmpty()) {
            for (ProtoTranDetPO detPo : protoTranDetList) {
                ProtoListDetDTO item = ProtoListDetDTO.builder()
                        .skuCode(detPo.getSkuCode())
                        .skuName(detPo.getSkuName())
                        .projectCode(detPo.getProjectCode())
                        .stageName(detPo.getStageName())
                        .deviceType(detPo.getDeviceType())
                        .laserCode(detPo.getLaserCode())
                        .deviceCode(detPo.getDeviceCode())
                        .imei(detPo.getImei())
                        .build();
                items.add(item);
            }
        }
        return items;
    }

    /**
     * 判断为null 则返回空字符串
     *
     * @param value
     * @return
     */
    private String setValue(String value) {
        if (StringUtil.isEmpty(value)) {
            value = "";
        }
        return value;
    }

}
