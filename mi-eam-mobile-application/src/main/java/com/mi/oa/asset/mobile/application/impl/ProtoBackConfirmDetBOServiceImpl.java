package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.ProtoBackConfirmDetBOService;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.FunAllControlService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoService;
import com.mi.oa.asset.mobile.utils.BeanUtil;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/20 16:21
 */
@Service
@Slf4j
public class ProtoBackConfirmDetBOServiceImpl implements ProtoBackConfirmDetBOService {
    @Autowired
    private ProtoInService protoInService;
    @Autowired
    private ProtoInDetService protoInDetService;
    @Autowired
    private ProtoService protoService;
    @Autowired
    private FunAllControlService funAllControlService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String setIsComplete(String inId, String isComplete, String[] detIds) {
        ProtoInPO protoInMap = protoInService.getById(inId);
        if(ObjectUtils.isEmpty(protoInMap)){
            throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR,RedisUniqueKeyEnum.PROTO_IN.getTableName(),inId);
        }
        checkProtoIn(protoInMap);

        // 更新选中的明细的良品状态和归还状态
        for (String det_id : detIds) {
            ProtoInDetPO detPO = ProtoInDetPO.builder().detId(det_id).isComplete(isComplete)
                    .isBack(ProtoInDetIsBackEnum.RETURNING_CONFIRMATION.getCode()).modifyDate(new Date()).build();
            try {
                protoInDetService.updateById(detPO);
            } catch (Exception e) {
                log.error("更新明细是否良品失败");
                //  更新明细是否良品失败 JsMessage.getValue("proto_back_confirm.ht_text005")
                throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_CONFIRM_005);
            }
        }

        return CommonConstant.TRUE;
    }

    @Override
    public String scanConfirm(String inId, String keyword) {

        // 解析扫码结果
        Map<String, String> scanResultMap = protoService.tranScanResult(keyword);
        String device_code = scanResultMap.get("device_code");

        ProtoInPO protoInMap = protoInService.getById(inId);
        if(ObjectUtils.isEmpty(protoInMap)){
            throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR,RedisUniqueKeyEnum.PROTO_IN.getTableName(),inId);
        }
        checkProtoIn(protoInMap);

        String in_code = protoInMap.getInCode();
        // 查询归还确认明细
        ProtoInDetPO protoInDetMap = protoInDetService.findOneByParam(ProtoInDetPO.builder().inId(inId).deviceCode(device_code).build());
        if (ObjectUtils.isEmpty(protoInDetMap)) {
            // 扫描的SN号【{0}】不存在归还单【{1}】中！ JsMessage.getValue("proto_back_confirm.ht_text002", device_code, in_code)
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_CONFIRM_002,device_code,in_code);
        }
        String is_back = protoInDetMap.getIsBack();
        if (!ProtoInDetIsBackEnum.UNCONFIRMED.getCode().equals(is_back)) {
            String is_complete = protoInDetMap.getIsComplete();
            is_complete = funAllControlService.getComboValue("proto_is_complete", is_complete);
            // 扫描的SN号【{0}】已确认，是否良品【{1}】 JsMessage.getValue("proto_back_confirm.ht_text003", device_code, is_complete)
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_CONFIRM_003,device_code,is_complete);
        }

        // 进行归还中
        String det_id = protoInDetMap.getDetId();
        try {
            protoInDetService.updateById(ProtoInDetPO.builder().detId(det_id).isBack(ProtoInDetIsBackEnum.RETURNING_CONFIRMATION.getCode()).modifyDate(new Date()).build());
        } catch (Exception e) {
            log.error("进行归还预确认失败!");
            // 扫描的SN号【{0}】进行预确认失败！ JsMessage.getValue("proto_back_confirm.ht_text004", device_code)
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_CONFIRM_004,device_code);
        }

        return det_id;
    }

    /**
     * 检测归还单
     *
     * @param protoInMap
     * @return
     */
    private void checkProtoIn(ProtoInPO protoInMap) {
        String auditingBackConfirm = protoInMap.getAuditingBackConfirm();
        String inStatus = protoInMap.getInStatus();
        if (MachAuditingEnum.HANDLE_MACH.getState().equals(auditingBackConfirm)
                || MachAuditingEnum.HAVE_MACH.getState().equals(auditingBackConfirm) || InStatusEnum.DUMPED_SAP.getState().equals(inStatus)) {
            //  该归还单已完成确认归还，不允许重复操作！JsMessage.getValue("proto_back_confirm.ht_text001")
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_CONFIRM_001);
        }
    }
}
