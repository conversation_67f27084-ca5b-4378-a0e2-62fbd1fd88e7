package com.mi.oa.asset.mobile.application.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.mobile.application.service.ProtoCardBOService;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.dto.hrod.EmployeeInfo;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoThirdUserPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysDeptPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.infra.oaucf.idm.api.rep.UserBaseInfoDto;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/3/30
 */
@Service
public class ProtoCardBOServiceImpl implements ProtoCardBOService {

    private static final Logger log = LoggerFactory.getLogger(ProtoCardBOServiceImpl.class);
    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private SysDeptService sysDeptService;

    @Autowired
    private ProtoThirdUserService protoThirdUserService;

    @Autowired
    private ProtoInService protoInService;

    @Autowired
    private MrpMachGrantRevertService mrpMachGrantRevertService;

    @Override
    public void refreshOldData() {
        protoCardService.updateSycFlag("0");
        Map<String, String> userCodeUserNameMap = new HashMap<>(1024);
        Map<String, String> userCodeEmpCodeMap = new HashMap<>(1024);
        while (true) {
            List<ProtoCardPO> list = protoCardService.listBySycFlag("0", CommonConstant.DEFAULT_PAGE_SIZE);
            List<String> snList = list.stream().map(ProtoCardPO::getDeviceCode).collect(Collectors.toList());
            // 新系统时间
            Map<String, Date> entryDateMap = this.getEntryDate(snList);
            // 老系统 归还时间
            Map<String, Date> revertTimeMap = mrpMachGrantRevertService.getRevertTimeMap(snList);
            // 老系统 发放时间
            Map<String, Date> grantTimeMap = mrpMachGrantRevertService.getGrantTimeMap(snList);
            // 老系统发放人 map
            Map<String, String> grantUserMap = mrpMachGrantRevertService.getGrantUserMap(snList);

            for (ProtoCardPO cardPO : list){
                String deviceCode = cardPO.getDeviceCode();
                cardPO.setSycFlag("3");
                // 先取新系统时间  再取老系统归还时间  再取老系统发放时间
                Date entryDate = entryDateMap.get(deviceCode);
                if (null == entryDate){
                    entryDate = revertTimeMap.get(deviceCode);
                }
                if (null == entryDate){
                    entryDate = grantTimeMap.get(deviceCode);
                }
                cardPO.setEntryDate(entryDate);
                String oriProjectCode = cardPO.getOriProjectCode();
                // 老系统数据
                if (StringUtils.isNotBlank(oriProjectCode)){
                    cardPO.setApplyDate(grantTimeMap.get(deviceCode));
                    // 发放人账号
                    String outUserCode = grantUserMap.get(deviceCode);
                    if (StringUtils.isNotBlank(outUserCode)){
                        cardPO.setOutUserCode(outUserCode);

                        String userName = userCodeUserNameMap.get(outUserCode);
                        String empCode = userCodeEmpCodeMap.get(outUserCode);
                        // 查询工号
                        if (StringUtils.isBlank(userName) || StringUtils.isBlank(empCode)){
                            try {
                                UserBaseInfoDto info = userInfoService.getCacheUserInfoByUserName(outUserCode);
                                if (null != info){
                                    userCodeUserNameMap.put(outUserCode, info.getName());
                                    userCodeEmpCodeMap.put(outUserCode, info.getPersonId());
                                }
                            }catch (Exception e){
                            }
                        }
                        cardPO.setOutUserName(userCodeUserNameMap.get(outUserCode));
                        cardPO.setOutEmpCode(userCodeEmpCodeMap.get(outUserCode));
                    }
                }
            }
            protoCardService.updateBatchById(list);
            if (list.size() < CommonConstant.DEFAULT_PAGE_SIZE){
                return;
            }
        }
    }

    @Override
    public void refreshAllDeptName() {
        // 查询所有有用户
        List<String> userCodeList = protoCardService.getNotRefreshUserCode();
        for (String userCode : userCodeList){
            try {
                String deptId = "";
                String longDeptName = "";
                String empCode = "";
                // p账号
                if (StringUtils.startsWith(userCode, "p-")) {
                    ProtoThirdUserPO userInfo = protoThirdUserService.findUserInfoCache(userCode);
                    if (Objects.nonNull(userInfo)) {
                        longDeptName = userInfo.getProviderName();
                    }
                } else {
                    UserBaseInfoDto info = userInfoService.getUserInfoByUserName(userCode);
                    if (Objects.nonNull(info)) {
                        deptId = info.getDeptId();
                        longDeptName = info.getDeptDescr();
                        empCode = info.getPersonId();
                        SysDeptPO deptPO = sysDeptService.getCacheByDeptId(deptId);
                        if (Objects.nonNull(deptPO)) {
                            longDeptName = deptPO.getLongDeptName();
                        }
                    }
                }
                if (StringUtils.isAllBlank(deptId, longDeptName, empCode)){
                    continue;
                }
                protoCardService.updateLongDeptNameByUserCode(deptId, longDeptName, empCode, userCode);
            } catch (Exception e) {
                log.error("refreshAllDeptName error. userCode:{}, {}", userCode, e.getMessage(), e);
            }
        }
    }

    @Override
    public void refreshEntryDate() {
        while (true) {
            List<ProtoCardPO> list = protoCardService.listNotEntryDate(CommonConstant.DEFAULT_PAGE_SIZE);
            for (ProtoCardPO cardPO : list){
                String inCode = cardPO.getInCode();
                String dateStr = "";
                if (inCode.startsWith("D")){
                    // D721101100648
                    dateStr = StringUtils.substring(inCode, 2, 8);
                } else {
                    // F1Y
                    dateStr = StringUtils.substring(inCode, 3, 9);
                }
                try {
                    Date entryDate = DateUtils.stringToDate(dateStr, "yyMMdd");
                    cardPO.setEntryDate(entryDate);
                } catch (Exception e) {
                }
            }
            protoCardService.updateBatchById(list);
            if (list.size() < CommonConstant.DEFAULT_PAGE_SIZE){
                return;
            }
        }

    }

    /**
     * 获取入库时间优先级
     * 1、EAM取归还时间
     * 2、收货时间
     * 3、老系统的最新时间
     * @param sn
     * @return
     */
    @Override
    public Map<String, Date> getEntryDate(List<String> snList) {
        Map<String, Date> result = new HashMap<>(snList.size()*2);
        List<Map<String, Object>> entryList = protoInService.getEntryDate(snList);
        for (Map<String, Object> dateMap : entryList){
            result.put(dateMap.get("sn").toString(), (Date) dateMap.get("inDate"));
        }
        return result;
    }

    @Override
    public void refreshAllApplyDeptCode() {
        protoCardService.updateSycFlag("0");
        Map<String, EmployeeInfo> infoMap = new HashMap<>(1024);
        while (true){
            List<ProtoCardPO> list = protoCardService.list(
                    Wrappers.<ProtoCardPO>lambdaQuery()
                            .select(ProtoCardPO::getDeviceId, ProtoCardPO::getApplyDeptCode,
                                    ProtoCardPO::getApplyUserCode, ProtoCardPO::getApplyEmpCode)
                            .eq(ProtoCardPO::getSycFlag, "0")
                            .last("limit 1000")
            );
            for (ProtoCardPO cardPO : list){
                cardPO.setSycFlag("1");
                String applyDeptCode = cardPO.getApplyDeptCode();
                String applyUserCode = cardPO.getApplyUserCode();
                String applyEmpCode = cardPO.getApplyEmpCode();
                if (StringUtils.isNotBlank(applyDeptCode) || StringUtils.isBlank(applyUserCode)){
                    continue;
                }
                EmployeeInfo info = infoMap.get(applyUserCode);
                if (null == info){
                    try {
                        info = userInfoService.getEmpInfoByUserName(applyUserCode);
                        if (null != info){
                            infoMap.put(applyUserCode, info);
                        }
                    } catch (Exception e){}
                }
                if (null == info && StringUtils.isNotBlank(applyEmpCode)){
                    try {
                        info = userInfoService.getEmpInfoByEmpId(applyEmpCode);
                        if (null != info){
                            infoMap.put(info.getUserName(), info);
                        }
                    } catch (Exception e){}
                }
                if (null != info){
                    cardPO.setApplyDeptCode(info.getDeptId());
                    cardPO.setApplyLongDeptName(info.getDeptName());
                }
            }
            protoCardService.updateBatchById(list);
            if (list.size() < 1000){
                return;
            }
        }
    }
}
