package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.service.ProtoAsnOrderBOService;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/2/16
 */
@Service
@Slf4j
public class ProtoAsnOrderBOServiceImpl implements ProtoAsnOrderBOService {

    @Autowired
    private ProtoService protoService;

    @Override
    public Map<String, String> scan(RequestContext request) {
        String scanResult = request.getRequestValue(CommonConstant.SCAN_RESULT);
        // 解析扫码
        Map<String, String> scanResultMap = protoService.tranScanResult(scanResult);
        return null;
    }
}
