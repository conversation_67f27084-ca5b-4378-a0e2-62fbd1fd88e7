package com.mi.oa.asset.mobile.application.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mi.oa.asset.mobile.application.dto.AssetTaskDTO;
import com.mi.oa.asset.mobile.application.dto.order.*;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.ProtoOrderBOService;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.dto.hrod.EmployeeInfo;
import com.mi.oa.asset.mobile.infra.remote.sdk.AssetMallClient;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.asset.mobile.utils.DateUtil;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.idm.api.rep.UserBaseInfoDto;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/1
 */
@Service
@Slf4j
public class ProtoOrderBOServiceImpl implements ProtoOrderBOService {

    private static final String PROTO_ORDER_INNER_END = "proto_inner_end";

    @Autowired
    private ProtoInnerPurchasePlanService protoInnerPurchasePlanService;

    @Autowired
    private ProtoPlanDetService protoPlanDetService;

    @Autowired
    private ProtoOrderService protoOrderService;

    @Autowired
    private AssetMallClient assetMallClient;

    @Autowired
    private TodoTaskService todoTaskService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProtoOrderPayService protoOrderPayService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private SysDeptService sysDeptService;

    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private ProtoOrderPayDetService protoOrderPayDetService;

    @Autowired
    private ProtoProjectStageService protoProjectStageService;

    @Autowired
    private ProtoPayAccountService protoPayAccountService;

    @Resource
    private SystemAttachService systemAttachService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generatePurOrder(PurOrderDTO purOrderDTO) {
        ProtoInnerPurchasePlanPO planPO = protoInnerPurchasePlanService.getById(purOrderDTO.getPlanId());
        if (null == planPO){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_PLAN_IS_NULL);
        }
        Integer planId = planPO.getId();
        String cacheKey = RedisCachePrefixKeyEnum.PROTO_PLAN_GENERATE_PUR_ORDER.getPrefixKey()+planId;
        boolean absent = RedisUtils.setIfAbsent(cacheKey, "1");
        if (!absent){
            log.info("pur_order_have_generated:{}", purOrderDTO);
            return;
        }
        RedisUtils.expire(cacheKey, 5, TimeUnit.MINUTES);
        String userCode = purOrderDTO.getUserCode();
        String userName = purOrderDTO.getUserName();
        List<ProtoPlanDetPO> detList = protoPlanDetService.listByPlanId(purOrderDTO.getPlanId());
        List<ProtoOrderPO> list = new ArrayList<>(detList.size());
        for (ProtoPlanDetPO detPO : detList){
            ProtoOrderPO orderPO  = new ProtoOrderPO();
            BeanUtils.copyProperties(detPO, orderPO);
            orderPO.setMrpType(planPO.getMrpType());
            orderPO.setSalesOrder("SA"+commonService.getAutoIncId(RedisUniqueKeyEnum.PROTO_ORDER,8));
            orderPO.setPlanCode(planPO.getOrderNo());
            orderPO.setPlanTheme(planPO.getTopic());
            orderPO.setOrderStatus(AssetOrderStatusEnum.WAIT_SELL.getState());
            orderPO.setOrderType(OrderTypeEnum.SALE_ORDER.getType());
            orderPO.setPurchaseType(planPO.getOrderType());
            orderPO.setPayWay(PayEnum.ON_LINE.getType());
            orderPO.setSalePrice(detPO.getActualDamages());
            orderPO.setPayAmount(detPO.getActualDamages());
            orderPO.setAddUserid(userCode);
            orderPO.setAddUserName(userName);
            orderPO.setModifyUserid(userCode);
            orderPO.setModifyUserName(userName);
            list.add(orderPO);
        }
        if (!MrpTypeEnum.AFTER_SALE.getType().equals(planPO.getMrpType())) {
            List<String> deviceCodeList = detList.stream().map(ProtoPlanDetPO::getDeviceCode).collect(Collectors.toList());
            List<ProtoCardPO> cardList = protoCardService.listByDevices(deviceCodeList);
            Map<String, ProtoCardPO> protoCardMap = cardList.stream().collect(Collectors.toMap(ProtoCardPO::getDeviceCode, o -> o, (o1, o2) -> o1));
            list.forEach(order -> {
                ProtoCardPO protoCardPO = protoCardMap.get(order.getDeviceCode());
                // 工程机业务线设置原始使用人
                order.setOriUserCode(protoCardPO.getUserCode());
                order.setOriUserName(protoCardPO.getUserName());
            });
        }
        protoOrderService.saveBatch(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void preOrderCreate(PreOrderDTO dto) {
        ProtoOrderPO existOrder = protoOrderService.getByOrderId(dto.getOrderId());
        if (Objects.nonNull(existOrder)) {
            log.info("pre order has been created. orderPO:{}", GsonUtil.toJsonString(existOrder));
            return;
        }
        ProtoPlanDetPO planDetPO = protoPlanDetService.getOne(new LambdaQueryWrapper<ProtoPlanDetPO>()
                .eq(ProtoPlanDetPO::getDeviceCode, dto.getDeviceCode()).orderByDesc(ProtoPlanDetPO::getId).last("limit 1"));
        if (Objects.isNull(planDetPO)) {
            throw new BizException(ApplicationErrorCodeEnum.PROTO_PRE_ORDER_CREATE_NO_DATA, GsonUtil.toJsonString(dto));
        }
        String houseCode = dto.getHouseCode();
        // 更新库存
        UserBaseInfoDto userInfo = userInfoService.getUserInfoByUserName(dto.getUserCode());
        ProtoOrderPO orderPO = new ProtoOrderPO();
        orderPO.setMrpType(dto.getMrpType());
        orderPO.setSalesOrder("SA"+commonService.getAutoIncId(RedisUniqueKeyEnum.PROTO_ORDER,8));
        orderPO.setOrderStatus(AssetOrderStatusEnum.WAIT_SELL.getState());
        orderPO.setOrderType(OrderTypeEnum.SALE_ORDER.getType());
        orderPO.setPurchaseType(MallOrderTypeEnum.ALL_STAFF.getType().toString());
        orderPO.setPayWay(PayEnum.ON_LINE.getType());
        orderPO.setSalePrice(dto.getCurrPrice());
        orderPO.setPayAmount(dto.getCurrPrice());
        orderPO.setOrderId(dto.getOrderId());
        orderPO.setDeliveryMethod(StringUtils.isBlank(dto.getDeliveryMethod()) ? DeliveryMethod.SELF.getType() : dto.getDeliveryMethod());
        orderPO.setSkuCode(dto.getSkuCode());
        orderPO.setSkuName(dto.getSkuName());
        orderPO.setNewPercent(dto.getNewPercent());
        orderPO.setLocationCode(houseCode);
        orderPO.setLocationName(dto.getHouseName());
        orderPO.setPriceCode(dto.getPriceCode());
        orderPO.setUserCode(dto.getUserCode());
        orderPO.setUserName(userInfo.getName());
        orderPO.setAddUserid(dto.getUserCode());
        orderPO.setAddUserName(userInfo.getName());
        orderPO.setModifyUserid(dto.getUserCode());
        orderPO.setModifyUserName(userInfo.getName());
        protoOrderService.save(orderPO);
        // 手机工程机
        if (MrpTypeEnum.MOBILE.getType().equals(orderPO.getMrpType())) {
            // 预订单创建后发送验货通知
            if (HouseCodeEnum.BEIJING.getCode().equals(houseCode)) {
                protoOrderService.checkNoticeSendLark(Collections.singletonList(orderPO), dto, houseCode, CommonConstant.PROTO_ORDER_CHECK_BEIJING);
            } else if (HouseCodeEnum.SHENZHEN.getCode().equals(houseCode)) {
                protoOrderService.checkNoticeSendLark(Collections.singletonList(orderPO), dto, houseCode, CommonConstant.PROTO_ORDER_CHECK_SHENZHEN);
            } else if (HouseCodeEnum.SHANGHAI.getCode().equals(houseCode)) {
                protoOrderService.checkNoticeSendLark(Collections.singletonList(orderPO), dto, houseCode, CommonConstant.PROTO_ORDER_CHECK_SHANGHAI);
            } else if (HouseCodeEnum.WUHAN.getCode().equals(houseCode)) {
                protoOrderService.checkNoticeSendLark(Collections.singletonList(orderPO), dto, houseCode, CommonConstant.PROTO_ORDER_CHECK_WUHAN);
            } else if (HouseCodeEnum.NANJING.getCode().equals(houseCode)) {
                protoOrderService.checkNoticeSendLark(Collections.singletonList(orderPO), dto, houseCode, CommonConstant.PROTO_ORDER_CHECK_NANJING);
            }
        } else if (MrpTypeEnum.TV.getType().equals(orderPO.getMrpType())) {
            // 预订单创建后发送验货通知
            if (HouseCodeEnum.BEIJING.getCode().equals(houseCode)) {
                protoOrderService.checkNoticeSendLark(Collections.singletonList(orderPO), dto, houseCode, CommonConstant.PROTO_ORDER_CHECK_TV_BEIJING);
            } else if (HouseCodeEnum.SHENZHEN.getCode().equals(houseCode)) {
                protoOrderService.checkNoticeSendLark(Collections.singletonList(orderPO), dto, houseCode, CommonConstant.PROTO_ORDER_CHECK_TV_SHENZHEN);
            } else if (HouseCodeEnum.SHANGHAI.getCode().equals(houseCode)) {
                protoOrderService.checkNoticeSendLark(Collections.singletonList(orderPO), dto, houseCode, CommonConstant.PROTO_ORDER_CHECK_TV_SHANGHAI);
            } else if (HouseCodeEnum.WUHAN.getCode().equals(houseCode)) {
                protoOrderService.checkNoticeSendLark(Collections.singletonList(orderPO), dto, houseCode, CommonConstant.PROTO_ORDER_CHECK_TV_WUHAN);
            } else if (HouseCodeEnum.NANJING.getCode().equals(houseCode)) {
                protoOrderService.checkNoticeSendLark(Collections.singletonList(orderPO), dto, houseCode, CommonConstant.PROTO_ORDER_CHECK_TV_NANJING);
            }
        } else if (MrpTypeEnum.AFTER_SALE.getType().equals(orderPO.getMrpType())) {
            // 售后内购机
            protoOrderService.checkNoticeSendLark(Collections.singletonList(orderPO), dto, houseCode, CommonConstant.PROTO_ORDER_CHECK_AFTER_SALE);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notifyPay(ProtoOrderDTO orderDTO) {
        List<ProtoOrderPO> orderList = protoOrderService.listByIds(orderDTO.getOrderIdList());
        List<String> notWaitList = orderList.stream().filter(o -> !AssetOrderStatusEnum.WAIT_SELL.getState().equals(o.getOrderStatus()))
                .map(ProtoOrderPO::getDeviceCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notWaitList)){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_NOT_WAIT_PAY, String.join(",", notWaitList));
        }
        List<String> notBindDeviceCodeList = orderList.stream().filter(o -> StringUtils.isEmpty(o.getDeviceCode()))
                .map(ProtoOrderPO::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notBindDeviceCodeList)){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_ORDER_IS_USER, String.join(",", notBindDeviceCodeList));
        }
        List<String> notUserList = orderList.stream().filter(o -> StringUtils.isEmpty(o.getUserCode()))
                .map(ProtoOrderPO::getDeviceCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notUserList)){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_ORDER_IS_USER, String.join(",", notUserList));
        }
        List<String> payAmountZeroList = orderList.stream().filter(o -> null == o.getPayAmount() || o.getPayAmount().doubleValue() == 0)
                .map(ProtoOrderPO::getDeviceCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(payAmountZeroList)){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_NOT_WAIT_PAY, String.join(",", payAmountZeroList));
        }
        List<String> userList = orderList.stream().map(ProtoOrderPO::getUserCode).distinct().collect(Collectors.toList());
        Map<String, EmployeeInfo> userInfoMap = new HashMap<>();
        for (String userCode : userList){
            EmployeeInfo empInfo = userInfoService.getEmpInfoByUserName(userCode);
            if (null == empInfo){
                throw new BizException(ApplicationErrorCodeEnum.PROTO_NOT_FIND_USER, userCode);
            }
            userInfoMap.put(userCode, empInfo);
        }
        // 更新用户姓名
        for (ProtoOrderPO orderPO : orderList){
            orderPO.setOrderStatus(AssetOrderStatusEnum.WAIT_PAY.getState());
            String userCode = orderPO.getUserCode();
            EmployeeInfo employeeInfo = userInfoMap.get(userCode);
            if (null != employeeInfo){
                orderPO.setUserName(employeeInfo.getDisplayName());
                orderPO.setEmpCode(employeeInfo.getEmployeeId());
                orderPO.setDeptCode(employeeInfo.getDeptId());
                SysDeptPO sysDeptPO = sysDeptService.getCacheByDeptId(employeeInfo.getDeptId());
                if (null != sysDeptPO){
                    orderPO.setDeptName(sysDeptPO.getLongDeptName());
                }
            }
            orderPO.setModifyUserid(orderDTO.getUserCode());
            orderPO.setModifyUserName(orderDTO.getUserName());
        }

        List<ProtoOrderPO> onLineList = orderList.stream().filter(o -> PayEnum.ON_LINE.getType().equals(o.getPayWay())).collect(Collectors.toList());
        // 线上支付
        List<ProtoOrderPO> protoOrderPOS = this.doOnline(onLineList);
        // 线下支付
        List<ProtoOrderPO> offlineList = orderList.stream().filter(o -> PayEnum.OFFLINE.getType().equals(o.getPayWay())).collect(Collectors.toList());
        this.doOffline(offlineList, userInfoMap);

        // 线上飞书通知
        for (ProtoOrderPO orderPO : protoOrderPOS){
            protoOrderService.notifySendLark(orderPO);
        }

        List<AssetTaskDTO> taskList = new ArrayList<>(offlineList.size());
        for (ProtoOrderPO orderPO : offlineList){
            protoOrderService.notifyUpload(orderPO);

            StringBuilder remarkSb = new StringBuilder();
            remarkSb.append(orderPO.getUserName()).append("，您好：您已成功预定内购产品，请及时打款并上传支付凭证。");

            AssetTaskDTO taskDTO = AssetTaskDTO.builder()
                    .taskId(orderPO.getSalesOrder())
                    .taskStatus((byte) 0)
                    .taskType((byte)11)
                    .taskTheme("工程机内购待支付提醒")
                    .executorDisplayName(orderPO.getUserName())
                    .executorName(orderPO.getUserCode())
                    .executorEmail(orderPO.getUserCode()+"@xiaomi.com")
                    .operatorName(orderPO.getUserCode())
                    .remark(remarkSb.toString()).build();
            taskList.add(taskDTO);
        }
        // 代办消息
        todoTaskService.sendMessage(taskList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void notifyCheck(ProtoOrderDTO orderDTO) {
        List<ProtoOrderPO> orderList = protoOrderService.listByIds(orderDTO.getOrderIdList());
        List<String> notWaitList = orderList.stream().filter(o -> !AssetOrderStatusEnum.WAIT_SELL.getState().equals(o.getOrderStatus()))
                .map(ProtoOrderPO::getDeviceCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notWaitList)) {
            throw new BizException(ApplicationErrorCodeEnum.PROTO_NOT_WAIT_PAY, String.join(",", notWaitList));
        }

        List<String> postDeliveryOrderIds = orderList.stream().filter(o -> DeliveryMethod.POST.getType().equals(o.getDeliveryMethod()))
                .map(ProtoOrderPO::getOrderId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(postDeliveryOrderIds)){
           throw new BizException(ApplicationErrorCodeEnum.PROTO_ORDER_IS_POST, String.join(",", postDeliveryOrderIds));
       }
//        List<String> notBindDeviceCodeList = orderList.stream().filter(o -> StringUtils.isEmpty(o.getDeviceCode()))
//                .map(ProtoOrderPO::getOrderId).collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(notBindDeviceCodeList)){
//            throw new BizException(ApplicationErrorCodeEnum.PROTO_ORDER_IS_USER, String.join(",", notBindDeviceCodeList));
//        }
        List<String> notUserList = orderList.stream().filter(o -> StringUtils.isEmpty(o.getUserCode()))
                .map(ProtoOrderPO::getDeviceCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notUserList)) {
            throw new BizException(ApplicationErrorCodeEnum.PROTO_ORDER_IS_USER, String.join(",", notUserList));
        }
        List<String> payAmountZeroList = orderList.stream().filter(o -> null == o.getPayAmount() || o.getPayAmount().doubleValue() == 0)
                .map(ProtoOrderPO::getDeviceCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(payAmountZeroList)) {
            throw new BizException(ApplicationErrorCodeEnum.PROTO_ORDER_IS_ZERO, String.join(",", payAmountZeroList));
        }

        Map<String, List<ProtoOrderPO>> protoOrderPOMap = new HashMap<>();
        Map<String, String> houseCodeMap = new HashMap<>();
        for (ProtoOrderPO protoOrderPO : orderList) {
            String userCode = protoOrderPO.getUserCode();
            String houseCode = protoOrderPO.getLocationCode();
            String key = userCode +";"+ houseCode;
            List<ProtoOrderPO> subProtoOrderMap = protoOrderPOMap.getOrDefault(key, new ArrayList<>());
            subProtoOrderMap.add(protoOrderPO);
            protoOrderPOMap.put(key, subProtoOrderMap);
            if (!houseCodeMap.containsKey(key)) {
                houseCodeMap.put(key, protoOrderPO.getLocationCode());
            }
        }
        for (Map.Entry<String, List<ProtoOrderPO>> protoOrderEntry : protoOrderPOMap.entrySet()) {
            String houseCode = houseCodeMap.getOrDefault(protoOrderEntry.getKey(), "");
            if (StringUtils.isNotBlank(houseCode)) {
                protoOrderService.checkNoticeSendLark(protoOrderEntry.getValue(), null, houseCode, CommonConstant.PROTO_ORDER_CHECK_MANUAL);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void soldOut(ProtoOrderDTO orderDTO) {
        List<ProtoOrderPO> orderList = protoOrderService.listByIds(orderDTO.getOrderIdList());
        List<String> notWaitList = orderList.stream().filter(o -> !AssetOrderStatusEnum.WAIT_SELL.getState().equals(o.getOrderStatus()))
                .map(ProtoOrderPO::getDeviceCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notWaitList)){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_NOT_WAIT_SELL, String.join(",", notWaitList));
        }

        for (ProtoOrderPO orderPO : orderList){
            orderPO.setOrderStatus(AssetOrderStatusEnum.CLOSE.getState());
            orderPO.setOrderType(OrderTypeEnum.SOLD_OUT.getType());
            orderPO.setModifyUserid(orderDTO.getUserCode());
            orderPO.setModifyUserName(orderDTO.getUserName());
        }
        protoOrderService.updateBatchById(orderList);
        // 更新台账的状态
        List<String> deviceList = orderList.stream().map(ProtoOrderPO::getDeviceCode).collect(Collectors.toList());
        List<ProtoCardPO> cardList = protoCardService.listByDevices(deviceList);
        List<ProtoPlanDetPO> planDetList = protoPlanDetService.listByOrders(orderList);
        Map<String, ProtoPlanDetPO> planDetPOMap = planDetList.stream().collect(
                Collectors.toMap(ProtoPlanDetPO::getDeviceCode, Function.identity(), (v1, v2) -> v1));
        if(CollectionUtils.isNotEmpty(cardList)){
            for (ProtoCardPO cardPO : cardList){
                cardPO.setUseState(planDetPOMap.get(cardPO.getDeviceCode()).getOriginCardStatus());
            }
            protoCardService.updateBatchById(cardList);
        }
        // 服务部更新计划明细的原台账状态字段
        List<Integer> afterSalePlanDetIdList = planDetList.stream()
                .filter(det -> MrpTypeEnum.AFTER_SALE.getType().equals(det.getMrpType()))
                .map(ProtoPlanDetPO::getId)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(afterSalePlanDetIdList)){
            protoPlanDetService.update(new LambdaUpdateWrapper<ProtoPlanDetPO>()
                    .set(ProtoPlanDetPO::getOriginCardStatus, CardStateEnum.IN_LIBRARY.getKey())
                    .in(ProtoPlanDetPO::getId, afterSalePlanDetIdList));
        }

        // 重新计算已下架数量
        List<String> planCodeList = orderList.stream().map(ProtoOrderPO::getPlanCode).distinct().collect(Collectors.toList());
        protoInnerPurchasePlanService.calculatePurchase(planCodeList);
    }

    @Override
    public void sendCompleteLark(String salesOrder) {
        ProtoOrderPO orderPO = protoOrderService.getBySalesOrder(salesOrder);
        if (null != orderPO){
            protoOrderService.completeSendLark(orderPO);
        }

    }

    /**
     * 关闭订单
     * @param orderPO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void close(ProtoOrderPO orderPO) {
        // 更新状态
        orderPO.setOrderStatus(AssetOrderStatusEnum.CLOSE.getState());
        orderPO.setTradeDate(null);
        protoOrderService.updateById(orderPO);
        // 发送邮件
        protoOrderService.notifyCancel(orderPO);
        // 重新生成预订单
        againPurOrder(Lists.list(orderPO), "admin", "admin");
    }

    /**
     * 完成订单
     * @param orderPO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void complete(ProtoOrderPO orderPO) {
        orderPO.setOrderStatus(AssetOrderStatusEnum.COMPLETED.getState());
        protoOrderService.updateById(orderPO);
        String mrpType = protoInnerPurchasePlanService.getMrpType(orderPO.getPlanCode());
        // 更改台账状态为已完成
        protoCardService.updateState(Lists.list(orderPO.getDeviceCode()), CardStateEnum.HAVE_PURCHASED, mrpType);
        // 插入资金往来明细
        ProtoOrderPayDetPO payDetPO = new ProtoOrderPayDetPO();
        BeanUtils.copyProperties(orderPO, payDetPO);
        payDetPO.setPayType(PayEnum.ON_LINE.getType());
        payDetPO.setOrderCode(orderPO.getSalesOrder());
        payDetPO.setDisposalType(PriceTypeEnum.IN_PURCHA.getDesc());
        payDetPO.setBusType(PriceTypeEnum.IN_PURCHA.getType());
        payDetPO.setAddUserid(orderPO.getUserCode());
        String listDesc = protoProjectStageService.getListDescBySkuCodeAndMrpType(orderPO.getSkuCode(), mrpType);
        payDetPO.setListDesc(listDesc);
        protoOrderPayDetService.save(payDetPO);
        // 重新计算实际内购数量和收益
        protoInnerPurchasePlanService.calculatePurchase(Lists.list(orderPO.getPlanCode()));
        // 发送飞书通知
        protoOrderService.completeSendLark(orderPO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelOrder(ProtoOrderDTO orderDTO) {
        List<ProtoOrderPO> orderList = protoOrderService.listByIds(orderDTO.getOrderIdList());
        List<String> notWaitList = orderList.stream().filter(o -> !(AssetOrderStatusEnum.WAIT_PAY.getState().equals(o.getOrderStatus())
                || AssetOrderStatusEnum.WAIT_SELL.getState().equals(o.getOrderStatus())))
                .map(ProtoOrderPO::getDeviceCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notWaitList)){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_NOT_WAIT_PAY, String.join(",", notWaitList));
        }
        orderList.forEach(orderPO -> {
            BaseResp resp = assetMallClient.orderCancel(orderPO.getOrderId());
            if (null == resp){
                throw new BizException(ApplicationErrorCodeEnum.APPLICATION_UNKNOWN_ERROR);
            }
            if (resp.getCode() != 0){
                throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, resp.getMessage());
            }

            orderPO.setModifyUserid(orderDTO.getUserCode());
            orderPO.setModifyUserName(orderDTO.getUserName());
            orderPO.setOrderStatus(AssetOrderStatusEnum.CLOSE.getState());

            AssetTaskDTO taskDTO=AssetTaskDTO.builder()
                    .taskId(orderPO.getSalesOrder())
                    .taskStatus((byte) 1)
                    .taskType((byte)11).build();
            protoOrderService.updateById(orderPO);
            String userCode = orderDTO.getUserCode();
            String userName = orderDTO.getUserName();
            // 通知消息
            protoOrderService.notifyAdminCancel(orderPO, userCode, userName);
            // 更新代办
            todoTaskService.sendMessage(Arrays.asList(taskDTO));
            // 生成预订单
            againPurOrder(Arrays.asList(orderPO), userCode, userName);

        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void preOrderCancel(PreOrderDTO req) {
        ProtoOrderPO orderPO = protoOrderService.getByOrderId(req.getOrderId());
        if (!AssetOrderStatusEnum.WAIT_PAY.getState().equals(orderPO.getOrderStatus())
                && !AssetOrderStatusEnum.WAIT_SELL.getState().equals(orderPO.getOrderStatus())) {
            throw new BizException(ApplicationErrorCodeEnum.PROTO_NOT_WAIT_PAY, req.getOrderId());
        }

        UserBaseInfoDto userInfo = userInfoService.getUserInfoByUserName(req.getUserCode());
        orderPO.setModifyUserid(req.getUserCode());
        orderPO.setModifyUserName(userInfo.getName());
        orderPO.setOrderStatus(AssetOrderStatusEnum.CLOSE.getState());

        AssetTaskDTO taskDTO=AssetTaskDTO.builder()
                .taskId(orderPO.getSalesOrder())
                .taskStatus((byte) 1)
                .taskType((byte)11).build();
        // 更新状态
        protoOrderService.updateById(orderPO);
        // 通知消息
        protoOrderService.notifyAdminCancel(orderPO, orderPO.getUserCode(), orderPO.getUserName());
        // 更新代办
        todoTaskService.sendMessage(Arrays.asList(taskDTO));
    }

    @Override
    public void checkAccount(String dateStr, String mrpType) {
        List<ProtoOrderPayDetPO> list = protoOrderPayDetService.listByTradeDate(mrpType, dateStr);
        if (CollectionUtils.isEmpty(list)){
            return;
        }
        Date accountDate = DateUtil.getZeroDate(dateStr, DateUtils.YEAR_MONTH_DATE);
        // 已经有当日对账数据  就不进行对账勒
        Integer accountCount = protoPayAccountService.countByAccountDate(mrpType, accountDate);
        if (accountCount > 0){
            return;
        }
        // 查询对账
        BillReqDTO billReqDTO = BillReqDTO.builder().billDate(dateStr).mrpType(mrpType).build();
        List<AccountBillResp> accountBillList;
        try {
            BaseResp<List<AccountBillResp>> baseResp = assetMallClient.accountBill(billReqDTO);
            log.info("get_account_bill_info req:{},res:{}", baseResp, baseResp);
            if (null == baseResp || CollectionUtils.isEmpty(baseResp.getData())){
                return;
            }
            accountBillList = baseResp.getData();
        } catch (Exception e){
            log.error("get_account_bill_error:{},", e);
            return;
        }
        Map<String, List<ProtoOrderPayDetPO>> orderPayDetMap = list.stream().collect(Collectors
                .groupingBy(o -> o.getPayOrderType()));
        List<ProtoPayAccountPO> accountList = new ArrayList<>(orderPayDetMap.size());
        for (String key : orderPayDetMap.keySet()){
            List<ProtoOrderPayDetPO> payDetPOList = orderPayDetMap.get(key);
            ProtoOrderPayDetPO orderPayDetPO = payDetPOList.get(0);
            ProtoPayAccountPO accountPO = new ProtoPayAccountPO();
            accountPO.setMrpType(orderPayDetPO.getMrpType());
            accountPO.setAccountDate(accountDate);
            accountPO.setPayOrderType(orderPayDetPO.getPayOrderType());
            BigDecimal sumPayAmount = new BigDecimal(0);
            BigDecimal sumChargeAmount = new BigDecimal(0);
            for (ProtoOrderPayDetPO payDetPO : payDetPOList){
                sumPayAmount = sumPayAmount.add(payDetPO.getPayAmount());
                sumChargeAmount = sumChargeAmount.add(payDetPO.getChargeAmount());
            }
            accountPO.setPayAmount(sumPayAmount);
            accountPO.setChargeAmount(sumChargeAmount);
            accountPO.setNetEarning(sumPayAmount.subtract(sumChargeAmount));

            for (AccountBillResp bill : accountBillList){
                String payType = bill.getPayType();
                // 支付渠道
                if ("小米钱包".equals(payType)){
                    payType = "1";
                } else if ("支付宝".equals(payType)){
                    payType = "2";
                }
                if (StringUtils.equals(payType, accountPO.getPayOrderType())){
                    accountPO.setEnterAccountAmount(bill.getTotalInflow());
                    accountPO.setOutAccountAmount(bill.getTotalOutflow());
                    accountPO.setSumChargeAmount(bill.getNetCommissionCharge());
                }

            }
            accountList.add(accountPO);
        }
        protoPayAccountService.saveBatch(accountList);
    }

    @Override
    public List<PlanProgressListDTO> planProgress(String planCode) {
        PlanProgressDTO progressDTO = protoOrderService.planProgress(planCode);
        if (null == progressDTO){
            return new ArrayList<>();
        }
        List<PlanProgressListDTO> result = new ArrayList<>(5);
        PlanProgressListDTO dto1 = new PlanProgressListDTO();
        dto1.setStatusDesc(AssetOrderStatusEnum.WAIT_SELL.getDesc());
        dto1.setAmount(progressDTO.getWaitSellAmount());
        dto1.setNum(progressDTO.getWaitSellNum());

        PlanProgressListDTO dto2 = new PlanProgressListDTO();
        dto2.setStatusDesc(AssetOrderStatusEnum.WAIT_PAY.getDesc());
        dto2.setAmount(progressDTO.getWaitPayAmount());
        dto2.setNum(progressDTO.getWaitPayNum());

        PlanProgressListDTO dto3 = new PlanProgressListDTO();
        dto3.setStatusDesc(AssetOrderStatusEnum.COMPLETED.getDesc());
        dto3.setAmount(progressDTO.getCompletedAmount());
        dto3.setNum(progressDTO.getCompletedNum());

        PlanProgressListDTO dto4 = new PlanProgressListDTO();
        dto4.setStatusDesc("已下架");
        dto4.setAmount(progressDTO.getSoldOutAmount());
        dto4.setNum(progressDTO.getSoldOutNum());

        PlanProgressListDTO dto5 = new PlanProgressListDTO();
        dto5.setStatusDesc("已退款");
        dto5.setAmount(BigDecimal.valueOf(0));
        dto5.setNum(0);

        PlanProgressListDTO dto6 = new PlanProgressListDTO();
        dto6.setStatusDesc("合计");
        BigDecimal sumAmount = progressDTO.getWaitSellAmount().add(progressDTO.getWaitPayAmount())
                .add(progressDTO.getCompletedAmount()).add(progressDTO.getSoldOutAmount());
        Integer sumNum = progressDTO.getWaitSellNum()+progressDTO.getWaitPayNum()
                +progressDTO.getCompletedNum()+progressDTO.getSoldOutNum();
        dto6.setAmount(sumAmount);
        dto6.setNum(sumNum);

        result.add(dto1);
        result.add(dto2);
        result.add(dto3);
        result.add(dto4);
        result.add(dto5);
        result.add(dto6);
        return result;
    }

    @Override
    public List<IncomeProgressDTO> incomeProgress(String planCode) {
        List<ProtoOrderPO> list = protoOrderService.listByPlanCode(planCode, AssetOrderStatusEnum.COMPLETED);
        List<String> salesOrderList = list.stream().map(ProtoOrderPO::getSalesOrder).collect(Collectors.toList());
        Map<String, List<ProtoOrderPayDetPO>> orderMap = protoOrderPayDetService.listByOrderCodes(salesOrderList)
                .stream().collect(Collectors.groupingBy(o -> o.getPayOrderType() + o.getPayType()));
        List<IncomeProgressDTO> result = new ArrayList<>(orderMap.size());
        BigDecimal sumReceiptAmount = BigDecimal.valueOf(0);
        BigDecimal sumChargeAmount = BigDecimal.valueOf(0);
        BigDecimal sumNetEarning = BigDecimal.valueOf(0);
        for (String key : orderMap.keySet()){
            IncomeProgressDTO progressDTO = new IncomeProgressDTO();
            List<ProtoOrderPayDetPO> orderList = orderMap.get(key);
            BigDecimal receiptAmount = BigDecimal.valueOf(0);
            BigDecimal chargeAmount = BigDecimal.valueOf(0);
            for (ProtoOrderPayDetPO orderPO : orderList){
                progressDTO.setPayWay(orderPO.getPayType());
                progressDTO.setPayOrderType(orderPO.getPayOrderType());
                receiptAmount = receiptAmount.add(orderPO.getPayAmount());
                chargeAmount = chargeAmount.add(orderPO.getChargeAmount());
            }
            progressDTO.setReceiptAmount(receiptAmount);
            progressDTO.setChargeAmount(chargeAmount);
            progressDTO.setRefundAmount(BigDecimal.valueOf(0));
            progressDTO.setNetEarning(receiptAmount.subtract(chargeAmount));
            sumReceiptAmount = sumReceiptAmount.add(receiptAmount);
            sumChargeAmount = sumChargeAmount.add(chargeAmount);
            sumNetEarning = sumNetEarning.add(progressDTO.getNetEarning());
            result.add(progressDTO);
        }
        // 添加合计
        IncomeProgressDTO totalDTO = new IncomeProgressDTO();
        totalDTO.setPayWay("合计");
        totalDTO.setReceiptAmount(sumReceiptAmount);
        totalDTO.setChargeAmount(sumChargeAmount);
        totalDTO.setRefundAmount(BigDecimal.valueOf(0));
        totalDTO.setNetEarning(sumNetEarning);
        result.add(totalDTO);

        return result;
    }

    @Override
    public void synChargeAmount(String mrpType, String dateStr) {
        List<PayBillResp> list = null;
        try {
            BillReqDTO billReqDTO = BillReqDTO.builder().billDate(dateStr).mrpType(mrpType).build();
            BaseResp<List<PayBillResp>> baseResp = assetMallClient.payBill(billReqDTO);
            log.info("get_pay_bill_info req:{},res:{}", billReqDTO, baseResp);
            list = baseResp.getData();
        } catch (Exception e){
            log.error("get_pay_bill error:{}", e);
        }
        if (list == null || list.size() == 0){
            return;
        }
        List<String> tradeCodeList = list.stream().map(PayBillResp::getTradeId).collect(Collectors.toList());
        List<ProtoOrderPayDetPO> payDetList = protoOrderPayDetService.listByTradeOrderCodeList(tradeCodeList);
        for (PayBillResp resp : list){
            for (ProtoOrderPayDetPO payDetPO : payDetList){
                if (resp.getTradeId().equals(payDetPO.getTradeOrderCode())){
                    payDetPO.setChargeAmount(resp.getFee());
                    payDetPO.setChannelNum(resp.getChannelMerchantNo());
                }
            }
        }
        protoOrderPayDetService.updateBatchById(payDetList);
    }

    @Override
    public void saveAssigneeInfo(OrderPostSaveDTO dto) {
        ProtoOrderPO orderPO = protoOrderService.getByOrderId(dto.getOrderId());
        if (Objects.isNull(orderPO)){
            throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR, dto.getOrderId());
        }
        orderPO.setConsignee(dto.getConsignee());
        orderPO.setConsigneeMobile(dto.getConsigneeMobile());
        orderPO.setConsigneeAddress(dto.getConsigneeAddress());
        protoOrderService.updateById(orderPO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitAfterSaleApply(AfterSaleApplyDTO dto) {
        ProtoOrderPO orderPO = protoOrderService.getByOrderId(dto.getOrderId());
        if (Objects.isNull(orderPO)){
            throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR, dto.getOrderId());
        }
        orderPO.setAfterSaleType(dto.getAfterSaleType());
        orderPO.setProcessStatus(dto.getProcessStatus());
        orderPO.setAfterSaleReason(dto.getReason());
        protoOrderService.updateById(orderPO);

        // 保存附件
        if (CollectionUtils.isEmpty(dto.getAttaches())) {
            return;
        }

        List<SystemAttachPO> attachList = new ArrayList<>(dto.getAttaches().size());
        for (int i = 0; i < dto.getAttaches().size(); i++) {
            SystemAttachPO attachPO = new SystemAttachPO();
            attachPO.setAttachId(MessageFormat.format("{0}-{1}", String.valueOf(System.currentTimeMillis()), i));
            attachPO.setTableName("proto_order");
            attachPO.setFunId(PROTO_ORDER_INNER_END);
            attachPO.setDataId(String.valueOf(orderPO.getId()));
            attachPO.setAttachName(dto.getAttaches().get(i).getOriginFileName());
            attachPO.setAttachPath("uri=" + dto.getAttaches().get(i).getDownloadUrl());
            attachPO.setStoreId("jxstar-684-201");
            attachPO.setStoreNo(new BigDecimal(6));
            attachPO.setUploadUser("");
            attachPO.setUploadDate(new Date());
            attachPO.setTenantId("jxstar");
            attachPO.setAddDate(new Date());
            attachPO.setAddUserid(dto.getUserCode());
            attachList.add(attachPO);
        }
        systemAttachService.saveAttachByBatch(attachList);
    }

    /**
     * 线下支付
     * @param offlineList
     */
    private void doOffline(List<ProtoOrderPO> offlineList, Map<String, EmployeeInfo> userInfoMap){
        if (CollectionUtils.isEmpty(offlineList)){
            return;
        }
        protoOrderService.updateBatchById(offlineList);
        // 保存待支付清单
        List<ProtoOrderPayPO> list = new ArrayList<>(offlineList.size());
        Date nowDate = new Date();
        for (ProtoOrderPO orderPO : offlineList){
            ProtoOrderPayPO orderPayPO = new ProtoOrderPayPO();
            BeanUtils.copyProperties(orderPO, orderPayPO);
            orderPayPO.setId(null);
            orderPayPO.setPayType(PayEnum.OFFLINE.getType());
            orderPayPO.setOrderCode(orderPO.getSalesOrder());
            orderPayPO.setDisposalType(PriceTypeEnum.IN_PURCHA.getDesc());
            orderPayPO.setApplyDate(nowDate);
            orderPayPO.setBusType(PriceTypeEnum.IN_PURCHA.getType());
            String userCode = orderPO.getUserCode();
            EmployeeInfo employeeInfo = userInfoMap.get(userCode);
            if (null != employeeInfo){
                orderPayPO.setEmpCode(employeeInfo.getEmployeeId());
                orderPayPO.setDeptCode(employeeInfo.getDeptId());
                orderPayPO.setDeptName(employeeInfo.getDeptName());
            }
            list.add(orderPayPO);
        }
        protoOrderPayService.saveBatch(list);
    }

    private List<ProtoOrderPO> doOnline(List<ProtoOrderPO> onLineList){
        if (CollectionUtils.isEmpty(onLineList)){
            return new ArrayList<>();
        }

        // 保存创建订单成功的订单信息，返回，用于发送消息
        List<ProtoOrderPO> successList = new ArrayList<>();
        List<MallOrderReq> mallOrderList = new ArrayList<>();
        for (ProtoOrderPO orderPO : onLineList){
            ProtoInnerPurchasePlanPO innerPurchasePlanPO =
                    protoInnerPurchasePlanService.getInnerPurchasePlanByCode(orderPO.getPlanCode());
            List<OrderDetailReq> orderDetailList = new ArrayList<>(1);
            OrderDetailReq orderDetailReq = OrderDetailReq.builder()
                    .planId(innerPurchasePlanPO.getId())
                    .planCode(innerPurchasePlanPO.getOrderNo())
                    .orderId(orderPO.getOrderId())
                    .productCode(orderPO.getDeviceCode())
                    .productName(orderPO.getSkuCode())
                    .productDesc(orderPO.getSkuName())
                    .productPrice(orderPO.getPayAmount())
                    .productNum(1)
                    .build();
            orderDetailList.add(orderDetailReq);

            MallOrderReq orderReq = MallOrderReq.builder()
                    .mrpType(orderPO.getMrpType())
                    .bizCode(orderPO.getSalesOrder())
                    .bizType(0)
                    .planOrderType(innerPurchasePlanPO.getOrderType())
                    .orderType(0)
                    .orderState(1)
                    .orderId(orderPO.getOrderId())
                    .totalAmount(orderPO.getPayAmount())
                    .payWay(1)
                    .owner(orderPO.getUserCode())
                    .orderDetailList(orderDetailList)
                    .orderDesc("工程机内购")
                    .build();
            BaseResp<MallOrderReq> resp = assetMallClient.orderCreate(orderReq);
            if (resp.getCode() != BaseResp.CODE_SUCCESS) {
                log.error("创建订单失败, 订单信息：" + orderPO);
            } else {
                successList.add(orderPO);
                mallOrderList.add(resp.getData());
            }
        }

        Map<String, MallOrderReq> orderMap = mallOrderList.stream().collect(Collectors.toMap(MallOrderReq::getBizCode, o -> o));
        for (ProtoOrderPO orderPO : successList){
            orderPO.setOrderStatus(AssetOrderStatusEnum.WAIT_PAY.getState());
            MallOrderReq orderReq = orderMap.get(orderPO.getSalesOrder());
            if (null != orderReq){
                orderPO.setOrderId(orderReq.getOrderId());
                orderPO.setPayLink(orderReq.getJumpPayLink());
            }
        }
        protoOrderService.updateBatchById(onLineList);
        return successList;
    }

    /**
     * 批量重新生成新的预订单,全员不生成
     * @param orderList
     * @param userCode
     * @param userName
     */
    private void againPurOrder(List<ProtoOrderPO> orderList, String userCode, String userName){
        if (CollectionUtils.isEmpty(orderList)){
            return;
        }
        Map<String, List<ProtoOrderPO>> planOrderListMap = orderList.stream()
                .collect(Collectors.groupingBy(ProtoOrderPO::getPlanCode));
        planOrderListMap.entrySet().forEach(item -> {
            if (StringUtils.isBlank(item.getKey())) {
                log.info("全员内购订单：orderList size:{}", item.getValue().size());
                return;
            }
            ProtoInnerPurchasePlanPO innerPlanPO = protoInnerPurchasePlanService.getInnerPurchasePlanByCode(item.getKey());
            // 全员内购不自动生成预订单
            if (PurchaseOrderTypeEnum.ALL_STAFF.getType().equals(innerPlanPO.getOrderType())) {
                return;
            }
            for (ProtoOrderPO orderPO : orderList){
                orderPO.setId(null);
                orderPO.setAddUserid(userCode);
                orderPO.setAddUserName(userName);
                orderPO.setModifyUserid(userCode);
                orderPO.setModifyUserName(userName);
                orderPO.setPayLink("");
                orderPO.setTradeOrderCode("");
                orderPO.setTradeWaterCode("");
                orderPO.setPayOrderType("");
                orderPO.setAddDate(null);
                orderPO.setModifyDate(null);
                orderPO.setOrderStatus(AssetOrderStatusEnum.WAIT_SELL.getState());
                orderPO.setSalesOrder("SA"+commonService.getAutoIncId(RedisUniqueKeyEnum.PROTO_ORDER,8));
            }
            protoOrderService.saveBatch(orderList);
        });
    }
}
