package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.dto.bpm.ProtoBackBizDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.ProtoBaselineRecordBizDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.ProtoSleeveBizDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.ProtoTranBizDTO;
import com.mi.oa.asset.mobile.application.service.BpmV3Service;
import com.mi.oa.asset.mobile.common.enums.BmpV3ProcessEnum;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import com.mi.oa.infra.oaucf.bpm.rep.CreateProcInstResp;
import com.mi.oa.infra.oaucf.bpm.req.CreateProcInstReq;
import com.mi.oa.infra.oaucf.bpm.req.TerminateProcInstReq;
import com.mi.oa.infra.oaucf.bpm.service.ProcessInstanceService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Map;

/**
 * BPM 3.0 流程
 * <AUTHOR>
 * @date 2022/10/31 20:02
 */

@Service("BpmV3Service")
@Slf4j
public class BpmV3ServiceImpl implements BpmV3Service {

    @Autowired
    private ProcessInstanceService processInstanceService;

    @Override
    public void terminate(String businessKey, String operator) {
        TerminateProcInstReq req = TerminateProcInstReq.builder()
                .businessKey(businessKey)
                .operator(operator)
                .comment("发起人主动终止")
                .build();

        BaseResp<Void> res = processInstanceService.terminate(req);
        Assert.isTrue(0 == res.getCode(), res.getMessage());
    }

    @Override
    public String submitProtoTranApply(ProtoTranBizDTO business, String startUser, Map<String, Object> variables) {
        CreateProcInstReq req = CreateProcInstReq.builder()
                .modelCode(BmpV3ProcessEnum.PROTO_TRAN_APPLY.getModelCode())
                .processInstanceName(String.format(BmpV3ProcessEnum.PROTO_TRAN_APPLY.getProcessInstanceName(), business.getTranCode()))
                .formData(GsonUtil.toMap(business))
                .startUserId(startUser)
                .variables(variables)
                .build();

        BaseResp<CreateProcInstResp> res = processInstanceService.create(req);
        Assert.isTrue(0 == res.getCode(), res.getMessage());

        return res.getData().getBusinessKey();
    }

    @Override
    public String submitProtoBackApply(ProtoBackBizDTO business, String startUser, Map<String, Object> variables) {
        CreateProcInstReq req = CreateProcInstReq.builder()
                .modelCode(BmpV3ProcessEnum.PROTO_BACK_APPLY.getModelCode())
                .processInstanceName(String.format(BmpV3ProcessEnum.PROTO_BACK_APPLY.getProcessInstanceName(), business.getInCode()))
                .formData(GsonUtil.toMap(business))
                .startUserId(startUser)
                .variables(variables)
                .build();

        BaseResp<CreateProcInstResp> res = processInstanceService.create(req);
        Assert.isTrue(0 == res.getCode(), res.getMessage());

        return res.getData().getBusinessKey();
    }

    @Override
    public String submitProtoSleeveApply(ProtoSleeveBizDTO business, String startUser, Map<String, Object> variables) {
        CreateProcInstReq req = CreateProcInstReq.builder()
                .modelCode(BmpV3ProcessEnum.PROTO_REMOVALL_COVER.getModelCode())
                .processInstanceName(String.format(BmpV3ProcessEnum.PROTO_REMOVALL_COVER.getProcessInstanceName(), business.getApplyCode()))
                .formData(GsonUtil.toMap(business))
                .startUserId(startUser)
                .variables(variables)
                .build();

        BaseResp<CreateProcInstResp> res = processInstanceService.create(req);
        Assert.isTrue(0 == res.getCode(), res.getMessage());

        return res.getData().getBusinessKey();
    }

    @Override
    public String submitBaselineApply(ProtoBaselineRecordBizDTO business, String startUser, Map<String, Object> variables) {
        CreateProcInstReq req = CreateProcInstReq.builder()
                .modelCode(BmpV3ProcessEnum.PROTO_BASELINE_APPLY.getModelCode())
                .processInstanceName(String.format(BmpV3ProcessEnum.PROTO_BASELINE_APPLY.getProcessInstanceName(), business.getChangeType(),business.getApplyCode()))
                .formData(GsonUtil.toMap(business))
                .startUserId(startUser)
                .variables(variables)
                .build();

        BaseResp<CreateProcInstResp> res = processInstanceService.create(req);
        Assert.isTrue(0 == res.getCode(), res.getMessage());

        return res.getData().getBusinessKey();
    }
}
