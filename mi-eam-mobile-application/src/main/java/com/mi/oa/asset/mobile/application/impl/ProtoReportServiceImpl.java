package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.config.FunEventEnum;
import com.mi.oa.asset.mobile.application.dto.EamApiResp;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.ProtoReportService;
import com.mi.oa.asset.mobile.common.enums.RedisCachePrefixKeyEnum;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/5/17
 */
@Service
public class ProtoReportServiceImpl implements ProtoReportService {

    @Autowired
    private ProtoCardStoreService protoCardStoreService;

    @Autowired
    private ProtoCardProjectTotalService protoCardProjectTotalService;

    @Autowired
    private ProtoCardSkuTotalService protoCardSkuTotalService;

    @Autowired
    private ProtoCardDeptTotalService protoCardDeptTotalService;

    @Autowired
    private ProtoCardDeptproTotalService protoCardDeptproTotalService;

    @Override
    public String getUpdateTime(RedisCachePrefixKeyEnum prefixKeyEnum) {
        Object updateTime = RedisUtils.get(prefixKeyEnum.getPrefixKey());
        if(null == updateTime) {
            return "";
        }
        return updateTime.toString();
    }

    @Override
    public void updateReport(RedisCachePrefixKeyEnum lockKey, RedisCachePrefixKeyEnum updateTimeKey, Supplier supplier) {
        //加锁
        String tokenId = RedisUtils.tryLock(lockKey.getPrefixKey(),30);
        if (StringUtils.isBlank(tokenId)) {
            throw new BizException(ApplicationErrorCodeEnum.DATA_IS_UPDATING_001);
        }
        //当前时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date nowDate = new Date();
        //数据统计
        supplier.get();
        //更新数据统计时间
        RedisUtils.set(updateTimeKey.getPrefixKey(), sdf.format(nowDate));
        //解锁
        RedisUtils.unlock(lockKey.getPrefixKey(), tokenId);
    }

    @Override
    public void updateReportInfo(RequestContext requestContext) {
        String funId = requestContext.getFunID();
        // 部门在用报表
        if (FunEventEnum.CARD_STORE_UPDATE.getFunId().equals(funId)){
            this.updateReport(RedisCachePrefixKeyEnum.PROTO_CARD_STORE_LOCK_KEY,
                    RedisCachePrefixKeyEnum.PROTO_CARD_STORE_UPDATE_TIME,
                    ()->protoCardStoreService.statistics());
        } else if (FunEventEnum.CARD_PROJECT_TOTAL_UPDATE.getFunId().equals(funId)){
            // 项目汇总
            this.updateReport(RedisCachePrefixKeyEnum.PROTO_CARD_PROJECT_LOCK_KEY,
                    RedisCachePrefixKeyEnum.PROTO_CARD_PROJECT_UPDATE_TIME,
                    ()->protoCardProjectTotalService.updCardProjectTotal());
        } else if (FunEventEnum.CARD_SKU_TOTAL_UPDATE.getFunId().equals(funId)){
            // 物料汇总
            this.updateReport(RedisCachePrefixKeyEnum.PROTO_CARD_SKU_LOCK_KEY,
                    RedisCachePrefixKeyEnum.PROTO_CARD_SKU_UPDATE_TIME,
                    ()->protoCardSkuTotalService.statistics());
        } else if (FunEventEnum.CARD_DEPT_TOTAL_UPDATE.getFunId().equals(funId)){
            // 部门 总量查询
            this.updateReport(RedisCachePrefixKeyEnum.PROTO_CARD_DEPT_LOCK_KEY,
                    RedisCachePrefixKeyEnum.PROTO_CARD_DEPT_UPDATE_TIME,
                    ()->protoCardDeptTotalService.statisticsDept());
        } else if (FunEventEnum.CARD_DEPT_PRO_TOTAL_UPDATE.getFunId().equals(funId)){
            // 项目查询
            this.updateReport(RedisCachePrefixKeyEnum.PROTO_CARD_DEPTPRO_LOCK_KEY,
                    RedisCachePrefixKeyEnum.PROTO_CARD_DEPTPRO_UPDATE_TIME,
                    ()->protoCardDeptproTotalService.statisticsDeptpro());
        }
    }

    @Override
    public Map<String, String> getUpdateTimeInfo(RequestContext requestContext) {
        String updateTime = "";
        String funId = requestContext.getFunID();
        // 部门在用报表
        if (FunEventEnum.CARD_STORE_UPDATE_TIME.getFunId().equals(funId)){
            updateTime = this.getUpdateTime(RedisCachePrefixKeyEnum.PROTO_CARD_STORE_UPDATE_TIME);
        } else if (FunEventEnum.CARD_PROJECT_TOTAL_UPDATE_TIME.getFunId().equals(funId)){
            // 项目汇总
            updateTime = this.getUpdateTime(RedisCachePrefixKeyEnum.PROTO_CARD_PROJECT_UPDATE_TIME);
        } else if (FunEventEnum.CARD_SKU_TOTAL_UPDATE_TIME.getFunId().equals(funId)){
            // 物料汇总
            updateTime = this.getUpdateTime(RedisCachePrefixKeyEnum.PROTO_CARD_SKU_UPDATE_TIME);
        } else if (FunEventEnum.CARD_DEPT_TOTAL_UPDATE_TIME.getFunId().equals(funId)){
            // 部门 总量查询
            updateTime = this.getUpdateTime(RedisCachePrefixKeyEnum.PROTO_CARD_DEPT_UPDATE_TIME);
        } else if (FunEventEnum.CARD_DEPT_PRO_TOTAL_UPDATE_TIME.getFunId().equals(funId)){
            // 项目查询
            updateTime = this.getUpdateTime(RedisCachePrefixKeyEnum.PROTO_CARD_DEPTPRO_UPDATE_TIME);
        }
        Map<String, String> result = new HashMap<>(2);
        result.put("rep_update_date", updateTime);
        return EamApiResp.getEamApiResp(true, null, result);
    }
}
