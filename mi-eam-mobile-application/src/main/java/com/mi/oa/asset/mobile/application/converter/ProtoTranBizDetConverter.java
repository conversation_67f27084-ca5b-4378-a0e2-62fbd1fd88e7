package com.mi.oa.asset.mobile.application.converter;

import com.mi.oa.asset.mobile.application.dto.bpm.ProtoTranBizDetDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranDetPO;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/31 19:25
 */

@Mapper(componentModel = "spring")
public interface ProtoTranBizDetConverter {

    ProtoTranBizDetDTO protoTranDetPOToDTO(ProtoTranDetPO protoTranDetPO);

    List<ProtoTranBizDetDTO> protoTranDetPOToDTOList(List<ProtoTranDetPO> po);
}
