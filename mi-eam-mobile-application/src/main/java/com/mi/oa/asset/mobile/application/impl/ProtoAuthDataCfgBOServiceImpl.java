package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.ProtoAuthDataCfgBOService;
import com.mi.oa.asset.mobile.application.service.SysUserDataBOService;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAuthDataCfgPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoAuthDataCfgService;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数据权限配置
 *
 * <AUTHOR>
 * @date 2022/4/6 19:07
 */
@Service
@Slf4j
public class ProtoAuthDataCfgBOServiceImpl implements ProtoAuthDataCfgBOService {

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProtoAuthDataCfgService protoAuthDataCfgService;

    @Autowired
    private SysUserDataBOService sysUserDataBOService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String audit(String[] keyIds, RequestContext request) {
        commonService.commonAudit(request);
        boolean flag = valAuditsIsRepeat(keyIds);
        if (flag == false) {
            throw new BizException(ApplicationErrorCodeEnum.PROTO_AUTH_DATA_CFG_001);
        }
        String rs = addInterfaceRoles(keyIds);
        return rs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String unAudit(String[] keyIds, RequestContext request) {
        commonService.commonAudit(request);
        String rs = delInterfaceRoles(keyIds);
        return rs;
    }

    @Transactional(rollbackFor = Exception.class)
    public String addInterfaceRoles(String[] keyIds) {
        for (String keyId : keyIds) {
            if (!StringUtils.isBlank(keyId)) {
                addInterfaceRole(keyId);
            }
        }
        return CommonConstant.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public String delInterfaceRoles(String[] keyIds) {
        for (String keyId : keyIds) {
            if (!StringUtils.isBlank(keyId)) {
                delInterfaceRole(keyId);
            }
        }
        return CommonConstant.TRUE;
    }

    public void addInterfaceRole(String keyId) {
        //查询主表信息
        ProtoAuthDataCfgPO mainData = protoAuthDataCfgService.findProtoAuthDataCfg(keyId);
        if (ObjectUtils.isEmpty(mainData)) {
            throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR, RedisUniqueKeyEnum.PROTO_AUTH_DATA_CFG.getTableName(), keyId);
        }
        String cpUserCode = mainData.getCpUserCode();
        String agDeptCode = mainData.getAgDeptCode();
        sysUserDataBOService.addInterfaceRole(cpUserCode, agDeptCode, mainData.getMrpType());
    }

    public void delInterfaceRole(String keyId) {
        //查询主表信息
        ProtoAuthDataCfgPO mainData = protoAuthDataCfgService.findProtoAuthDataCfg(keyId);
        if (ObjectUtils.isEmpty(mainData)) {
            throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR, RedisUniqueKeyEnum.PROTO_AUTH_DATA_CFG.getTableName(), keyId);
        }
        String cpUserCode = mainData.getCpUserCode();

        sysUserDataBOService.deleteInterfaceRole(cpUserCode, mainData.getAgDeptCode());
    }

    public boolean valAuditsIsRepeat(String[] keyIds) {
        for (String keyId : keyIds) {
            if (!StringUtils.isBlank(keyId)) {
                int num = valAuditIsRepeat(keyId);
                if (num > 1) {
                    return false;
                }
            }
        }
        return true;
    }

    public int valAuditIsRepeat(String keyId) {
        //查询主表信息
        ProtoAuthDataCfgPO mainData = protoAuthDataCfgService.findProtoAuthDataCfg(keyId);
        String cpUserCode = mainData.getCpUserCode();
        String agDeptCode = mainData.getAgDeptCode();
        return protoAuthDataCfgService.countAuthDataCfg(cpUserCode, agDeptCode, mainData.getMrpType());
    }

}
