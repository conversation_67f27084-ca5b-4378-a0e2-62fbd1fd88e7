package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.service.ProtoInBoService;
import com.mi.oa.asset.mobile.common.enums.SapMakeCnEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2022/2/24 10:08
 */
@Service
@Slf4j
public class ProtoInBoServiceImpl implements ProtoInBoService {

    @Autowired
    private ProtoService protoService;
    @Autowired
    private ProtoInService protoInService;
    @Autowired
    private ProtoInDetService protoInDetService;

    @Autowired
    private ProtoOutService protoOutService;

    @Autowired
    private ProtoOutDetService protoOutDetService;

    @Autowired
    private ProtoAsnOrderDetService protoAsnOrderDetService;

    @Autowired
    private ProtoAsnOrderService protoAsnOrderService;

    @Override
    public String inScanQry(String scanResult) {
        // 解析扫码结果
        Map<String, String> scanResultMap = protoService.tranScanResult(scanResult);
        String deviceCode = scanResultMap.get("device_code");
        List<ProtoInDetPO> detList = protoInDetService.findListByParam(ProtoInDetPO.builder().deviceCode(deviceCode).build());
        return detList.stream().filter(t-> !ObjectUtils.isEmpty(protoInService.findOneByParam(ProtoInPO.builder()
                .inId(t.getInId()).inType("2").build()))).map(ProtoInDetPO::getInId).collect(Collectors.joining(","));
    }

    @Override
    public String getMakeCn(String applyCode) {
        List<ProtoOutPO> outList = protoOutService.listByApplyCode(applyCode);
        for (ProtoOutPO outPO : outList){
            List<ProtoOutDetPO> detList = protoOutDetService.listByOutId(outPO.getOutId());
            for (ProtoOutDetPO detPO : detList){
                String deviceCode = detPO.getDeviceCode();
                String orderCode = protoAsnOrderDetService.getSapSuccessOrderCode(deviceCode);
                ProtoAsnOrderPO asnOrderPO = protoAsnOrderService.getAsnOrderByCode(orderCode);
                if (null != asnOrderPO){
                    return SapMakeCnEnum.getMakeCn(asnOrderPO.getSapFactory());
                }
            }
        }
        return null;
    }

    @Override
    public ProtoAsnOrderPO getSapSuccessAsnOrder(String applyCode) {
        List<ProtoOutPO> outList = protoOutService.listByApplyCode(applyCode);
        for (ProtoOutPO outPO : outList){
            List<ProtoOutDetPO> detList = protoOutDetService.listByOutId(outPO.getOutId());
            for (ProtoOutDetPO detPO : detList){
                String deviceCode = detPO.getDeviceCode();
                String orderCode = protoAsnOrderDetService.getSapSuccessOrderCode(deviceCode);
                ProtoAsnOrderPO asnOrderPO = protoAsnOrderService.getAsnOrderByCode(orderCode);
                if (null != asnOrderPO){
                    return asnOrderPO;
                }
            }
        }
        return null;
    }
}
