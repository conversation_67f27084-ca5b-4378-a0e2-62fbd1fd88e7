package com.mi.oa.asset.mobile.application.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.service.ProtoPlanDetSummaryService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPlanDetSummaryPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoPlanDetSummaryMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SystemAttachService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service("ProtoPlanDetSummaryService")
public class ProtoPlanDetSummaryServiceImpl extends ServiceImpl<ProtoPlanDetSummaryMapper, ProtoPlanDetSummaryPO> implements ProtoPlanDetSummaryService {

    private static final String PROTO_PLAN_DET_SUMMARY_FUN_ID = "proto_plan_det_sum";

    @Autowired
    private SystemAttachService systemAttachService;
}
