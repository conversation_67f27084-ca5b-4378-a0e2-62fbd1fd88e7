package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.service.ProtoDataSyncService;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.dto.sap.SapBaseVO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapSkuDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapSkuDetailVO;
import com.mi.oa.asset.mobile.infra.dto.upc.UpcProjectDTO;
import com.mi.oa.asset.mobile.infra.dto.upc.UpcProjectDetailDTO;
import com.mi.oa.asset.mobile.infra.dto.upc.UpcProjectReq;
import com.mi.oa.asset.mobile.infra.remote.sdk.SapClient;
import com.mi.oa.asset.mobile.infra.remote.sdk.UpcClient;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectCfgPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectListPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectStagePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectCfgService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectListService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectStageService;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * Description: 三方数据同步服务（UPC项目、SAP半成品物料）
 *
 * <AUTHOR>
 * @date 2023/8/18
 */
@Service
@Slf4j
public class ProtoDataSyncServiceImpl implements ProtoDataSyncService {

    private static final String SYSTEMID = "EAM";
    private static final int PAGE_SIZE = 500;
    private static final String ECO_FACTORY = "[{\"WERKS\":\"1185\"},{\"WERKS\":\"1112\"}]";

    @Autowired
    private UpcClient upcClient;
    @Autowired
    private SapClient sapClient;

    @Autowired
    private ProtoProjectListService protoProjectListService;

    @Autowired
    private ProtoProjectCfgService protoProjectCfgService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProtoProjectStageService protoProjectStageService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncEcoAndTvProjectInfo(LocalDateTime dataTime) {
        log.info("syncEcoAndTvProjectInfo start==dataTime:{}", dataTime);
        long start = System.currentTimeMillis();
            //工程机只同步生态链和电视二级项目
            for (Integer categoryId : ProtoProjectCategroyEnum.getAllCode()){
                try {
                    dealProject(categoryId,dataTime);
                } catch (Exception e) {
                    log.error("syncEcoAndTvProjectInfo categoryId:{},dataTime:{}, error", categoryId,dataTime, e);
                }
            }
        log.info("syncEcoAndTvProjectInfo end 耗时:{}s", (System.currentTimeMillis()-start)/1000);
    }


    private void dealProject(int categoryId, LocalDateTime dataTime) {
        int pageNum = 1;
        UpcProjectDTO res = getUpcInfo(pageNum, categoryId, dataTime);

        if (res.getTotal() <= 0) {
            return;
        }
        syncProjectCfgOrList(res, ProtoProjectCategroyEnum.getMrpTypeByCode(categoryId));

        long pageTotal = res.getTotal() % PAGE_SIZE == 0 ? res.getTotal() / PAGE_SIZE : res.getTotal() / PAGE_SIZE + 1;
        for (pageNum = 2; pageNum <= pageTotal; pageNum ++) {
            UpcProjectDTO upcInfo = getUpcInfo(pageNum, categoryId, dataTime);
            syncProjectCfgOrList(upcInfo, ProtoProjectCategroyEnum.getMrpTypeByCode(categoryId));
        }
    }

    /**
     * 处理二级项目
     *
     * @param page
     * @param categoryId
     * @param dataTime
     * @return
     */
    private UpcProjectDTO getUpcInfo(int page, int categoryId, LocalDateTime dataTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.COMMON_PATTERN);
        UpcProjectReq upcReq = UpcProjectReq.builder()
                .level(2)
                .page(page)
                .pageSize(PAGE_SIZE)
                .categoryId(categoryId)
                .startTime(dateTimeFormatter.format(dataTime))
                .endTime(dateTimeFormatter.format(LocalDateTime.now())).build();

        log.info("upcClient.skuProjectQuery request:{}", GsonUtil.toJsonString(upcReq));
        UpcProjectDTO res = upcClient.skuProjectQuery(upcReq);
        log.info("upcClient.skuProjectQuery result:{}", GsonUtil.toJsonString(res));
        return res;
    }

    public UpcProjectDTO getUpcInfoByProjectCode(int page, String projectCode, LocalDateTime dataTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.COMMON_PATTERN);
        UpcProjectReq upcReq = UpcProjectReq.builder()
                .level(2)
                .page(page)
                .pageSize(PAGE_SIZE)
                .projectCode(projectCode)
                .startTime(dateTimeFormatter.format(dataTime))
                .endTime(dateTimeFormatter.format(LocalDateTime.now())).build();

        log.info("upcClient.skuProjectQuery request:{}", GsonUtil.toJsonString(upcReq));
        UpcProjectDTO res = upcClient.skuProjectQuery(upcReq);
        log.info("upcClient.skuProjectQuery result:{}", GsonUtil.toJsonString(res));
        return res;
    }

    public void dealProject(String projectCode, String mrpType) {
        UpcProjectDTO res = getUpcInfoByProjectCode(1, projectCode, LocalDateTime.now().plusYears(-10));
        this.syncProjectCfgOrList(res, mrpType);
    }

    private int syncProjectCfgOrList(UpcProjectDTO res,String mrpType){
        if (res != null && CollectionUtils.isNotEmpty(res.getData())) {
            List<ProtoProjectListPO> projectList = new ArrayList<>();
            for (UpcProjectDetailDTO detailDTO : res.getData()) {
                projectList.add(assembleProject(detailDTO, mrpType));
            }
            List<ProtoProjectListPO> saveProjectList = new ArrayList<>();
            List<ProtoProjectListPO> updateProjectList = new ArrayList<>();
            List<ProtoProjectCfgPO> saveCfgList = new ArrayList<>();
            List<ProtoProjectCfgPO> updateCfgList = new ArrayList<>();
            List<String> projectCodeMap = projectList.stream().map(ProtoProjectListPO::getProjectCode).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(projectCodeMap)) {
                List<ProtoProjectListPO> existProjectListMap = protoProjectListService.getByProjectCodes(projectCodeMap);
                Map<String, ProtoProjectListPO> projectListPOMap = existProjectListMap.stream()
                        .collect(Collectors.toMap(ProtoProjectListPO::getProjectCode, o -> o, (o1, o2) -> o1));
                Map<String, ProtoProjectCfgPO> existProjectCfgMap = protoProjectCfgService.listByProjectCodeList(projectCodeMap)
                        .stream().collect(Collectors.toMap(ProtoProjectCfgPO::getProjectCode, o -> o, (o1, o2) -> o1));
                this.assembleProjectCfg(saveCfgList, updateCfgList, projectList, existProjectCfgMap);
                for (ProtoProjectListPO projectListPO : projectList) {
                    if (MapUtils.isNotEmpty(projectListPOMap) && projectListPOMap.containsKey(projectListPO.getProjectCode())) {
                        ProtoProjectListPO projectPo = projectListPOMap.get(projectListPO.getProjectCode());
                        projectPo.setProjectName(projectListPO.getProjectName());
                        projectPo.setParentCode(projectListPO.getParentCode());
                        projectPo.setModifyDate(new Date());
                        updateProjectList.add(projectPo);
                    } else {
                        saveProjectList.add(projectListPO);
                    }
                }
            }
            // 批量入库项目配置
            if (CollectionUtils.isNotEmpty(saveProjectList)) {
                protoProjectListService.saveBatch(saveProjectList);
            }
            if (CollectionUtils.isNotEmpty(updateProjectList)) {
                protoProjectListService.updateBatchById(updateProjectList);
            }
            if (CollectionUtils.isNotEmpty(saveCfgList)) {
                // 批量入库项目配置
                protoProjectCfgService.saveBatch(saveCfgList);
            }
            if (CollectionUtils.isNotEmpty(updateCfgList)) {
                // 批量入库项目配置
                protoProjectCfgService.updateBatchById(updateCfgList);
            }
            return res.getData().size();
        }
        return 0;
    }


    private void assembleProjectCfg(List<ProtoProjectCfgPO> saveCfgList,
                                    List<ProtoProjectCfgPO> updateCfgList,
                                    List<ProtoProjectListPO> projectList,
                                    Map<String, ProtoProjectCfgPO> existProjectMap) {

        for (ProtoProjectListPO projectListPO : projectList) {
            ProtoProjectCfgPO cfgPO = null;
            if (MapUtils.isNotEmpty(existProjectMap) && existProjectMap.containsKey(projectListPO.getProjectCode())) {
                cfgPO = existProjectMap.get(projectListPO.getProjectCode());
                BeanUtils.copyProperties(projectListPO, cfgPO);
                cfgPO.setParentProjectCode(projectListPO.getParentCode());
                cfgPO.setAuditing(CommonConstant.RecordStatus.COMMITTED.getStatus());
                cfgPO.setModifyDate(new Date());
                cfgPO.setFinalUserName(CommonConstant.ADMIN_KEY);
                updateCfgList.add(cfgPO);
            } else {
                cfgPO = new ProtoProjectCfgPO();
                cfgPO.setCfgId(commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_PROJECT_CFG));
                cfgPO.setCfgCode(commonService.getFlowNum(RedisFlowNumEnum.PROJECT_CODE_NUM));
                cfgPO.setProjectCode(cfgPO.getProjectCode());
                cfgPO.setIsJis(YesNoEnum.INNER_TO_INNER.getType());
                BeanUtils.copyProperties(projectListPO, cfgPO);
                cfgPO.setParentProjectCode(projectListPO.getParentCode());
                cfgPO.setAuditing(CommonConstant.RecordStatus.COMMITTED.getStatus());
                cfgPO.setModifyDate(new Date());
                cfgPO.setFinalUserName(CommonConstant.ADMIN_KEY);
                saveCfgList.add(cfgPO);
            }
        }
    }

    private ProtoProjectListPO assembleProject(UpcProjectDetailDTO item,String mrpType) {
        ProtoProjectListPO project = new ProtoProjectListPO();
        project.setProjectListId(commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_PROJECT_LIST));
        project.setProjectName(item.getNameCn());
        project.setProjectCode(item.getProjectCode());
        project.setParentCode(String.valueOf(item.getParentId()));
        project.setIsOld(item.getParentId() == -1 ? new BigDecimal(1) : new BigDecimal(0));
        project.setProjectType(mrpType);
        project.setAuditing("1");
        project.setAddDate(new Date());
        project.setModifyDate(new Date());
        project.setProjectLevel(String.valueOf(item.getLevel()));
        project.setMrpType(mrpType);
        return project;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncSapEcoSkuData(LocalDateTime dataTime) {
        log.info("syncSapEcoSkuData start==dataTime:{}", dataTime);
        int pageNum = 1;
        while (true) {
            boolean success = queryFactoryMaterialList(pageNum, dataTime).size() < PAGE_SIZE;
            if (success) {
                return;
            }
            pageNum++;
        }
    }


    private List<SapSkuDetailVO> queryFactoryMaterialList(Integer pageNum, LocalDateTime dataTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.YEAR_MONTH_DATE_INT);
        SapSkuDTO request = new SapSkuDTO();
        request.setSystemId(SYSTEMID);
        request.setPage(pageNum);
        request.setPageSize(PAGE_SIZE);
        request.setStartTime(dateTimeFormatter.format(dataTime));
        request.setEndTime(dateTimeFormatter.format(LocalDateTime.now()));
        request.setItems(GsonUtil.toList(ECO_FACTORY, SapSkuDTO.SapSkuItemDTO.class));
        log.info("sapClient.queryFactoryMaterialList request:{}", GsonUtil.toJsonString(request));
        SapBaseVO<SapSkuDetailVO> result = sapClient.queryFactoryMaterialList(request);
        log.info("sapClient.queryFactoryMaterialList result size:{}", CollectionUtils.isEmpty(result.getData()) ? 0 : result.getData().size());
        if (!result.getSuccess()) {
            return Collections.EMPTY_LIST;
        }
        List<ProtoProjectStagePO> saveList = new ArrayList<>();
        List<ProtoProjectStagePO> updateList = new ArrayList<>();

        List<SapSkuDetailVO> skuList = result.getData().stream()
                .filter(e -> e.getSkuCode().startsWith("L90") || e.getSkuCode().startsWith("T910"))
                .filter(distinctByKey(f->f.getSkuCode())).collect(Collectors.toList());
        //生态链半成品物料编码取智能硬件取L90开头的料。确认料号范围T910开头料号，调用方过滤，防止其他业务需要其他
        List<String> skuCodeList = skuList.stream().map(SapSkuDetailVO::getSkuCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(skuCodeList)) {
            Map<String, ProtoProjectStagePO> poMap = protoProjectStageService.listBySkuCodeListAndMrpType(skuCodeList, MrpTypeEnum.ECOCHAIN.getType())
                    .stream().collect(Collectors.toMap(ProtoProjectStagePO::getSkuCode, o -> o, (o1, o2) -> o1));
            for (SapSkuDetailVO skuVO : skuList) {
                if (MapUtils.isNotEmpty(poMap) && poMap.containsKey(skuVO.getSkuCode())) {
                    ProtoProjectStagePO po = poMap.get(skuVO.getSkuCode());
                    po.setSkuName(skuVO.getSkuName());
                    updateList.add(po);
                } else {
                    saveList.add(assembleStage(skuVO));
                }
            }
        }
        log.info("sapClient.queryFactoryMaterialList filter saveList result:{}", GsonUtil.toJsonString(saveList));
        if (CollectionUtils.isNotEmpty(saveList)) {
            protoProjectStageService.saveBatch(saveList);
        }
        log.info("sapClient.queryFactoryMaterialList filter updateList result:{}", GsonUtil.toJsonString(updateList));
        if (CollectionUtils.isNotEmpty(updateList)) {
            protoProjectStageService.updateBatchById(updateList);
        }
        return result.getData();
    }
    public static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    private ProtoProjectStagePO assembleStage(SapSkuDetailVO skuVO) {
        ProtoProjectStagePO po = new ProtoProjectStagePO();
        po.setStageId(commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_PROJECT_STAGE));
        po.setSkuCode(skuVO.getSkuCode());
        po.setSkuName(skuVO.getSkuName().replaceAll("\"", " "));
        po.setMrpType(MrpTypeEnum.ECOCHAIN.getType());
        po.setAddDate(new Date());
        po.setModifyDate(new Date());
        return po;
    }
}
