package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.converter.CardVOConverter;
import com.mi.oa.asset.mobile.application.dto.AssetCommonDTO;
import com.mi.oa.asset.mobile.application.dto.CommonConditionDTO;
import com.mi.oa.asset.mobile.application.dto.ConditionDTO;
import com.mi.oa.asset.mobile.application.dto.cardusertab.CardDTO;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.ProtoCardUserTabBOService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardService;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 个人工程机
 *
 * <AUTHOR>
 * @date 2022/2/23 16:49
 */
@Service
public class ProtoCardUserTabBOServiceImpl implements ProtoCardUserTabBOService {

    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private CardVOConverter cardVOConverter;

    @Autowired
    private CommonService commonService;

    @Override
    public AssetCommonDTO snTraceQuery(CommonConditionDTO requestParam) {
        List<ConditionDTO> conditionList = commonService.getConditionList(requestParam);
        int start = requestParam.getStart();
        //如果查询条件都为空，则提示查询条件不能都为空
        if (CollectionUtils.isEmpty(conditionList)) {
            throw new BizException(ApplicationErrorCodeEnum.PROTO_CARD_USER_TAB_001);
        }
        //如果查询出来的数据大于1条，则提示：单次仅能追溯一条SN记录！当前查询条件下，有多条符合要求数据，请细化检索条件或仅用SN查询！
        int cnt = protoCardService.countForSnTrace(conditionList);
        if (cnt <1) {
            throw new BizException(ApplicationErrorCodeEnum.PROTO_CARD_USER_TAB_003);
        }
        if (cnt > 1) {
            throw new BizException(ApplicationErrorCodeEnum.PROTO_CARD_USER_TAB_002);
        }
        //按条件查询台账信息
        List<ProtoCardPO> list = protoCardService.getListForSnTrace(conditionList);
        List<CardDTO> cardPOtoDTOs = cardVOConverter.cardPOtoDTOs(list);
        return AssetCommonDTO.builder().total((long) cardPOtoDTOs.size()).root(cardPOtoDTOs.subList(start, cardPOtoDTOs.size())).build();
    }
}
