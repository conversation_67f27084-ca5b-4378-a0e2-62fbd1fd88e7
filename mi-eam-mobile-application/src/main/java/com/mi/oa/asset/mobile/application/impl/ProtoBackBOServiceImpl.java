package com.mi.oa.asset.mobile.application.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.mobile.application.converter.ProtoBackBizConverter;
import com.mi.oa.asset.mobile.application.converter.ProtoBackBizDetConverter;
import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.dto.bpm.*;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.BpmV3Service;
import com.mi.oa.asset.mobile.application.service.ProtoBackBOService;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.dto.BpmExeLogDTO;
import com.mi.oa.asset.mobile.infra.dto.hrod.EmployeeInfo;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.asset.mobile.utils.StringUtil;
import com.mi.oa.infra.oaucf.bpm.enums.ProcessInstanceStatus;
import com.mi.oa.infra.oaucf.bpm.enums.UserTaskOperation;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/21 10:22
 */
@Service
@Slf4j
public class ProtoBackBOServiceImpl implements ProtoBackBOService {

    private static final String FUNID = "proto_back";

    private static final String ORDER_NAME = "工程机归还-BPM回调";

    @Autowired
    private ProtoInService protoInService;
    @Autowired
    private ProtoCardService protoCardService;
    @Autowired
    private ProtoInDetService protoInDetService;
    @Autowired
    private ProtoService protoService;
    @Autowired
    private FunAllControlService funAllControlService;

    @Autowired
    private BpmV3Service bpmV3Service;

    @Autowired
    private BpmExeLogService bpmExeLogService;
    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private SysUserRoleService userRoleService;

    @Autowired
    private ProtoBackBizConverter protoBackBizConverter;

    @Autowired
    private ProtoBackBizDetConverter protoBackBizDetConverter;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInStatusAndProtoCard(String inId, String inStatus, String isBack, String useState) {
        protoInService.updateById(ProtoInPO.builder().inId(inId).inStatus(inStatus).build());
        updateProtoCardToUseState(inId, isBack, useState);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String afterAudit(String[] keyIds) {
        for (String keyId : keyIds) {
            // 更新归还状态
            updateInStatusAndProtoCard(keyId, CardStateEnum.RETURN_CONFIRMATION_IN_PROGRESS.getKey()
                    , ProtoInDetIsBackEnum.UNCONFIRMED.getCode(), CardStateEnum.RETURN_CONFIRMATION_IN_PROGRESS.getKey());
        }
        return CommonConstant.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String pushBpm(String keyId) {
        //获取状态的显示值
        List<FunAllControlPO> stateList = funAllControlService.getComboValue("proto_use_state");
        List<FunAllControlPO> typeList = funAllControlService.getComboValue("apply_user_type");
        // 查询主表信息
        ProtoInPO mainData = protoInService.getById(keyId);
        String mainMrpType = mainData.getMrpType();
        if (ObjectUtils.isEmpty(mainData)) {
            log.error("查询主表信息失败！");
            throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR, RedisUniqueKeyEnum.PROTO_IN.getTableName(), keyId);
        }
        String inCode = mainData.getInCode();
        String userCode = mainData.getApplyUserCode();
        String applyUserName = mainData.getApplyUserName();
        String checkUserCode = mainData.getUserCode();

        List<ProtoInDetPO> checkList = protoInDetService.list(Wrappers.<ProtoInDetPO>lambdaQuery()
                .eq(ProtoInDetPO::getInId, keyId));
        for (ProtoInDetPO protoInDetPO : checkList) {
            // 明细表存在不合格项，请检查！
            if (ProtoInDetCheckStatusEnum.UNQUALIFIED.getCode().equals(protoInDetPO.getCheckStatus())) {
                //明细表存在不合格项，请检查！ JsMessage.getValue("proto_tran.ht_text027")
                log.error("明细表存在不合格项，请检查！");
                throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, "明细表存在不合格项，请检查！");
            }
            if (null == protoInDetPO.getMrpType() || "".equals(protoInDetPO.getMrpType())) {
                // 明细表业务类型不能为空，请重新导入
                throw new BizException(ApplicationErrorCodeEnum.ERR_NULL_TYPE, "子表业务类型不能为空，请重新导入数据！");
            }
            // 判断主表和明细表业务类型是否一致
            if (!protoInDetPO.getMrpType().equals(mainMrpType)) {
                // 主表和明细表业务类型一致才可提交
                throw new BizException(ApplicationErrorCodeEnum.ERR_INCONSISTENCY_TYPE, "主表与子表的业务类型不一致，不允许提交！");
            }
        }
        List<ProtoCardPO> changeList = isChange(keyId);
        if (!CollectionUtils.isEmpty(changeList)) {
            StringBuilder msgBuilder = new StringBuilder();
            for (ProtoCardPO changeMap : changeList) {
                String deviceCode = changeMap.getDeviceCode();
                String cardUserCode = changeMap.getUserCode();
                String useStateDisplay = displayValue(stateList, changeMap.getUseState());
                String applyUserTypeDisplay = displayValue(typeList, changeMap.getApplyUserType());
                //设备编号SN【{0}】-当前使用人【{1}】-状态【{2}】-领用方类型【{3}】
                msgBuilder.append(String.format("设备编号SN【%s】-当前使用人【%s】-状态【%s】-领用方类型【%s】 %n", deviceCode, cardUserCode, useStateDisplay, applyUserTypeDisplay));
            }
            //以下设备编号SN在台账中出现异动，请检查！
            log.error("以下设备编号SN在台账中出现异动，请检查！" + msgBuilder);
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, "以下设备编号SN在台账中出现异动，请检查！<br/>" + msgBuilder.toString());
        }
        //校验明细的SN是否已出库
        qryProtoOut(keyId, mainMrpType);

        String applyUserType = protoService.getApplyUserType(userCode);
        if (StringUtils.isBlank(applyUserType)) {
            //p账号用户必须在第三方人员配置表里维护才能选择 JsMessage.getValue("proto_collect_plan.ht_text006")
            log.error("p账号用户必须在第三方人员配置表里维护才能选择!");
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, "p账号用户必须在第三方人员配置表里维护才能选择!");
        }
        // 更新归还单：领用方类型
        protoInService.updateById(ProtoInPO.builder().inId(keyId).applyUserType(applyUserType).build());
        if (CommonConstant.ApplyUserTypeEnum.PROVIDER.getValue().equals(applyUserType)) {
            //第三方供应商不允许发起归还业务！ JsMessage.getValue("proto_back.ht_text015")
            log.error("第三方供应商不允许发起归还业务！");
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, "第三方供应商不允许发起归还业务！");
        }
        // 售后样机归还流程 odm审批流：ODM>小米确认人>售后样管  小米员工：申请人>售后样管
        if (CommonConstant.ApplyUserTypeEnum.ODM.getValue().equals(applyUserType)) {
            String userName = mainData.getUserName();
            if (CommonConstant.ApplyUserTypeEnum.ODM.getValue().equals(applyUserType) && StringUtils.isEmpty(userName)) {
                //领用方类型为：第三方供应商的归还单需要选择“确认人”！ JsMessage.getValue("proto_back.ht_text007")
                log.error("领用方类型为：第三方供应商的归还单需要选择确认人！");
                throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, "领用方类型为：第三方供应商的归还单需要选择确认人！");
            }
            // 提交BPM流程
            submitBpm(userCode, applyUserName, keyId, inCode, checkUserCode, applyUserType);
            // 更新归还状态
            updateInStatusAndProtoCard(keyId, InStatusEnum.NOT_DELIVERY.getState(), null, CardStateEnum.RETURN_CONFIRMATION_IN_PROGRESS.getKey());
        } else {
            protoInService.updateById(ProtoInPO.builder().inId(keyId).auditingBack(TranAuditEnum.CHECK_OK.getKey()).modifyDate(new Date()).build());
            // 更新归还状态
            updateInStatusAndProtoCard(keyId, CardStateEnum.RETURN_CONFIRMATION_IN_PROGRESS.getKey(), null, CardStateEnum.RETURN_CONFIRMATION_IN_PROGRESS.getKey());
        }
        return CommonConstant.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bpmCallback(BpmCallbackDTO<MobileProcessBaseBizDTO> bpmCallbackDTO) {
        if (bpmCallbackDTO == null) {
            insertlog("工程机归还-BPM回调", null, null, null, false, "bpmCallbackDTO参数为空", bpmCallbackDTO);
            throw new BizException(ApplicationErrorCodeEnum.PARAM_INVALID_ERROR, "bpmCallbackDTO参数为空");
        }
        // 获取business_key
        String businessKey = bpmCallbackDTO.getBusinessKey();
        ProtoInPO mainData = protoInService.findOneByParam(ProtoInPO.builder().businessKey(businessKey).build());
        if (ObjectUtils.isEmpty(mainData)) {
            insertlog("工程机归还-BPM回调", null, null, null, false, "业务数据找不到business_key【" + businessKey + "】", bpmCallbackDTO);
            return true;
        }
        // 获取业务参数
        String keyId = mainData.getInId();
        String inCode = mainData.getInCode();
        String applyUserName = mainData.getApplyUserName();
        //获取审批状态
        BpmCallbackStatusEnum status = bpmCallbackDTO.getStatus();
        // 驳回
        if (BpmCallbackStatusEnum.REJECT.equals(status)) {
            protoInService.updateById(ProtoInPO.builder().inId(keyId).auditingBack(TranAuditEnum.CANCELLED.getKey()).modifyDate(new Date()).build());
            updateInStatusAndProtoCard(keyId, InStatusEnum.NOT_DELIVERY.getState(), null, CardStateEnum.TO_BE_RETURNED.getKey());
        }
        // 审批完成
        else if (BpmCallbackStatusEnum.END.equals(status)) {
            String auditing = mainData.getAuditingBack();
            // 检测流程是否已审批
            if (TranAuditEnum.CHECK_OK.getKey().equals(auditing)) {
                return true;
            }
            protoInService.updateById(ProtoInPO.builder().inId(keyId).auditingBack(TranAuditEnum.CHECK_OK.getKey()).modifyDate(new Date()).build());
            updateInStatusAndProtoCard(keyId, CardStateEnum.RETURN_CONFIRMATION_IN_PROGRESS.getKey(), null, CardStateEnum.TO_BE_RETURNED.getKey());
        }
        // 执行成功
        insertlog("工程机归还-BPM回调", keyId, inCode, applyUserName, true, "工程机归还Bpm审批完成！", bpmCallbackDTO);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bpmCallbackV3(BpmProcessCompletedDTO param) {
        log.debug("========正在处理工程机归还Bpm回调事件");
        if (Objects.isNull(param.getFormData())) {
            log.warn(String.format(BmpV3ProcessEnum.PROTO_BACK_APPLY.getProcessInstanceName(), "回调无表单数据"));
            bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, null, null,
                    null, false, "param参数为空", param);
        }

        // 获取business_key
        String businessKey = param.getBusinessKey();
        ProtoInPO mainData = protoInService.findOneByParam(ProtoInPO.builder().businessKey(businessKey).build());
        if (ObjectUtils.isEmpty(mainData)) {
            bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, null, null,
                    null, false, "业务数据找不到business_key【" + businessKey + "】", param);
            return true;
        }

        // 获取业务参数
        String keyId = mainData.getInId();
        String inCode = mainData.getInCode();
        String applyUserName = mainData.getApplyUserName();

        // 流程结束事件
        if (param.getProcessInstanceStatus() == ProcessInstanceStatus.COMPLETED) {
            String auditing = mainData.getAuditingBack();
            // 检测流程是否已审批
            if (TranAuditEnum.CHECK_OK.getKey().equals(auditing)) {
                return true;
            }
            protoInService.updateById(ProtoInPO.builder().inId(keyId).auditingBack(TranAuditEnum.CHECK_OK.getKey()).modifyDate(new Date()).build());
            updateInStatusAndProtoCard(keyId, "2", null, CardStateEnum.RETURN_CONFIRMATION_IN_PROGRESS.getKey());
            bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, keyId, inCode, applyUserName, true, "工程机归还Bpm审批完成！", param);
        }

        // 流程终止（撤回）
        if (param.getProcessInstanceStatus() == ProcessInstanceStatus.TERMINATED) {
            protoInService.updateById(ProtoInPO.builder().inId(keyId).auditingBack(TranAuditEnum.NOT_SUBMIT.getKey()).modifyDate(new Date()).build());
            updateInStatusAndProtoCard(keyId, InStatusEnum.NOT_DELIVERY.getState(), null, CardStateEnum.TO_BE_RETURNED.getKey());
            bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, keyId, inCode, applyUserName, true, "工程机归还Bpm审批被终止（撤回）！", param);
        }

        // 流程审批事件
        String status = param.getVariables().get("status").toString();
        if (StringUtils.isNotBlank(status)) {
            UserTaskOperation userTaskOperation = UserTaskOperation.findByCode(status);
            switch (userTaskOperation) {
                case REJECT:
                    protoInService.updateById(ProtoInPO.builder().inId(keyId).auditingBack(TranAuditEnum.CANCELLED.getKey()).modifyDate(new Date()).build());
                    updateInStatusAndProtoCard(keyId, InStatusEnum.NOT_DELIVERY.getState(), null, CardStateEnum.TO_BE_RETURNED.getKey());
                    bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, keyId, inCode, applyUserName, true, "工程机归还Bpm审批被拒绝！", param);
                    break;
                default:
                    log.debug("其他状态不处理...");
            }
        }

        log.debug("========处理工程机归还Bpm回调事件结束");
        return true;
    }

    /**
     * 撤回
     *
     * @param keyIds
     * @param userInfo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String withDraw(String[] keyIds, UserInfo userInfo) {
        String userCode = userInfo.getUserCode();
        for (String keyId : keyIds) {
            ProtoInPO inMap = protoInService.getById(keyId);
            if (ObjectUtils.isEmpty(inMap)) {
                throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR, RedisUniqueKeyEnum.PROTO_IN.getTableName(), keyId);
            }
            String auditingBackConfirm = inMap.getAuditingBackConfirm();
            String auditingBack = inMap.getAuditingBack();
            String applyUserCode = inMap.getApplyUserCode();
            String businessKey = inMap.getBusinessKey();
            String applyUserType = inMap.getApplyUserType();
            String mrpType = inMap.getMrpType();
            // 售后样机，审批中才能撤回
            if (MrpTypeEnum.PROTOTYPE.getType().equals(mrpType) && !TranAuditEnum.CHECKING.getKey().equals(auditingBack)) {
                throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_009);
            }
            if (!MrpTypeEnum.PROTOTYPE.getType().equals(mrpType) && !TranAuditEnum.CHECK_OK.getKey().equals(auditingBack)) {
                //只有已审批的单据才可以撤回！ JsMessage.getValue("proto_back.ht_text018")
                throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_004);
            }
            if (TranAuditEnum.SUBMITED.getKey().equals(auditingBackConfirm)) {
                //只有未确认的单据才可以撤回！  JsMessage.getValue("proto_back.ht_text019")
                throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_005);
            }

            if (!StringUtils.equals(userCode, applyUserCode)) {
                //只有申请人才可以撤回！ JsMessage.getValue("proto_back.ht_text020")
                throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_006);
            }
            // 非售后样机只有odm申请才能撤回，售后样机归还单可撤回
            if (CommonConstant.ApplyUserTypeEnum.ODM.getValue().equals(applyUserType) || MrpTypeEnum.PROTOTYPE.getType().equals(mrpType)) {
                // 撤回Bpm流程
                log.info("撤回Bpm流程...");
                try {
                    bpmV3Service.terminate(businessKey, userCode);
                } catch (Exception e) {
                    log.error("撤回Bpm流程失败！");
                    throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_007);
                }
            }
            protoInService.updateById(ProtoInPO.builder().inId(keyId).auditingBack(TranAuditEnum.NOT_SUBMIT.getKey()).modifyDate(new Date()).build());
            //更新工程机归还单状态,台账状态
            updateInStatusAndProtoCard(keyId, "0", null, "4");

            //更新明细表归还状态
            protoInDetService.updateById(ProtoInDetPO.builder().detId(keyId).isComplete(CommonConstant.ProtoCardIsComplete.DEFAULT.getType()).build());

        }
        return CommonConstant.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String interruptBpm(String[] keyIds, UserInfo userInfo) {
        for (String keyId : keyIds) {
            interruptBpm(keyId, userInfo);
        }
        return CommonConstant.TRUE;
    }

    /**
     * 撤回BPM流程具体逻辑
     *
     * @param keyId
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public String interruptBpm(String keyId, UserInfo userInfo) {
        // 查询主表信息
        ProtoInPO mainData = protoInService.getById(keyId);
        String inCode = mainData.getInCode();
        String userCode = mainData.getApplyUserCode();
        String auditing = mainData.getAuditingBack();
        String businessKey = mainData.getBusinessKey();

        if (!StringUtils.equals(userCode, userInfo.getUserCode())) {
            //只能撤回本人发起的单据！ JsMessage.getValue("proto_bpm.ht_text003")
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_003);
        }
        // 检测参数
        if (StringUtils.isEmpty(businessKey)) {
            //JsMessage.getValue("proto_bpm.ht_text005")
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_005);
        }

        if (!TranAuditEnum.CHECKING.getKey().equals(auditing)) {
            //JsMessage.getValue("proto_bpm.ht_text006")
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_006);
        }
        // 撤回Bpm流程
        log.info("撤回Bpm流程...");
        try {
            bpmV3Service.terminate(businessKey, userCode);
        } catch (Exception e) {
            log.error("撤回Bpm流程失败！");
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_007);
        }

        protoInService.updateById(ProtoInPO.builder().inId(keyId).auditingBack(TranAuditEnum.NOT_SUBMIT.getKey()).modifyDate(new Date()).build());
        // 更新归还状态
        updateInStatusAndProtoCard(keyId, InStatusEnum.NOT_DELIVERY.getState(), null, CardStateEnum.TO_BE_RETURNED.getKey());
        return CommonConstant.TRUE;
    }

    private String submitBpm(String userCode, String applyUserName, String keyId, String inCode, String checkUserCode, String applyUserType) {
        ProtoInPO protoInPO = protoInService.getById(keyId);
        // 工程机类型
        String mrpType = setValue(protoInPO.getMrpType());
        List<ProtoInDetPO> protoInDetPOList = protoInDetService.listByInId(protoInPO.getInId());

        ProtoBackBizDTO protoBackBizDTO = protoBackBizConverter.protoBackBizPOToDTO(protoInPO);
        List<ProtoBackBizDetDTO> protoBackBizDetDTOList = protoBackBizDetConverter.protoBackDetPOToDTOList(protoInDetPOList);

        int code = 1;
        for (ProtoBackBizDetDTO protoBackBizDetDTO : protoBackBizDetDTOList) {
            protoBackBizDetDTO.setCode(String.valueOf(code++));
            protoBackBizDetDTO.setAssetNewold(funAllControlService.getComboValue("asset_newold", setValue(protoBackBizDetDTO.getAssetNewold())));
        }
        protoBackBizDTO.setMrpType(funAllControlService.getComboValue("mrp_type", mrpType));
        // 小米确认人
        protoBackBizDTO.setUserCode(Objects.isNull(checkUserCode) ? "" : checkUserCode);
        protoBackBizDTO.setProtoBackBizDetDTOList(protoBackBizDetDTOList);

        Map<String, Object> variables = new HashMap<>();
        variables.put("mrp_type", mrpType);
        // 售后样机
        if (MrpTypeEnum.PROTOTYPE.getType().equals(mrpType)) {
            variables.put("applyUserType", applyUserType);
        }

        String businessKey = bpmV3Service.submitProtoBackApply(protoBackBizDTO, userCode, variables);
        if (StringUtils.isEmpty(businessKey)) {
            insertlog("工程机归还", keyId, inCode, applyUserName, false, "抛送BPM失败！", protoBackBizDTO);
            //抛送BPM失败！ JsMessage.getValue("proto_bpm.ht_text001")
            log.error("抛送BPM失败！");
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_001);
        }
        protoInService.updateById(ProtoInPO.builder().inId(keyId).auditingBack(TranAuditEnum.CHECKING.getKey()).modifyDate(new Date()).businessKey(businessKey).build());
        // 保存BPM流程信息
        insertlog("工程机归还", keyId, inCode, applyUserName, true, businessKey, protoBackBizDTO);
        return businessKey;
    }

    /**
     * 插入BPM日志
     *
     * @param orderName 单据名称
     * @param keyId     单据主键
     * @param code      单据号
     * @param userName  执行人
     * @param success   执行代号
     * @param desc      执行结果
     * @param body      执行报文
     * @return
     */
    private void insertlog(String orderName, String keyId, String code, String userName, boolean success, String desc, Object body) {
        BpmExeLogDTO protoBpmExeLogDTO = BpmExeLogDTO.builder()
                .funId("proto_back")
                .orderId(keyId)
                .orderCode(code)
                .orderName(orderName)
                .doUser(userName)
                .doDate(DateUtils.getFormatDate(DateUtils.COMMON_PATTERN))
                .exeCode(success)
                .exeDesc(desc)
                .exeBody(body)
                .build();
        bpmExeLogService.save(protoBpmExeLogDTO);
    }

    /**
     * 找出所有未出库的设备编号SN
     *
     * @param keyId
     * @return
     */
    public void qryProtoOut(String keyId, String mrpType) {
        List<ProtoInDetPO> list = protoInDetService.findListByParam(ProtoInDetPO.builder().inId(keyId).build());
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<String> deviceCodeList = list.stream().map(ProtoInDetPO::getDeviceCode).collect(Collectors.toList());
        List<ProtoOutDetPO> protoOut = protoService.getOutDet(deviceCodeList
                , OutStateEnum.TO_BE_CONFIRMED.getState(), ProtoOutTypeEnum.PURCHASE_RETURN.getCode());
        if (!CollectionUtils.isEmpty(protoOut)) {
            //以下设备编号SN尚未【确认收货】，请确认收货后，再次尝试提交！
            StringBuilder sb = new StringBuilder();
            protoOut.forEach(t -> sb.append(",").append(t.getDeviceCode()));
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, "以下设备编号SN尚未【确认收货】，请确认收货后，再次尝试提交！" + sb.substring(1));
        }
        // 售后样机校验资产台账状态
        if (MrpTypeEnum.PROTOTYPE.getType().equals(mrpType)) {
            List<ProtoCardPO> protoCardPOList = protoCardService.listByDevices(deviceCodeList);
            StringBuilder sb = new StringBuilder();
            protoCardPOList.forEach(protoCard -> {
                if (CardStateEnum.UNABLE_TO_RETURN.getKey().equals(protoCard.getUseState())) {
                    sb.append(",").append(protoCard.getDeviceCode());
                }
            });
            if (sb.length() > 0) {
                throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, "以下设备编号SN已处置，请检查！" + sb.substring(1));
            }

        }
    }

    /**
     * 检查明细数据在台账中是否出现异动（即不满足导入条件）
     *
     * @param keyId
     * @return
     */
    private List<ProtoCardPO> isChange(String keyId) {
        ProtoInPO main = protoInService.getById(keyId);
        if (ObjectUtils.isEmpty(main)) {
            throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR, RedisUniqueKeyEnum.PROTO_IN.getTableName(), keyId);
        }
        List<ProtoInDetPO> detList = protoInDetService.findListByParam(ProtoInDetPO.builder().inId(keyId).build());
        if (CollectionUtils.isEmpty(detList)) {
            return null;
        }
        String applyUserCode = main.getApplyUserCode();
        List<String> deviceCodes = detList.stream().map(ProtoInDetPO::getDeviceCode).collect(Collectors.toList());
        List<ProtoCardPO> cardList = protoCardService.findNotSelfCard(deviceCodes, applyUserCode, CommonConstant.ApplyUserTypeEnum.PROVIDER);

        return cardList;
    }

    private String displayValue(List<FunAllControlPO> comboList, String val) {
        if (CollectionUtils.isEmpty(comboList)) {
            return val;
        } else {
            Optional<FunAllControlPO> res = comboList.stream().filter(t -> StringUtils.equals(val, t.getValueData())).findFirst();
            if (res.isPresent()) {
                return res.get().getDisplayData();
            } else {
                return val;
            }
        }
    }

    /**
     * 更新工程机归台账状态
     *
     * @param in_id
     * @param is_back
     * @param use_state
     * @return
     */
    public void updateProtoCardToUseState(String in_id, String is_back, String use_state) {
        ProtoInDetPO param = ProtoInDetPO.builder().inId(in_id).build();
        if (!StringUtils.isBlank(is_back)) {
            param.setIsBack(is_back);
        }
        List<ProtoInDetPO> detPOList = protoInDetService.findListByParam(param);
        List<String> deviceCodeList = detPOList.stream().map(t -> t.getDeviceCode()).collect(Collectors.toList());

        ProtoInPO build = ProtoInPO.builder().inId(in_id).build();
        ProtoInPO inPO = protoInService.findOneByParam(build);
        // 售后样机需要剔除已处置的资产台账，不更新其状态
        if (MrpTypeEnum.PROTOTYPE.getType().equals(inPO.getMrpType())) {
            List<ProtoCardPO> cardPOList = protoCardService.getByDeviceCodeAndExcludeUseState(deviceCodeList, CardStateEnum.UNABLE_TO_RETURN.getKey());
            deviceCodeList = cardPOList.stream().map(ProtoCardPO::getDeviceCode).collect(Collectors.toList());
        }
        protoCardService.updateState(deviceCodeList, CardStateEnum.convert(use_state), inPO.getMrpType());
    }

    /**
     * 修复小米内部员工工号为空的数据
     *
     * @return
     */
    @Override
    public String repairEmpCode() {
        List<ProtoInPO> userList = protoInService.getInByEmpCode();
        if (CollectionUtils.isEmpty(userList)) {
            return CommonConstant.TRUE;
        }
        for (ProtoInPO userMap : userList) {
            String userCode = userMap.getUserCode();
            EmployeeInfo empInfo = userInfoService.getEmpInfoByUserName(userCode);
            if (empInfo == null || empInfo.getEmployeeId() == null) {
                continue;
            }
            protoInService.updateInByEmpCode(empInfo.getEmployeeId(), userCode);
        }
        return CommonConstant.TRUE;
    }

    @Override
    public void clearDet(String keyId, String mrpType) {
        // 删除业务类型不同的数据
        protoInDetService.remove(new LambdaQueryWrapper<ProtoInDetPO>().eq(ProtoInDetPO::getInId, keyId).ne(ProtoInDetPO::getMrpType, mrpType));
        int num = protoInDetService.list(new LambdaQueryWrapper<ProtoInDetPO>().eq(ProtoInDetPO::getInId, keyId)).size();
        protoInService.update(null, new LambdaUpdateWrapper<ProtoInPO>().set(ProtoInPO::getInNum, num).eq(ProtoInPO::getInId, keyId));
    }

    /**
     * 判断为null 则返回空字符串
     *
     * @param value
     * @return
     */
    private String setValue(String value) {
        if (StringUtil.isEmpty(value)) {
            value = "";
        }
        return value;
    }

}
