package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.dto.ProtoDisposeDTO;
import com.mi.oa.asset.mobile.application.dto.ProtoPurchaseDTO;
import com.mi.oa.asset.mobile.application.service.ProtoProjectCfgBOService;
import com.mi.oa.asset.mobile.common.enums.PriceStageEnum;
import com.mi.oa.asset.mobile.common.enums.PriceTypeEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.asset.mobile.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 项目配置类
 *
 * <AUTHOR>
 * @date 2022/1/20 17:46
 */
@Service
@Slf4j
public class ProtoProjectCfgBOServiceImpl implements ProtoProjectCfgBOService {


    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private ProtoProjectStageService protoProjectStageService;

    @Autowired
    private ProtoProjectCfgService protoProjectCfgService;

    @Autowired
    private ProtoPriceService protoPriceService;

    @Autowired
    private ProtoPriceDetService protoPriceDetService;

    @Override
    public List<ProtoDisposeDTO> getDisposeDamages(List<String> deviceCodeList) {
        if (CollectionUtils.isEmpty(deviceCodeList)){
            return new ArrayList<>();
        }
        List<ProtoDisposeDTO> result = new ArrayList<>(deviceCodeList.size());
        List<ProtoCardPO> cardList = protoCardService.getByDeviceCode(deviceCodeList);
        // SN-skuCode
        Map<String, String> deviceCodeMap = cardList.stream().collect(Collectors.toMap(ProtoCardPO::getDeviceCode, ProtoCardPO::getSkuCode));
        // SN-projectCode
        Map<String, String> deviceProjectMap = cardList.stream().collect(Collectors.toMap(ProtoCardPO::getDeviceCode, ProtoCardPO::getProjectCode));

        List<String> projectCodeList = cardList.stream().map(ProtoCardPO::getProjectCode).distinct().collect(Collectors.toList());
        // sku-ProtoProjectStagePO
        Map<String, ProtoProjectStagePO> stageMap = protoProjectStageService.listByProjectCode(projectCodeList)
                .stream().collect(Collectors.toMap(ProtoProjectStagePO::getSkuCode, Function.identity(), (o1, o2) -> o1));
        // projectCode-ProtoProjectCfgPO
        Map<String, ProtoProjectCfgPO> projectCodeMap = protoProjectCfgService.listByProjectCodeList(projectCodeList)
                .stream().collect(Collectors.toMap(ProtoProjectCfgPO::getProjectCode, Function.identity(), (o1, o2) -> o1));

        List<ProtoPricePO> priceList = protoPriceService.listPrice();
        List<Integer> priceIdList = priceList.stream().map(ProtoPricePO::getId).collect(Collectors.toList());
        Map<Integer, List<ProtoPriceDetPO>> priceDetMap = protoPriceDetService.listByPriceIdList(priceIdList)
                .stream().collect(Collectors.groupingBy(ProtoPriceDetPO::getPriceId));

        for (String deviceCode : deviceCodeList){
            ProtoDisposeDTO disposeDTO = new ProtoDisposeDTO();
            disposeDTO.setDeviceCode(deviceCode);
            result.add(disposeDTO);
            String skuCode = deviceCodeMap.get(deviceCode);
            String projectCode = deviceProjectMap.get(deviceCode);
            if (StringUtils.isEmpty(skuCode) || StringUtils.isEmpty(projectCode)){
               continue;
            }
            ProtoProjectCfgPO cfgPO = projectCodeMap.get(projectCode);
            // 没有项目配置
            if (null == cfgPO){
                continue;
            }
            ProtoProjectStagePO stagePO = stageMap.get(skuCode);
            // 没有试产阶段
            if (stagePO == null){
                continue;
            }
            // 上市时间
            Date listDate = stagePO.getListDate();
            disposeDTO.setListDesc(stagePO.getListDesc());
            disposeDTO.setListDate(listDate);

            // 上市金额
            BigDecimal listMoney = stagePO.getListMoney();
            for (ProtoPricePO pricePO : priceList){
                if (!PriceTypeEnum.DISPOSE.getType().equals(pricePO.getBusType()) || !StringUtils.equals(pricePO.getIsJis(), cfgPO.getIsJis())){
                    continue;
                }
                List<ProtoPriceDetPO> priceDetList = priceDetMap.get(pricePO.getId());
                if (CollectionUtils.isEmpty(priceDetList)){
                    continue;
                }
                ProtoPriceDetPO detPO = getProtoPriceDetPO(priceDetList, listDate);
                if (null != detPO){
                    BigDecimal priceStage = detPO.getPriceStage();
                    disposeDTO.setDisposalBasis(detPO.getRemark());
                    if (null != listMoney){
                        if (PriceStageEnum.RATIO_PRICE.getType().equals(detPO.getCurStage())){
                            disposeDTO.setReferenceDamages(priceStage.multiply(listMoney).setScale(2, RoundingMode.HALF_UP));
                        } else if (PriceStageEnum.FIX_PRICE.getType().equals(detPO.getCurStage())){
                            disposeDTO.setReferenceDamages(priceStage);
                        }
                    }
                }
            }
        }
        return result;
    }

    @Override
    public List<ProtoPurchaseDTO> getPurchaseDesc(List<ProtoPurchaseDTO> purchaseList) {
        if (CollectionUtils.isEmpty(purchaseList)){
            return new ArrayList<>();
        }
        List<String> skuCodeList = purchaseList.stream().map(ProtoPurchaseDTO::getSkuCode).distinct().collect(Collectors.toList());
        List<ProtoPricePO> priceList = protoPriceService.listPrice();
        Map<String, ProtoPricePO> priceMap = priceList.stream()
                .collect(Collectors.toMap(ProtoPricePO::getPriceCode, Function.identity(), (o1, o2) -> o1));
        List<Integer> priceIdList = priceList.stream().map(ProtoPricePO::getId).collect(Collectors.toList());
        Map<Integer, List<ProtoPriceDetPO>> priceDetMap = protoPriceDetService.listByPriceIdList(priceIdList)
                .stream().collect(Collectors.groupingBy(ProtoPriceDetPO::getPriceId));

        Map<String, ProtoProjectStagePO> stageMap = protoProjectStageService.listBySkuCodeListAndMrpType(skuCodeList, purchaseList.get(0).getMrpType())
                .stream().collect(Collectors.toMap(ProtoProjectStagePO::getSkuCode, Function.identity(), (o1, o2) -> o1));
        for (ProtoPurchaseDTO purchaseDTO : purchaseList){
            String skuCode = purchaseDTO.getSkuCode();
            String priceCode = purchaseDTO.getPriceCode();
            // 上市价格
            ProtoProjectStagePO stagePO = stageMap.get(skuCode);
            if (null == stagePO){
                continue;
            }
            // 当前市价
            purchaseDTO.setMarketPrice(stagePO.getMarketPrice());
            purchaseDTO.setListDate(stagePO.getListDate());
            purchaseDTO.setListDesc(stagePO.getListDesc());
            purchaseDTO.setListMoney(stagePO.getListMoney());
            // 上市价格为空 或者没有配置码
            if (StringUtils.isBlank(priceCode)){
                continue;
            }
            ProtoPricePO pricePO = priceMap.get(priceCode);
            if (null == pricePO){
                continue;
            }
            purchaseDTO.setPriceDesc(pricePO.getRemark());
            List<ProtoPriceDetPO> priceDetList = priceDetMap.get(pricePO.getId());
            ProtoPriceDetPO detPO = getProtoPriceDetPO(priceDetList, stagePO.getListDate());
            if (null != detPO){
                purchaseDTO.setDisposalBasis(detPO.getRemark());
                BigDecimal priceStage = detPO.getPriceStage();
                if (Objects.nonNull(purchaseDTO.getListMoney())){
                    if (PriceStageEnum.RATIO_PRICE.getType().equals(detPO.getCurStage())){
                        purchaseDTO.setReferenceDamages(detPO.getPriceStage().multiply(purchaseDTO.getListMoney()).setScale(2, RoundingMode.HALF_UP));
                    } else if (PriceStageEnum.FIX_PRICE.getType().equals(detPO.getCurStage())){
                        purchaseDTO.setReferenceDamages(priceStage);
                    }
                } else if (Objects.nonNull(stagePO.getMarketPrice())) {
                    if (PriceStageEnum.RATIO_PRICE.getType().equals(detPO.getCurStage())){
                        purchaseDTO.setReferenceDamages(detPO.getPriceStage().multiply(stagePO.getMarketPrice()).setScale(2, RoundingMode.HALF_UP));
                    } else if (PriceStageEnum.FIX_PRICE.getType().equals(detPO.getCurStage())){
                        purchaseDTO.setReferenceDamages(priceStage);
                    }
                }
            }
        }
        return purchaseList;
    }

    /**
     * 获取定价规则明细
     * @param priceDetList
     * @param listDate
     * @return
     */
    private ProtoPriceDetPO getProtoPriceDetPO(List<ProtoPriceDetPO> priceDetList, Date listDate){
        if (null == listDate || CollectionUtils.isEmpty(priceDetList)){
            return null;
        }
        int month = DateUtil.monthDiff(new Date(), listDate);
        for (ProtoPriceDetPO detPO : priceDetList){
            if (month == detPO.getMarketMonth().intValue()){
                return detPO;
            }
        }
        return null;
    }
}
