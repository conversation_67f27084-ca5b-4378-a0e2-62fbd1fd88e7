package com.mi.oa.asset.mobile.application.converter;

import com.mi.oa.asset.mobile.application.dto.tran.TranDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * 工程机转移前端页面显示字段转换
 *
 * <AUTHOR>
 * @date 2022/4/7 15:58
 */
@Mapper(componentModel = "spring")
public interface TranVOConverter {
    /**
     * ProtoTranPO 转化为 TranDTO
     * @param protoTranPO
     * @return
     */
    @Mappings({
            @Mapping(target = "proto_tran__tran_id", source = "tranId"),
            @Mapping(target = "proto_tran__tran_code", source = "tranCode"),
            @Mapping(target = "proto_tran__auditing", source = "auditing"),
            @Mapping(target = "proto_tran__user_name", source = "userName"),
            @Mapping(target = "proto_tran__user_code", source = "userCode"),
            @Mapping(target = "proto_tran__emp_code", source = "empCode"),
            @Mapping(target = "proto_tran__acc_user_name", source = "accUserName"),
            @Mapping(target = "proto_tran__acc_user_code", source = "accUserCode"),
            @Mapping(target = "proto_tran__acc_emp_code", source = "accEmpCode"),
            @Mapping(target = "proto_tran__apply_date", source = "applyDate", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "proto_tran__dept_name", source = "deptName"),
            @Mapping(target = "proto_tran__dept_code", source = "deptCode"),
            @Mapping(target = "proto_tran__in_dept_name", source = "inDeptName"),
            @Mapping(target = "proto_tran__in_dept_code", source = "inDeptCode"),
            @Mapping(target = "proto_tran__add_userid", source = "addUserid"),
            @Mapping(target = "proto_tran__add_date", source = "addDate", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "proto_tran__modify_userid", source = "modifyUserid"),
            @Mapping(target = "proto_tran__modify_date", source = "modifyDate", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "proto_tran__tenant_id", source = "tenantId"),
            @Mapping(target = "proto_tran__remark", source = "remark"),
            @Mapping(target = "proto_tran__business_key", source = "businessKey"),
            @Mapping(target = "proto_tran__apply_user_type", source = "applyUserType"),
            @Mapping(target = "proto_tran__acc_center_code", source = "accCenterCode"),
            @Mapping(target = "proto_tran__center_code", source = "centerCode"),
            @Mapping(target = "proto_tran__out_comp_dept_code", source = "outCompDeptCode"),
            @Mapping(target = "proto_tran__in_comp_dept_code", source = "inCompDeptCode"),
            @Mapping(target = "proto_tran__make_cn", source = "makeCn"),
            @Mapping(target = "proto_tran__tran_type", source = "tranType"),
            @Mapping(target = "proto_tran__asset_newold", source = "assetNewold"),
            @Mapping(target = "proto_tran__sap_ret_type", source = "sapRetType"),
            @Mapping(target = "proto_tran__batch_id", source = "batchId"),
            @Mapping(target = "proto_tran__confirm_status", source = "confirmStatus")
    })
    public TranDTO tranPOtoDTO(ProtoTranPO protoTranPO);

    public List<TranDTO> tranPOtoDTOs(List<ProtoTranPO> protoTranPO);
}
