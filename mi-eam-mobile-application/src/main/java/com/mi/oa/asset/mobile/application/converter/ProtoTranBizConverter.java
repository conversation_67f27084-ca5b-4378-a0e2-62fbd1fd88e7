package com.mi.oa.asset.mobile.application.converter;

import com.mi.oa.asset.mobile.application.dto.bpm.ProtoTranBizDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranPO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @date 2022/10/31 19:23
 */

@Mapper(componentModel = "spring")
public interface ProtoTranBizConverter {
    ProtoTranBizDTO protoTranBizPOToDTO(ProtoTranPO po);
}
