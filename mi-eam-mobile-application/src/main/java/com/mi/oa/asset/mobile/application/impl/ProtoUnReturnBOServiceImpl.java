package com.mi.oa.asset.mobile.application.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.mi.oa.asset.mobile.application.dto.message.LarkDTO;
import com.mi.oa.asset.mobile.application.service.MessageService;
import com.mi.oa.asset.mobile.application.service.ProtoUnReturnBOService;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @date 2022/2/25 14:47
 */
@Slf4j
@Service
public class ProtoUnReturnBOServiceImpl implements ProtoUnReturnBOService {
    @Autowired
    private ProtoService protoService;
    @Autowired
    private ProtoUnReturnService unReturnService;
    @Autowired
    private ProtoUnReturnDetService protoUnReturnDetService;

    @Autowired
    private MailTemplateService mailTemplateService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private SysVarService sysVarService;

    @Autowired
    private SysAttachService sysAttachService;
    
    @Autowired
    private SysProxyService sysProxyService;

    @Autowired
    private ProtoOrderPayService protoOrderPayService;

    @Autowired
    private ProtoOrderPayDetService protoOrderPayDetService;

    @Autowired
    private ProtoOrderService protoOrderService;

    private final String PROTO_ORDER_PAY = "proto_order_pay";

    @Override
    public String unReturnScanQry(String scanResult) {
        // 解析扫码结果
        Map<String, String> scanResultMap = protoService.tranScanResult(scanResult);
        String deviceCode = scanResultMap.get("device_code");
        List<ProtoUnReturnMatPO> detList =  protoUnReturnDetService.findListByParam(ProtoUnReturnMatPO.builder().deviceCode(deviceCode).build());
        return detList.stream().map(ProtoUnReturnMatPO::getUnreturnId).collect(Collectors.joining(","));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletedFile(String dataId, String attachPath, String tableName) {
        // 删除内购执行附件
        if (PROTO_ORDER_PAY.equals(tableName)){
            ProtoOrderPayPO orderPayPO = protoOrderPayService.getById(dataId);
            if (null == orderPayPO){
                return;
            }
            ProtoOrderPO orderPO = protoOrderService.getBySalesOrder(orderPayPO.getOrderCode());
            if (null == orderPO){
                return;
            }
            List<SysAttachPO> list = sysAttachService.getByTableDataId("proto_order", orderPO.getId().toString());
            List<String> attachIds = list.stream().map(SysAttachPO::getAttachId).collect(Collectors.toList());
            sysAttachService.removeByIds(attachIds);
            return;
        }
        // 删除附件
        ProtoUnReturnPO unReturnPO = unReturnService.getById(dataId);
        if (null != unReturnPO){
            LambdaUpdateWrapper<ProtoUnReturnPO> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(ProtoUnReturnPO::getUnreturnId, dataId);
            if (StringUtils.contains(unReturnPO.getPayVoucher1(), attachPath)){
                wrapper.set(ProtoUnReturnPO::getPayVoucher1, null);
                unReturnService.update(wrapper);
            } else if (StringUtils.contains(unReturnPO.getPayVoucher2(), attachPath)){
                wrapper.set(ProtoUnReturnPO::getPayVoucher2, null);
                unReturnService.update(wrapper);
            } else if (StringUtils.contains(unReturnPO.getPayVoucher3(), attachPath)){
                wrapper.set(ProtoUnReturnPO::getPayVoucher3, null);
                unReturnService.update(wrapper);
            }
        }
        this.deleteAttach(dataId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAttach(String dataId) {
        ProtoUnReturnPO unReturnPO = unReturnService.getById(dataId);
        if (null == unReturnPO){
            return;
        }
        String unreturnCode = unReturnPO.getUnreturnCode();
        ProtoOrderPayPO orderPayPO = protoOrderPayService.getByOrderCode(unreturnCode);
        if (null == orderPayPO){
            return;
        }
        List<SysAttachPO> list = sysAttachService.getByTableDataId(PROTO_ORDER_PAY, orderPayPO.getId().toString());
        List<String> attachIds = list.stream().map(SysAttachPO::getAttachId).collect(Collectors.toList());
        sysAttachService.removeByIds(attachIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadFile(List<SysAttachPO> attachList) {
        if (CollectionUtils.isEmpty(attachList)){
            return;
        }
        // 线下支付订单的附件拷贝到内购执行
        if ("proto_pay_off_unconfirm".equals(attachList.get(0).getFunId())){
            protoOrderPayService.copyAttach(attachList);
            return;
        }
        // 无法归还的附件拷贝到线下支付清单
        List<String> dataIds = attachList.stream().map(SysAttachPO::getDataId).collect(Collectors.toList());
        List<ProtoUnReturnPO> unreturnList = unReturnService.listByIds(dataIds);
        Map<String, ProtoUnReturnPO> unreturnMap = unreturnList.stream().collect(Collectors.toMap(ProtoUnReturnPO::getUnreturnId, Function.identity()));

        for (SysAttachPO sysAttachDTO : attachList){
            String dataId = sysAttachDTO.getDataId();
            ProtoUnReturnPO unReturnPO = unreturnMap.get(sysAttachDTO.getDataId());
            if (null != unReturnPO){
                LambdaUpdateWrapper<ProtoUnReturnPO> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(ProtoUnReturnPO::getUnreturnId, dataId);

                if (StringUtils.isBlank(unReturnPO.getPayVoucher1())){
                    wrapper.set(ProtoUnReturnPO::getPayVoucher1, sysAttachDTO.getAttachPath());
                    unReturnService.update(wrapper);
                    sysAttachService.updateAttachField("pay_voucher1", CommonConstant.PROTO_UNRETURN, dataId, sysAttachDTO.getAttachPath());
                } else if (StringUtils.isBlank(unReturnPO.getPayVoucher2())){
                    wrapper.set(ProtoUnReturnPO::getPayVoucher2, sysAttachDTO.getAttachPath());
                    unReturnService.update(wrapper);
                    sysAttachService.updateAttachField("pay_voucher2", CommonConstant.PROTO_UNRETURN, dataId, sysAttachDTO.getAttachPath());
                }else if (StringUtils.isBlank(unReturnPO.getPayVoucher3())){
                    wrapper.set(ProtoUnReturnPO::getPayVoucher3, sysAttachDTO.getAttachPath());
                    unReturnService.update(wrapper);
                    sysAttachService.updateAttachField("pay_voucher3", CommonConstant.PROTO_UNRETURN, dataId, sysAttachDTO.getAttachPath());
                }
            }
        }
        // copy附件
        this.copyAttach(attachList, unreturnList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyAttach(List<SysAttachPO> attachList, List<ProtoUnReturnPO> unreturnList) {
        List<String> unturnCodeList = unreturnList.stream().map(ProtoUnReturnPO::getUnreturnCode).collect(Collectors.toList());
        List<ProtoOrderPayPO> orderPayList = protoOrderPayService.listUnturnCodeList(unturnCodeList);
        Map<String, ProtoUnReturnPO> unturnMap = unreturnList.stream().collect(Collectors.toMap(ProtoUnReturnPO::getUnreturnId, Function.identity()));
        Map<String, ProtoOrderPayPO> orderPayMap = orderPayList.stream().collect(Collectors.toMap(ProtoOrderPayPO::getOrderCode, Function.identity()));

        for (SysAttachPO attachDTO : attachList){
            String dataId = attachDTO.getDataId();
            ProtoUnReturnPO returnPO = unturnMap.get(dataId);
            if (null == returnPO){
                continue;
            }
            ProtoOrderPayPO orderPayPO = orderPayMap.get(returnPO.getUnreturnCode());
            if (null == orderPayPO){
                continue;
            }
            attachDTO.setAttachId(null);
            attachDTO.setDataId(orderPayPO.getId().toString());
            attachDTO.setFunId("proto_pay_off_confirm");
            attachDTO.setTableName(PROTO_ORDER_PAY);
        }
        List<SysAttachPO> list = attachList.stream().filter(o -> PROTO_ORDER_PAY.equals(o.getTableName())).collect(Collectors.toList());
        sysAttachService.saveBatch(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyAttach(List<SysAttachPO> attachList) {
        if (CollectionUtils.isEmpty(attachList)){
            return;
        }
        List<String> dataIds = attachList.stream().map(SysAttachPO::getDataId).collect(Collectors.toList());
        List<ProtoUnReturnPO> unreturnList = unReturnService.listByIds(dataIds);
        this.copyAttach(attachList, unreturnList);
    }

    @Override
    public void unreturnSubmit(String unreturnCode) {
        ProtoOrderPayPO orderPayPO = protoOrderPayService.getByOrderCode(unreturnCode);
        ProtoUnReturnPO unReturnPO = unReturnService.getByUnreturnCode(unreturnCode);
        log.info("unreturnSubmit_orderPayPO:{},unReturnPO:{}", orderPayPO, unReturnPO);
        if (null == orderPayPO || null == unReturnPO || UnReturnTaskStatusEnum.COMPLETE.getType().equals(orderPayPO.getTaskStatus())){
            return;
        }
        orderPayPO.setTaskStatus(UnReturnTaskStatusEnum.COMPLETE.getType());
        protoOrderPayService.updateById(orderPayPO);

        /**
         * 给申请人飞书提醒
         */
        String url = sysVarService.findValByVarCode(CommonConstant.EAM_URL)
                + "/page.html?pagetype=form&nodeid=proto_unreturn&keyid="+unReturnPO.getUnreturnId();
        Map<String, String> valueMap = new HashMap<>(4);
        valueMap.put("userInfo", orderPayPO.getUserName()+orderPayPO.getUserCode());
        valueMap.put("unreturnCode", orderPayPO.getOrderCode());
        String varCode = "proto.dis.submit.user_"+unReturnPO.getMrpType();
        String finance = sysVarService.findValByVarCode(varCode);
        valueMap.put("finance", finance);

        String content = mailTemplateService.getMailContent(CommonConstant.PROTO_DIS_SUBMIT_LARK, valueMap);
        // 加上被代理人
        List<String> sendUserList = sysProxyService.getProxy(Lists.newArrayList(orderPayPO.getUserCode()));
        sendUserList.add(orderPayPO.getUserCode());
        messageService.sendLark(LarkDTO.builder()
                .userNames(sendUserList)
                .content(content)
                .title("【工程机】处置申请待确认")
                .url(url)
                .buttonName("查询处置单据")
                .build());


        /**
         * 给财务飞书提醒
         */
        String financeKey = "proto.dis.finance.confirm_"+orderPayPO.getMrpType();
        String financePerson = sysVarService.findValByVarCode(financeKey);

        url = sysVarService.findValByVarCode(CommonConstant.EAM_URL)
                + "/page.html?pagetype=grid&nodeid=proto_pay_off_unconfirm&keyid="+orderPayPO.getId();

        messageService.sendLark(LarkDTO.builder()
                .userNames(Lists.newArrayList(financePerson.split(";")))
                .content(content)
                .title("【工程机】处置申请待确认")
                .url(url)
                .buttonName("去确认")
                .build());
    }

}
