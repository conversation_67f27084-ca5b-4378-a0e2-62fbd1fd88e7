package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.service.ProtoThirdUserBOService;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoThirdUserPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoThirdUserService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysUserRoleService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysVarService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 第三方人员配置
 *
 * <AUTHOR>
 * @date 2022/1/24 18:49
 */
@Service
@Slf4j
public class ProtoThirdUserBOServiceImpl implements ProtoThirdUserBOService {

    @Autowired
    private ProtoThirdUserService protoThirdUserService;

    @Autowired
    private SysVarService sysVarService;

    @Autowired
    private SysUserRoleService sysUserRoleService;


    /**
     * P账户添加工程机领用人角色
     *
     * @param req
     * @return
     */
    @Override
    public void afterAudit(RequestContext req) {
        //如果是P账户
        String[] keyids = req.getRequestValues("keyid");
        String roleNo = sysVarService.findValByVarCode("sys.login.userrightassignment", "user_login");
        for (String keyid : keyids) {
            ProtoThirdUserPO protoThirdUserPO = protoThirdUserService.findUserById(keyid);
            String userCode = protoThirdUserPO.getUserCode();
            if (userCode != null && userCode.indexOf("p-") == 0) {
                // 添加权限
                sysUserRoleService.addSysUserRole(roleNo, userCode);
            }
        }
    }

    /**
     * 导入文件
     *
     * @param request
     * @return
     */
    /*public String dataImp(RequestContext request){
        //List<String> ids = (List<String>) request.getRequestObject("imp_keyids");
        String[] ids = request.getRequestValues("imp_keyids");
        Map<String, String> userInfo = request.getUserInfo();
        String userId = MapUtil.getValue(userInfo,"user_id");
        if (ids.isEmpty()){
            //请填写信息后再导入
            //setMessage(JsMessage.getValue("proto_scrap.ht_text004"));
            //return _returnFaild;
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR);
        }
        //ImpUtil iu = new ImpUtil();
        String idStr = inStrSql(ids, " or user_id");
        //查找用户不存在的数据
        String strSql = " and user_name is null";
        List<Map<String, String>> noUserCodeList = getData(idStr,strSql);

        if(!noUserCodeList.isEmpty()){
            String userCodes = noUserCodeList.stream().map(map -> MapUtil.getValue(map, "user_code")).collect(Collectors.joining(","));
            //以下账号不存在，请检查！
            setMessage(JsMessage.getValue("proto_third_user.ht_text002")+"<br/>"+
                    JsMessage.getValue("proto_third_user.ht_text001",userCodes));
            return _returnFaild;
        }
        strSql = " and provider_code is not null and provider_name is null";
        List<Map<String, String>> noProviderCodeList = getData(idStr,strSql);
        if(!noProviderCodeList.isEmpty()){
            String providerCodes = noProviderCodeList.stream().map(map -> MapUtil.getValue(map,"provider_code")).collect(Collectors.joining(","));
            //以下供应商编码不存在，请检查！
            setMessage(JsMessage.getValue("proto_third_user.ht_text003")+"<br/>"+
                    JsMessage.getValue("proto_third_user.ht_text001",providerCodes));
            return _returnFaild;
        }
        strSql = " group by user_code having count(*) > 1";
        List<Map<String, String>> impRepeatList = getData(idStr,strSql);
        if(!impRepeatList.isEmpty()){
            String userCodes = impRepeatList.stream().map(map -> MapUtil.getValue(map,"user_code")).collect(Collectors.joining(","));
            //以下账号在导入文件内重复出现，请检查！
            setMessage(JsMessage.getValue("proto_third_user.ht_text004")+"<br/>"+
                    JsMessage.getValue("proto_third_user.ht_text001",userCodes));
            return _returnFaild;
        }
        List<Map<String, String>> repeatList = getRepeat();
        if(!repeatList.isEmpty()){
            String userCodes = repeatList.stream().map(map -> MapUtil.getValue(map,"user_code")).collect(Collectors.joining(","));
            //以下账号在第三方配置表里已经存在，请检查！
            setMessage(JsMessage.getValue("proto_third_user.ht_text005")+"<br/>"+
                    JsMessage.getValue("proto_third_user.ht_text001",userCodes));
            return _returnFaild;
        }
        return _returnSuccess;
    }*/

    /**
     * TODO 获取导入数据
     * @param idStr 主键集合
     * @param strSql 带拼接的SQL条件
     * @return
     */
    /*private List<ProtoThirdUserPO> getData(String idStr,String strSql){
        String sql = "select user_id,user_code,center_code,center_name,provider_code,email from proto_third_user where (user_id in (" + idStr + ")) " + strSql;
        //DaoParam param = _dao.createParam(sql);
        //return _dao.query(param);
        return protoThirdUserService.getThirdUserList(sql);
    }*/

    /**
     * TODO 检查第三方配置表是否出现重复人员记录
     * @return
     */
    /*private List<Map<String, String>> getRepeat(){
        String sql = "select user_code from proto_third_user group by user_code having count(*) > 1";
        DaoParam param = _dao.createParam(sql);
        return _dao.query(param);
    }*/

    /*public String inStrSql(List<String> lsImpKeys,String rel){
        System.out.println("inStrSql被调用了----------------");
        StringBuffer inStr = new StringBuffer("");
        for (int i = 0; i < lsImpKeys.size(); i++) {

            if (i == (lsImpKeys.size() - 1)) {
                inStr.append("'"+lsImpKeys.get(i)+"'"); //SQL拼装，最后一条不加“,”。
            }else if((i%999)==0 && i>0){
                inStr.append("'"+lsImpKeys.get(i)+"'").append(") "+rel+" in ("); //解决ORA-01795问题
            }else{
                inStr.append("'"+lsImpKeys.get(i)+"'").append(",");
            }
        }
        return inStr.toString();
    }*/


}
