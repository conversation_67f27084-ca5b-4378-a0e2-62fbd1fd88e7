package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.service.SysUserDataBOService;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysUserDataService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysUserRoleService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/6
 */
@Service
public class SysUserDataBOServiceImpl implements SysUserDataBOService {

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired
    private SysUserDataService sysUserDataService;

    @Override
    public void addInterfaceRole(String userCode, String deptCode, String mrpType) {
        if (StringUtils.isBlank(userCode) || StringUtils.isBlank(deptCode)) {
            return;
        }
        // 添加工程机接口人角色
        sysUserRoleService.addSysUserRole(CommonConstant.INTERFACE_ROLE + "_" + mrpType, userCode);
        // 添加数据权限
        sysUserDataService.addDataAuthority(userCode, CommonConstant.DEPT_CODE_FIELD, deptCode);
    }

    @Override
    public void deleteInterfaceRole(String userCode, String deptCode) {
        if (StringUtils.isBlank(userCode) || StringUtils.isBlank(deptCode)) {
            return;
        }
        sysUserDataService.deleteDataAuthority(userCode, CommonConstant.DEPT_CODE_FIELD, deptCode);
    }
}
