package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.dto.statusnotify.ProtoListDTO;
import com.mi.oa.asset.mobile.application.dto.statusnotify.ProtoListDetDTO;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.ProtoBackBOService;
import com.mi.oa.asset.mobile.application.service.ProtoBackConfirmBOService;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.dto.BackConfirmDto;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.asset.mobile.infra.service.ProtoListStatusNotifyService;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/20 15:12
 */
@Slf4j
@Service
public class ProtoBackConfirmBOServiceImpl implements ProtoBackConfirmBOService {
    @Autowired
    private ProtoService protoService;
    @Autowired
    private ProtoInService protoInService;
    @Autowired
    private ProtoInDetService protoInDetService;
    @Autowired
    ProtoBackBOService protoBackBOService;
    @Autowired
    private ProtoCardService protoCardService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private ProtoListStatusNotifyService protoListStatusNotifyService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String scanConfirm(String scanResult, UserInfo userInfo) {
        // 获取设备编号SN
        Map<String, String> scanResultMap = protoService.tranScanResult(scanResult);
        String deviceCode = scanResultMap.get("device_code");

        // 查询存在的待确认单据
        List<BackConfirmDto> backConfirmList = protoInService.getbackConfirmByDeviceCode(deviceCode);
        if (CollectionUtils.isEmpty(backConfirmList)) {
            log.error("扫描的SN号【{}】未找到对应的归还确认单！", deviceCode);
            // 扫描的SN号【{0}】未找到对应的归还确认单！ JsMessage.getValue("proto_back_confirm.ht_text006", scanResult
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_CONFIRM_006, deviceCode);
        }

        // 如果有的话就取第一条单据，并判断状态
        BackConfirmDto backConfirm = backConfirmList.get(0);
        String inId = backConfirm.getInId();
        String inStatus = backConfirm.getInStatus();
        if (!InTypeEnum.SECOND_HAND_DELIVERY.getType().equals(inStatus)) {
            return "";
        }

        // 更新确认人
        try {
            protoInService.updateBackConfirmToUser(inId, userInfo.getUserName(), userInfo.getUserCode(), userInfo.getEmpCode());
        } catch (Exception e) {
            log.error("updateBackConfirmToUser error: {}", e.getMessage());
            //  JsMessage.getValue("proto_back_confirm.ht_text007")
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_CONFIRM_007);
        }

        String is_back = backConfirm.getIsBack();
        if (ProtoInDetIsBackEnum.UNCONFIRMED.getCode().equals(is_back)) {
            // 更新明细归还确认中状态
            log.info("正在更新明细归还确认中状态...");

            String detId = backConfirm.getDetId();
            try {
                protoInDetService.updateById(ProtoInDetPO.builder().detId(detId).isBack(ProtoInDetIsBackEnum.UNCONFIRMED.getCode()).modifyDate(new Date()).build());
            } catch (Exception e) {
                log.error("更新明细是否良品失败！");
                // 更新明细是否良品失败！ JsMessage.getValue("proto_back_confirm.ht_text005")
                throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_CONFIRM_005);
            }
        }

        return inId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String audit(String[] keyIds, UserInfo userInfo, RequestContext request){
        commonService.commonAudit(request);
        return this.afterAudit(keyIds,userInfo);
    }

    // TODO 更新台账操作存在优化空间
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String afterAudit(String[] keyIds, UserInfo userInfo) {

        for (String keyId : keyIds) {
            // 更新确认人
            protoInService.updateById(ProtoInPO.builder().inId(keyId).userName(userInfo.getUserName())
                    .userCode(userInfo.getUserCode()).empCode(userInfo.getEmpCode()).inDate(new Date()).build());
            ProtoInPO protoInMap = protoInService.getById(keyId);
            String inStatus = protoInMap.getInStatus();
            if (!CardStateEnum.RETURN_CONFIRMATION_IN_PROGRESS.getKey().equals(inStatus)) {
                //  只允许确认归还“归还中”状态的单据！  JsMessage.getValue("proto_back.ht_text003")
                throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_003);
            }

            // 检测明细是否有未确认的
            if (!checkDetIsBack(keyId)) {
                // 存在未确认归还的明细，请先进行确认！  JsMessage.getValue("proto_back.ht_text012")
                throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_012);
            }

            // 检测明细是否有默认归还状态
            if (!checkDetIsComplete(keyId)) {
                //  存在默认归还状态的明细，请您重新确认！  JsMessage.getValue("proto_back.ht_text022")
                throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_022);
            }

            // 更新归还状态
            protoBackBOService.updateInStatusAndProtoCard(keyId, InStatusEnum.DUMPED_SAP.getState(), null, CardStateEnum.IN_LIBRARY.getKey());

            // 更新归还单操作日期
            try {
                protoInService.updateById(ProtoInPO.builder().inId(keyId).inDate(new Date()).build());
            } catch (Exception e) {
                //  更新操作时间失败！ JsMessage.getValue("proto_back.ht_text013")
                throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_013);
            }

            // 反馈工程机台账信息 合并一次更新
            //第一次将是否良品为非无法归还的全部更改
            //第二次将是否良品为无法归还的全部更改
            try {
                feekBackProtoCard(protoInMap, CardStateEnum.IN_LIBRARY.getKey());
            } catch (Exception e) {
                //  更新工程机台账失败！  JsMessage.getValue("proto_back.ht_text011")
                throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_011);
            }

            // 反馈明细为已归还
            try {
                protoInDetService.updateProtoInDetToIsBack(keyId, ProtoInDetIsBackEnum.CONFIRMED, null);
            } catch (Exception e) {
                //  更新明细是否已归还信息失败！ JsMessage.getValue("proto_back.ht_text014")
                throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_014);
            }
            //如果存在明细记录中是否良品字段为无法归还，则反馈明细为待归还
            try {
                protoInDetService.updateProtoInDetToIsBack(keyId, ProtoInDetIsBackEnum.UNCONFIRMED, CommonConstant.ProtoCardIsComplete.TEMPORARY_GOOD_PRODUCT);
            } catch (Exception e) {
                // 更新明细是否已归还信息失败！ JsMessage.getValue("proto_back.ht_text014")
                throw new BizException(ApplicationErrorCodeEnum.PROTO_BACK_014);
            }

            //工程机确认归还发送消息通知
            confirmBackNotify(keyId);
        }

        return CommonConstant.TRUE;
    }


    private void feekBackProtoCard(ProtoInPO protoInMap, String useState) {
        List<ProtoInDetPO> detList = protoInDetService.findIsComplete(protoInMap.getInId());
        List<ProtoCardPO> cardParamList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(detList)) {
            detList.forEach(det -> {
                ProtoCardPO cardPO;
                if (CommonConstant.ProtoCardIsComplete.RETURN_NOT_ALLOWED.getType().equals(det.getIsComplete())) {
                    cardPO = ProtoCardPO.builder().useState(CardStateEnum.TO_BE_RETURNED.getKey()).lastDate(new Date()).build();
                } else {
                    cardPO = ProtoCardPO.builder().deviceCode(det.getDeviceCode()).useState(useState)
                            .isComplete(det.getIsComplete()).assetNewold(CommonConstant.ProtoCardAssetNewOld.SECONDHAND.getType())
                            .userName("").userCode("").empCode("").deptCode("").longDeptName("").houseCode(protoInMap.getHouseCode())
                            .houseName(protoInMap.getHouseName()).houseId(protoInMap.getHouseId()).lastDate(new Date()).build();
                }
                cardParamList.add(cardPO);
            });
            protoCardService.updateByDeviceCode(cardParamList);
        }
    }

    /**
     * 检查明细是否已归还
     *
     * @param inId
     * @return
     */
    private boolean checkDetIsBack(String inId) {
        return protoInDetService.countDet(inId, ProtoInDetIsBackEnum.UNCONFIRMED, null) <= 0;
    }

    /**
     * 检测明细是否有默认归还状态
     *
     * @param inId
     * @return
     */
    private boolean checkDetIsComplete(String inId) {
        return protoInDetService.countDet(inId, null, CommonConstant.ProtoCardIsComplete.DEFAULT) <= 0;
    }

    /**
     * 工程机确认归还发送消息通知
     */
    public void confirmBackNotify(String keyId) {
        String tag = "proto_back";
        ProtoInPO protoInPo = protoInService.getById(keyId);
        //归还单号
        String inCode = protoInPo.getInCode();
        //申请人
        String applyUserCode = protoInPo.getApplyUserCode();
        //确认归还时间
        String inDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(protoInPo.getInDate());

        //不推不允许归还的数据
        List<ProtoInDetPO> protoInDetList = getProtoInDetListByFeignKey(keyId);
        if (!CollectionUtils.isEmpty(protoInDetList)) {
            //明细信息
            List<ProtoListDetDTO> detList = getDets(keyId);
            ProtoListDTO protoListDTO = ProtoListDTO.builder()
                    .inCode(inCode)
                    .applyUserCode(applyUserCode)
                    .inDate(inDate)
                    .items(detList)
                    .build();
            protoListStatusNotifyService.sendMessage(tag, protoListDTO);
        }
    }

    private List<ProtoListDetDTO> getDets(String fKeyId) {
        //查询子表信息 is_complete!='9'
        List<ProtoInDetPO> protoInDetList = getProtoInDetListByFeignKey(fKeyId);
        List<ProtoListDetDTO> items = new ArrayList<>();
        for (ProtoInDetPO detPO : protoInDetList) {
            ProtoListDetDTO item = ProtoListDetDTO.builder()
                    .skuCode(detPO.getSkuCode())
                    .skuName(detPO.getSkuName())
                    .projectCode(detPO.getProjectCode())
                    .stageName(detPO.getStageName())
                    .deviceType(detPO.getDeviceType())
                    .laserCode(detPO.getLaserCode())
                    .deviceCode(detPO.getDeviceCode())
                    .imei(detPO.getImei())
                    .isComplete(detPO.getIsComplete())
                    .build();
            items.add(item);
        }
        return items;
    }

    public List<ProtoInDetPO> getProtoInDetListByFeignKey(String inId) {
        return protoInDetService.listByInIdAndIsComplete(inId, "9");
    }
}
