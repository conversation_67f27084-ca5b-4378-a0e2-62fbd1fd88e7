package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.dto.bpm.*;
import com.mi.oa.asset.mobile.application.dto.delay.DelayDTO;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.BpmService;
import com.mi.oa.asset.mobile.application.service.ProtoDelayBOService;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.dto.BpmExeLogDTO;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.asset.mobile.utils.DateUtil;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 工程机延期归还
 *
 * <AUTHOR>
 * @date 2022/2/17 19:28
 */
@Service
@Slf4j
public class ProtoDelayBOServiceImpl implements ProtoDelayBOService {

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProtoDelayService protoDelayService;

    @Autowired
    private ProtoDelayMatService protoDelayMatService;

    @Autowired
    private ProtoThirdUserService protoThirdUserService;

    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private FunAllControlService funAllControlService;

    @Autowired
    private BpmExeLogService bpmExeLogService;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private ProtoService protoService;

    public static final String TABLE_PROTO_DELAY = "proto_delay";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String save(DelayDTO delayDTO, RequestContext request) {
        String keyId = commonService.commonSave(request);
        delayDTO.setKeyId(keyId);
        return afterSave(delayDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String afterSave(DelayDTO delayDTO) {
        String keyId = delayDTO.getKeyId();
        ProtoDelayPO protoDelayPO = protoDelayService.findByDelayId(keyId);
        if (ObjectUtils.isEmpty(protoDelayPO)) {
            throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR, RedisUniqueKeyEnum.PROTO_DELAY.getTableName(), keyId);
        }
        String applyUserCode = protoDelayPO.getApplyUserCode();
        String applyUserType = "";
        ProtoThirdUserPO protoThirdUserPO = protoThirdUserService.findUserInfo(applyUserCode);
        if (ObjectUtils.isEmpty(protoThirdUserPO)) {
            if (applyUserCode.startsWith("p-")) {
                //p账号用户必须在第三方人员配置表里维护才能选择
                throw new BizException(ApplicationErrorCodeEnum.PROTO_DELAY_001);
            } else {
                applyUserType = CommonConstant.ApplyUserTypeEnum.INNER.getValue();
            }
        } else {
            if (applyUserCode.startsWith("p-")) {
                applyUserType = CommonConstant.ApplyUserTypeEnum.ODM.getValue();
            } else {
                applyUserType = CommonConstant.ApplyUserTypeEnum.INNER.getValue();
            }
        }
        //更新领用方类型
        protoDelayService.updateApplyUserType(keyId, applyUserType);
        return CommonConstant.TRUE;
    }

    /**
     * 提交前校验
     *
     * @param keyIds
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String audit(String[] keyIds, RequestContext request) {
        String rs = this.beforeAudit(keyIds);
        commonService.commonAudit(request);
        this.pushBpm(keyIds);
        return rs;
    }

    /**
     * 提交前校验
     *
     * @param keyIds
     * @return
     */
    public String beforeAudit(String[] keyIds) {

        for (String keyId : keyIds) {
            ProtoDelayPO protoDelayPO = protoDelayService.findByDelayId(keyId);
            if (ObjectUtils.isEmpty(protoDelayPO)) {
                throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR, RedisUniqueKeyEnum.PROTO_DELAY.getTableName(), keyId);
            }
            String applyUserType = protoDelayPO.getApplyUserType();
            String checkUserCode = protoDelayPO.getCheckUserCode();
            String delayDate = null == protoDelayPO.getDelayDate() ? "":protoDelayPO.getDelayDate().toString();
            //如果是小米员工
            if (CommonConstant.ApplyUserTypeEnum.INNER.getValue().equals(applyUserType)) {
                if (!StringUtils.isEmpty(checkUserCode)) {
                    //领用方类型为小米员工，无需填写确认人！
                    //setMessage(JsMessage.getValue("proto_delay.ht_text002"));
                    throw new BizException(ApplicationErrorCodeEnum.PROTO_DELAY_002);
                }
            } else if (CommonConstant.ApplyUserTypeEnum.ODM.getValue().equals(applyUserType)) {
                if (StringUtils.isEmpty(checkUserCode)) {
                    //领用方类型为ODM+EMS+黑鲨，必须填写确认人！
                    //setMessage(JsMessage.getValue("proto_delay.ht_text003"));
                    throw new BizException(ApplicationErrorCodeEnum.PROTO_DELAY_003);
                }
            }
            List<ProtoDelayMatPO> matList = protoDelayMatService.getListByDelayIdAndEndDate(keyId, delayDate);
            if (!CollectionUtils.isEmpty(matList)) {
                String str = "";
                StringBuilder msgBuilder = new StringBuilder();
                for (ProtoDelayMatPO matMap : matList) {
                    String endDate = "";
                    String deviceCode = matMap.getDeviceCode();
                    if (null != matMap.getEndDate()) {
                         endDate = matMap.getEndDate().toString();
                    }
                    //SN【{0}】当前归还时间为【{1}】,延期归还时间为【{2}】，不符合延期归还要求，请核实后再提交
                    //str += JsMessage.getValue("proto_delay.ht_text001",deviceCode,endDate,delayDate) + "<br/>";
                    msgBuilder.append(String.format("SN【%s】当前归还时间为【%s】,延期归还时间为【%s】，不符合延期归还要求，请核实后再提交 %n", deviceCode, endDate, delayDate));
                }
                //setMessage(str);
                throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, msgBuilder.toString());
            }
        }
        return CommonConstant.TRUE;
    }

    /**
     * 推送BPM
     *
     * @param keyIds
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String pushBpm(String[] keyIds) {
        for (String keyId : keyIds) {
            if (!StringUtils.isBlank(keyId)) {
                pushBpm(keyId);
            }
        }
        return CommonConstant.TRUE;
    }

    public String pushBpm(String keyId) {
        log.info("========正在处理工程机延期归还推送Bpm事件");
        log.info("申请单ID：【{}】", keyId);
        String funId = TABLE_PROTO_DELAY;
        //获取状态的显示值
        List<FunAllControlPO> userTypeList = funAllControlService.getComboValue("apply_user_type");
        //查询主表信息
        ProtoDelayPO protoDelayPO = protoDelayService.findByDelayId(keyId);
        if (ObjectUtils.isEmpty(protoDelayPO)) {
            throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR, RedisUniqueKeyEnum.PROTO_DELAY.getTableName(), keyId);
        }
        String delayCode = protoDelayPO.getDelayCode();
        //申请人
        String applyUserCode = protoDelayPO.getApplyUserCode();
        String applyUserName = protoDelayPO.getApplyUserName();
        String applyUserType = protoDelayPO.getApplyUserType();
        //PM
        String pmUserCode = protoDelayPO.getPmUserCode();
        //查询子表信息
        List<ProtoDelayMatPO> matList = protoDelayMatService.getListByDelayId(keyId);
        log.info("申请单信息：delay_code【{}】，delay_id【{}】，流程发起用户：user_code【{}】", delayCode, keyId, applyUserCode);
        log.info("------------------------------------------------------------------------");
        // 创建Bpm主表
        log.info("创建Bpm工程机延期归还主表...");
        ProtoDelayBizDTO protoDelayBizDTO = ProtoDelayBizDTO.builder()
                .applyUserName(applyUserName + "(" + applyUserCode + ")")
                .deptName(protoDelayPO.getDeptName())
                .applyUserType(displayValue(userTypeList, applyUserType))
                .projectCode(protoDelayPO.getProjectCode())
                .delayDate(protoDelayPO.getDelayDate().toString())
                .remark(protoDelayPO.getRemark())
                .build();
        //创建Bpm物料明细表
        log.info("创建Bpm延期归还物料明细子表...");
        List<ProtoDelayItemBizDTO> bpmItems = getBpmItems(matList);
        protoDelayBizDTO.setItems(bpmItems);
        //指定PM
        VaraibleDTO varaibleDTO = VaraibleDTO
                .builder()
                .projectManager(pmUserCode)
                .build();
        //指定子流程
        MobileProcessBaseBizDTO baseBizDTO = MobileProcessBaseBizDTO.builder()
                .protoDelayBizDTO(protoDelayBizDTO)
                .bizId(delayCode)
                .build();
        if (CommonConstant.ApplyUserTypeEnum.INNER.getValue().equals(applyUserType)) {
            baseBizDTO.setVaraibleDTO(varaibleDTO);
            baseBizDTO.setSubProcess(MobileProcessBaseBizDTO.SubProcessEnum.PROCESS_ONE);
        } else {
            varaibleDTO.setConfirmor(protoDelayPO.getCheckUserCode());
            baseBizDTO.setVaraibleDTO(varaibleDTO);
            baseBizDTO.setSubProcess(MobileProcessBaseBizDTO.SubProcessEnum.PROCESS_TWO);
        }
        // 创建Bpm流程
        log.info("创建Bpm流程...");
        SubmitProcessDTO submitProcessDTO = SubmitProcessDTO.builder().startUser(applyUserCode)
                .mobileProcessEnum(MobileProcessEnum.PROCESS_KEY_MOBILE_DELAY).mobileProcessBaseBizDTO(baseBizDTO).build();
        SubmitResultDTO submitResultDTO = bpmService.submitProcess(submitProcessDTO);
        String businessKey = submitResultDTO.getBusinessKey();
        log.info("创建Bpm流程返回：businessKey【{}】", businessKey);
        if (StringUtils.isEmpty(businessKey)) {
            log.info("抛送BPM失败！");
            // 抛送BPM失败！
            insertlog(funId, "工程机延期归还", keyId, delayCode, applyUserName, false, "抛送BPM失败！", baseBizDTO);
            //setMessage(JsMessage.getValue("proto_bpm.ht_text001"));
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_001);
        }
        // 保存流程信息
        protoDelayService.updProtoDelayStatusByDelayId(keyId, TranAuditEnum.CHECKING.getKey(), DateUtil.getTodaySec(), businessKey);
        insertlog(funId, "工程机延期归还", keyId, delayCode, applyUserName, true, businessKey, baseBizDTO);
        log.info("========处理工程机延期归还推送Bpm事件结束");
        return CommonConstant.TRUE;
    }

    /**
     * 撤回BPM流程具体逻辑
     *
     * @param keyIds
     * @param userInfo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String interruptBpm(String[] keyIds, UserInfo userInfo) {
        for (String keyId : keyIds) {
            if (!StringUtils.isBlank(keyId) && null != userInfo) {
                interruptBpm(keyId, userInfo.getUserCode());
            }
        }
        return CommonConstant.TRUE;
    }

    public String interruptBpm(String keyId, String userCode) {
        log.info("========正在处理工程机延期归还撤回Bpm流程事件");
        log.info("申请单ID：【{}】", keyId);
        // 查询主表信息
        ProtoDelayPO mainData = protoDelayService.findByDelayId(keyId);
        if (ObjectUtils.isEmpty(mainData)) {
            throw new BizException(ApplicationErrorCodeEnum.DATA_NOT_FOUND_ERROR, RedisUniqueKeyEnum.PROTO_DELAY.getTableName(), keyId);
        }
        String delayCode = mainData.getDelayCode();
        //申请人
        String applyUserCode = mainData.getApplyUserCode();
        String auditing = mainData.getAuditing();
        String businessKey = mainData.getBusinessKey();

        if (!StringUtils.equals(applyUserCode, userCode)) {
            //只能撤回本人发起的单据！JsMessage.getValue("proto_bpm.ht_text003")
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_003);
        }
        log.info("申请单信息：delay_code【{}】，delay_id【{}】，操作流程撤回用户：user_code【{}】，auditing【{}】，business_key【{}】", delayCode, keyId, applyUserCode, auditing, businessKey);
        // 检测参数
        if (StringUtils.isEmpty(businessKey)) {
            //setMessage(JsMessage.getValue("proto_bpm.ht_text005"));
            log.info("单据未发起Bpm流程，无需撤回！");
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_005);
        }
        if (!TranAuditEnum.CHECKING.getKey().equals(auditing)) {
            //setMessage(JsMessage.getValue("proto_bpm.ht_text006"));
            log.info("只能撤回审批中的单据！");
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_006);
        }
        // 撤回Bpm流程
        log.info("撤回Bpm流程...");
        InterruptProcessDTO interruptProcessDTO = InterruptProcessDTO.builder().startUser(userCode)
                .businessKey(businessKey).mobileProcessEnum(MobileProcessEnum.PROCESS_KEY_MOBILE_DELAY).build();
        try {
            bpmService.interruptProcess(interruptProcessDTO);
        } catch (Exception e) {
            log.error("撤回Bpm流程失败！");
            throw new BizException(ApplicationErrorCodeEnum.PROTO_BPM_007);
        }
        log.info("更新记录状态为：未提交...");
        protoDelayService.updProtoDelayStatusByDelayId(keyId, TranAuditEnum.NOT_SUBMIT.getKey(), DateUtil.getTodaySec());
        log.info("成功撤回Bpm流程business_key【{}】", businessKey);
        log.info("========处理工程机延期归还撤回Bpm流程事件结束");
        return CommonConstant.TRUE;
    }

    /**
     * bpm回调方法
     *
     * @param bpmCallbackDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bpmCallback(BpmCallbackDTO<MobileProcessBaseBizDTO> bpmCallbackDTO) {
        log.info("========正在处理工程机延期归还Bpm回调事件");

        String funId = TABLE_PROTO_DELAY;
        String orderName = "工程机延期归还-BPM回调";

        if (bpmCallbackDTO == null) {
            log.info("bpmCallbackDTO参数为空");
            insertlog(funId, orderName, null, null, null, false, "bpmCallbackDTO参数为空", bpmCallbackDTO);
            throw new BizException(ApplicationErrorCodeEnum.PARAM_INVALID_ERROR, "bpmCallbackDTO参数为空");
        }

        // 获取business_key
        String businessKey = bpmCallbackDTO.getBusinessKey();
        log.info("获取business_key【{}】", businessKey);
        ProtoDelayPO mainData = protoDelayService.findByBusinessKey(businessKey);
        if (ObjectUtils.isEmpty(mainData)) {
            log.info("业务数据找不到business_key【{}】", businessKey);
            insertlog(funId, orderName, null, null, null, false, "业务数据找不到business_key【" + businessKey + "】", bpmCallbackDTO);
            return true;
        }

        // 获取业务参数
        String keyId = mainData.getDelayId();
        String delayCode = mainData.getDelayCode();
        String applyUserName = mainData.getApplyUserName();
        String operator = bpmCallbackDTO.getOperator();
        log.info("业务参数delay_id【{}】，operator【{}】", keyId, operator);
        //获取审批状态
        BpmCallbackStatusEnum status = bpmCallbackDTO.getStatus();
        log.info("审批状态status【{}】", status);
        // 驳回
        if (BpmCallbackStatusEnum.REJECT.equals(status)) {
            log.info("单据被驳回...");
            // 已驳回  更新记录状态
            protoDelayService.updProtoDelayStatusByDelayId(keyId, TranAuditEnum.CANCELLED.getKey(), DateUtil.getTodaySec());
        }
        // 审批完成
        else if (BpmCallbackStatusEnum.END.equals(status)) {
            String auditing = mainData.getAuditing();
            // 检测流程是否已审批
            if ("3".equals(auditing)) {
                log.info("单据已审批完成，无需再审...");
                return true;
            }
            // 更新记录状态
            // 已审批
            protoDelayService.updProtoDelayStatusByDelayId(keyId, TranAuditEnum.CHECK_OK.getKey(), DateUtil.getTodaySec());
            //更新工程机台账截止归还时间
            updateProtoCardInfo(keyId, mainData.getDelayDate());
        } else {
            log.info("其他状态不处理...");
        }
        // 执行成功
        if (BpmCallbackStatusEnum.REJECT.equals(status) || BpmCallbackStatusEnum.END.equals(status)) {
            insertlog(funId, orderName, keyId, delayCode, applyUserName, true, "工程机延期归还Bpm审批完成！", bpmCallbackDTO);
        }
        log.info("========处理工程机延期归还Bpm回调事件结束");
        return true;
    }

    @Override
    public String delayBackScanQry(String scanResult) {
        // 解析扫码结果
        Map<String, String> scanResultMap = protoService.tranScanResult(scanResult);
        String deviceCode = scanResultMap.get("device_code");
        List<ProtoDelayMatPO> detList = protoDelayMatService.findListByParam(ProtoDelayMatPO.builder().deviceCode(deviceCode).build());
        return detList.stream().map(ProtoDelayMatPO::getDelayId).collect(Collectors.joining(","));
    }

    /**
     * 获取BPM明细表
     *
     * @return
     */
    public List<ProtoDelayItemBizDTO> getBpmItems(List<ProtoDelayMatPO> matList) {
        List<ProtoDelayItemBizDTO> items = new ArrayList<>();
        List<FunAllControlPO> assetNewoldList = funAllControlService.getComboValue("asset_newold");
        int index = 0;
        for (ProtoDelayMatPO matMap : matList) {
            index++;
            String assetNewold = displayValue(assetNewoldList, matMap.getAssetNewold());
            ProtoDelayItemBizDTO item = ProtoDelayItemBizDTO.builder()
                    .number(String.valueOf(index))
                    .skuCode(matMap.getSkuCode())
                    .skuName(matMap.getSkuName())
                    .deviceCode(matMap.getDeviceCode())
                    .imei(matMap.getImei())
                    .laserCode(matMap.getLaserCode())
                    .projectCode(matMap.getProjectCode())
                    .stageName(matMap.getStageName())
                    .assetNewold(assetNewold)
                    .endDate(null == matMap.getEndDate() ? "" : matMap.getEndDate().toString())
                    .delayDate(null == matMap.getDelayDate() ? "" : matMap.getDelayDate().toString())
                    .build();
            items.add(item);
        }
        return items;
    }

    private String displayValue(List<FunAllControlPO> comboList, String val) {
        if (CollectionUtils.isEmpty(comboList)) {
            return val;
        } else {
            Optional<FunAllControlPO> res = comboList.stream().filter(t -> StringUtils.equals(val, t.getValueData())).findFirst();
            if (res.isPresent()) {
                return res.get().getDisplayData();
            } else {
                return val;
            }
        }
    }

    /**
     * 插入BPM日志
     *
     * @param funId     功能ID
     * @param orderName 单据名称
     * @param keyId     单据主键
     * @param code      单据号
     * @param userName  执行人
     * @param success   执行代号
     * @param desc      执行结果
     * @param body      执行报文
     * @return
     */
    public void insertlog(String funId, String orderName, String keyId, String code, String userName, boolean success, String desc, Object body) {
        BpmExeLogDTO protoBpmExeLogDTO = BpmExeLogDTO.builder()
                .funId(funId)
                .orderId(keyId)
                .orderCode(code)
                .orderName(orderName)
                .doUser(userName)
                .doDate(DateUtils.getFormatDate(DateUtils.COMMON_PATTERN))
                .exeCode(success)
                .exeDesc(desc)
                .exeBody(body)
                .build();
        bpmExeLogService.save(protoBpmExeLogDTO);
    }

    /**
     * TODO 更新台账截止归还时间
     *
     * @param delayId
     * @param delayDate
     * @return
     */
    public boolean updateProtoCardInfo(String delayId, Date delayDate) {
        List<ProtoDelayMatPO> matList = protoDelayMatService.getListByDelayId(delayId);
        if (!CollectionUtils.isEmpty(matList)) {
            List<String> deviceCodes = matList.stream().map(ProtoDelayMatPO::getDeviceCode).collect(Collectors.toList());
            return protoCardService.updProtoCardDelayStatus(delayDate, deviceCodes);
        }
        return true;
    }

}
