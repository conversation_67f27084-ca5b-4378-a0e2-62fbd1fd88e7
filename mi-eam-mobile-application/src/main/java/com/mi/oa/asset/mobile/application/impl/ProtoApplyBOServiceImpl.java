package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.application.service.ProtoApplyBOService;
import com.mi.oa.asset.mobile.application.service.ProtoInBoService;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.dto.CostCenter;
import com.mi.oa.asset.mobile.infra.dto.sap.SapCustomVO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapPhoneTransferDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyMatPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoApplyMatService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoApplyService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SapService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/6 15:27
 */
@Service
@Slf4j
public class ProtoApplyBOServiceImpl implements ProtoApplyBOService {

    @Autowired
    private ProtoApplyMatService protoApplyMatService;

    @Autowired
    private SapService sapService;

    @Autowired
    private ProtoApplyService protoApplyService;

    @Autowired
    private ProtoInBoService protoInBoService;

    @Autowired
    private ProtoService protoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushSap(ProtoApplyPO applyPO) {
        if (StringUtils.isBlank(applyPO.getMakeCn())){
            return;
        }
        if (SapRetEnum.SUCCESS.getKey().equals(applyPO.getSapRetType())){
            return;
        }
        SapPhoneTransferDTO sapMain = new SapPhoneTransferDTO();
        String applyCode = applyPO.getApplyCode();
        String makeCn = applyPO.getMakeCn();
        sapMain.setBillType(SapBatchIdEnum.RECEIVE.getBillType());
        sapMain.setMidocNum(applyCode);
        sapMain.setDemandDept(applyPO.getPreProjectCode());
        String isOther = applyPO.getIsOther();
        this.setCostCenter(applyPO, makeCn);
        sapMain.setSapFactory(makeCn.substring(0, 4));
        if ("1".equals(isOther)) {
            // 第三方发放
            sapMain.setBusinessType(CommonConstant.SapBusiTypeEnum.MRP_PROVIDE.getType());
            sapMain.setStorageFactoryCode(applyPO.getUserCode());
            sapMain.setCutPaymentSubject(applyPO.getDebitOnwer());
        } else {
            sapMain.setBusinessType(CommonConstant.SapBusiTypeEnum.PROVIDE.getType());
            sapMain.setKostl(applyPO.getCenterCode());
        }
        sapMain.setProject(applyPO.getProjectCode());
        sapMain.setCreateUserName(applyPO.getAddUserid());
        sapMain.setRemark(applyPO.getRemark());
        sapMain.setApplyDept(applyPO.getDeptCode());
        sapMain.setApplyPerson(applyPO.getApplyUserCode());
        sapMain.setUsePerson(applyPO.getUserCode());
        sapMain.setMark("");
        sapMain.setBsart(applyPO.getBsart());
        List<ProtoApplyMatPO> protoApplyDetList = protoApplyMatService.getProtoApplyDetSAPList(applyPO.getApplyId());
        List<SapPhoneTransferDTO.Item> sapItems = new ArrayList<>();
        int midocRow = 10;
        for (ProtoApplyMatPO matPO : protoApplyDetList) {
            String skuCode = matPO.getSkuCode();

            SapPhoneTransferDTO.Item sapItem = new SapPhoneTransferDTO.Item();
            sapItem.setMidocNum(applyCode);
            sapItem.setMidocRow(String.valueOf(midocRow));
            sapItem.setMaterialOne(matPO.getSkuCode());
            sapItem.setQuantityOne(matPO.getApplyNum().intValue()+"");
            sapItems.add(sapItem);
            // 更新明细SAP行号
            protoApplyMatService.updateProtoApplyMatToSapItemRowNo(applyPO.getApplyId(), skuCode, midocRow);
            midocRow += 10;
        }

        sapMain.setItems(sapItems);
        // 推送SAP
        SapCustomVO sapCustomVO = sapService.enginePhoneTransfer(sapMain, "proto_apply", applyPO.getApplyId(), applyCode, "工程机领用申请", applyPO.getUserName(), applyPO.getMrpType());
        // 更新抛送SAP状态
        applyPO.setSapRetType(sapCustomVO.getSuccess() ? SapRetEnum.SUCCESS.getKey() : SapRetEnum.ERROR.getKey());
        protoApplyService.updateById(applyPO);
    }

    @Override
    public void searchAndSetMakeCn(ProtoApplyPO applyPO) {
        String applyCode = applyPO.getApplyCode();
        ProtoAsnOrderPO asnOrderPO = protoInBoService.getSapSuccessAsnOrder(applyCode);
        if (asnOrderPO != null) {
            String makeCn = SapMakeCnEnum.getMakeCn(asnOrderPO.getSapFactory());
            if (StringUtils.isNotBlank(makeCn)) {
                applyPO.setMakeCn(makeCn);
                applyPO.setBsart(asnOrderPO.getBsart());
                protoApplyService.updateById(applyPO);
            }
        }
    }

    /**
     * 设置成本中心
     * @param applyPO
     * @param makeCn
     */
    private void setCostCenter(ProtoApplyPO applyPO, String makeCn){
        String centerCode = applyPO.getCenterCode();
        if (StringUtils.isNotBlank(centerCode)){
            return;
        }
        String applyUserType = applyPO.getApplyUserType();

        CostCenter costCenter = null;
        if(CommonConstant.ApplyUserTypeEnum.ODM.getValue().equals(applyUserType)){
            costCenter = protoService.getSAPCenterCode(applyPO.getCheckUserCode(), CommonConstant.ApplyUserTypeEnum.INNER.getValue(), makeCn);
        }else {
            costCenter = protoService.getSAPCenterCode(applyPO.getUserCode(), applyUserType, makeCn);
        }
        if (null != costCenter){
            applyPO.setCenterCode(costCenter.getCenterCode());
            applyPO.setCompDeptCode(costCenter.getCompDeptCode());
        }
    }
}
