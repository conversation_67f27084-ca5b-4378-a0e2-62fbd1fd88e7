package com.mi.oa.asset.mobile.application.converter;

import com.mi.oa.asset.mobile.application.dto.cardusertab.CardDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * 台账前端页面显示字段转换
 *
 * <AUTHOR>
 * @date 2022/2/23 17:31
 */
@Mapper(componentModel = "spring")
public interface CardVOConverter {
    /**
     * ProtoCardPO 转化为 CardDTO
     * @param rotoCardPO
     * @return
     */
    @Mappings({
            @Mapping(target = "proto_card__mrp_type", source = "mrpType"),
            @Mapping(target = "proto_card__device_id", source = "deviceId"),
            @Mapping(target = "proto_card__sku_code", source = "skuCode"),
            @Mapping(target = "proto_card__sku_name", source = "skuName"),
            @Mapping(target = "proto_card__device_code", source = "deviceCode"),
            @Mapping(target = "proto_card__laser_code", source = "laserCode"),
            @Mapping(target = "proto_card__imei", source = "imei"),
            @Mapping(target = "proto_card__device_type", source = "deviceType"),
            @Mapping(target = "proto_card__apply_date", source = "applyDate", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "proto_card__apply_code", source = "applyCode"),
            @Mapping(target = "proto_card__apply_itemid", source = "applyItemid"),
            @Mapping(target = "proto_card__apply_user_name", source = "applyUserName"),
            @Mapping(target = "proto_card__apply_user_code", source = "applyUserCode"),
            @Mapping(target = "proto_card__apply_emp_code", source = " applyEmpCode"),
            @Mapping(target = "proto_card__project_name", source = "projectName"),
            @Mapping(target = "proto_card__project_code", source = "projectCode"),
            @Mapping(target = "proto_card__stage_name", source = "stageName"),
            @Mapping(target = "proto_card__make_cn", source = "makeCn"),
            @Mapping(target = "proto_card__use_state", source = "useState"),
            @Mapping(target = "proto_card__user_name", source = "userName"),
            @Mapping(target = "proto_card__user_code", source = "userCode"),
            @Mapping(target = "proto_card__emp_code", source = "empCode"),
            @Mapping(target = "proto_card__is_open", source = "isOpen"),
            @Mapping(target = "proto_card__open_user_name", source = "openUserName"),
            @Mapping(target = "proto_card__open_user_code", source = "openUserCode"),
            @Mapping(target = "proto_card__open_emp_code", source = "openEmpCode"),
            @Mapping(target = "proto_card__open_date", source = "openDate", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "proto_card__asset_newold", source = "assetNewold"),
            @Mapping(target = "proto_card__house_name", source = "houseName"),
            @Mapping(target = "proto_card__house_code", source = "houseCode"),
            @Mapping(target = "proto_card__last_apply_date", source = "lastApplyDate", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "proto_card__last_apply_code", source = "lastApplyCode"),
            @Mapping(target = "proto_card__last_apply_itemid", source = "lastApplyItemid"),
            @Mapping(target = "proto_card__last_apply_user_name", source = "lastApplyUserName"),
            @Mapping(target = "proto_card__last_apply_user_code", source = "lastApplyUserCode"),
            @Mapping(target = "proto_card__last_apply_emp_code", source = "lastApplyEmpCode"),
            @Mapping(target = "proto_card__out_date", source = "outDate", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "proto_card__last_date", source = "lastDate", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "proto_card__center_code", source = "centerCode"),
            @Mapping(target = "proto_card__center_name", source = "centerName"),
            @Mapping(target = "proto_card__comp_dept_code", source = "compDeptCode"),
            @Mapping(target = "proto_card__comp_dept_name", source = "compDeptName"),
            @Mapping(target = "proto_card__sap_factory", source = "sapFactory"),
            @Mapping(target = "proto_card__apply_user_type", source = "applyUserType"),
            @Mapping(target = "proto_card__occupy_apply_code", source = "occupyApplyCode"),
            @Mapping(target = "proto_card__is_complete", source = "isComplete"),
            @Mapping(target = "proto_card__in_code", source = "inCode"),
            @Mapping(target = "proto_card__out_code", source = "outCode"),
            @Mapping(target = "proto_card__order_code", source = "orderCode"),
            @Mapping(target = "proto_card__ori_project_code", source = "oriProjectCode"),
            @Mapping(target = "proto_card__ori_stage_name", source = "oriStageName"),
            @Mapping(target = "proto_card__syc_flag", source = "sycFlag"),
            @Mapping(target = "proto_card__new_project_code", source = "newProjectCode"),
            @Mapping(target = "proto_card__new_stage_name", source = "newStageName"),
            @Mapping(target = "proto_card__house_id", source = "houseId"),
            @Mapping(target = "proto_card__old_use_code", source = "oldUseCode"),
            @Mapping(target = "proto_card__old_use_name", source = "oldUseName"),
            @Mapping(target = "proto_card__remark", source = "remark"),
            @Mapping(target = "proto_card__dept_code", source = "deptCode"),
            @Mapping(target = "proto_card__long_dept_name", source = "longDeptName"),
            @Mapping(target = "proto_card__modify_date", source = "modifyDate", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "proto_card__end_date", source = "endDate", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "proto_card__list_date", source = "listDate", dateFormat = "yyyy-MM-dd HH:mm:ss"),
            @Mapping(target = "proto_card__sku_code_type", source = "skuCodeType")
    })
    public CardDTO cardPOtoDTO(ProtoCardPO rotoCardPO);

    public List<CardDTO> cardPOtoDTOs(List<ProtoCardPO> rotoCardPO);
}
