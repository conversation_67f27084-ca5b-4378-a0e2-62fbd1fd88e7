# MVC脚手架概要
依照四层架构剔除domain层之后，提供给简单应用使用的MVC三层架构脚手架

## 关于MVC架构的各层依赖
<p align="center">
   <img width="550" src="./img.png">
</p>

## 项目结构
详细解释：https://xiaomi.f.mioffice.cn/docs/dock4KNHCprNvTAKU7U5EfIFMvh

## 脚手架的使用
https://xiaomi.f.mioffice.cn/docs/dock41qavEGSLxaV9ewZk2ZIAye

## 三层架构说明
- interface      代表MVC三层架构中的表现层
- application    代表MVC三层架构中的业务逻辑层
- infra          代表MVC三层架构中的数据访问层

## 此脚手架坚持的原则
- 分层清楚，做默认规范

##更新说明
20211115支持xml里引入ZonedDateTimeBigIntTypeHandler：时间类型的字段需要参照下面方式进行定义，生成的xml里resMap,插入，更新语句会自动映射到ZonedDateTimeBigIntTypeHandler，
如果需要写自定义sql语句，按照相同原理在xml里加上TypeHandler即可。

## 维护者
罗一帆（<EMAIL>)