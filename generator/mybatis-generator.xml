<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>

    <context id="MysqlTables" targetRuntime="MyBatis3">

        <!-- 格式化XML代码,去除重复的 -->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin" />

        <!-- 自定义的lombok插件 -->
        <plugin type="com.mi.oa.infra.oaucf.mybatis.generator.OAMybatisPlugin">
            <!-- 是否使用mybatis-plus，使用会在PO上添加@tableName 并在mapper接口继承BaseMapper -->
            <property name="useMybatisPlus" value="true"/>
            <!-- 需要添加的lombok的注解 -->
            <property name="data" value="true"/>
            <property name="allArgsConstructor" value="true"/>
            <property name="noArgsConstructor" value="true"/>
            <property name="toString" value="true"/>
        </plugin>

        <!-- 自定义的，旨在创建class时，对注释进行控制 -->
        <commentGenerator type="com.mi.oa.infra.oaucf.mybatis.generator.MybatisCommentGenerator">
            <!--<property name="suppressDate" value="true"/>-->
            <!--<property name="suppressAllComments" value="true" />-->
            <!-- 是否生成注释代时间戳-->
            <property name="suppressDate" value="true"/>
        </commentGenerator>

        <!--数据库链接地址账号密码-->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="***********************************************************************************************************************************************************************************************************************************"
                        userId="info_asset_wn"
                        password="awncFNd8lGPSeAW2-c7tvmPf4tEMnMfp">
        </jdbcConnection>
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!--生成Model类存放位置-->
        <javaModelGenerator targetPackage="com.mi.oa.asset.mobile.infra.repository.mybatis.entity"
                            targetProject="../mi-eam-mobile-common/mi-eam-mobile-common-infra/src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!--生成映射文件存放位置-->
        <sqlMapGenerator targetPackage="mapper" targetProject="../mi-eam-mobile-infra/src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>
        <!--生成Dao类存放位置-->
        <!-- 客户端代码，生成易于使用的针对Model对象和XML配置文件 的代码
                type="ANNOTATEDMAPPER",生成Java Model 和基于注解的Mapper对象
                type="MIXEDMAPPER",生成基于注解的Java Model 和相应的Mapper对象
                type="XMLMAPPER",生成SQLMap XML文件和独立的Mapper接口
        -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper"
                             targetProject="../mi-eam-mobile-infra/src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!--生成对应表及类名-->
        <table tableName="sys_oper_log" domainObjectName="SysOperLogPO" mapperName="SysOperLogMapper"
               enableInsert="false"
               enableSelectByPrimaryKey="true"
               enableUpdateByPrimaryKey="false"
               enableDeleteByPrimaryKey="false"
               enableCountByExample="false"
               enableUpdateByExample="false"
               enableDeleteByExample="false"
               enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>
    </context>
</generatorConfiguration>
