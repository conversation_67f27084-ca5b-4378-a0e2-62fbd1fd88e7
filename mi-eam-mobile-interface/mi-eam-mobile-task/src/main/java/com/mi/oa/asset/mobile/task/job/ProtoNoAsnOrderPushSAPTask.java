package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.FeedBackEnum;
import com.mi.oa.asset.mobile.common.enums.InTypeEnum;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoInSapDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoAsnOrderDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 无ASN单抛送SAPRunner类的Task类
 *
 * <AUTHOR>
 * @version 1.0 2021-11-20
 */
@PlanTask(name = "ProtoNoAsnOrderPushSAPTask", quartzCron = "0 20 0/3 * * ?", description = "收货定时任务")
@Slf4j
public class ProtoNoAsnOrderPushSAPTask implements PlanExecutor {

    @Autowired
    private ProtoInService protoInService;

    @Autowired
    private ProtoInDetService protoInDetService;

    @Autowired
    private ProtoAsnOrderDetService protoAsnOrderDetService;

    @Override
    public void execute() {
        int pageNum = 1;
        while (true) {
            List<ProtoInPO> list = protoInService.getNotPushSAPList(pageNum, CommonConstant.DEFAULT_PAGE_SIZE);
            for (ProtoInPO proto : list){
                if (InTypeEnum.HAVE_ASN_DELIVERY.getType().equals(proto.getInType())){
                    // ASN收货抛SAP
                    try {
                        protoInService.pushSAP(proto.getInId(), proto.getUserName());
                    } catch (Exception e){
                        log.error("proto_push_sap_task info:{}, error", proto, e);
                    }
                    continue;
                }

                if (!FeedBackEnum.HAVE_FEED_BACK.getState().equals(proto.getIsFeekback())){
                    continue;
                }
                if (!InTypeEnum.NOT_ASN_DELIVERY.getType().equals(proto.getInType())){
                    continue;
                }
                //匹配失败的入库单不能推送sap
                List<ProtoInSapDTO> notAsnPushSapList = protoInDetService.getNotAsnPushSap(proto.getInId());
                for (ProtoInSapDTO sapDTO : notAsnPushSapList){
                    try {
                        protoInService.noAsnOrderPushSAP(sapDTO, proto);
                    } catch (Exception e) {
                        log.error("proto_in_push_sap error sapDTO:{}, e:{}", sapDTO, e);
                    }
                }
            }
            if (list.size() < CommonConstant.DEFAULT_PAGE_SIZE){
                break;
            }
            pageNum++;
        }
        // 更新收货单抛SAP状态
        protoAsnOrderDetService.updateSapStatus();
    }


}


