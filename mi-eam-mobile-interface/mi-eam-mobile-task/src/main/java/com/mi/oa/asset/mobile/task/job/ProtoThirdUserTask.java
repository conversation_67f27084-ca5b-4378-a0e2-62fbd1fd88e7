package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoThirdUserService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/2/14
 */
@PlanTask(name = "ProtoThirdUserTask", quartzCron = "0 0 3 * * ?", description = "同步第三人账号有效期")
public class ProtoThirdUserTask implements PlanExecutor {

    @Autowired
    private ProtoThirdUserService protoThirdUserService;

    @Override
    public void execute() {
        protoThirdUserService.synValidDate();
    }
}
