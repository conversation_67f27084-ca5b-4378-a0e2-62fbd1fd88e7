package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.application.service.ProtoCardBOService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/3/2
 */
@Slf4j
@PlanTask(name = "ProtoCardRefreshAllDeptTask", quartzCron = "0 30 6 * * ?", description = "全量更新台账中的部门")
public class ProtoCardRefreshAllDeptTask implements PlanExecutor {

    @Autowired
    private ProtoCardBOService protoCardBOService;

    @Override
    public void execute() {
        protoCardBOService.refreshAllDeptName();
    }
}
