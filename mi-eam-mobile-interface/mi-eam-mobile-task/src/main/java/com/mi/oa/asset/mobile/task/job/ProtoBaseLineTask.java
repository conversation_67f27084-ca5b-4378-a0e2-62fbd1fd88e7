package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoBaselineService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/3/17
 */
@Slf4j
@PlanTask(name = "ProtoBaseLineTask", quartzCron = "0 30 6 * * ?", description = "全量更新基线台账接口人部门信息")
public class ProtoBaseLineTask implements PlanExecutor {

    @Autowired
    private ProtoBaselineService protoBaselineService;

    @Override
    public void execute() {
        protoBaselineService.refreshAllDeptName();
    }
}
