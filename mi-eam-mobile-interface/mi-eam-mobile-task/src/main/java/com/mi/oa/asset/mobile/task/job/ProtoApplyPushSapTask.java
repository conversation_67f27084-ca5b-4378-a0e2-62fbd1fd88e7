package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.application.service.ProtoApplyBOService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoApplyService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/10/24
 */
@Slf4j
@PlanTask(name = "ProtoApplyPushSapTask", quartzCron = "0 28 0/2 * * ?", description = "工程机领用申请抛送SAP")
public class ProtoApplyPushSapTask implements PlanExecutor {

    @Autowired
    private ProtoApplyBOService protoApplyBOServiceService;

    @Autowired
    private ProtoApplyService protoApplyService;

    @Autowired
    private ProtoApplyBOService protoApplyBOService;

    @Override
    public void execute() {
        // 设置样机生产地
        List<ProtoApplyPO> list = protoApplyService.listMackCnIsBlank();
        for (ProtoApplyPO applyPO : list){
            try {
                protoApplyBOService.searchAndSetMakeCn(applyPO);
            } catch (Exception e){
                log.info("ProtoApplyMakeCnTask set makeCn applyInfo:{}", applyPO, e);
            }
        }
        // 抛SAP
        list = protoApplyService.listToPushSap();
        for (ProtoApplyPO applyPO : list){
            try {
                protoApplyBOServiceService.pushSap(applyPO);
            } catch (Exception e){
                log.error("ProtoApplyPushSapTask_push_sap_error applyPO:{}", applyPO, e);
            }
        }
    }
}
