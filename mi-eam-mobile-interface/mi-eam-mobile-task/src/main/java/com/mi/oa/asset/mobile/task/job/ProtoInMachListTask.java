package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 待匹配清单Task类
 *
 * <AUTHOR>
 * @version 1.0 2021-11-20
 */
@PlanTask(name = "ProtoInMachListTask", quartzCron = "0 20 0/2 * * ?", description = "待匹配订单定时任务 每隔一小时执行一次")
@Slf4j
public class ProtoInMachListTask implements PlanExecutor {

    @Autowired
    private ProtoInService protoInService;

    @Autowired
    private ProtoInDetService protoInDetService;

    @Override
    public void execute() {
        Integer pageNum = 1;
        while (true){
            List<ProtoInPO> unMachList = protoInService.getUnMachList(pageNum);
            pageNum++;
            for(ProtoInPO proto : unMachList){
                try {
                    protoInService.dealUnMach(proto);
                } catch (Exception e){
                    log.error("ProtoInMachListTask_dealUnMach_error proto:{}, e:{}", proto, e);
                }

            }
            if (unMachList.size() < CommonConstant.DEFAULT_PAGE_SIZE){
                break;
            }
        }
        // 更新 auditing 状态
        protoInDetService.updateDetAuditing();
    }
}


