package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.application.service.ProtoProjectListBOService;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/14
 */
@PlanTask(name = "ProtoSynProjectTask", quartzCron = "0 0 2 * * ?", description = "从MDM同步项目信息")
public class ProtoSynProjectTask implements PlanExecutor {

    @Autowired
    private ProtoProjectListBOService protoProjectListBOService;

    @Value("${mdm.projectSync:1}")
    private String mddProjectSync;

    @Override
    public void execute() {
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(DateUtils.COMMON_PATTERN);
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.plusDays(-2);
        if ("1".equals(mddProjectSync)) {
            startDate = endDate.plusYears(-2);
        }
        String updateTime = startDate.format(dtf) + "," + endDate.format(dtf);
        protoProjectListBOService.synProjectListByTime(updateTime);
    }
}
