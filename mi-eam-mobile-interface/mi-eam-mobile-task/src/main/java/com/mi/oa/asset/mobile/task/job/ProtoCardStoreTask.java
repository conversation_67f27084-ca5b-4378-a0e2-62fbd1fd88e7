package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.application.service.ProtoReportService;
import com.mi.oa.asset.mobile.common.enums.RedisCachePrefixKeyEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardStoreService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/5/17
 */
@Slf4j
@PlanTask(name = "ProtoCardStoreTask", quartzCron = "0 20 0/1 * * ?", description = "库存查询报表统计")
public class ProtoCardStoreTask implements PlanExecutor {

    @Autowired
    private ProtoReportService protoReportService;

    @Autowired
    private ProtoCardStoreService protoCardStoreService;

    @Override
    public void execute() {
        protoReportService.updateReport(RedisCachePrefixKeyEnum.PROTO_CARD_STORE_LOCK_KEY,
                RedisCachePrefixKeyEnum.PROTO_CARD_STORE_UPDATE_TIME,
                ()->protoCardStoreService.statistics());
    }
}
