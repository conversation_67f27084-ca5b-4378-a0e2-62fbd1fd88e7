package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoOutService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 工程机发放超时自动确认Task类
 *
 * <AUTHOR>
 * @version 1.0 2021-11-24
 */
@PlanTask(name = "ProtoOutTimeOutAutoConfirmTask", quartzCron = "0 55 0/1 * * ?", description = "工程机发放超时自动确认 每隔一小时执行一次")
public class ProtoOutTimeOutAutoConfirmTask implements PlanExecutor {

    @Autowired
    private ProtoOutService protoOutService;

    @Override
    public void execute() {
        protoOutService.timeOutAutoConfirm();
    }
}


