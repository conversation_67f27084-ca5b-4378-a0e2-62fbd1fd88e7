package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.application.service.ProtoReportService;
import com.mi.oa.asset.mobile.common.enums.RedisCachePrefixKeyEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardProjectTotalService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 工程机项目汇总台账Task
 *
 * <AUTHOR>
 * @date 2022/3/24 15:55
 */
@Slf4j
@PlanTask(name = "ProtoCardProjectTotalTask", quartzCron = "0 0 0/1 * * ?", description = "工程机项目汇总台账统计")
public class ProtoCardProjectTotalTask implements PlanExecutor {

    @Autowired
    private ProtoReportService protoReportService;

    @Autowired
    private ProtoCardProjectTotalService protoCardProjectTotalService;

    @Override
    public void execute() {
        protoReportService.updateReport(RedisCachePrefixKeyEnum.PROTO_CARD_PROJECT_LOCK_KEY,
                RedisCachePrefixKeyEnum.PROTO_CARD_PROJECT_UPDATE_TIME,
                ()->protoCardProjectTotalService.updCardProjectTotal());
    }
}
