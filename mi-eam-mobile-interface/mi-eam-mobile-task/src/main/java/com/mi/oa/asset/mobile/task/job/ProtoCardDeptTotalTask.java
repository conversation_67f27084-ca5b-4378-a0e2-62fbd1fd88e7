package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.application.service.ProtoReportService;
import com.mi.oa.asset.mobile.common.enums.RedisCachePrefixKeyEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardDeptTotalService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 工程机部门在用报表总量查询task
 *
 * <AUTHOR>
 * @date 2022/4/1 14:25
 */
@Slf4j
@PlanTask(name = "ProtoCardDeptTotalTask", quartzCron = "0 0 0/1 * * ?", description = "工程机部门在用报表总量查询统计")
public class ProtoCardDeptTotalTask implements PlanExecutor {

    @Autowired
    private ProtoReportService protoReportService;

    @Autowired
    private ProtoCardDeptTotalService protoCardDeptTotalService;

    @Override
    public void execute() {
        protoReportService.updateReport(RedisCachePrefixKeyEnum.PROTO_CARD_DEPT_LOCK_KEY,
                RedisCachePrefixKeyEnum.PROTO_CARD_DEPT_UPDATE_TIME,
                ()->protoCardDeptTotalService.statisticsDept());
    }

}
