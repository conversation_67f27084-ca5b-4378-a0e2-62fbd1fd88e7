package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.application.service.ProtoReportService;
import com.mi.oa.asset.mobile.common.enums.RedisCachePrefixKeyEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardDeptproTotalService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/4/1 14:29
 */
@Slf4j
@PlanTask(name = "ProtoCardDeptproTotalTask", quartzCron = "0 0 0/1 * * ?", description = "工程机部门在用报表项目查询统计")
public class ProtoCardDeptproTotalTask implements PlanExecutor {

    @Autowired
    private ProtoCardDeptproTotalService protoCardDeptproTotalService;

    @Autowired
    private ProtoReportService protoReportService;

    @Override
    public void execute() {
        protoReportService.updateReport(RedisCachePrefixKeyEnum.PROTO_CARD_DEPTPRO_LOCK_KEY,
                RedisCachePrefixKeyEnum.PROTO_CARD_DEPTPRO_UPDATE_TIME,
                ()->protoCardDeptproTotalService.statisticsDeptpro());
    }
}
