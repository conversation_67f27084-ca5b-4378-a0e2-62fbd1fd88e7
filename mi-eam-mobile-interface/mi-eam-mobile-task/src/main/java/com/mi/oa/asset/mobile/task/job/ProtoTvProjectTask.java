package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.application.service.ProtoTvProjectService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Copyright (c) 2022 XiaoMi Inc.All Rights Reserved.
 * Description: your description
 *
 * <AUTHOR>
 * @date 2022/11/2 17:29
 */
@Slf4j
@PlanTask(name = "ProtoTvProjectTask", quartzCron = "0 5 1 * * ? ", description = "工程机电视项目数据同步")
public class ProtoTvProjectTask implements PlanExecutor {

    @Autowired
    private ProtoTvProjectService protoTvProjectService;

    @Override
    public void execute() {
        protoTvProjectService.syncTvProjectInfo();
    }
}
