package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCollectService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import org.springframework.beans.factory.annotation.Autowired;

@PlanTask(name = "ProtoCollectTask", quartzCron = "0 0 3 1/1 * ?", description = "将超过需求下达截止时间的未提交的需求计划收集记录失效 每隔一天执行一次")
public class ProtoCollectTask implements PlanExecutor {

    @Autowired
    private ProtoCollectService protoCollectService;

    @Override
    public void execute() {
        protoCollectService.updateProtoCollectStatus();
    }
}
