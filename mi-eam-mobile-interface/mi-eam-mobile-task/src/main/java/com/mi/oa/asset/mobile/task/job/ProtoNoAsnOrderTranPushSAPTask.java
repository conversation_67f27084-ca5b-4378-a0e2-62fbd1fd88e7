package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.application.dto.tran.TranSapSplitDTO;
import com.mi.oa.asset.mobile.application.service.TranService;
import com.mi.oa.asset.mobile.common.enums.SapRetEnum;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoTranSapDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoTranDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoTranService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/21
 */
// @PlanTask(name = "ProtoNoAsnOrderTranPushSAPTask", quartzCron = "0 15 0/1 * * ?", description = "工程机转移（无ASN单）抛送SAP 每隔一小时执行一次")
@Slf4j
public class ProtoNoAsnOrderTranPushSAPTask implements PlanExecutor {

    @Autowired
    private ProtoTranService protoTranService;

    @Autowired
    private ProtoTranDetService protoTranDetService;

    @Autowired
    private TranService tranService;

    /**
     * TODO 找出所有抛送SAP的转移单，要求如下：
     * 1.转移单主表记录状态为已审批（auditing = '3'）
     * 2.明细表的新旧属性为一手（asset_newold = '1'）
     * 3.明细表的SAP消息类型与SAP_107消息类型任一字段为抛送失败（ifnull(det.sap_ret_type,'E') = 'E' or ifnull(det.is_success,'E') = 'E'）
     * 4.发放单的发放状态为已出库（out_status = '2'），SAP消息类型为处理成功（sap_ret_type = 'S'）
     * 5.由于是整单处理，已处理的单据需要存储到集合，后续单据先检查是否存在于集合中，若不存在则继续处理（减少多余操作）
     *
     */
    @Override
    public void execute() {
        /**
         * 找出所有未抛送SAP的转移单（由于关联了明细表，所以会有重复单，后续程序过滤）
         */
        List<ProtoTranSapDTO> list = protoTranService.getNoAsnOrderTranList();
        List<String> tranIdList = list.stream().map(ProtoTranSapDTO::getTranId).distinct().collect(Collectors.toList());
        // 转移时没有样机生产底
        protoTranDetService.updateMakeCnByTranIds(tranIdList);
        // 转移时 领用申请单未抛SAP时 领用申请单行号为空
        protoTranDetService.updateUpdateApplyItemid(tranIdList);
        Set<String> noPushList = new HashSet<>();
        for(ProtoTranSapDTO sapDTO : list){
            String tranId = sapDTO.getTranId();
            String tranCode = sapDTO.getTranCode();
            ProtoTranPO tranPO = protoTranService.getByTranId(tranId);

            //Transfer接口标识："E"-抛送失败，"S"-抛送成功
            String sapRetType = sapDTO.getSapRetType();
            //找出Transfer抛送成功，但107接口抛送失败的转移单，只需抛送107接口
            if(SapRetEnum.SUCCESS.getKey().equals(sapRetType)){
                //如果主表单号存在于集合中，说明明细表拆分出的子单已全部处理过了
                //由于一整单拆单后可能存在两个接口都抛送失败的，也可能存在唯独107接口抛送失败的，所以单号作区分处理
                if(noPushList.contains(tranCode+"-107")){
                    continue;
                }
                //获取拆除后的子单并遍历（只拆出唯独107接口抛送失败的子单）
                List<ProtoTranDetPO> tranList = protoTranDetService.list107FailByTranId(tranId);
                for (ProtoTranDetPO tranDetPO : tranList){
                    List<ProtoTranDetPO> detList = protoTranDetService.listByTranInfo(tranId, tranDetPO.getAssetNewold(), tranDetPO.getProjectCode(), tranDetPO.getCenterCode(), tranDetPO.getMakeCn());
                    Map<TranSapSplitDTO, List<ProtoTranDetPO>> splitTranDetMap = protoTranDetService.splitTranDetMap(detList);
                    for (Map.Entry<TranSapSplitDTO, List<ProtoTranDetPO>> entry : splitTranDetMap.entrySet()) {
                        List<ProtoTranDetPO> value = entry.getValue();
                        try {
                            tranService.pushSap107(tranPO, entry.getKey(), value);
                            protoTranDetService.updateBatchById(value);
                        } catch (Exception e){
                            log.error("push sap 107 error tranId:{}, error:{}", tranId, e);
                        }
                    }
                }

                //已处理的单号作记录
                noPushList.add(tranCode+"-107");
            }else {
                if(noPushList.contains(tranCode)){
                    continue;
                }
                List<ProtoTranDetPO> tranList = protoTranDetService.getDetGroupBy(tranId);
                for (ProtoTranDetPO tranDetPO : tranList){
                    List<ProtoTranDetPO> detList = protoTranDetService.listByTranInfo(tranId, tranDetPO.getAssetNewold(), tranDetPO.getProjectCode(), tranDetPO.getCenterCode(), tranDetPO.getMakeCn());
                    try {
                        tranService.pushSap(tranPO, detList, false);
                        protoTranDetService.updateBatchById(detList);
                    } catch (Exception e) {
                        log.error("push sap tran error tranId:{}, error:{}", tranId, e);
                    }
                }
                //已处理的单号作记录
                noPushList.add(tranCode);
            }
        }

    }
}
