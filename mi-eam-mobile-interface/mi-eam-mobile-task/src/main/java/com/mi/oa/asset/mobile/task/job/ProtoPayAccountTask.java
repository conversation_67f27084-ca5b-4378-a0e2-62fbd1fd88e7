package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.application.service.ProtoOrderBOService;
import com.mi.oa.asset.mobile.infra.config.MallSyncBillConfig;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/23
 */
@PlanTask(name = "ProtoPayAccountTask", quartzCron = "0 0 14 * * ?", description = "资金往来对账")
public class ProtoPayAccountTask implements PlanExecutor {

    @Autowired
    private ProtoOrderBOService protoOrderBOService;

    @Autowired
    private MallSyncBillConfig syncBillConfig;

    @Override
    public void execute() {
        String dateStr = LocalDate.now().plusDays(-1).toString();
        for (String mrpType : syncBillConfig.getMrpTypeList()) {
            protoOrderBOService.checkAccount(dateStr, mrpType);
        }
    }
}
