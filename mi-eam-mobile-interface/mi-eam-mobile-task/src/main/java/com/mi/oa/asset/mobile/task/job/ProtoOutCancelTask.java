package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoOutService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.core.AutoConfigureCache;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/25
 */
@PlanTask(name = "ProtoOutCancelTask", quartzCron = "0 0 3 1/1 * ?", description = "自动注销发放状态为编制中的发放单 每隔一天凌晨3点执行一次")
public class ProtoOutCancelTask implements PlanExecutor {

    @Autowired
    private ProtoOutService protoOutService;

    @Override
    public void execute() {
        protoOutService.updateCancelStatus();
    }
}
