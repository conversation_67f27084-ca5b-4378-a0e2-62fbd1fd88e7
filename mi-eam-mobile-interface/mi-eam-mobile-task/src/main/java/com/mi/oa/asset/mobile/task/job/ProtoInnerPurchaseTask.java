package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInnerPurchasePlanService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import org.springframework.beans.factory.annotation.Autowired;


@PlanTask(name = "ProtoInnerPurchaseTask", quartzCron = "0 0/10 * * * ?", description = "内购计划单推送检查消息通知，每十分钟执行一次")
public class ProtoInnerPurchaseTask implements PlanExecutor {

    @Autowired
    private ProtoInnerPurchasePlanService protoInnerPurchasePlanService;

    @Override
    public void execute() {
        protoInnerPurchasePlanService.checkInnerPurchaseOrder(null);
    }
}
