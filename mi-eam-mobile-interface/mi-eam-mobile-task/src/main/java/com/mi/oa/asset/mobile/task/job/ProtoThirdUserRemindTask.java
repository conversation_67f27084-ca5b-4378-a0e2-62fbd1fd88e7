package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoThirdUserService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/3/18
 */
@PlanTask(name = "ProtoThirdUserRemindTask", quartzCron = "0 0 10 * * ?", description = "第三方账号到期提醒")
public class ProtoThirdUserRemindTask implements PlanExecutor {

    @Autowired
    private ProtoThirdUserService protoThirdUserService;

    @Override
    public void execute() {
        protoThirdUserService.sendReminderEmail();
    }
}
