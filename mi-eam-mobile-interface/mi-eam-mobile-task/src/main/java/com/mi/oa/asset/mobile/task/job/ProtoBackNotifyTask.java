package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2023/7/20 19:58
 */

@Slf4j
@PlanTask(name = "ProtoBackNotifyTask", quartzCron = "0 55 9 ? * 2,3,4,5,6 *", description = "工程机归还提醒")
public class ProtoBackNotifyTask implements PlanExecutor {

    @Autowired
    private ProtoCardService protoCardService;

    @Override
    public void execute() {
        protoCardService.protoBackNotifyTask();
    }
}
