/*
 * Copyright (c) 2020. XiaoMi Inc.All Rights Reserved
 */
package com.mi.oa.asset.mobile.task;


import com.mi.oa.infra.oaucf.EnableOACache;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;

/**
* Task启动类
* <AUTHOR>
* @date          2021/6/16 11:22
*/
@SpringBootApplication
@ComponentScan(basePackages = {"com.mi.oa"})
@EnableOACache
@EnableFeignClients(basePackages = {"com.mi.oa"})
@EnableDiscoveryClient
public class MobileTaskApplication {

    public static void main(String[] args) {
        SpringApplication.run(MobileTaskApplication.class, args);
    }

}
