package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.application.service.ProtoDataSyncService;
import com.mi.oa.asset.mobile.utils.DateUtil;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * Description: 生态链SAP 同步半成品物料定时任务
 * https://xiaomi.f.mioffice.cn/docx/doxk40zwcBMHUTA3ZEimTiyiy6e
 *
 * <AUTHOR>
 * @date 2023/8/29
 */
@PlanTask(name = "ProtoEcoSkuSyncTask", quartzCron = "0 30 3 * * ? ", description = "工程机生态链同步Sap半成品物料定时任务")
@Slf4j
public class ProtoEcoSkuSyncTask implements PlanExecutor {
    @Autowired
    private ProtoDataSyncService protoDataSyncService;

    @Value("${sap.syncType:1}")
    private String sapX5SyncType;

    @Override
    public void execute() {
        if ("1".equals(sapX5SyncType)) {
            protoDataSyncService.syncSapEcoSkuData(LocalDateTime.now().plusYears(-23));
        } else {
            protoDataSyncService.syncSapEcoSkuData(LocalDateTime.now().plusDays(-2));
        }
        log.info("ProtoEcoSkuSyncTask end==dataTime:{}", DateUtil.getDateStr(new Date(), DateUtils.COMMON_PATTERN));
    }
}
