package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.application.service.ProtoDataSyncService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.time.LocalDateTime;

/**
 * Copyright (c) 2022 XiaoMi Inc.All Rights Reserved.
 * Description: your description
 *
 * <AUTHOR>
 * @date 2023/08/18 17:24
 */
@Slf4j
@PlanTask(name = "ProtoEcoAndTvProjectSyncTask", quartzCron = "0 30 1 * * ? ", description = "工程机生态链和电视项目数据同步")
public class ProtoEcoAndTvProjectSyncTask implements PlanExecutor {

    @Autowired
    private ProtoDataSyncService protoDataSyncService;

    @Value("${upcX5.syncType:2}")
    private String upcX5SyncType;

    @Override
    public void execute() {
        if ("1".equals(upcX5SyncType)) {
            protoDataSyncService.syncEcoAndTvProjectInfo(LocalDateTime.now().plusYears(-10));
        } else {
            protoDataSyncService.syncEcoAndTvProjectInfo(LocalDateTime.now().plusDays(-1));
        }
    }
}
