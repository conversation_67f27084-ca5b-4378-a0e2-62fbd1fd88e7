package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.application.service.ProtoOrderBOService;
import com.mi.oa.asset.mobile.infra.config.MallSyncBillConfig;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/26
 */
@PlanTask(name = "ProtoChargeAmountTask", quartzCron = "0 0 13 * * ?", description = "同步账单手续费接口")
public class ProtoChargeAmountTask implements PlanExecutor {

    @Autowired
    private ProtoOrderBOService protoOrderBOService;

    @Autowired
    private MallSyncBillConfig syncBillConfig;

    @Override
    public void execute() {
        String dateStr = LocalDate.now().plusDays(-1).toString();
        for (String mrpType : syncBillConfig.getMrpTypeList()) {
            protoOrderBOService.synChargeAmount(mrpType, dateStr);
        }
    }
}
