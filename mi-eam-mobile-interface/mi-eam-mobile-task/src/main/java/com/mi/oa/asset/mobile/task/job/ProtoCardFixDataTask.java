package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.application.service.ProtoCardFixDataBOService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 工程机台账数据修复task
 *
 * <AUTHOR>
 * @date 2022/4/1 14:25
 */
@Slf4j
@PlanTask(name = "ProtoCardFixDataTask", quartzCron = "0 0 1 * * ?", description = "工程机台账数据修复任务")
public class ProtoCardFixDataTask implements PlanExecutor {

    @Autowired
    private ProtoCardFixDataBOService protoCardFixDataBOService;

    @Override
    public void execute() {
        protoCardFixDataBOService.fixProtoCardSkuName();
    }

}
