package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.common.enums.RedisCachePrefixKeyEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardService;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.Set;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/12/6
 */
@PlanTask(name = "PSLeaveCheckTask", quartzCron = "0 0 0/1 * * ?", description = "离职检查，每隔1小时执行")
@Slf4j
public class PSLeaveCheckTask implements PlanExecutor {

    @Autowired
    private ProtoCardService protoCardService;

    @Override
    public void execute() {
        Set<Object> objects = RedisUtils.setMembers(RedisCachePrefixKeyEnum.PS_LEAVE_CHECK.getPrefixKey());
        log.info("PSLeaveCheckTask start member:{}", objects);
        if (CollectionUtils.isEmpty(objects)){
            return;
        }
        for (Object obj : objects){
            String empCode = String.valueOf(obj);
            protoCardService.changePSPhoneNum(empCode);
        }
    }
}
