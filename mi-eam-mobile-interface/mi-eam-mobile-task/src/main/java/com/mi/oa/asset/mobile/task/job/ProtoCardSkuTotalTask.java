package com.mi.oa.asset.mobile.task.job;


import com.mi.oa.asset.mobile.application.service.ProtoReportService;
import com.mi.oa.asset.mobile.common.enums.RedisCachePrefixKeyEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardSkuTotalService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 工程机物料汇总台账Task
 *
 * <AUTHOR>
 * @date 2022/3/25 11:52
 */
@Slf4j
@PlanTask(name = "ProtoCardSkuTotalTask", quartzCron = "0 0 0/1 * * ?", description = "工程机物料汇总台账统计")
public class ProtoCardSkuTotalTask implements PlanExecutor {

    @Autowired
    private ProtoReportService protoReportService;

    @Autowired
    private ProtoCardSkuTotalService protoCardSkuTotalService;

    @Override
    public void execute() {
        protoReportService.updateReport(RedisCachePrefixKeyEnum.PROTO_CARD_SKU_LOCK_KEY,
                RedisCachePrefixKeyEnum.PROTO_CARD_SKU_UPDATE_TIME,
                ()->protoCardSkuTotalService.statistics());
    }

}
