package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDevicePlanPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoDevicePlanService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/23
 */
@PlanTask(name = "ProtoPlanTask", quartzCron = "0 0/5 * * * ?", description = "盘点定时任务消息通知")
public class ProtoPlanTask implements PlanExecutor {

    @Autowired
    private ProtoDevicePlanService protoDevicePlanService;

    @Override
    public void execute() {
        List<ProtoDevicePlanPO> planList = protoDevicePlanService.listPlaning();
        for (ProtoDevicePlanPO planPO : planList){
            protoDevicePlanService.sendPlanMessage(planPO.getPlanId());
        }
    }
}
