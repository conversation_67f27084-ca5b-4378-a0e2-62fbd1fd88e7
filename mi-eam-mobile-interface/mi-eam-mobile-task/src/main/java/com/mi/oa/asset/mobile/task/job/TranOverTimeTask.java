package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoTranService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/3/7
 */
@PlanTask(name = "TranOverTimeTask", quartzCron = "0 0 9 * * ?", description = "转移超7天未确认提醒")
@Slf4j
public class TranOverTimeTask implements PlanExecutor {

    @Autowired
    private ProtoTranService protoTranService;

    @Override
    public void execute() {
        List<ProtoTranPO> list = protoTranService.listToRemind();
        // 推送消息提醒
        for (ProtoTranPO tranPO : list){
            try {
                protoTranService.remindMessage(tranPO);
            } catch (Exception e){
                log.error("TranOverTimeTask_send_message_error tran:{}, e:{}", tranPO, e);
            }
        }
    }
}
