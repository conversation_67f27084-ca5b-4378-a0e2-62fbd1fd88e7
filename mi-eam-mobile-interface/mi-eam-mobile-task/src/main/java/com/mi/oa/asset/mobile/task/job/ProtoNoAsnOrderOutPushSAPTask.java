package com.mi.oa.asset.mobile.task.job;

import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOutPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoOutDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoOutService;
import com.xiaomi.xms.plans.client.PlanExecutor;
import com.xiaomi.xms.plans.client.support.spring.annotation.PlanTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 工程机快速发放（含无ASN收货）抛送SAP的Task类
 *
 * <AUTHOR>
 * @version 1.0 2021-11-20
 */
@Slf4j
@PlanTask(name = "ProtoNoAsnOrderOutPushSAPTask", quartzCron = "0 40 0/1 * * ?", description = "发放定时任务")
public class ProtoNoAsnOrderOutPushSAPTask implements PlanExecutor {

    @Autowired
    private ProtoOutService protoOutService;

    @Autowired
    private ProtoOutDetService protoOutDetService;

    @Override
    public void execute() {
        // 更新发货单子表抛SAP状态
        protoOutDetService.updateDetInStatus();

        List<ProtoOutPO> list = protoOutService.getProtoOutPushSAPList();
        for (ProtoOutPO protoOut : list){
            try {
                protoOutService.pushSAP(protoOut.getOutId(), protoOut.getOutUserName());
            } catch (Exception e) {
                log.error("ProtoNoAsnOrderOutPushSAPTask.execute:{}", e.getMessage(), e);
            }
        }
    }
}


