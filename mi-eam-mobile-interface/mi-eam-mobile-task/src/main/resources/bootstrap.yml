spring:
  profiles:
    active: @env@
    include: actuator,datasource,jackson,orm,plan,web,redis,oaucf,remote
  application:
    name: mi-eam-mobile-task
  cloud:
    nacos:
      config:
        extension-configs[0]:
          data-id: mi-eam-mobile-remote.yaml
          group: DEFAULT_GROUP   # 默认为DEFAULT_GROUP
          refresh: true   # 是否动态刷新，默认为false
        extension-configs[1]:
          data-id: mi-eam-mobile-datasource.yaml
          group: DEFAULT_GROUP   # 默认为DEFAULT_GROUP
          refresh: true   # 是否动态刷新，默认为false
        extension-configs[2]:
          data-id: mi-eam-mobile-web.yaml
          group: DEFAULT_GROUP   # 默认为DEFAULT_GROUP
          refresh: true   # 是否动态刷新，默认为false
        extension-configs[3]:
          data-id: mi-eam-mobile-cache.yaml
          group: DEFAULT_GROUP   # 默认为DEFAULT_GROUP
          refresh: true   # 是否动态刷新，默认为false
        extension-configs[4]:
          data-id: mi-eam-mobile-jackson.yaml
          group: DEFAULT_GROUP   # 默认为DEFAULT_GROUP
          refresh: true   # 是否动态刷新，默认为false
        extension-configs[5]:
          data-id: mi-eam-mobile-orm.yaml
          group: DEFAULT_GROUP   # 默认为DEFAULT_GROUP
          refresh: true   # 是否动态刷新，默认为false
        extension-configs[6]:
          data-id: mi-eam-mobile-actuator.yaml
          group: DEFAULT_GROUP   # 默认为DEFAULT_GROUP
          refresh: true   # 是否动态刷新，默认为false
#开发环境
---
spring:
  profiles: local
  cloud:
    nacos:
      config:
        server-addr: http://dev-nacos.api.xiaomi.net
        file-extension: yaml
        shared-dataids: mi-eam-mobile-remote.yaml
        namespace: info_mims_dev
        username: info_mims_dev
        password: iX!FD9#IEdcU
      discovery:
        server-addr: http://dev-nacos.api.xiaomi.net
        namespace: info_mims_dev
        username: info_mims_dev
        password: iX!FD9#IEdcU

---
#测试
spring:
  profiles: test
  cloud:
    nacos:
      config:
        server-addr: http://staging-nacos.api.xiaomi.net
        file-extension: yaml
        namespace: info_mims_asset_test
        username: info_mims_asset_test
        password: JfiGKL1fcnh3
      discovery:
        server-addr: http://staging-nacos.api.xiaomi.net
        namespace: info_mims_asset_test
        username: info_mims_asset_test
        password: JfiGKL1fcnh3

---
#生产
spring:
  profiles: prod
  cloud:
    nacos:
      config:
        server-addr: http://cnbj1-nacos.api.xiaomi.net
        file-extension: yaml
        shared-dataids: mi-eam-mobile-remote.yaml
        namespace: info_mims_asset_prod
        username: info_mims_asset_prod
        password: CSavdvTx5sRf
      discovery:
        server-addr: http://cnbj1-nacos.api.xiaomi.net
        namespace: info_mims_asset_prod
        username: info_mims_asset_prod
        password: CSavdvTx5sRf