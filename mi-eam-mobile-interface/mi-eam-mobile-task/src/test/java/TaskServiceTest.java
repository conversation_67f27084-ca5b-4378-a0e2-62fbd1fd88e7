//import com.mi.oa.asset.mobile.infra.dto.sap.ProtoInSapDTO;
//import com.mi.oa.asset.mobile.infra.dto.sap.SapCustomVO;
//import com.mi.oa.asset.mobile.infra.dto.sap.SapMierDTO;
//import com.mi.oa.asset.mobile.infra.remote.sdk.SapClient;
//import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInPO;
//import com.mi.oa.asset.mobile.infra.repository.mybatis.service.EmployeeService;
//import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInDetService;
//import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInService;
//import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoOutService;
//import com.mi.oa.asset.mobile.task.MobileTaskApplication;
//import com.mi.oa.asset.mobile.task.job.*;
//import com.mi.oa.infra.oaucf.utils.JacksonUtils;
//import com.xiaomi.core.test.TestWithKeycenter;
//import feign.Request;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.jupiter.api.Disabled;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//
///**
// * @Desc:
// * @Author: huy
// * @Date: 2022/1/24
// */
//@Slf4j
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = MobileTaskApplication.class)
//@WebAppConfiguration
//@Disabled
//public class TaskServiceTest implements TestWithKeycenter {
//
//    @Autowired
//    private ProtoInMachListTask protoInMachListTask;
//
//    @Autowired
//    private ProtoNoAsnOrderOutPushSAPTask protoNoAsnOrderOutPushSAPTask;
//
//    @Autowired
//    private ProtoOutService protoOutService;
//    @Autowired
//    private ProtoInDetService protoInDetService;
//    @Autowired
//    private ProtoInService protoInService;
//    @Autowired
//    private SapClient sapClient;
//    @Autowired
//    private ProtoNoAsnOrderPushSAPTask protoNoAsnOrderPushSAPTask;
//
////    @Autowired
////    private ProtoNoAsnOrderTranPushSAPTask protoNoAsnOrderTranPushSAPTask;
//
//    @Autowired
//    private ProtoOutTimeOutAutoConfirmTask protoOutTimeOutAutoConfirmTask;
//
//    @Autowired
//    private PSLeaveCheckTask psLeaveCheckTask;
//
//    @Autowired
//    private EmployeeService employeeService;
//
//    @Autowired
//    private TranOverTimeTask tranOverTimeTask;
//
//    @Autowired
//    private ProtoThirdUserTask protoThirdUserTask;
//
//    @Autowired
//    private ProtoBaseLineTask protoBaseLineTask;
//
//    @Autowired
//    private ProtoThirdUserRemindTask protoThirdUserRemindTask;
//
//    @Autowired
//    private ProtoCardProjectTotalTask protoCardProjectTotalTask;
//
//    @Autowired
//    private ProtoCardSkuTotalTask protoCardSkuTotalTask;
//
//    @Autowired
//    private ProtoCardDeptproTotalTask protoCardDeptproTotalTask;
//
//    @Autowired
//    private ProtoSynProjectTask protoSynProjectTask;
//
//    @Autowired
//    private ProtoCardStoreTask protoCardStoreTask;
//
//    @Autowired
//    private ProtoPlanTask protoPlanTask;
//
//    @Autowired
//    private ProtoPayAccountTask protoPayAccountTask;
//
//    @Autowired
//    private ProtoCardDeptTotalTask protoCardDeptTotalTask;
//
//    @Autowired
//    private ProtoApplyPushSapTask protoApplyPushSapTask;
//
//    @Autowired
//    private ProtoEcoAndTvProjectSyncTask protoEcoAndTvProjectSyncTask;
//    @Autowired
//    private ProtoEcoSkuSyncTask protoEcoSkuSyncTask;
//
//    @Test
//    public void test25(){
//        protoApplyPushSapTask.execute();
//    }
//
//    @Test
//    public void test5(){
//        protoNoAsnOrderPushSAPTask.execute();
//    }
//
//    @Test
//    public void test3(){
//        protoNoAsnOrderOutPushSAPTask.execute();
//    }
//
//    @Test
//    public void test2(){
//        protoInMachListTask.execute();
//    }
//
//    @Test
//    public void test4(){
//        protoOutService.pushSAP("mi-157-24502", "杨媛媛");
//    }
//
////    @Test
////    public void test6(){
////        protoNoAsnOrderTranPushSAPTask.execute();
////    }
//
//    @Test
//    public void test8(){
//        protoOutTimeOutAutoConfirmTask.execute();
//    }
//
//    @Test
//    public void test9(){
//        psLeaveCheckTask.execute();
//    }
//
//    @Test
//    public void test10(){
//        employeeService.leaveCheckPhoneNum("123124");
//    }
//
//    @Test
//    public void test11(){
//        tranOverTimeTask.execute();
//    }
//
//    @Test
//    public void test13(){
//        protoThirdUserTask.execute();
//    }
//
//    @Test
//    public void test14(){
//        protoBaseLineTask.execute();
//    }
//
//    @Test
//    public void test15(){
//        protoThirdUserRemindTask.execute();
//    }
//
//    @Test
//    public void test17(){
//        protoCardProjectTotalTask.execute();
//    }
//
//    @Test
//    public void test18() {
//        protoCardSkuTotalTask.execute();
//    }
//
//    @Test
//    public void test19() {
//        protoCardDeptproTotalTask.execute();
//    }
//
//    @Test
//    public void test20(){
//        protoSynProjectTask.execute();
//    }
//
//    @Test
//    public void test21(){
//        protoCardStoreTask.execute();
//    }
//
//    @Test
//    public void test22(){
//        protoPlanTask.execute();
//    }
//
//    @Test
//    public void test23(){
//        protoPayAccountTask.execute();
//    }
//
//    @Test
//    public void test24(){
//        protoEcoAndTvProjectSyncTask.execute();
//    }
//    @Test
//    public void test26(){
//        protoCardDeptTotalTask.execute();
//    }
//
//    @Test
//    public void test27(){
//        protoEcoSkuSyncTask.execute();
//    }
//
//    @Test
//    public void testSap107SendTimeout(){
//        //匹配失败的入库单不能推送sap
//        List<ProtoInSapDTO> notAsnPushSapList = protoInDetService.getNotAsnPushSap("mi-127-288590");
//        ProtoInPO proto = protoInService.getById("mi-127-288590");
//        for (ProtoInSapDTO sapDTO : notAsnPushSapList){
//            try {
//                protoInService.noAsnOrderPushSAP(sapDTO, proto);
//            } catch (Exception e) {
//                log.error("proto_in_push_sap error sapDTO:{}, e:{}", sapDTO, e);
//            }
//        }
//    }
//
//    @Test
//    public void testSap107Time() {
//        Request.Options options = new Request.Options(1, TimeUnit.SECONDS, 1, TimeUnit.SECONDS, true);
//        SapMierDTO request = JacksonUtils.json2Bean("{\"I_LIFNR\":\"M10001\",\"LANG\":\"ZHS\",\"Side\":\"MierAPP\",\"is_header\":{\"BUSI_TYPE\":\"YJRK\",\"BATCH_ID\":\"D723091820396\",\"VENDOR\":\"M10001\",\"PURC_ORG\":\"1110\",\"BUSI_DATE\":\"20230918\",\"MIDOC_NUM\":\"9313728313\",\"VNDOC_NUM\":\"9313728313\",\"COMM_HDR\":\"20230918\"},\"it_items\":[{\"BATCH_ID\":\"D723091820396\",\"BATCH_ROW\":\"10\",\"MIDOC_NUM\":\"9313728313\",\"MIDOC_ROW\":\"10\",\"VNDOC_NUM\":\"9313728313\",\"VNDOC_ROW\":\"10\",\"MATERIAL1\":\"901N11AP1004\",\"MAT_DESC1\":\"机头组件_中国_16GB_1024GB_P10_白色_N11\",\"QUANTITY1\":\"2\",\"GOOD_BAD\":\"GM\"}]}", SapMierDTO.class);
//        SapCustomVO rs = sapClient.sap107Json(request, options);
//        System.out.println(JacksonUtils.bean2Json(rs));
//    }
//}
