<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>mi-eam-mobile-parent</artifactId>
        <groupId>com.mi.oa.asset.mobile</groupId>
        <version>${revision}</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>mi-eam-mobile-api</artifactId>

    <dependencies>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.oa.asset</groupId>
            <artifactId>mi-asset-x5</artifactId>
        </dependency>
        <!-- nacos组件-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.github.openfeign</groupId>
                    <artifactId>feign-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 小米自己的组件及jar包版本 -->
        <dependency>
            <groupId>com.mi.oa.asset.mobile</groupId>
            <artifactId>mi-eam-mobile-application</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.oa.infra.oaucf</groupId>
            <artifactId>oaucf-skylog-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.oa.infra.oaucf</groupId>
            <artifactId>oaucf-autoconfig-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.info.comb</groupId>
            <artifactId>neptune-client</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <!-- spring-boot:run 中文乱码解决 -->
                <configuration>
                    <fork>true</fork>
                    <!--增加jvm参数-->
                    <jvmArguments>-Dfile.encoding=UTF-8</jvmArguments>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
