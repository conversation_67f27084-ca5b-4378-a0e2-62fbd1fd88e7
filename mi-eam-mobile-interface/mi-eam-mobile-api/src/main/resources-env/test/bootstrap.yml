spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://staging-nacos.api.xiaomi.net
        namespace: info_mims_asset_test
        username: info_mims_asset_test
        password: GBD6R/+1A46qJiXkwh246x2IGBICObSrH6NHea44FMxcGtkfuP8YED+muKwc40b1mTNJjzCKfHUYFDUk84o71Ew6CB7Yt5y7K6UVh+cmAA==
        password@kc-sid: oa-asset.g  # keycenter的加密密匙sid
      config:
        server-addr: http://staging-nacos.api.xiaomi.net
        file-extension: yaml
        namespace: info_mims_asset_test
        username: info_mims_asset_test
        password: GBD6R/+1A46qJiXkwh246x2IGBICObSrH6NHea44FMxcGtkfuP8YED+muKwc40b1mTNJjzCKfHUYFDUk84o71Ew6CB7Yt5y7K6UVh+cmAA==
        password@kc-sid: oa-asset.g  # keycenter的加密密匙sid