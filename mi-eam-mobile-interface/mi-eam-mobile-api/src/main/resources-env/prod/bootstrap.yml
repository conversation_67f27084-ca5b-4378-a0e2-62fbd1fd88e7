spring:
  cloud:
    nacos:
      discovery:
        server-addr: http://cnbj1-nacos.api.xiaomi.net
        namespace: info_mims_asset_prod
        username: info_mims_asset_prod
        password: GBBVWuUuNZ5P3QxpR/CgyUHyGBKFeHGPVWlGK5RV1JcOV7spzwEYEC3W3hzhEk5us2vuzrmAhr4YFP4w+LtwEli0m35hk8IBGbqnmz9dAA==
        password@kc-sid: oa-asset.g  # keycenter的加密密匙sid
      config:
        server-addr: http://cnbj1-nacos.api.xiaomi.net
        file-extension: yaml
        namespace: info_mims_asset_prod
        username: info_mims_asset_prod
        password: GBBVWuUuNZ5P3QxpR/CgyUHyGBKFeHGPVWlGK5RV1JcOV7spzwEYEC3W3hzhEk5us2vuzrmAhr4YFP4w+LtwEli0m35hk8IBGbqnmz9dAA==
        password@kc-sid: oa-asset.g  # keycenter的加密密匙sid