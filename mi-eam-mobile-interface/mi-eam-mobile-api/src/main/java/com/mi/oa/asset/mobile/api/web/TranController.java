package com.mi.oa.asset.mobile.api.web;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.mi.oa.asset.mobile.api.converter.RequestContextConverter;
import com.mi.oa.asset.mobile.api.converter.TranConverter;
import com.mi.oa.asset.mobile.api.model.req.BpmCallbackReq;
import com.mi.oa.asset.mobile.api.model.req.TranCommitReq;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmProcessCompletedDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.MobileProcessBaseBizDTO;
import com.mi.oa.asset.mobile.application.service.TranService;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description: bpm相关处理类
 * @date 2021/12/7 11:17
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/tran")
public class TranController {

    @Autowired
    private TranService tranService;

    @Autowired
    private TranConverter tranConverter;

    @Autowired
    private RequestContextConverter requestContextConverter;

    /**
     * 业务类型修改清空明细
     *
     * @return
     */
    @PostMapping("/clearDet")
    public BaseResp tranClearDet(@RequestBody RequestContext request) {
        String keyId = request.getRequestValue("keyid");
        String mrpType = request.getRequestValue("mrpType");
        tranService.tranClearDet(keyId,mrpType);
        return BaseResp.success();
    }

    /**
     * 提交转移申请
     *
     * @return
     */
    @PostMapping("/scanQry")
    public BaseResp tranScanQry(@RequestBody RequestContext request) {
        String scanResult = request.getRequestValue("scan_result");
        return BaseResp.success(tranService.tranScanQry(scanResult));
    }

    /**
     * 提交转移申请
     *
     * @return
     */
    @PostMapping("/commit")
    public BaseResp commitTran(@RequestBody @Valid  TranCommitReq req) {
        List<String> tranIds = req.getTranIds();
        String userCode = req.getUserCode();
        for (String tranId : tranIds) {
            tranService.tranCommit(tranId, userCode);
        }
        return BaseResp.success();
    }

    /**
     * bpm回调 (保留，兼容2.0)
     * @param req
     * @return
     */
    @PostMapping("/callback")
    public BaseResp tranCallBack(@RequestBody BpmCallbackReq<MobileProcessBaseBizDTO> req){
        tranService.bpmCallBack(tranConverter.reqToDTO(req));
        return BaseResp.success();
    }

    /**
     * bpm回调3.0
     * @param req
     * @return
     */
    @PostMapping("/callbackV3")
    public BaseResp tranCallBackV3(@RequestBody BpmProcessCompletedDTO req){
        tranService.bpmCallBackV3(req);
        return BaseResp.success();
    }

    @PostMapping(value = "/query")
    public BaseResp query(@RequestBody RequestContext request){
        return BaseResp.success(tranService.query(requestContextConverter.reqToTranDTO(request)));
    }

}
