package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.application.service.ProtoAuthDataCfgBOService;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据权限配置
 *
 * <AUTHOR>
 * @date 2022/4/6 19:00
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/authdatacfg")
public class ProtoAuthDataCfgController {

    public static final String KEYID = "keyid";

    @Autowired
    private ProtoAuthDataCfgBOService protoAuthDataCfgBOService;

    @PostMapping(value = "/audit")
    public BaseResp audit(@RequestBody RequestContext request) {
        String[] keyIds = request.getRequestValues(KEYID);
        return BaseResp.success(protoAuthDataCfgBOService.audit(keyIds, request));
    }

    @PostMapping(value = "/unaudit")
    public BaseResp unAudit(@RequestBody RequestContext request) {
        String[] keyIds = request.getRequestValues(KEYID);
        return BaseResp.success(protoAuthDataCfgBOService.unAudit(keyIds, request));
    }
}
