package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.model.req.SyncProjectReq;
import com.mi.oa.asset.mobile.application.service.ProtoProjectListBOService;
import com.mi.oa.asset.x5.common.JacksonUtils;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目目录
 *
 * <AUTHOR>
 * @date 2022/1/27 16:11
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/projectList")
public class ProtoProjectListController {

    @Autowired
    private ProtoProjectListBOService protoProjectListBOService;

    /**
     * 根据项目同步项目信息
     * @param projectCodeList
     * @return
     */
    @PostMapping(value = "/distributeProject")
    public BaseResp synProject(@RequestBody List<String> projectCodeList){
        log.info("synProject req:{}", projectCodeList);
        protoProjectListBOService.synProjectListByProjectCode(projectCodeList);
        log.info("synProject end.");
        return BaseResp.success();
    }
    @PostMapping(value = "/synEcoAndTvProject")
    public BaseResp synEcoAndTvProject(@RequestBody SyncProjectReq req){
        log.info("synProject req:{}", JacksonUtils.bean2Json(req));
        protoProjectListBOService.synEcoAndTvProjectListByProjectCode(req.getProjectCodes(),req.getMrpType());
        log.info("synProject end.");
        return BaseResp.success();
    }

    @GetMapping(value = "/synProjectListByTime")
    public BaseResp synProjectListByTime(String updateTime){
        protoProjectListBOService.synProjectListByTime(updateTime);
        return BaseResp.success();
    }
}
