package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.infra.dto.mall.InnerPlanInfo;
import com.mi.oa.asset.mobile.infra.dto.mall.InnerProductDTO;
import com.mi.oa.asset.mobile.infra.dto.mall.InnerProductReq;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoDevicePlanService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInnerPurchasePlanService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/24
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/protoPlan")
public class ProtoPlanController {

    @Autowired
    private ProtoDevicePlanService protoDevicePlanService;

    @Autowired
    private ProtoInnerPurchasePlanService protoInnerPurchasePlanService;

    @GetMapping(value = "/submitSendMessage")
    public BaseResp submitSendMessage(String planId) {
        protoDevicePlanService.submitSendMessage(planId);
        return BaseResp.success();
    }

    @PostMapping("/product/pull")
    public BaseResp<List<InnerProductDTO>> pullProduct(@RequestBody InnerProductReq req) {
        log.info("pull product start...");
        try {
            List<InnerProductDTO> productList = protoInnerPurchasePlanService.pullProduct(req);
            log.info("pull product end.");
            return BaseResp.success(productList);
        } catch (Exception e) {
            log.error("pull product error. msg:{}", e.getMessage(), e);
            return BaseResp.error(e.getMessage(), null);
        }
    }

    @PostMapping("/productOff/update-assetCard")
    public BaseResp productOffUpdateAssetCard(@RequestBody List<InnerProductReq> innerProductReqs) {
        log.info("product off update assetCard start...");
        try {
            protoInnerPurchasePlanService.productOffUpdateAssetCard(innerProductReqs);
            log.info("product off update assetCard end.");
            return BaseResp.success();
        } catch (Exception e) {
            log.error("product off update assetCard error. msg:{}", e.getMessage(), e);
            return BaseResp.error(e.getMessage(), null);
        }
    }

    @GetMapping("/info")
    public BaseResp<InnerPlanInfo> getProtoPlanInfo() {
        try {
            return BaseResp.success(protoInnerPurchasePlanService.getProtoPlanInfo());
        } catch (Exception e) {
            log.error("getProtoNum error. msg:{}", e.getMessage(), e);
            return BaseResp.error(e.getMessage(), null);
        }
    }
}
