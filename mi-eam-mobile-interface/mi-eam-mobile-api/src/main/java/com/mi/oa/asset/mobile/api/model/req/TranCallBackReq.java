package com.mi.oa.asset.mobile.api.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TranCallBackReq {
    //email 收件人
    @NotEmpty(message = "转移单不能为空")
    private String businessKey;
    //标题
    @NotBlank(message = "用户不能为空")
    private String userCode;
}
