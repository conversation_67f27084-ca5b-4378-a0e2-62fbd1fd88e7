package com.mi.oa.asset.mobile.api.consumer;

import com.mi.oa.asset.mobile.application.dto.order.PayOrderCallBackDTO;
import com.mi.oa.asset.mobile.application.service.ProtoOrderBOService;
import com.mi.oa.asset.mobile.common.enums.AssetOrderStatusEnum;
import com.mi.oa.asset.mobile.common.enums.MallOrderTypeEnum;
import com.mi.oa.asset.mobile.common.enums.PreOrderTypeEnum;
import com.mi.oa.asset.mobile.common.enums.TradeStateEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOrderPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoOrderService;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Desc: 消费线下付款的数据
 * @Author: huy
 * @Date: 2022/8/2
 */
@Service
@Slf4j
@RocketMQMessageListener(topic = "${rocketmq.order.topic}", consumerGroup = "${rocketmq.order.consumerGroup}",
        consumeThreadMax = 1)
public class OrderConsumer implements RocketMQListener<MessageExt>{

    @Autowired
    private ProtoOrderService protoOrderService;

    @Autowired
    private ProtoOrderBOService protoOrderBOService;

    @Override
    public void onMessage(MessageExt message) {
        String tags = message.getTags();
        if (!"0".equals(tags)){
            return;
        }
        String messageBody =  new String(message.getBody());;
        log.info("OrderConsumer message:{}", messageBody);
        // 工程机内购
        PayOrderCallBackDTO callBackDTO = GsonUtil.parseObject(messageBody, PayOrderCallBackDTO.class);

        // 如果是全员内购预订单创建，则创建订单
        if (MallOrderTypeEnum.ALL_STAFF.getType().toString().equals(callBackDTO.getOrderType())
                && PreOrderTypeEnum.CREATE.getType().equals(callBackDTO.getPreOrderType())) {
            protoOrderBOService.preOrderCreate(callBackDTO.getPreOrderDTO());
            return;
        }

        String orderId = callBackDTO.getOutOrderId();
        ProtoOrderPO orderPO = protoOrderService.getByOrderId(orderId);
        if (null == orderPO
                || AssetOrderStatusEnum.CLOSE.getState().equals(orderPO.getOrderStatus())
                || AssetOrderStatusEnum.COMPLETED.getState().equals(orderPO.getOrderStatus())){
            return;
        }
        String payType = callBackDTO.getPayType();
        if ("24".equals(payType)){
            payType = "2";
        }
        orderPO.setPayOrderType(payType);
        orderPO.setTradeDate(callBackDTO.getPayTime());
        orderPO.setTradeOrderCode(callBackDTO.getTradeId());
        orderPO.setTradeWaterCode(callBackDTO.getTradeNo());
        if (TradeStateEnum.TRADE_CLOSED.getName().equals(callBackDTO.getTradeStatus())){
            protoOrderBOService.close(orderPO);
        } else if (TradeStateEnum.TRADE_SUCCESS.getName().equals(callBackDTO.getTradeStatus())){
            protoOrderBOService.complete(orderPO);
        }
    }
}
