package com.mi.oa.asset.mobile.api.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LarkReq {
    private List<String> userNames;
    private String title;
    private String content;
    private String url;
    /**
     * 按钮名称
     */
    private String buttonName;
}
