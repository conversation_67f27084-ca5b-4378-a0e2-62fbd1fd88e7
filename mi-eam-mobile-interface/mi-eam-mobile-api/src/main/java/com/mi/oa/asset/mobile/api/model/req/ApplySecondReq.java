package com.mi.oa.asset.mobile.api.model.req;

import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/5 11:23
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplySecondReq {
    private String projectCode;
    private String stageName;
    private String skuCode;
    private String skuName;
    private UserInfo userInfo;
}
