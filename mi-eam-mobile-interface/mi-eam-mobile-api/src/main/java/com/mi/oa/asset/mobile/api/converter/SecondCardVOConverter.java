package com.mi.oa.asset.mobile.api.converter;

import com.mi.oa.asset.mobile.api.model.req.SecondCardReq;
import com.mi.oa.asset.mobile.application.dto.CommonConditionDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface SecondCardVOConverter {

    SecondCardVOConverter INSTANCE = Mappers.getMapper(SecondCardVOConverter.class);

    CommonConditionDTO reqToDTO(SecondCardReq req);

}
