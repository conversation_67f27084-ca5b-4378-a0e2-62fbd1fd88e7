package com.mi.oa.asset.mobile.api.web;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.mi.oa.asset.mobile.api.errorcode.ApiErrorCodeEnum;
import com.mi.oa.asset.mobile.application.dto.order.*;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.ProtoOrderBOService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInnerPurchasePlanService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoOrderPayService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/1
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/protoOrder")
public class ProtoOrderController {

    @Autowired
    private ProtoOrderBOService protoOrderBOService;

    @Autowired
    private ProtoOrderPayService protoOrderPayService;

    @Autowired
    private ProtoInnerPurchasePlanService protoInnerPurchasePlanService;

    /**
     * 非全员内购生成预订单
     * @return
     */
    @PostMapping(value = "/generatePurOrder")
    public BaseResp generatePurOrder(@RequestBody PurOrderDTO purOrderDTO) {
        protoOrderBOService.generatePurOrder(purOrderDTO);
        return BaseResp.success();
    }

    /**
     * 全员内购，由用户生成内购平台预订单
     * @return
     */
    @PostMapping(value = "/pre-order-create")
    public BaseResp preOrderCreate(@RequestBody PreOrderDTO dto) {
        if (StringUtils.isAnyBlank(dto.getDeviceCode(), dto.getUserCode())
                || Objects.isNull(dto.getNum())
                || Objects.isNull(dto.getSkuId())) {
            return BaseResp.error(ApiErrorCodeEnum.PARAMETER_NULL_ERROR);
        }
        try {
            protoOrderBOService.preOrderCreate(dto);
            return BaseResp.success();
        } catch (Exception e) {
            log.error("pre order create fail. error:", e.getMessage(), e);
            return BaseResp.error(e.getMessage(), null);
        }
    }

    /**
     * 全员内购，由管理员取消内购平台预订单
     * @return
     */
    @PostMapping(value = "/pre-order-cancel")
    public BaseResp preOrderCancel(@RequestBody PreOrderDTO dto) {
        if (StringUtils.isBlank(dto.getOrderId())) {
            return BaseResp.error(ApiErrorCodeEnum.PARAMETER_NULL_ERROR);
        }
        try {
            protoOrderBOService.preOrderCancel(dto);
            return BaseResp.success();
        } catch (Exception e) {
            log.error("pre order create fail. error:", e.getMessage(), e);
            return BaseResp.error(e.getMessage(), null);
        }
    }

    @PostMapping(value = "/notifyPay")
    public BaseResp notifyPay(@RequestBody ProtoOrderDTO protoOrderDTO) {
        if (CollectionUtils.isEmpty(protoOrderDTO.getOrderIdList())){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_ORDER_IS_NULL);
        }
        protoOrderBOService.notifyPay(protoOrderDTO);
        return BaseResp.success();
    }

    @PostMapping(value = "/notifyCheck")
    public BaseResp notifyCheck(@RequestBody ProtoOrderDTO protoOrderDTO) {
        if (CollectionUtils.isEmpty(protoOrderDTO.getOrderIdList())){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_ORDER_IS_NULL);
        }
        protoOrderBOService.notifyCheck(protoOrderDTO);
        return BaseResp.success();
    }

    /**
     * 下架
     * @param orderDTO
     * @return
     */
    @PostMapping(value = "/soldOut")
    public BaseResp soldOut(@RequestBody ProtoOrderDTO orderDTO) {
        if (CollectionUtils.isEmpty(orderDTO.getOrderIdList())){
            throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, "订单ID列表为空");
        }
        protoOrderBOService.soldOut(orderDTO);
        return BaseResp.success();
    }

    @GetMapping(value = "/getBySalesOrder")
    public BaseResp getBySalesOrder(String salesOrder) {
        return BaseResp.success(protoOrderPayService.getBySalesOrder(salesOrder));
    }

    @GetMapping(value = "/orderSubmit")
    public BaseResp orderSubmit(String salesOrder) {
        protoOrderPayService.orderSubmit(salesOrder);
        return BaseResp.success();
    }

    @GetMapping(value = "/sendCompleteLark")
    public BaseResp sendCompleteLark(String salesOrder){
        protoOrderBOService.sendCompleteLark(salesOrder);
        return BaseResp.success();
    }

    /**
     * 取消订单
     * @param orderDTO
     * @return
     */
    @PostMapping(value = "/cancelOrder")
    public BaseResp cancelOrder(@RequestBody ProtoOrderDTO orderDTO) {
        if (CollectionUtils.isEmpty(orderDTO.getOrderIdList())){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_ORDER_IS_NULL);
        }
        protoOrderBOService.cancelOrder(orderDTO);
        return BaseResp.success();
    }

    /**
     * 内购进度
     * @param planCode
     * @return
     */
    @GetMapping(value = "/planProgress")
    public BaseResp planProgress(String planCode) {
        return BaseResp.success(protoOrderBOService.planProgress(planCode));
    }

    /**
     * 收益详情
     * @param planCode
     * @return
     */
    @GetMapping(value = "/incomeProgress")
    public BaseResp incomeProgress(String planCode) {
        return BaseResp.success(protoOrderBOService.incomeProgress(planCode));
    }

    /**
     * 重新计算内购 已下架 实际内购数量 实际内购收益
     * @param orderList
     * @return
     */
    @PostMapping(value = "/calculatePurchase")
    public BaseResp calculatePurchase(@RequestBody List<String> orderList) {
        protoInnerPurchasePlanService.calculatePurchase(orderList);
        return BaseResp.success();
    }


	/**
     * 邮寄信息保存
     * @param dto
     * @return
     */
    @PostMapping(value = "/saveAssigneeInfo")
    public BaseResp saveAssigneeInfo(@RequestBody OrderPostSaveDTO dto) {
        protoOrderBOService.saveAssigneeInfo(dto);
        return BaseResp.success();
    }

    /**
     * 提交售后订单
     * @param dto
     * @return
     */
    @PostMapping(value = "/submitAfterSaleApply")
    public BaseResp submitAfterSaleApply(@RequestBody AfterSaleApplyDTO dto) {
        protoOrderBOService.submitAfterSaleApply(dto);
        return BaseResp.success();
    }
}
