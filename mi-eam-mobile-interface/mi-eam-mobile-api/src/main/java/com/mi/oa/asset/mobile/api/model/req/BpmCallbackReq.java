package com.mi.oa.asset.mobile.api.model.req;

import com.google.gson.annotations.SerializedName;
import com.mi.oa.asset.mobile.common.enums.BpmCallbackStatusEnum;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/10/6 14:17
 */

@Data
public class BpmCallbackReq<T> {

    private String businessKey;

    @SerializedName("task_no")
    private String taskNo;

    @SerializedName("task_name")
    private String taskName;

    private String assignee;

    private String operator;

    private BpmCallbackStatusEnum status;

    private String reason;

    private NextNode next;

    private List<NextNode> nextNodes;

    private T business;

    private String sequenceId;

    private Map<String, Object> variables = new HashMap<>();

    @Data
    public static class NextNode {

        @SerializedName("nextNodeId")
        String nodeId;

        @SerializedName("nextNodeName")
        String nodeName;

        @SerializedName("nextOperatorId")
        String operatorId;

        @SerializedName("nextOperator")
        String operatorName;
    }
}


