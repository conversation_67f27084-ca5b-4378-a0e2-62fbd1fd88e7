package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.converter.BpmVOConverter;
import com.mi.oa.asset.mobile.api.converter.RequestContextConverter;
import com.mi.oa.asset.mobile.api.model.req.InterruptProcessReq;
import com.mi.oa.asset.mobile.api.model.req.SubmitProcessReq;
import com.mi.oa.asset.mobile.api.model.vo.SubmitResultVo;
import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmCallbackDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.SubmitResultDTO;
import com.mi.oa.asset.mobile.application.service.BpmService;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.remote.entity.BpmDTO;
import com.mi.oa.asset.x5.provider.X5;
import com.mi.oa.asset.x5.provider.X5RequestBody;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: bpm相关处理类
 * @date 2021/12/7 11:17
 */
@WebLog
@Slf4j
@RestController
public class BpmController {

    public static final String KEYID = "keyid";

    @Autowired
    private BpmVOConverter bpmVOConverter;

    @Autowired
    private BpmService bpmService;

    @Autowired
    private RequestContextConverter requestContextConverter;

    /**
     * 中断bpm流程接口
     *
     * @param req
     * @return
     */
    @PostMapping("/v1/bpm/interrupt")
    public BaseResp interruptProcess(@Valid @RequestBody InterruptProcessReq req) {
        bpmService.interruptProcess(bpmVOConverter.reqToDTO(req));
        return BaseResp.success();
    }

    /**
     * 中断bpm流程接口V2
     *
     * @return
     */
    @PostMapping("/v2/bpm/interrupt")
    public BaseResp interruptProcessV2(@RequestBody RequestContext req) {
        String processKey = req.getRequestValue("processKey");
        String  keyId = req.getRequestValue(KEYID);
        UserInfo userInfo = requestContextConverter.reqToUserInfo(req);
        String startUser = userInfo.getUserCode();
        bpmService.interruptProcessV2(processKey,keyId,startUser);
        return BaseResp.success();
    }

    /**
     * 发起bpm流程
     *
     * @param req
     * @return 返回业务id
     */
    @PostMapping("/v1/bpm/submit")
    public BaseResp<SubmitResultVo> submitProcess(@RequestBody SubmitProcessReq req) {
        SubmitResultDTO submitResultDTO = bpmService.submitProcess(bpmVOConverter.submitReqToDTO(req));
        return BaseResp.success(bpmVOConverter.submitResultDTOToVo(submitResultDTO));
    }

    /**
     * 中断bpm流程接口
     *
     * @param param
     * @return
     */
    @PostMapping("/v1/bpm/callback")
    public BaseResp bpmCallback(@RequestBody BpmCallbackDTO param) {
        log.info("mobile bpm callback, {}", param);
        bpmService.bpmCallback(param);
        return BaseResp.success();
    }

}
