package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.converter.RequestContextConverter;
import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.dto.secondcard.ApplySecondDTO;
import com.mi.oa.asset.mobile.application.service.ProtoSecondCardBOService;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/4 19:52
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/second-card")
public class ProtoSecondCardController {

    @Autowired
    private ProtoSecondCardBOService secondCardBOService;
    @Autowired
    private RequestContextConverter requestContextConverter;

    @PostMapping(value = "/query")
    public BaseResp query(@RequestBody RequestContext request){
        String userId = request.getUserInfo().get("user_id");
        String funId = request.getFunID();
        return BaseResp.success(secondCardBOService.query(requestContextConverter.reqToSecondCardDTO(request), userId, funId));
    }

    @PostMapping("/createApplySecond")
    public BaseResp createApplySecond(@RequestBody RequestContext request){
        return BaseResp.success(secondCardBOService.createApplySecond(requestContextConverter.reqToApplySecondDTO(request)
                ,requestContextConverter.reqToUserInfo(request)));
    }

    @PostMapping("/createData")
    public BaseResp createData(@RequestBody RequestContext request){
        secondCardBOService.createData(requestContextConverter.secondCardCreateDataReqToDTO(request)
                ,requestContextConverter.reqToUserInfo(request));
        return BaseResp.success();
    }

    @PostMapping("/deleteData")
    public BaseResp deleteData(@RequestBody RequestContext request){
        List<ApplySecondDTO> dto = requestContextConverter.secondCardReqsToDto(request);
        UserInfo userInfo = requestContextConverter.reqToUserInfo(request);
        secondCardBOService.deleteData(dto, userInfo);
        return BaseResp.success();
    }
}
