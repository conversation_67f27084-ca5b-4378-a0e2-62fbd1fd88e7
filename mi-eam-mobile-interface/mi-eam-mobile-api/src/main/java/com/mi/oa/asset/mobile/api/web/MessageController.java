package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.converter.MessageConverter;
import com.mi.oa.asset.mobile.api.model.req.EmailReq;
import com.mi.oa.asset.mobile.api.model.req.LarkReq;
import com.mi.oa.asset.mobile.application.service.MessageService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 飞书和邮件通知
 * @date 2021/12/7 11:17
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/message")
public class MessageController {

    @Autowired
    private MessageConverter converter;

    @Autowired
    private MessageService messageService;

    @PostMapping("/email")
    public BaseResp sendEmail(@Valid @RequestBody EmailReq emailReq) {
        messageService.sendEmail(converter.reqToDTO(emailReq));
        return BaseResp.success();
    }

    @PostMapping("/lark")
    public BaseResp sendLark(@Valid @RequestBody LarkReq req) {
        messageService.sendLark(converter.reqToDTO(req));
        return BaseResp.success();
    }

    @PostMapping("/sms")
    public BaseResp sendLark(@RequestParam List<String> tels,
                             @RequestParam String content) {
        messageService.sendSMS(tels, content);
        return BaseResp.success();
    }
}
