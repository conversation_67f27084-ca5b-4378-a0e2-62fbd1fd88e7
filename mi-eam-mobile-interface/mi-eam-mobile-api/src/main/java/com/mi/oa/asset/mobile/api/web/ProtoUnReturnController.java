package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.application.service.ProtoUnReturnBOService;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysAttachPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoOrderPayService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/2/25 14:26
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/unreturn")
public class ProtoUnReturnController {

    @Autowired
    private ProtoOrderPayService protoOrderPayService;

    @Autowired
    private ProtoUnReturnBOService protoUnReturnBOService;

    @PostMapping(value = "/scanQry")
    public BaseResp unReturnScanQry(@RequestBody RequestContext request){
        String scanResult = request.getRequestValue("scan_result");
        return BaseResp.success(protoUnReturnBOService.unReturnScanQry(scanResult));
    }

    @GetMapping(value = "/getUnreturnByCode")
    public BaseResp getUnReturnById(String unreturnCode){
        return BaseResp.success(protoOrderPayService.getByUnturnCode(unreturnCode));
    }

    @GetMapping(value = "/unreturnSubmit")
    public BaseResp unreturnSubmit(String unreturnCode){
        protoUnReturnBOService.unreturnSubmit(unreturnCode);
        return BaseResp.success();
    }

    @PostMapping(value = "/deletedFile")
    public BaseResp deletedFile(@RequestBody SysAttachPO attachDTO){
        protoUnReturnBOService.deletedFile(attachDTO.getDataId(), attachDTO.getAttachPath(), attachDTO.getTableName());
        return BaseResp.success();
    }

    @PostMapping(value = "/uploadFile")
    public BaseResp uploadFile(@RequestBody List<SysAttachPO> attachList){
        protoUnReturnBOService.uploadFile(attachList);
        return BaseResp.success();
    }

}
