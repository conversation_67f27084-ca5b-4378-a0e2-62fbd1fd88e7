package com.mi.oa.asset.mobile.api.web;


import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 翻译服务demo
 * @date 2021/12/7 10:39
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/demo")
public class I18NDemoController {

    @GetMapping("/list")
    public BaseResp list() {
        throw new BizException(ApplicationErrorCodeEnum.APPLICATION_UNKNOWN_ERROR);
    }

}
