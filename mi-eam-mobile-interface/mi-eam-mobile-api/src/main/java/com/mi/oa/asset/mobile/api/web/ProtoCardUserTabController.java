package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.converter.RequestContextConverter;
import com.mi.oa.asset.mobile.application.service.ProtoCardUserTabBOService;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 个人工程机
 *
 * <AUTHOR>
 * @date 2022/2/23 16:52
 */
@Slf4j
@RestController
@RequestMapping("/v1/cardUserTab")
public class ProtoCardUserTabController {

    @Autowired
    private ProtoCardUserTabBOService protoCardUserTabBOService;

    @Autowired
    private RequestContextConverter requestContextConverter;

    @PostMapping(value = "/snTraceQuery")
    public BaseResp snTraceQuery(@RequestBody RequestContext request){
        return BaseResp.success(protoCardUserTabBOService.snTraceQuery(requestContextConverter.reqToCardSnTraceDTO(request)));
    }

}
