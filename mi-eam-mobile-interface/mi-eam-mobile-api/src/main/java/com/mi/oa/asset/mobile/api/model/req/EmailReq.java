package com.mi.oa.asset.mobile.api.model.req;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmailReq {
    //email 收件人
    @NotEmpty(message = "收件人不能为空")
    private List<String> emails;
    //标题
    @NotBlank(message = "标题不能为空")
    private String title;
    //内容
    @NotBlank(message = "内容不能为空")
    private String content;
    //抄送人
    private List<String> ccEmails;
}
