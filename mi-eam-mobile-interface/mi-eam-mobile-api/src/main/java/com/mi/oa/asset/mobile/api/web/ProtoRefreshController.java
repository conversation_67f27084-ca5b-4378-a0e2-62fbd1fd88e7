package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.application.service.ProtoCardBOService;
import com.mi.oa.asset.mobile.application.service.ProtoOrderBOService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInnerPurchasePlanService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/3/23
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/refresh")
public class ProtoRefreshController {

    @Autowired
    private ProtoCardBOService protoCardBOService;

    @Autowired
    private ProtoInnerPurchasePlanService protoInnerPurchasePlanService;

    @Autowired
    private ProtoOrderBOService protoOrderBOService;

    @Autowired
    private ProtoCardService protoCardService;

    /**
     * 刷台账数据
     * @return
     */
    @GetMapping(value = "/cardOldData")
    public BaseResp refreshOldData() {
        protoCardBOService.refreshOldData();
        return BaseResp.success();
    }

    /**
     * 刷台账数据
     * @return
     */
    @GetMapping(value = "/refreshEntryDate")
    public BaseResp refreshEntryDate() {
        protoCardBOService.refreshEntryDate();
        return BaseResp.success();
    }

    /**
     * 刷历史内购计划数据
     * @param orderList
     * @return
     */
    @PostMapping(value = "/calculatePurchase")
    public BaseResp calculatePurchase(@RequestBody List<String> orderList) {
        protoInnerPurchasePlanService.calculatePurchase(orderList);
        return BaseResp.success();
    }

    /**
     * 更新对账单数据
     * @param dateStr
     * @return
     */
    @GetMapping(value = "/checkAccount")
    public BaseResp checkAccount(String dateStr, String mrpType) {
        protoOrderBOService.checkAccount(dateStr, mrpType);
        return BaseResp.success();
    }

    /**
     * 同步订单手续费
     * @param mrpType
     * @param dateStr
     * @return
     */
    @GetMapping(value = "/synChargeAmount")
    public BaseResp synChargeAmount(String mrpType, String dateStr) {
        protoOrderBOService.synChargeAmount(mrpType, dateStr);
        return BaseResp.success();
    }

    /**
     * 刷所有初始部门编码
     * @return
     */
    @GetMapping(value = "/refreshAllApplyDeptCode")
    public BaseResp refreshAllApplyDeptCode() {
        protoCardBOService.refreshAllApplyDeptCode();
        return BaseResp.success();
    }

    /**
     * 刷所有初始部门编码
     * @return
     */
    @GetMapping(value = "/repairAllSkuName")
    public BaseResp repairAllSkuName() {
        protoCardService.repairAllSkuName();
        return BaseResp.success();
    }
}
