package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.converter.RequestContextConverter;
import com.mi.oa.asset.mobile.application.dto.apply.ApplyDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmCallbackDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.MobileProcessBaseBizDTO;
import com.mi.oa.asset.mobile.application.dto.delay.DelayDTO;
import com.mi.oa.asset.mobile.application.service.ProtoDelayBOService;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工程机延期归还
 *
 * <AUTHOR>
 * @date 2022/2/18 16:27
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/delay")
public class ProtoDelayController {

    public static final String KEYID = "keyid";

    @Autowired
    private RequestContextConverter requestContextConverter;

    @Autowired
    private ProtoDelayBOService protoDelayBOService;

    @PostMapping(value = "/save")
    public BaseResp save(@RequestBody RequestContext request) {
        DelayDTO dto = requestContextConverter.reqToDelayDTO(request);
        return BaseResp.success(protoDelayBOService.save(dto, request));
    }

    @PostMapping(value = "/audit")
    public BaseResp beforeAudit(@RequestBody RequestContext request) {
        String[] keyIds = request.getRequestValues(KEYID);
        return BaseResp.success(protoDelayBOService.audit(keyIds, request));
    }

    @PostMapping(value = "/pushBpm")
    public BaseResp pushBpm(@RequestBody RequestContext request) {
        String[] keyIds = request.getRequestValues(KEYID);
        return BaseResp.success(protoDelayBOService.pushBpm(keyIds));
    }

    @PostMapping(value = "/interruptBpm")
    public BaseResp interruptBpm(@RequestBody RequestContext request) {
        String[] keyIds = request.getRequestValues(KEYID);
        return BaseResp.success(protoDelayBOService.interruptBpm(keyIds, requestContextConverter.reqToUserInfo(request)));
    }

    @PostMapping(value = "/bpmCallback")
    public BaseResp bpmCallback(@RequestBody BpmCallbackDTO<MobileProcessBaseBizDTO> bpmCallbackDTO) {
        return BaseResp.success(protoDelayBOService.bpmCallback(bpmCallbackDTO));
    }

    @PostMapping(value = "/scanQry")
    public BaseResp delayBackScanQry(@RequestBody RequestContext request) {
        String scanResult = request.getRequestValue("scan_result");
        return BaseResp.success(protoDelayBOService.delayBackScanQry(scanResult));
    }
}
