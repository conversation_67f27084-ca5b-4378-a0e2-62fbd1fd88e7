package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.model.req.PsReq;
import com.mi.oa.asset.mobile.api.model.vo.PsVo;
import com.mi.oa.asset.mobile.common.enums.RedisCachePrefixKeyEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/6
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/ps")
public class PsController {

    @Autowired
    private ProtoCardService protoCardService;

    /**
     * 离职信息提供给PS
     * @return
     */
    @PostMapping("/leaveCheck")
    public BaseResp leaveCheck(@RequestBody PsReq request) {
        String emplid = request.getEmplid();
        if (StringUtils.isBlank(emplid)){
            return BaseResp.error("工号不能为空", null);
        }
        PsVo response = new PsVo();
        Integer count = protoCardService.countByEmpCode(emplid);
        if (count > 0){
            RedisUtils.sAdd(RedisCachePrefixKeyEnum.PS_LEAVE_CHECK.getPrefixKey(), emplid);
        }
        response.setPhoneNum(count);
        response.setPass(count == 0);
        return BaseResp.success(response);
    }
}
