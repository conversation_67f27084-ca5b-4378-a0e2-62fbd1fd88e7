package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.application.service.ProtoAsnOrderBOService;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/2/16
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/asnOrder")
public class ProtoAsnOrderController {

    @Autowired
    private ProtoAsnOrderBOService protoAsnOrderBOService;

    /**
     * 无ASN收货
     * @param request
     * @return
     */
    @GetMapping(value = "/scan")
    public BaseResp scan(@RequestBody RequestContext request){
        Map<String, String> scanMap = protoAsnOrderBOService.scan(request);
        return BaseResp.success(scanMap);
    }
}
