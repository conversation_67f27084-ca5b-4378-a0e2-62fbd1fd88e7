package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.converter.RequestContextConverter;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmCallbackDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmProcessCompletedDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.MobileProcessBaseBizDTO;
import com.mi.oa.asset.mobile.application.service.ProtoBackBOService;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工程机归还
 *
 * <AUTHOR>
 * @date 2022/2/17 15:04
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/back")
public class ProtoBackController {

    public static final String KEYID = "keyid";

    @Autowired
    private RequestContextConverter requestContextConverter;

    @Autowired
    private ProtoBackBOService protoBackBOService;

    /**
     * 业务类型修改清空明细
     *
     * @return
     */
    @PostMapping("/clearDet")
    public BaseResp backClearDet(@RequestBody RequestContext request) {
        String keyId = request.getRequestValue("keyid");
        String mrpType = request.getRequestValue("mrpType");
        protoBackBOService.clearDet(keyId,mrpType);
        return BaseResp.success();
    }

    @PostMapping(value = "/pushBpm")
    public BaseResp pushBpm(@RequestBody RequestContext request) {
        String[] keyIds = request.getRequestValues(KEYID);
        for (String keyId : keyIds){
            protoBackBOService.pushBpm(keyId);
        }
        return BaseResp.success();
    }

    @PostMapping(value = "/interruptBpm")
    public BaseResp interruptBpm(@RequestBody RequestContext request) {
        String[] keyIds = request.getRequestValues(KEYID);
        return BaseResp.success(protoBackBOService.interruptBpm(keyIds, requestContextConverter.reqToUserInfo(request)));
    }

    @PostMapping(value = "/bpmCallback")
    public BaseResp bpmCallback(@RequestBody BpmCallbackDTO<MobileProcessBaseBizDTO> bpmCallbackDTO) {
        return BaseResp.success(protoBackBOService.bpmCallback(bpmCallbackDTO));
    }

    @PostMapping(value = "/bpmCallbackV3")
    public BaseResp bpmCallbackV3(@RequestBody BpmProcessCompletedDTO param) {
        return BaseResp.success(protoBackBOService.bpmCallbackV3(param));
    }

    @PostMapping(value = "/withDraw")
    public BaseResp withDraw(@RequestBody RequestContext request) {
        String[] keyIds = request.getRequestValues(KEYID);
        return BaseResp.success(protoBackBOService.withDraw(keyIds, requestContextConverter.reqToUserInfo(request)));
    }

    @PostMapping(value = "/repairEmpCode")
    public BaseResp repairEmpCode() {
        return BaseResp.success(protoBackBOService.repairEmpCode());
    }
}
