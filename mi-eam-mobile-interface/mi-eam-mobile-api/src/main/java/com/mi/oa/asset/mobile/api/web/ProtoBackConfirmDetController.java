package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.converter.RequestContextConverter;
import com.mi.oa.asset.mobile.application.service.ProtoBackConfirmBOService;
import com.mi.oa.asset.mobile.application.service.ProtoBackConfirmDetBOService;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/24 17:22
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/back/confirm/det")
public class ProtoBackConfirmDetController {
    public static final String KEYID = "keyid";
    public static final String IN_ID = "in_id";
    public static final String TEMP_IS_COMPLETE = "temp.is_complete";
    public static final String KEYWORD = "keyword";
    @Autowired
    private RequestContextConverter requestContextConverter;
    @Autowired
    private ProtoBackConfirmDetBOService protoBackConfirmDetBOService;

    @PostMapping(value = "/setIsComplete")
    public BaseResp setIsComplete(@RequestBody RequestContext request){
        String inId = request.getRequestValue(IN_ID);
        String isComplete = request.getRequestValue(TEMP_IS_COMPLETE);
        String[] detIds = request.getRequestValues(KEYID);
        return BaseResp.success(protoBackConfirmDetBOService.setIsComplete(inId,isComplete,detIds));
    }

    @PostMapping(value = "/scanConfirm")
    public BaseResp scanConfirm(@RequestBody RequestContext request){
        String inId = request.getRequestValue(IN_ID);
        String keyword = request.getRequestValue(KEYWORD);
        return BaseResp.success(protoBackConfirmDetBOService.scanConfirm(inId,keyword));
    }
}
