package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.application.dto.ProtoPurchaseDTO;
import com.mi.oa.asset.mobile.application.service.ProtoProjectCfgBOService;
import com.mi.oa.asset.mobile.common.enums.PriceTypeEnum;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 项目配置类
 *
 * <AUTHOR>
 * @date 2022/1/20 16:41
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/projectCfg")
public class ProtoProjectCfgController {

    @Autowired
    private ProtoProjectCfgBOService protoProjectCfgBOService;

    /**
     * 获取处置 处置依据、参考金额
     * @param deviceCodeList
     * @return
     */
    @PostMapping(value = "/getDisposeDamages")
    public BaseResp getDisposeDamages(@RequestBody List<String> deviceCodeList){
        return BaseResp.success(protoProjectCfgBOService.getDisposeDamages(deviceCodeList));
    }

    /**
     * 内购 处置依据、参考金额等
     * @param purchaseList
     * @return
     */
    @PostMapping(value = "/getPurchaseDesc")
    public BaseResp getPurchaseDesc(@RequestBody List<ProtoPurchaseDTO> purchaseList){
        return BaseResp.success(protoProjectCfgBOService.getPurchaseDesc(purchaseList));
    }

}
