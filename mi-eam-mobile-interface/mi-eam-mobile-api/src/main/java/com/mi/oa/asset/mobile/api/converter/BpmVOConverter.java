package com.mi.oa.asset.mobile.api.converter;

import com.mi.oa.asset.mobile.api.model.req.InterruptProcessReq;
import com.mi.oa.asset.mobile.api.model.req.SubmitProcessReq;
import com.mi.oa.asset.mobile.api.model.vo.SubmitResultVo;
import com.mi.oa.asset.mobile.application.dto.bpm.InterruptProcessDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.SubmitProcessDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.SubmitResultDTO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @description: bpm相关属性转换
 * @date 2021/12/7 14:32
 */
@Mapper(componentModel = "spring")
public interface BpmVOConverter {

    InterruptProcessDTO reqToDTO(InterruptProcessReq req);

    SubmitProcessDTO submitReqToDTO(SubmitProcessReq req);

    SubmitResultVo submitResultDTOToVo(SubmitResultDTO dto);
}
