package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.converter.RequestContextConverter;
import com.mi.oa.asset.mobile.application.dto.apply.ApplyDTO;
import com.mi.oa.asset.mobile.application.service.ProtoBackBOService;
import com.mi.oa.asset.mobile.application.service.ProtoBackConfirmBOService;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/24 17:22
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/back/confirm")
public class ProtoBackConfirmController {
    @Autowired
    private RequestContextConverter requestContextConverter;
    @Autowired
    private ProtoBackConfirmBOService protoBackConfirmBOService;

    @PostMapping(value = "/audit")
    public BaseResp audit(@RequestBody RequestContext request){
        String[] keyIds = request.getRequestValues(CommonConstant.KEY_ID);
        return BaseResp.success(protoBackConfirmBOService.audit(keyIds,requestContextConverter.reqToUserInfo(request),request));
    }

    @PostMapping(value = "/scanConfirm")
    public BaseResp scanConfirm(@RequestBody RequestContext request){
        String scanResult = request.getRequestValue(CommonConstant.SCAN_RESULT);
        return BaseResp.success(protoBackConfirmBOService.scanConfirm(scanResult,requestContextConverter.reqToUserInfo(request)));
    }
}
