package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/3/16 17:04
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/redis")
public class RedisKeyController {

    @Autowired
    private CommonService commonService;

    @PostMapping("/key")
    public BaseResp setKeyVal(@RequestParam String key,@RequestParam Long val){
        RedisUtils.setIncr(key, val);
        return BaseResp.success(val);
    }

}
