package com.mi.oa.asset.mobile.api.converter;

import com.mi.oa.asset.mobile.api.model.req.BpmCallbackReq;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmCallbackDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.MobileProcessBaseBizDTO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface TranConverter {
    BpmCallbackDTO<MobileProcessBaseBizDTO> reqToDTO(BpmCallbackReq<MobileProcessBaseBizDTO> req);
}
