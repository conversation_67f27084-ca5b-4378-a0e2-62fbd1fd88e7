package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.application.service.ProtoInBoService;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/2/24 10:03
 */
@WebLog
@Slf4j
@RestController
@RequestMapping
public class ProtoInController {

    @Autowired
    private ProtoInBoService protoInBoService;
    /**
     * 提交转移申请
     *
     * @return
     */
    @PostMapping("/v1/in/scanQry")
    public BaseResp inScanQry(@RequestBody RequestContext request) {
        String scanResult = request.getRequestValue("scan_result");
        return BaseResp.success(protoInBoService.inScanQry(scanResult));
    }
}
