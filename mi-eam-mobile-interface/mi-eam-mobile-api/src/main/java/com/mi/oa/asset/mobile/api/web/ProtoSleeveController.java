package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.converter.RequestContextConverter;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmCallbackDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmProcessCompletedDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.MobileProcessBaseBizDTO;
import com.mi.oa.asset.mobile.application.service.ProtoSleeveBOService;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoSleeveService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工程机保密套拆除
 *
 * <AUTHOR>
 * @date 2022/2/8 14:27
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/sleeve")
public class ProtoSleeveController {

    public static final String KEYID = "keyid";

    @Autowired
    private RequestContextConverter requestContextConverter;

    @Autowired
    private ProtoSleeveBOService protoSleeveBOService;

    @Autowired
    private ProtoSleeveService protoSleeveService;

    @PostMapping(value = "/audit")
    public BaseResp beforeAudit(@RequestBody RequestContext request) {
        String[] keyIds = request.getRequestValues(KEYID);
        return BaseResp.success(protoSleeveBOService.audit(keyIds, requestContextConverter.reqToUserInfo(request), request));
    }

    @PostMapping(value = "/pushBpm")
    public BaseResp pushBpm(@RequestBody RequestContext request) {
        String[] keyIds = request.getRequestValues(KEYID);
        return BaseResp.success(protoSleeveBOService.pushBpm(keyIds));
    }

    @PostMapping(value = "/interruptBpm")
    public BaseResp interruptBpm(@RequestBody RequestContext request) {
        String[] keyIds = request.getRequestValues(KEYID);
        return BaseResp.success(protoSleeveBOService.interruptBpm(keyIds, requestContextConverter.reqToUserInfo(request)));
    }

    @PostMapping(value = "/bpmCallback")
    public BaseResp bpmCallback(@RequestBody BpmCallbackDTO<MobileProcessBaseBizDTO> bpmCallbackDTO) {
        return BaseResp.success(protoSleeveBOService.bpmCallback(bpmCallbackDTO));
    }
    @PostMapping(value = "/bpmCallbackV3")
    public BaseResp bpmCallbackV3(@RequestBody BpmProcessCompletedDTO param) {
        return BaseResp.success(protoSleeveService.bpmCallback(param));
    }

    @PostMapping(value = "/det/scanConfirm")
    public BaseResp sleeveDetScanConfirm(@RequestBody RequestContext request) {
        String sleeveId = request.getRequestValue("sleeve_id");
        String keyword = request.getRequestValue("keyword");
        protoSleeveBOService.sleeveDetScanConfirm(sleeveId,keyword,requestContextConverter.reqToUserInfo(request));
        return BaseResp.success();
    }

}
