package com.mi.oa.asset.mobile.api.model.req;

import com.mi.oa.infra.oaucf.core.dto.DTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 *
 * <AUTHOR>
 * @date 2022/1/4 14:53
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SecondCardReq extends DTO{
    private String fieldNames;
    private String fieldConds;
    private String fieldValues;
    @NotNull(message = "start不能为空")
    private Integer start;
    @NotNull(message = "limit不能为空")
    private Integer limit;
}
