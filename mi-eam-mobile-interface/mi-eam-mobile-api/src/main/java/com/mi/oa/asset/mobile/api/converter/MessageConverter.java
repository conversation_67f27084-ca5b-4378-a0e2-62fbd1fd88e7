package com.mi.oa.asset.mobile.api.converter;

import com.mi.oa.asset.mobile.api.model.req.EmailReq;
import com.mi.oa.asset.mobile.api.model.req.LarkReq;
import com.mi.oa.asset.mobile.application.dto.message.EmailDTO;
import com.mi.oa.asset.mobile.application.dto.message.LarkDTO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface MessageConverter {

    EmailDTO reqToDTO(EmailReq req);

    LarkDTO reqToDTO(LarkReq req);
}
