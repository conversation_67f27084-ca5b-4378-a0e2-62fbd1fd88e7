package com.mi.oa.asset.mobile.api.model.req;

import com.mi.oa.asset.mobile.application.dto.bpm.MobileProcessBaseBizDTO;
import com.mi.oa.asset.mobile.common.enums.MobileProcessEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description: 流程发起
 * @date 2021/12/7 16:11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SubmitProcessReq {
    @NotNull(message = "发起人不能为空")
    private String startUser;
    @NotNull(message = "流程类型不能为空")
    private MobileProcessEnum mobileProcessEnum;

    private MobileProcessBaseBizDTO mobileProcessBaseBizDTO;
}
