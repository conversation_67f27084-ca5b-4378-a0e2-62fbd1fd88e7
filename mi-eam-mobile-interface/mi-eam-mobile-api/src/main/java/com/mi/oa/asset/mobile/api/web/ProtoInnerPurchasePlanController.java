package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInnerPurchasePlanService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 项目配置类
 *
 * <AUTHOR>
 * @date 2022/1/20 16:41
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/inner-purchase")
public class ProtoInnerPurchasePlanController {

    @Autowired
    private ProtoInnerPurchasePlanService protoInnerPurchasePlanService;

    /**
     * 检查内购订单
     * @return
     */
    @PostMapping(value = "/check")
    public BaseResp checkInnerPurchaseOrder(String planCode){
        protoInnerPurchasePlanService.checkInnerPurchaseOrder(planCode);
        return BaseResp.success();
    }

    /**
     * 查询内购计划审批状态
     * @return
     */
    @PostMapping(value = "/query-approval-status")
    public BaseResp queryApprovalStatus(@RequestParam("planCode") String planCode) {
        return BaseResp.success(protoInnerPurchasePlanService.queryApprovalStatus(planCode));
    }
}
