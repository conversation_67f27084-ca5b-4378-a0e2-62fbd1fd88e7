package com.mi.oa.asset.mobile.api.model.req;

import com.mi.oa.asset.mobile.common.enums.MobileProcessEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2021/12/7 11:33
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InterruptProcessReq {
    @NotNull(message = "businessKey不能为空")
    private String businessKey;
    @NotNull(message = "发起人不能为空")
    private String startUser;
    @NotNull(message = "流程类型不能为空")
    private MobileProcessEnum mobileProcessEnum;
}
