package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.application.service.ProtoThirdUserBOService;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 第三方人员配置
 *
 * <AUTHOR>
 * @date 2022/1/24 18:46
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/thirdUser")
public class ProtoThirdUserController {

    @Autowired
    private ProtoThirdUserBOService protoThirdUserBOService;

    @PostMapping(value = "/afterAudit")
    public BaseResp afterAudit(@RequestBody RequestContext request) {
        protoThirdUserBOService.afterAudit(request);
        return BaseResp.success();
    }

}
