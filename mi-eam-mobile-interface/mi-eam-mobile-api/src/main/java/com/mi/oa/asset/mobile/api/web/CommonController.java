package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.EamFunEventService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/7
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/common")
public class CommonController {

    @Autowired
    private CommonService commonService;

    @Autowired
    private EamFunEventService eamFunEventService;

    /**
     * 通用修改接口
     *
     * @param requestContext
     * @return
     */
    @PostMapping("/commonEvent")
    public BaseResp updateEvent(@RequestBody RequestContext requestContext) {
        return BaseResp.success(commonService.commonSave(requestContext));
    }

    @PostMapping("/commonRequest")
    public BaseResp commonRequest(@RequestBody RequestContext req) {
        return BaseResp.success(eamFunEventService.invokeFunEvent(req));
    }

}
