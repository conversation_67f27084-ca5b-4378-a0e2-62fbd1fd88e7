/*
 * Copyright (c) 2020. XiaoMi Inc.All Rights Reserved
 */
package com.mi.oa.asset.mobile.api;

import com.mi.oa.asset.x5.provider.EnableX5Filter;
import com.mi.oa.infra.oaucf.EnableOACache;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 启动类
 *
 * <AUTHOR>
 * @date 2021/5/21 10:24 上午
 */
@EnableDiscoveryClient
@SpringBootApplication
@EnableFeignClients("com.mi.oa.asset.mobile.infra.remote.sdk")
@EnableX5Filter
@ComponentScan(basePackages = {"com.mi.oa"})
@EnableOACache
@EnableAsync
public class MobileApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(MobileApiApplication.class, args);
    }

}
