package com.mi.oa.asset.mobile.api.converter;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.mi.oa.asset.mobile.application.dto.CommonConditionDTO;
import com.mi.oa.asset.mobile.application.dto.apply.ApplyDTO;
import com.mi.oa.asset.mobile.application.dto.delay.DelayDTO;
import com.mi.oa.asset.mobile.application.dto.tran.TranReqDTO;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.dto.secondcard.ApplySecondDTO;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @date 2022/1/13 17:16
 */
@Service
public class RequestContextConverter {

    private static final ObjectMapper objMapper = new ObjectMapper();
    static {
        objMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public List<ApplySecondDTO> secondCardReqsToDto(RequestContext source){
        List<ApplySecondDTO> res = new ArrayList<>();
        if (Objects.isNull(source)) {
            return res;
        }
        String[] mrpTypes = source.getRequestValues("mrp_type");
        String[] projectCodes = source.getRequestValues("project_code");
        String[] stageNames = source.getRequestValues("stage_name");
        String[] skuCodes = source.getRequestValues("sku_code");
        String[] isCompletes = source.getRequestValues("is_complete");
        Map<String, String> userInfo = source.getUserInfo();
        for(int i=0;null != projectCodes && i<projectCodes.length;i++){
            ApplySecondDTO dto = ApplySecondDTO.builder().mrpType(mrpTypes[i]).projectCode(projectCodes[i])
                    .stageName(stageNames[i]).skuCode(skuCodes[i]).isComplete(isCompletes[i]).build();
            res.add(dto);
        }
        return res;
    }

    public UserInfo reqToUserInfo(RequestContext source){
        Map<String, String> userInfo = source.getUserInfo();
        return objMapper.convertValue(userInfo, UserInfo.class);
    }

    public ApplySecondDTO reqToApplySecondDTO(RequestContext source){
        return ApplySecondDTO.builder()
                .mrpType(source.getRequestValue("mrp_type"))
                .projectCode(source.getRequestValue("project_code"))
                .stageName(source.getRequestValue("stage_name"))
                .skuCode(source.getRequestValue("sku_code"))
                .skuName(source.getRequestValue("sku_name"))
                .houseCode(source.getRequestValue("house_code"))
                .isComplete(source.getRequestValue("is_complete"))
                .goodsId(source.getRequestValue("goods_id"))
                .build();
    }

    public ApplySecondDTO secondCardCreateDataReqToDTO(RequestContext source){
        return ApplySecondDTO.builder()
                .projectCode(source.getRequestValue("temp.project_code"))
                .stageName(source.getRequestValue("temp.stage_name"))
                .skuCode(source.getRequestValue("temp.sku_code"))
                .skuName(source.getRequestValue("temp.sku_name"))
                .houseCode(source.getRequestValue("temp.house_code"))
                .houseName(source.getRequestValue("temp.house_name"))
                .num(Integer.parseInt(source.getRequestValue("temp.num")))
                .isComplete(source.getRequestValue("temp.is_complete")).build();
    }

    public CommonConditionDTO reqToSecondCardDTO(RequestContext source){
        return CommonConditionDTO.builder()
                .fieldNames(source.getRequestValue("field_names"))
                .fieldValues(source.getRequestValue("field_values"))
                .fieldConds(source.getRequestValue("field_conds"))
                .start(Integer.parseInt(source.getRequestValue("start")))
                .limit(Integer.parseInt(source.getRequestValue("limit"))).build();
    }

    public ApplyDTO reqToApplyDTO(RequestContext source){
        return ApplyDTO.builder()
                .keyId(source.getRequestValue("keyid"))
                .projectCode(source.getRequestValue("proto_apply.project_code"))
                .stageName(source.getRequestValue("proto_apply.stage_name"))
                .isOther(source.getRequestValue("proto_apply.is_other"))
                .userCode(source.getRequestValue("proto_apply.user_code"))
                .userInfo(reqToUserInfo(source))
                .build();
    }

    public DelayDTO reqToDelayDTO(RequestContext source){
        return DelayDTO.builder()
                .keyId(source.getRequestValue("keyid"))
                .projectCode(source.getRequestValue("proto_delay.project_code"))
                .delayCode(source.getRequestValue("proto_delay.delay_code"))
                .applyUserCode(source.getRequestValue("proto_delay.apply_user_code"))
                .userInfo(reqToUserInfo(source))
                .build();
    }

    public CommonConditionDTO reqToCardSnTraceDTO(RequestContext source){
        return CommonConditionDTO.builder()
                .fieldNames(source.getRequestValue("field_names"))
                .fieldConds(source.getRequestValue("field_conds"))
                .fieldValues(source.getRequestValue("field_values"))
                .start(Integer.parseInt(source.getRequestValue("start")))
                .limit(Integer.parseInt(source.getRequestValue("limit"))).build();
    }

    public TranReqDTO reqToTranDTO(RequestContext source){
        return TranReqDTO.builder()
                .fieldNames(source.getRequestValue("field_names"))
                .fieldValues(source.getRequestValue("field_values"))
                .fieldConds(source.getRequestValue("field_conds"))
                .whereSql(source.getRequestValue(CommonConstant.WHERE_SQL))
                .userCode(source.getUserInfo().get("user_id"))
                .start(Integer.parseInt(source.getRequestValue("start")))
                .limit(Integer.parseInt(source.getRequestValue("limit"))).build();
    }
}
