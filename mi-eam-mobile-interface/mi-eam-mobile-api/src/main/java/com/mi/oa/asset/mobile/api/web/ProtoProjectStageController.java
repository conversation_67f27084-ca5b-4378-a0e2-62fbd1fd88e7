package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.infra.dto.mier.MaterialDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectStageService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Copyright (c) 2022 XiaoMi Inc.All Rights Reserved.
 * Description: your description
 *
 * <AUTHOR>
 * @date 2022/11/14 17:23
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/proto-project")
public class ProtoProjectStageController {

    @Autowired
    private ProtoProjectStageService protoProjectStageService;

    @PostMapping("/sync-tv-data")
    public BaseResp<List<MaterialDTO>> syncTvData(@RequestBody List<MaterialDTO> dtoList) {
        return BaseResp.success(protoProjectStageService.syncTvData(dtoList));
    }
}
