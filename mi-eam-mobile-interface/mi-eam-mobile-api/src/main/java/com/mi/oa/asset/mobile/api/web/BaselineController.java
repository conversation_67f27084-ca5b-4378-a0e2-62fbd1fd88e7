package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.converter.RequestContextConverter;
import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmProcessCompletedDTO;
import com.mi.oa.asset.mobile.application.service.BaselineBizService;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.skylog.aop.WebLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/3/1 16:05
 */
@WebLog
@Slf4j
@RestController
@RequestMapping("/v1/baseline")
public class BaselineController {

    public static final String KEYID = "keyid";
    @Autowired
    private BaselineBizService baselineBizService;
    @Autowired
    private RequestContextConverter requestContextConverter;

    /**
     * 提交基线变更
     *
     * @param req
     * @return 返回业务id
     */
    @PostMapping("/record/submit")
    public BaseResp submit(@RequestBody RequestContext req) {
        String keyId = req.getRequestValue(KEYID);
        baselineBizService.submit(keyId);
        return BaseResp.success();
    }

    /**
     * 提交基线变更中断
     *
     * @param req
     * @return 返回业务id
     */
    @PostMapping("/record/interruptBpm")
    public BaseResp interruptBpm(@RequestBody RequestContext req) {
        String keyId = req.getRequestValue(KEYID);
        UserInfo userInfo = requestContextConverter.reqToUserInfo(req);
        String startUser = userInfo.getUserCode();
        baselineBizService.interruptBpm(keyId, startUser);
        return BaseResp.success();
    }

    /**
     * 基线变更BPM v3回调事件
     *
     * @param param
     * @return 成功与否
     */
    @PostMapping(value = "/record/bpmCallbackV3")
    public BaseResp bpmCallbackV3(@RequestBody BpmProcessCompletedDTO param) {
        return BaseResp.success(baselineBizService.bpmCallbackV3(param));
    }

}
