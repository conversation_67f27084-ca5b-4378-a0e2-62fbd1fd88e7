package com.mi.oa.asset.mobile.api.errorcode;

import com.mi.oa.infra.oaucf.core.exception.InterfaceErrorCode;

/**
 * create by roger
 */
public enum ApiErrorCodeEnum implements InterfaceErrorCode {

    /**
     * 应用层未知错误
     */
    UNKNOWN_ERROR(1, "{{{应用层未知错误}}}"),
    PARAMETER_NULL_ERROR(2, "参数为空"),
    ;

    /**
     * 具体错误码
     */
    private int errCode;
    /**
     * 描述
     */
    private String errDesc;

    @Override
    public int getBizCode() {
        return 0;
    }

    @Override
    public int getErrorCode() {
        return errCode;
    }

    @Override
    public String getErrDesc() {
        return this.errDesc;
    }

    /**
     * 构造方法
     *
     * @param errCode
     * @param errDesc
     */
    ApiErrorCodeEnum(int errCode, String errDesc) {
        this.errCode = errCode;
        this.errDesc = errDesc;
    }


}
