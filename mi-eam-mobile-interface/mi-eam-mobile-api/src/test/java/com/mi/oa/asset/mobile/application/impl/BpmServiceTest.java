package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.application.dto.bpm.*;
import com.mi.oa.asset.mobile.application.service.BpmService;
import com.mi.oa.asset.mobile.common.enums.BpmCallbackStatusEnum;
import com.mi.oa.asset.mobile.common.enums.MobileProcessEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @description: bpm流程单元测试
 * @date 2021/12/8 14:46
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
class BpmServiceTest {

    @Autowired
    private BpmService bpmService;

    private MobileProcessBaseBizDTO mobileProcessBaseBizDTO;


    @BeforeEach
    void setUp() {
        VaraibleDTO varaibleDTO = VaraibleDTO
                .builder()
                .projectManager("dengjuzhen")
                .build();

        MobileClaimProcessBizDTO.Item item = MobileClaimProcessBizDTO.Item.builder()
                .sn("2")
                .miMaterialCode("902K81AP1004")
                .desc("机头组件_中国_8GB_128GB_P10_白色_K81A")
                .applyCount(25)
                .removeShellCount(10)
                .remark("研发需要领取，需申请拆除10台保密套").build();

        MobileClaimProcessBizDTO phoneClaim = MobileClaimProcessBizDTO.builder()
                .project("K81A-CN")
                .isMrp("是")
                .stage("P10")
                .remark("员工填写的备注信息")
                .projectNo("员工填写的projectNo")
                .userInfo("caoxinyi")
                .item(item)
                .build();
        mobileProcessBaseBizDTO = MobileProcessBaseBizDTO.builder()
                .mobileClaimProcessBizDTO(phoneClaim)
                //.subProcess(MobileProcessBaseBizDTO.SubProcessEnum.PROCESS_ONE)
                .varaibleDTO(varaibleDTO)
                .build();
    }

    @Test
    void bpmCallback() {
        BpmCallbackDTO dto = new BpmCallbackDTO();
        dto.setStatus(BpmCallbackStatusEnum.END);
        Map<String, Object> variable = new HashMap();
        variable.put("processKey","eam_mobile_pubBase");
        dto.setVariables(variable);
        bpmService.bpmCallback(dto);
    }

    @Test
    void interruptProcess() {
        InterruptProcessDTO interruptProcessDTO = InterruptProcessDTO.builder()
                .mobileProcessEnum(MobileProcessEnum.PROCESS_KEY_MOBILE_CLAIM)
                .businessKey("e7b1a78b-a50f-4cd6-8725-6bea88c63d09")
                .startUser("caoxinyi")
                .build();
        Exception exception = null;
        try{
            bpmService.interruptProcess(interruptProcessDTO);
        }catch (Exception e){
            exception = e;
        }
        Assertions.assertNull(exception);
    }

    @Test
    void submitProcess() {
        VaraibleDTO varaibleDTO = VaraibleDTO
                .builder()
                .projectManager("dengjuzhen")
                .build();
        MobileClaimProcessBizDTO.Item item = MobileClaimProcessBizDTO.Item.builder()
                .sn("2")
                .miMaterialCode("902K81APwwwww1004")
                .desc("机头组件_中国_8GB_128GB_P10_白色_K81A")
                .applyCount(25)
                .removeShellCount(10)
                .remark("研发需要领取，需申请拆除10台保密套").build();
        MobileClaimProcessBizDTO phoneClaim = MobileClaimProcessBizDTO.builder()
                .project("K81A-CN")
                .isMrp("是")
                .stage("P10")
                .remark("员工填写的备注信息")
                .projectNo("员工填写的projectNo")
                .userInfo("caoxinyi")
                .item(item)
                .build();
        MobileProcessBaseBizDTO mobileProcessBaseBizDTO = MobileProcessBaseBizDTO.builder()
                .mobileClaimProcessBizDTO(phoneClaim)
                .subProcess(MobileProcessBaseBizDTO.SubProcessEnum.PROCESS_ONE)
                .varaibleDTO(varaibleDTO)
                .build();
        SubmitProcessDTO submitProcessDTO = SubmitProcessDTO.builder()
                .mobileProcessBaseBizDTO(mobileProcessBaseBizDTO)
                .mobileProcessEnum(MobileProcessEnum.PROCESS_KEY_MOBILE_CLAIM)
                .startUser("caoxinyi")
                .build();

        SubmitResultDTO submitResultDTO = bpmService.submitProcess(submitProcessDTO);

        Assertions.assertTrue(StringUtils.isNotBlank(submitResultDTO.getBusinessKey()));
    }
}