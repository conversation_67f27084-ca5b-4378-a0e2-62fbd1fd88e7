package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoThirdUserPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoThirdUserService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.UserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
class ProtoThirdUserServiceImplTest {

    @Autowired
    private ProtoThirdUserService protoThirdUserService;

    @Test
    void findUserInfo() {

        ProtoThirdUserPO userPO = protoThirdUserService.getOne(Wrappers.<ProtoThirdUserPO>lambdaQuery()
                .eq(ProtoThirdUserPO::getUserCode, "123213123"));


    }
}