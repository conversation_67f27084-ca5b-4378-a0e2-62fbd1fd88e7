package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.application.dto.message.TemplateMsgBaseDTO;
import com.mi.oa.asset.mobile.application.dto.tran.TranAuditEmailDTO;
import com.mi.oa.asset.mobile.application.service.MessageService;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
class MessageServiceImplTest {

    @Autowired
    private MessageService messageService;

    @Test
    void getTemplateMsgById() {
        Map<String, String> map = new HashMap<>();
        map.put("user_name", "caoxinyi");
        map.put("user_code", "123");

        //Assertions.assertTrue(StringUtils.isBlank(templateDTO.getContent()));
    }

    @Test
    void sendEmail() {
    }

    @Test
    void sendLark() {
    }

    @Test
    void testSendLark() {
        TemplateMsgBaseDTO baseDTO = TranAuditEmailDTO.builder()
                .tranCode("1")
                .accUserName("2")
                .accUserCode("3")
                .userCode("4")
                .userName("5")
                .today(DateUtils.getFormatDate(DateUtils.COMMON_PATTERN))
                .build();
        baseDTO.setBusinessKey("123");
        baseDTO.setContainUrl(true);
        baseDTO.setDataId("22");
        baseDTO.setFunId("proto_tran");
        Exception exception = null;
        try {
            messageService.sendLark("tran_audit_lark",
                    Collections.singletonList("caoxinyi"), "test", baseDTO);
        } catch (Exception e) {
            log.error("err", e);
            exception = e;
        }

        Assertions.assertNull(exception);
    }

    @Test
    void testSendEmail() {
        TemplateMsgBaseDTO baseDTO = TranAuditEmailDTO.builder()
                .tranCode("1")
                .accUserName("2")
                .accUserCode("3")
                .userCode("4")
                .userName("5")
                .today(DateUtils.getFormatDate(DateUtils.COMMON_PATTERN))
                .build();
        baseDTO.setBusinessKey("123");
        baseDTO.setContainUrl(true);
        baseDTO.setDataId("22");
        baseDTO.setFunId("proto_tran");
        Exception exception = null;
        try {
            messageService.sendEmail("tran_audit",
                    Collections.singletonList("<EMAIL>"), "test", baseDTO);
        } catch (Exception e) {
            log.error("err", e);
            exception = e;
        }

        Assertions.assertNull(exception);
    }

    @Test
    public void testSendSMS() {
        messageService.sendSMS(Lists.list(""),  "预警通知：内购计划单未推送至商城库");
    }
}