package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.api.model.req.InterruptProcessReq;
import com.mi.oa.asset.mobile.api.model.req.SubmitProcessReq;
import com.mi.oa.asset.mobile.application.dto.bpm.*;
import com.mi.oa.asset.mobile.common.enums.MobileProcessEnum;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import javax.ws.rs.core.MediaType;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: bpm Controller 测试类
 * @date 2022/1/5 14:00
 */

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MobileApiApplication.class)
//测试环境使用，用来表示测试环境使用的ApplicationContext将是WebApplicationContext类型的
@WebAppConfiguration
@Disabled
class RoleApplyBpmControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    private String successJson;

    @BeforeEach
    void setUp() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        successJson = JacksonUtils.bean2Json(BaseResp.success());
    }

    @Test
    void interruptProcess() throws Exception {
        InterruptProcessReq req = InterruptProcessReq.builder()
                .mobileProcessEnum(MobileProcessEnum.PROCESS_KEY_MOBILE_CLAIM)
                .businessKey("c779cf4b-4bd1-4215-b3ab-f43fae65247e")
                .startUser("chenxingyu")
                .build();
        String result = mockMvc
                .perform(MockMvcRequestBuilders.post("/api/v1/bpm/interrupt")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JacksonUtils.bean2Json(req))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().json(successJson))
                .andReturn()
                .getResponse().getContentAsString();//将相应的数据转换为字符
        log.info("result:{}", result);
    }

    @Test
    void submitProcess() throws Exception {
        VaraibleDTO varaibleDTO = VaraibleDTO
                .builder()
                .projectManager("chenxingyu")
                .build();

        ProtoRoleApplyDetBizDTO protoRoleApplyDetBizDTO = ProtoRoleApplyDetBizDTO.builder()
                .sn("1")
                .roleNo("gcj_01")
                .roleName("工程机领用人")
                .roleMemo("包含领用申请，转移，归还，无法归还，工程机台账")
                .build();
        List<ProtoRoleApplyDetBizDTO> detBizList = new ArrayList();
        detBizList.add(protoRoleApplyDetBizDTO);

        ProtoRoleApplyBizDTO protoRoleApplyBizDTO = ProtoRoleApplyBizDTO.builder()
                .applyUser("陈星宇(chenxingyu)")
                .deptName("固资组")
                .applySystemName("EAM资产管理系统-工程机管理")
                .remark("12313")
                .items(detBizList)
                .build();

        MobileProcessBaseBizDTO mobileProcessBaseBizDTO = MobileProcessBaseBizDTO.builder()
                .protoRoleApplyBizDTO(protoRoleApplyBizDTO)
                .subProcess(MobileProcessBaseBizDTO.SubProcessEnum.PROCESS_ONE)
                .varaibleDTO(varaibleDTO)
                .build();

        SubmitProcessReq req = SubmitProcessReq.builder()
                .mobileProcessBaseBizDTO(mobileProcessBaseBizDTO)
                .mobileProcessEnum(MobileProcessEnum.PROCESS_KEY_MOBILE_ROLE_APPLY)
                .startUser("chenxingyu")
                .build();

        String result = mockMvc
                .perform(MockMvcRequestBuilders.post("/api/v1/bpm/submit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JacksonUtils.bean2Json(req))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn()
                .getResponse().getContentAsString();
        log.info("result:{}", result);
        BaseResp baseResp = JacksonUtils.json2Bean(result, BaseResp.class);
        Assertions.assertEquals(BaseResp.CODE_SUCCESS, baseResp.getCode());
    }
}