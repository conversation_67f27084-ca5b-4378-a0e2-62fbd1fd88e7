package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.utils.GsonUtil;
import com.mi.oa.asset.mobile.utils.HttpClientUtil;
import com.mi.oa.asset.x5.common.JacksonUtils;
import com.mi.oa.asset.x5.common.X5Response;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * description: your description
 *
 * <AUTHOR>
 * @date 2023/9/19
 */
@Slf4j
@Disabled
class X5HttpTest {


    @Test
    void test1() {

        String host = "https://t.mioffice.cn";
        String appid = "sku_1003";
        String appkey = "65165862a1019effd5055b6bd1fa083c";
        Map<String, String> request = new HashMap<>();
        request.put("level", "1");
        X5Response x5Response = HttpClientUtil.doX5Post(host + "/sku/api/tvCode/getTvProjectInfo", appid, appkey, request, "");
        System.out.println(JacksonUtils.bean2Json(x5Response));
    }

    @Test
    void testMdmProjectQuery() {
        String url = "https://mdm.be.mi.com/getMaster/x5";
        String appId = "eam";
        String appKey = "6897e967-c07f-40bc-b01d-983c0785d40b";
        Map<String, Object> param1 = new HashMap<>();
        param1.put("queryKey", "7079d3164ac24a308d417bf083d06c88");
        param1.put("pageIndex", 1);
        param1.put("pageSize", 200);
        param1.put("projectName", "J6B2-GL");
        X5Response response = HttpClientUtil.doX5Post(url, appId, appKey, param1, "");
        System.out.println("测试结果:" + GsonUtil.toJsonString(response));
    }

    @Test
    void testMdmTestProjectQuery() {
        String url = "http://mdm.be.test.mi.com/getMaster/x5";
        String appId = "eam";
        String appKey = "e2d3fa25-5f76-4749-aead-24dd3677aa8f";
        Map<String, Object> param1 = new HashMap<>();
        param1.put("queryKey", "e44026df00f04befa7ecf7b8492e92f6");
        param1.put("pageIndex", 1);
        param1.put("pageSize", 200);
        param1.put("projectName", "N801-CN");
        X5Response response = HttpClientUtil.doX5Post(url, appId, appKey, param1, "");
        System.out.println("测试结果:" + GsonUtil.toJsonString(response));
    }
}
