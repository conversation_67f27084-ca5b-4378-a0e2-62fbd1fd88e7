package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.api.model.req.InterruptProcessReq;
import com.mi.oa.asset.mobile.api.model.req.SubmitProcessReq;
import com.mi.oa.asset.mobile.application.dto.bpm.MobileClaimProcessBizDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.MobileProcessBaseBizDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.VaraibleDTO;
import com.mi.oa.asset.mobile.common.enums.MobileProcessEnum;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import javax.ws.rs.core.MediaType;

/**
 * <AUTHOR>
 * @description: bpm Controller 测试类
 * @date 2023/07/25 15:00
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MobileApiApplication.class)
//测试环境使用，用来表示测试环境使用的ApplicationContext将是WebApplicationContext类型的
@WebAppConfiguration
@Disabled
class BpmControllerTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    private MockMvc mockMvc;

    private String successJson;

    @BeforeEach
    void setUp() throws Exception {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        successJson = JacksonUtils.bean2Json(BaseResp.success());
    }

    void callbackProcess() throws  Exception{

    }


    @Test
    void interruptProcess() throws Exception {
        InterruptProcessReq req = InterruptProcessReq.builder()
                .mobileProcessEnum(MobileProcessEnum.PROCESS_KEY_MOBILE_CLAIM)
                .businessKey("e7b1a78b-a50f-4cd6-8725-6bea88c63d09")
                .startUser("caoxinyi")
                .build();
        String result = mockMvc
                .perform(MockMvcRequestBuilders.post("/api/v1/bpm/interrupt")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JacksonUtils.bean2Json(req))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().json(successJson))
                .andReturn()
                .getResponse().getContentAsString();//将相应的数据转换为字符
        log.info("result:{}", result);
    }

    @Test
    void submitProcess() throws Exception {
        VaraibleDTO varaibleDTO = VaraibleDTO
                .builder()
                .projectManager("dengzhibin")
                .build();
        MobileClaimProcessBizDTO.Item item = MobileClaimProcessBizDTO.Item.builder()
                .sn("2")
                .miMaterialCode("902K81APwwwww1004")
                .desc("机头组件_中国_8GB_128GB_P10_白色_K81A")
                .applyCount(25)
                .removeShellCount(10)
                .remark("研发需要领取，需申请拆除10台保密套").build();
        MobileClaimProcessBizDTO phoneClaim = MobileClaimProcessBizDTO.builder()
                .project("K81A-CN")
                .isMrp("是")
                .stage("P10")
                .remark("员工填写的备注信息")
                .projectNo("员工填写的projectNo")
                .userInfo("caoxinyi")
                .item(item)
                .build();

        MobileProcessBaseBizDTO mobileProcessBaseBizDTO = MobileProcessBaseBizDTO.builder()
                .mobileClaimProcessBizDTO(phoneClaim)
                .subProcess(MobileProcessBaseBizDTO.SubProcessEnum.PROCESS_ONE)
                .varaibleDTO(varaibleDTO)
                .build();

        SubmitProcessReq req = SubmitProcessReq.builder()
                .mobileProcessBaseBizDTO(mobileProcessBaseBizDTO)
                .mobileProcessEnum(MobileProcessEnum.PROCESS_KEY_MOBILE_CLAIM)
                .startUser("caoxinyi")
                .build();

        String result = mockMvc
                .perform(MockMvcRequestBuilders.post("/api/v1/bpm/submit")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(JacksonUtils.bean2Json(req))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn()
                .getResponse().getContentAsString();//将相应的数据转换为字符
        log.info("result:{}", result);
        BaseResp baseResp = JacksonUtils.json2Bean(result, BaseResp.class);
        Assertions.assertEquals(BaseResp.CODE_SUCCESS, baseResp.getCode());
    }
}
