package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.application.service.ProtoBackBOService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/25 14:42
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class ProtoBackBOServiceImplTest {

    @Autowired
    private ProtoBackBOServiceImpl backBOService;

    @Test
    public void pushBpm() {
        backBOService.pushBpm("mi-385-19651");
    }

    @Test
    public void qryProtoOut() {
        backBOService.qryProtoOut("mi-384-19701", null);
    }
}
