package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.application.dto.CommonConditionDTO;
import com.mi.oa.asset.mobile.application.dto.secondcard.ApplySecondDTO;
import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.HrodService;
import com.xiaomi.core.test.TestWithKeycenter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/4 16:51
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class ProtoSecondCardBOServiceImplTest implements TestWithKeycenter {
    @Autowired
    private ProtoSecondCardBOServiceImpl secondCardBOService;
    @Autowired
    private HrodService hrodService;

    @Test
    void query() {
        String[] names={"project_code","stage_name"}, conds={"like","="}, values={"C","P20"};
        CommonConditionDTO dto = CommonConditionDTO.builder()
                .fieldNames("sku_code")
                .fieldConds("like")
                .fieldValues("1").start(0).limit(5).build();
        secondCardBOService.query(dto, "", "proto_second_card");
    }

    @Test
    public void createApplySecond(){
        ApplySecondDTO applySecondDto=ApplySecondDTO.builder().projectCode("a").skuCode("sku").skuName("skuname")
                .stageName("stagename").build();
        secondCardBOService.createApplySecond(applySecondDto,UserInfo.builder().userId("a").userCode("chenxingyu").deptCode("a").empCode("e")
                .userName("un").deptName("dn").build());
    }
}
