package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOutDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl.ProtoOutDetServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * Copyright (c) 2025 XiaoMi Inc.All Rights Reserved.
 * Description: your description
 *
 * <AUTHOR>
 * @date 2025/1/14 14:47
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class ProtoOutDetServiceTest {

    @Resource
    private ProtoOutDetServiceImpl protoOutDetServiceImpl;


    @Test
    public void testListNotPushSap() {
        Integer pageNum = 2;
        Integer pageSize = 100;
        List<ProtoOutDetPO> result = protoOutDetServiceImpl.listNotPushSap(pageNum, pageSize);
        assertNotNull(result);
    }


}
