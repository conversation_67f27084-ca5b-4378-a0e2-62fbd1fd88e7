package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.application.service.ProtoProjectListBOService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.Collections;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MobileApiApplication.class)
//测试环境使用，用来表示测试环境使用的ApplicationContext将是WebApplicationContext类型的
@WebAppConfiguration
@Disabled
class ProjectSyncTest {


    @Autowired
    private ProtoProjectListBOService protoProjectListBOService;

    @Test
    void testSyncProjectList() {
        protoProjectListBOService.synProjectListByProjectCode(Collections.singletonList("N801-CN"));
    }
}
