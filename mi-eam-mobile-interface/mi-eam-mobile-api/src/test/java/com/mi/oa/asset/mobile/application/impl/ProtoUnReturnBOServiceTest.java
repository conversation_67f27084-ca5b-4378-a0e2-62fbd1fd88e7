package com.mi.oa.asset.mobile.application.impl;

import com.google.common.collect.Lists;
import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.application.service.ProtoUnReturnBOService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysAttachPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoOrderPayService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysAttachService;
import com.xiaomi.core.test.TestWithKeycenter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/7/6
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class ProtoUnReturnBOServiceTest implements TestWithKeycenter {

    @Autowired
    private ProtoUnReturnBOService protoUnReturnBOService;

    @Autowired
    private SysAttachService sysAttachService;

    @Autowired
    private ProtoOrderPayService protoOrderPayService;

    @Test
    public void test1() {
        protoUnReturnBOService.unreturnSubmit("UN202111000121");
    }

    @Test
    public void test2() {
        List<SysAttachPO> list = sysAttachService.listByIds(Lists.newArrayList("1557263331218956290"));
        protoUnReturnBOService.uploadFile(list);
    }

    @Test
    void test4(){
        SysAttachPO sysAttachPO = sysAttachService.getById("mi-158-87507");
        protoUnReturnBOService.copyAttach(Lists.newArrayList(sysAttachPO));
    }

    @Test
    void test5(){
        protoUnReturnBOService.deleteAttach("mi-134-6351");
    }

    @Test
    void test6(){
        protoOrderPayService.getByUnturnCode("UN202208000021");
    }
}
