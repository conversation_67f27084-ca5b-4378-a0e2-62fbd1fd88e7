package com.mi.oa.asset.mobile.application.impl;

import com.alibaba.nacos.client.utils.JSONUtils;
import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmProcessCompletedDTO;
import com.mi.oa.asset.mobile.application.service.BaselineBizService;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import com.mioffice.ums.open.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import net.minidev.json.JSONUtil;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
class BaselineBizServiceImplTest {
    @Autowired
    private BaselineBizService baselineBizService;
    @Autowired
    private CommonService commonService;

    @Test
    void submit() {
        baselineBizService.submit("mi-398-7651");
    }

    @Test
    void interruptBpm(){
        baselineBizService.interruptBpm("mi-398-7651","p-wangaolin");
    }
    @Test
    void bpmCallbackV3(){
        BpmProcessCompletedDTO param = JsonUtils.parse("{\n" +
                "\t\"taskId\": \"63267483\",\n" +
                "\t\"taskName\": \"项目管理员\",\n" +
                "\t\"delegationUser\": null,\n" +
                "\t\"comment\": \"审批工具审批通过\",\n" +
                "\t\"taskDefinitionKey\": \"Activity_1y9habx\",\n" +
                "\t\"operator\": {\n" +
                "\t\t\"uid\": \"6ce17af8347b4afaa38e9f63f285722f\",\n" +
                "\t\t\"userName\": \"minpan\",\n" +
                "\t\t\"displayName\": \"闵攀\",\n" +
                "\t\t\"personId\": \"30470\",\n" +
                "\t\t\"userRank\": null,\n" +
                "\t\t\"hrStatus\": \"A\",\n" +
                "\t\t\"org\": {\n" +
                "\t\t\t\"orgId\": \"IT550202\",\n" +
                "\t\t\t\"orgDesc\": \"集团资产组\",\n" +
                "\t\t\t\"fullOrgDesc\": \"[{\\\"deptEnName\\\":\\\"Xiaomi Company\\\",\\\"deptName\\\":\\\"小米公司\\\",\\\"deptId\\\":\\\"MI\\\",\\\"level\\\":\\\"0\\\"},{\\\"deptEnName\\\":\\\"集团信息技术部\\\",\\\"deptName\\\":\\\"集团信息技术部\\\",\\\"deptId\\\":\\\"IT\\\",\\\"level\\\":\\\"1\\\"},{\\\"deptEnName\\\":\\\"企业效率部\\\",\\\"deptName\\\":\\\"企业效率部\\\",\\\"deptId\\\":\\\"IT55\\\",\\\"level\\\":\\\"2\\\"},{\\\"deptEnName\\\":\\\"企业协同数字化部\\\",\\\"deptName\\\":\\\"企业协同数字化部\\\",\\\"deptId\\\":\\\"IT5502\\\",\\\"level\\\":\\\"3\\\"},{\\\"deptEnName\\\":\\\"集团资产组\\\",\\\"deptName\\\":\\\"集团资产组\\\",\\\"deptId\\\":\\\"IT550202\\\",\\\"level\\\":\\\"4\\\"}]\"\n" +
                "\t\t}\n" +
                "\t},\n" +
                "\t\"assignee\": {\n" +
                "\t\t\"uid\": \"6ce17af8347b4afaa38e9f63f285722f\",\n" +
                "\t\t\"userName\": \"minpan\",\n" +
                "\t\t\"displayName\": \"闵攀\",\n" +
                "\t\t\"personId\": \"30470\",\n" +
                "\t\t\"userRank\": null,\n" +
                "\t\t\"hrStatus\": \"A\",\n" +
                "\t\t\"org\": {\n" +
                "\t\t\t\"orgId\": \"IT550202\",\n" +
                "\t\t\t\"orgDesc\": \"集团资产组\",\n" +
                "\t\t\t\"fullOrgDesc\": \"[{\\\"deptEnName\\\":\\\"Xiaomi Company\\\",\\\"deptName\\\":\\\"小米公司\\\",\\\"deptId\\\":\\\"MI\\\",\\\"level\\\":\\\"0\\\"},{\\\"deptEnName\\\":\\\"集团信息技术部\\\",\\\"deptName\\\":\\\"集团信息技术部\\\",\\\"deptId\\\":\\\"IT\\\",\\\"level\\\":\\\"1\\\"},{\\\"deptEnName\\\":\\\"企业效率部\\\",\\\"deptName\\\":\\\"企业效率部\\\",\\\"deptId\\\":\\\"IT55\\\",\\\"level\\\":\\\"2\\\"},{\\\"deptEnName\\\":\\\"企业协同数字化部\\\",\\\"deptName\\\":\\\"企业协同数字化部\\\",\\\"deptId\\\":\\\"IT5502\\\",\\\"level\\\":\\\"3\\\"},{\\\"deptEnName\\\":\\\"集团资产组\\\",\\\"deptName\\\":\\\"集团资产组\\\",\\\"deptId\\\":\\\"IT550202\\\",\\\"level\\\":\\\"4\\\"}]\"\n" +
                "\t\t}\n" +
                "\t},\n" +
                "\t\"processInstanceId\": \"63268031\",\n" +
                "\t\"processDefinitionId\": \"bpmn_890264921154117632:5:63099571\",\n" +
                "\t\"starTaskName\": null,\n" +
                "\t\"startTaskId\": null,\n" +
                "\t\"startTaskDefinitionKey\": null,\n" +
                "\t\"variables\": {\n" +
                "\t\t\"summary\": [],\n" +
                "\t\t\"_FLOWABLE_SKIP_EXPRESSION_ENABLED\": true,\n" +
                "\t\t\"initiatorUserId\": \"c82e3be7a76d4d028d44614469ffac7d\",\n" +
                "\t\t\"initiateTime\": \"202307251603\",\n" +
                "\t\t\"initiatorOrgId\": \"IT550501\",\n" +
                "\t\t\"gm\": \"minpan\",\n" +
                "\t\t\"initiator\": \"p-wangaolin\",\n" +
                "\t\t\"processDefinitionName\": \"工程机公共基线（新增/删除/变更）申请\",\n" +
                "\t\t\"title\": \"工程机公共基线（新增）申请（Application for a common baseline of the Engineering machine）SQ202307000011\",\n" +
                "\t\t\"initiatorUser\": \"王奥林\",\n" +
                "\t\t\"status\": \"agree\"\n" +
                "\t},\n" +
                "\t\"initiator\": null,\n" +
                "\t\"initiatorUser\": null,\n" +
                "\t\"currentDate\": \"2023-07-25 16:04:23\",\n" +
                "\t\"processDefinitionName\": \"工程机公共基线（新增/删除/变更）申请\",\n" +
                "\t\"title\": null,\n" +
                "\t\"modelCode\": \"bpmn_890264921154117632\",\n" +
                "\t\"businessKey\": \"dd71c6aa920f465585aac36a64fc7059\",\n" +
                "\t\"startTime\": {\n" +
                "\t\t\"dateTime\": {\n" +
                "\t\t\t\"date\": {\n" +
                "\t\t\t\t\"year\": 2023,\n" +
                "\t\t\t\t\"month\": 7,\n" +
                "\t\t\t\t\"day\": 25\n" +
                "\t\t\t},\n" +
                "\t\t\t\"time\": {\n" +
                "\t\t\t\t\"hour\": 16,\n" +
                "\t\t\t\t\"minute\": 3,\n" +
                "\t\t\t\t\"second\": 54,\n" +
                "\t\t\t\t\"nano\": 0\n" +
                "\t\t\t}\n" +
                "\t\t},\n" +
                "\t\t\"offset\": {\n" +
                "\t\t\t\"totalSeconds\": 28800\n" +
                "\t\t},\n" +
                "\t\t\"zone\": {\n" +
                "\t\t\t\"id\": \"Asia/Shanghai\"\n" +
                "\t\t}\n" +
                "\t},\n" +
                "\t\"endTime\": {\n" +
                "\t\t\"dateTime\": {\n" +
                "\t\t\t\"date\": {\n" +
                "\t\t\t\t\"year\": 2023,\n" +
                "\t\t\t\t\"month\": 7,\n" +
                "\t\t\t\t\"day\": 25\n" +
                "\t\t\t},\n" +
                "\t\t\t\"time\": {\n" +
                "\t\t\t\t\"hour\": 16,\n" +
                "\t\t\t\t\"minute\": 4,\n" +
                "\t\t\t\t\"second\": 23,\n" +
                "\t\t\t\t\"nano\": 0\n" +
                "\t\t\t}\n" +
                "\t\t},\n" +
                "\t\t\"offset\": {\n" +
                "\t\t\t\"totalSeconds\": 28800\n" +
                "\t\t},\n" +
                "\t\t\"zone\": {\n" +
                "\t\t\t\"id\": \"Asia/Shanghai\"\n" +
                "\t\t}\n" +
                "\t},\n" +
                "\t\"processDefinitionVersion\": 5,\n" +
                "\t\"processInstanceStatus\": \"COMPLETED\",\n" +
                "\t\"status\": \"agree\",\n" +
                "\t\"formData\": {}\n" +
                "}",BpmProcessCompletedDTO.class);
        baselineBizService.bpmCallbackV3(param);
    }
    @Test
    void getAutoIncId(){
        String uqCode = "JXM"+commonService.getAutoIncId(RedisUniqueKeyEnum.PROTO_BASELINE,9);
        log.info(uqCode);
    }

    @Test
    void setKeyVal(){
        String key = "mobile:proto_baseline:maxId";
//        RedisUtils.delete(key);
//        RedisUtils.setIncr(key, 43L);
//        Object o = RedisUtils.get(key);
//        log.info(RedisUtils.get(key).toString());
    }
}
