package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.api.converter.RequestContextConverter;
import com.mi.oa.asset.mobile.application.service.TranService;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoTranDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoTranService;
import com.mi.oa.infra.oaucf.bpm.service.ApprovalService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.xiaomi.core.test.TestWithKeycenter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.List;

@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
class TranServiceImplTest implements TestWithKeycenter {

    @Autowired
    private TranService tranService;

    @Autowired
    private ProtoTranService protoTranService;

    @Autowired
    private ProtoTranDetService protoTranDetService;

    @Autowired
    private ApprovalService approvalService;

    @Test
    void test2(){
        ProtoTranPO tranPO = protoTranService.getByTranId("mi-090-16401");
        List<ProtoTranDetPO> detPOList = protoTranDetService.listByTranId("mi-090-16401");
        tranService.sendSuccessMessage(tranPO, detPOList);
    }

    @Test
    void test3(){
        ProtoTranPO tranPO = protoTranService.getByTranId("mi-175-13201");
        List<ProtoTranDetPO> detPOList = protoTranDetService.listByTranId("mi-309-16251");
        tranService.sendAuditMessage(tranPO, detPOList, tranPO.getBusinessKey());
    }

    @Test
    void test4(){
        BaseResp<String> taskDetailLink = approvalService.getTaskDetailLink("43b1ac69-70bd-4b6f-bad0-0d55ece1b00e", "chenxingyu");
        System.out.println(taskDetailLink.getData());
    }



    @Test
    void commitAudit() {
        Exception exception = null;
        try {
            tranService.tranCommit("mi-110-10501", "chenxingyu");
        } catch (Exception e) {
            log.error("err:", e);
            exception = e;
        }
        Assertions.assertNull(exception);
    }

    @Test
    void bpmCallBack() {
//        BpmCallbackDTO<MobileProcessBaseBizDTO> bpmCallbackDTO = new BpmCallbackDTO<>();
//        bpmCallbackDTO.setBusinessKey("ef14471e-1fc2-482c-a66a-e9d7ae0fa755");
//        bpmCallbackDTO.setStatus(BpmCallbackStatusEnum.END);
//        Exception exception = null;
//        try {
//            tranService.bpmCallBack(bpmCallbackDTO);
//        } catch (Exception e) {
//            log.error("err:", e);
//            exception = e;
//        }
//        Assertions.assertNull(exception);
    }

    @Autowired
    private RequestContextConverter requestContextConverter;

    @Test
    void test1() {
        RequestContext request = new RequestContext(new HashMap<>());
        request.getRequestMap().put("where_value", "");
        request.getRequestMap().put("user_id", "guankangping1");
        request.getRequestMap().put("where_sql", "TYHcHJvdG9fdHJhbi5jb25maXJtX3N0YXR1cyA9ICczJyBhbmQgIHByb3RvX3RyYW4uYXVkaXRpbmcgPSAnMycgYW5kIGFjY191c2VyX2NvZGUgPSB7Q1VSVVNFUklEfQ==A=");
        tranService.query(requestContextConverter.reqToTranDTO(request));
    }
}