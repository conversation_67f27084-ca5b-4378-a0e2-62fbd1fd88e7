package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardDeptTotalService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
class ProtoCardDeptTotalServiceImplTest {

    @Autowired
    private ProtoCardDeptTotalService protoCardDeptTotalService;

    @Test
    void test1() {
        protoCardDeptTotalService.statisticsDept();
    }
}