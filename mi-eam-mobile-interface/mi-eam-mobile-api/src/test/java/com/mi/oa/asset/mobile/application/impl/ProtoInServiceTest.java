package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/21
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class ProtoInServiceTest {

    @Autowired
    private ProtoInService protoInService;

    @Test
    public void test(){
        protoInService.pushSAP("mi-787-44251", "王奥林");
    }

}
