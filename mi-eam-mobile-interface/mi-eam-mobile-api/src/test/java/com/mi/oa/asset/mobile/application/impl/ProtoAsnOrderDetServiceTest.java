package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoAsnOrderDetService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/21
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class ProtoAsnOrderDetServiceTest {

    @Autowired
    private ProtoAsnOrderDetService protoAsnOrderDetService;

    @Test
    public void test(){
        protoAsnOrderDetService.updateSapStatus();
    }

    @Test
    public void test1(){
        String[] detIds = new String[3];
        detIds[0] = "20220118000009";
        detIds[1] = "20220118000010";
        detIds[2] = "20220118000011";
        UserInfo userInfo = new UserInfo();
        userInfo.setUserName("陈星宇");
        userInfo.setUserCode("chenxingyu");
        userInfo.setEmpCode("30310");
        protoAsnOrderDetService.confirmReceiving("9311008217", "", detIds, "北京库房", "beijing", "beijing", userInfo);
    }
}
