package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.application.service.SysUserDataBOService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/6
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class SysUserDataBOServiceImplTest {

    @Autowired
    private SysUserDataBOService sysUserDataBOService;

    @Test
    public void test() {
         sysUserDataBOService.addInterfaceRole("v-xuli1", "HW", "mobile");
    }

    @Test
    public void test1() {
        sysUserDataBOService.deleteInterfaceRole("v-xuli1", "IT");
    }
}
