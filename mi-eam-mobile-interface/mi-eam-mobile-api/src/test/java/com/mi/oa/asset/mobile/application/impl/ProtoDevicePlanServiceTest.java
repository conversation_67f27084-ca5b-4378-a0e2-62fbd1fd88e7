package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoDevicePlanService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.scheduling.annotation.Async;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/23
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class ProtoDevicePlanServiceTest {

    @Autowired
    private ProtoDevicePlanService protoDevicePlanService;

    @Test
    public void test(){
        protoDevicePlanService.sendPlanMessage("mi-262-4501");
    }

    @Test
    public void test1(){
        protoDevicePlanService.submitSendMessage("mi-262-4501");
    }
}
