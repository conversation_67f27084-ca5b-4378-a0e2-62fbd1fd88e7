package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/27 18:01
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class ProtoBackConfirmBOServiceImplTest {
    @Autowired
    ProtoBackConfirmBOServiceImpl protoBackConfirmBOService;
    @Test
    public void audit(){
        String[] keyIds = new String[]{"mi-042-19001"};
        UserInfo userInfo = UserInfo.builder().userName("陈星宇").userCode("chenxingyu")
                .empCode("30310").build();
        Assert.assertThrows("{{{只允许确认归还[归还中]状态的单据}}}",BizException.class,()->protoBackConfirmBOService.afterAudit(keyIds, userInfo));
    }
}
