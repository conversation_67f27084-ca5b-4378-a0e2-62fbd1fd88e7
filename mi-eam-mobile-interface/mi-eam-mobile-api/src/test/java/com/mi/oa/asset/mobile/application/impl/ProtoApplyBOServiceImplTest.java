package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.application.service.ProtoApplyBOService;
import com.mi.oa.asset.mobile.common.enums.SapMakeCnEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSecondCardPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoApplyMatService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoApplyService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoSecondCardService;
import com.xiaomi.core.test.TestWithKeycenter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/10 17:38
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class ProtoApplyBOServiceImplTest implements TestWithKeycenter {
    @Autowired
    private ProtoSecondCardService protoSecondCardService;
    @Autowired
    private ProtoApplyService applyService;
    @Autowired
    private ProtoApplyMatService applyMatService;
    @Autowired
    private ProtoApplyBOService protoApplyBOService;

    @Test
    public void trans() {
        ProtoSecondCardPO queryParam = ProtoSecondCardPO.builder()
                .projectCode("asd").stageName("asd")
                .skuCode("wq").build();
        List<ProtoSecondCardPO> secondCardList = protoSecondCardService.findListByParam(queryParam);
        log.info(secondCardList.toString());
        ProtoSecondCardPO po= protoSecondCardService.findOneByParam(queryParam);
        log.info(po.toString());
    }

    @Test
    public void test() {
        ProtoApplyPO applyPO = applyService.getByApplyCode("YJFM003564");
        protoApplyBOService.pushSap(applyPO);
    }

    @Test
    public void costCenterTest(){
        String costCode = "C113030028";
        String makeCn ="1181-1180";
        String makeCnComp = SapMakeCnEnum.CHANG_PING.getSapFactory().equals(makeCn.split("-")[0])
                ? costCode.substring(1,5).equals(makeCn.split("-")[1])
                ? makeCn.split("-")[1]
                : SapMakeCnEnum.INTERNAL.getMakeCn().split("-")[1]
                : makeCn.split("-")[1];
        // String makeCnComp = makeCn.split("-")[1];
        //截取前1位
        String begin = costCode.substring(0,1);
        //截取后5位
        String end =   costCode.substring(5);
        costCode = begin +  makeCnComp + end ;

        System.out.println("costCode:"+costCode+"  compDeptCode:  "+makeCnComp);
    }
}
