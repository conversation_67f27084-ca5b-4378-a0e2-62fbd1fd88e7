package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.application.service.ProtoCardBOService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
class ProtoCardServiceImplTest {

    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private ProtoCardBOService protoCardBOService;

    @Test
    void findNotSelfCard() {
        List<ProtoCardPO> protoCardPOS = protoCardService
                .findNotSelfCard(Collections.singletonList("123"), "caoxinyi",null);
        Assertions.assertEquals(0, protoCardPOS.size());
    }

    @Test
    void test1() {
        protoCardBOService.refreshAllDeptName();
    }

    @Test
    void test2() {
        protoCardBOService.refreshOldData();
    }

    @Test
    void test3() {
        protoCardBOService.refreshEntryDate();
    }
}