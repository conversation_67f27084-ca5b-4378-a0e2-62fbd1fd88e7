package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.infra.oaucf.idm.api.IdmAccountService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
class UserInfoServiceTest {

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private IdmAccountService idmAccountService;

    @Test
    void getEmpInfoByUserName() {

    }

    @Test
    void getUserInfoByUserName() {
    }

    @Test
    void getEmailByName() {
        String email = userInfoService.getEmailByName("caoxinyi");
        Assertions.assertEquals("<EMAIL>",email);
        email = userInfoService.getEmailByName("p-jinbiaochen");
        Assertions.assertEquals("<EMAIL>",email);
    }

    @Test
    void testIdmService(){
        idmAccountService.findUidByUserName("caoxinyi");
    }
}