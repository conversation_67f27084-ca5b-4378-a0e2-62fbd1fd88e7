package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoOutService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/3/24
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class ProtoOutServiceTest {

    @Autowired
    private ProtoOutService protoOutService;

    @Test
    public void test(){
        protoOutService.pushSAP("mi-551-36008", "闵攀");
    }

}
