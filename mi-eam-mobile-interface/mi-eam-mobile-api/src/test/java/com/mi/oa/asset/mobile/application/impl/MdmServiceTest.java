package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.api.web.ProtoProjectListController;
import com.mi.oa.asset.mobile.application.service.ProtoProjectListBOService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectFactoryPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.MdmService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectFactoryService;
import com.xiaomi.core.test.TestWithKeycenter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/12
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class MdmServiceTest implements TestWithKeycenter {

    @Autowired
    ProtoProjectListController protoProjectListController;

    @Autowired
    private MdmService mdmService;

    @Autowired
    private ProtoProjectListBOService protoProjectListBOService;

    @Autowired
    private ProtoProjectFactoryService protoProjectFactoryService;

    @Test
    public void test(){
        mdmService.createCostCenter("leijun", "C111060010");
    }

    @Test
    public void test2(){
        protoProjectListBOService.synProjectListByTime(null);
    }

    @Test
    public void test3(){
        protoProjectListController.synProject(Arrays.asList("N83-BR"));
    }

    @Test
    public void test5(){
        List<ProtoProjectFactoryPO> projectFactoryList = protoProjectFactoryService.listByCfgId("220513000006");
        Map<String, ProtoProjectFactoryPO> factoryMap = projectFactoryList.stream().filter(o -> !StringUtils.isEmpty(o.getUserCode()) || !StringUtils.isEmpty(o.getFactoryPm()))
                .collect(Collectors.toMap(ProtoProjectFactoryPO::getFactoryCode, o -> o));
        System.out.println(factoryMap.toString());
    }


}
