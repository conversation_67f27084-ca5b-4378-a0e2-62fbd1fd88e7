package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.application.dto.ProtoDisposeDTO;
import com.mi.oa.asset.mobile.application.dto.ProtoPurchaseDTO;
import com.mi.oa.asset.mobile.application.service.ProtoProjectCfgBOService;
import com.mi.oa.asset.mobile.common.enums.PriceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/22
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class ProtoProjectCfgServiceTest {

    @Autowired
    private ProtoProjectCfgBOService protoProjectCfgBOService;

    @Test
    public void test(){
        List<String> deviceCodeList = new ArrayList<>();
        deviceCodeList.add("5200074B00028");
        List<ProtoDisposeDTO> disposeDamages = protoProjectCfgBOService.getDisposeDamages(deviceCodeList);
        System.out.println("=============");
        System.out.println(disposeDamages.toString());
    }

    @Test
    public void test1(){
        List<ProtoPurchaseDTO> purchaseList = new ArrayList<>();
        ProtoPurchaseDTO purchaseDTO = new ProtoPurchaseDTO();
        purchaseDTO.setSkuCode("5200074B0000");
        purchaseDTO.setPriceCode("CZ03");
        purchaseDTO.setListMoney(BigDecimal.valueOf(1000));
        purchaseList.add(purchaseDTO);
        List<ProtoPurchaseDTO> disposeDamages = protoProjectCfgBOService.getPurchaseDesc(purchaseList);
        System.out.println("=============");
        System.out.println(disposeDamages.toString());
    }
}
