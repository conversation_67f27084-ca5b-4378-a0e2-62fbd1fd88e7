package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInnerPurchasePlanService;
import com.xiaomi.core.test.TestWithKeycenter;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/24
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class ProtoInnerPurchasePlanServiceTest implements TestWithKeycenter {

    @Autowired
    private ProtoInnerPurchasePlanService protoInnerPurchasePlanService;

    @Test
    public void test(){
        protoInnerPurchasePlanService.calculatePurchase(Lists.newArrayList("NGJH202208000201"));
    }


    @Test
    public void test2() {
        String mrpType = protoInnerPurchasePlanService.getMrpType("1");
        System.out.println("------------------" + mrpType);
        String mrpType1 = protoInnerPurchasePlanService.getMrpType("NGJH202307000001");
        System.out.println("------------------" + mrpType1);
    }

    @Test
    public void checkTest() {
        protoInnerPurchasePlanService.checkInnerPurchaseOrder(null);
    }
}
