package com.mi.oa.asset.mobile.application.impl;

import com.google.common.collect.Lists;
import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysProxyService;
import com.xiaomi.core.test.TestWithKeycenter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/7/12
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class SysProxyServiceTest implements TestWithKeycenter {

    @Autowired
    private SysProxyService sysProxyService;

    @Test
    void test() {
        sysProxyService.getProxy(Lists.newArrayList("guosongmao", "leijun"));
    }
}
