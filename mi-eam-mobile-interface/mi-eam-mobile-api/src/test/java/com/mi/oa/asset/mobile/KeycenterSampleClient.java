package com.mi.oa.asset.mobile;
import com.xiaomi.core.test.TestWithKeycenter;
import com.xiaomi.keycenter.agent.client.DataProtectionProvider;
import org.apache.commons.codec.binary.Base64;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;
/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/4/18 14:54
 */
@Disabled
public class KeycenterSampleClient implements TestWithKeycenter {

    private final static DataProtectionProvider provider = DataProtectionProvider.getProvider("mims-asset-config"); //特意typo

    @Test
    void someCase() throws Exception{
        // test code that uses keycenter
//        String testString = "asset_wn";
//        System.out.println("Before Encrypted: " + testString);
//        byte[] encrypted = provider.encrypt(testString.getBytes(StandardCharsets.UTF_8), null, false);
//        String encryptedStringBase64 = Base64.encodeBase64String(encrypted);
        String encryptedStringBase64 = "GBBgC4uhH3o+Dhz9VlMP75TWGBIgzmXZBFNNsbjM4PUCwmlYqv8YEOodfkazNU/7mapXsc3Qrg0YFK7W0m0NcnPC0+hzMrOoYoddarX/AA==";

        System.out.println("Encrypted String with Base64 format: " + encryptedStringBase64);

        byte[] decrypted = provider.decrypt(Base64.decodeBase64(encryptedStringBase64), null, false);

        String decryptedString = new String(decrypted, StandardCharsets.UTF_8);
        System.out.println("After decrypted :" + decryptedString);
    }

}
