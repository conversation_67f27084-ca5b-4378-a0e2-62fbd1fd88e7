package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SrmService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @Desc: SRM接口单元测试
 * @Author: huy
 * @Date: 2022/1/5
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class SrmServiceTest {

    @Autowired
    private SrmService srmService;

    @Test
    public void test(){
        srmService.synAsnOrderByAsnNo("9311008543");
    }

    @Test
    public void test1(){
        srmService.synAsnOrderByBizDate("2022-01-04");
    }
}
