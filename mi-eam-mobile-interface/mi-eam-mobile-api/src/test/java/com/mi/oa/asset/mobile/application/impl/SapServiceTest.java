package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.dto.sap.Sap107JsonDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapPhoneTransferDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapProjectInfoDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SapService;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

/**
 * @Desc: SRM接口单元测试
 * @Author: huy
 * @Date: 2022/1/5
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class SapServiceTest {

    @Autowired
    private SapService sapService;

    @Test
    public void test(){
        List<SapProjectInfoDTO> ret =sapService.getProjectInfo("G7");
        System.out.println(GsonUtil.toJsonString(ret));
    }
}
