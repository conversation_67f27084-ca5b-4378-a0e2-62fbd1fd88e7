package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoTranDetService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
class ProtoTranDetServiceImplTest {

    @Autowired
    private ProtoTranDetService protoTranDetService;

    @Test
    void getDetGroupBy() {
        List<ProtoTranDetPO> tranDetPOS = protoTranDetService.getDetGroupBy("mi-708-8851");
        Assertions.assertTrue(tranDetPOS.size() > 0);
    }
}