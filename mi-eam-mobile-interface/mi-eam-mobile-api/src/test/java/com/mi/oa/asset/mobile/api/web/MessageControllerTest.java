package com.mi.oa.asset.mobile.api.web;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.api.model.req.EmailReq;
import com.mi.oa.asset.mobile.api.model.req.LarkReq;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MobileApiApplication.class)
//测试环境使用，用来表示测试环境使用的ApplicationContext将是WebApplicationContext类型的
@WebAppConfiguration
@Disabled
class MessageControllerTest {

    @Autowired
    private MessageController controller;

    @Test
    void sendEmail() {
        BaseResp baseResp = controller.sendEmail(EmailReq
                .builder()
                .emails(Arrays.asList("<EMAIL>"))
                .title("test")
                .content("test")
                .build());
        Assertions.assertEquals(BaseResp.CODE_SUCCESS, baseResp.getCode());
    }

    @Test
    void sendLark() {
        BaseResp baseResp = controller.sendLark(LarkReq
                .builder()
                .userNames(Arrays.asList("caoxinyi"))
                .title("test")
                .content("test")
                .url("http://www.baidu.com")
                .build());
        Assertions.assertEquals(BaseResp.CODE_SUCCESS, baseResp.getCode());
    }
}