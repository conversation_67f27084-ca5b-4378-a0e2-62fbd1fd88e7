package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.application.dto.order.BillReqDTO;
import com.mi.oa.asset.mobile.application.dto.order.ProtoOrderDTO;
import com.mi.oa.asset.mobile.application.dto.order.PurOrderDTO;
import com.mi.oa.asset.mobile.application.service.ProtoOrderBOService;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.MrpTypeEnum;
import com.mi.oa.asset.mobile.infra.config.MallSyncBillConfig;
import com.mi.oa.asset.mobile.infra.remote.sdk.AssetMallClient;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOrderPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoOrderPayService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoOrderService;
import com.xiaomi.core.test.TestWithKeycenter;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/1
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class ProtoOrderBOServiceTest implements TestWithKeycenter {

    @Autowired
    private ProtoOrderBOService protoOrderBOService;

    @Autowired
    private ProtoOrderService protoOrderService;

    @Autowired
    private ProtoOrderPayService protoOrderPayService;

    @Autowired
    private AssetMallClient assetMallClient;

    @Autowired
    private MallSyncBillConfig syncBillConfig;

    @Test
    void test(){
        PurOrderDTO orderDTO = new PurOrderDTO();
        orderDTO.setPlanId(2);
        orderDTO.setUserCode("v-huyong");
        orderDTO.setUserName("胡勇");
        protoOrderBOService.generatePurOrder(orderDTO);
    }

    @Test
    void test1(){
        ProtoOrderPO orderPO = protoOrderService.getById(5514);
        protoOrderService.notifySendLark(orderPO);
    }

    @Test
    void test2(){
        ProtoOrderPO orderPO = protoOrderService.getById(5514);
        protoOrderService.completeSendLark(orderPO);

        protoOrderService.checkNoticeSendLark(Arrays.asList(orderPO), null,"", CommonConstant.PROTO_ORDER_CHECK_BEIJING);
    }

    @Test
    void test3(){
        ProtoOrderPO orderPO = protoOrderService.getById(9);
        protoOrderService.notifyUpload(orderPO);
    }

    @Test
    void test5(){
        protoOrderPayService.orderSubmit("**********");
    }

    @Test
    void test6(){
        ProtoOrderDTO orderDTO = new ProtoOrderDTO();
        orderDTO.setOrderIdList(Lists.list(9));
        orderDTO.setUserCode("v-huyong");
        orderDTO.setUserName("胡勇");
        protoOrderBOService.notifyPay(orderDTO);
    }

    @Test
    void test7(){
        ProtoOrderPO orderPO = protoOrderService.getById(9);
        protoOrderService.notifyCancel(orderPO);
    }

    @Test
    void test8(){
        ProtoOrderDTO orderDTO = new ProtoOrderDTO();
        orderDTO.setOrderIdList(Lists.list(31));
        orderDTO.setUserCode("v-huyong");
        orderDTO.setUserName("胡勇");
        protoOrderBOService.soldOut(orderDTO);
    }

    @Test
    void test9(){
        ProtoOrderPO orderPO = protoOrderService.getById(21);
        protoOrderBOService.complete(orderPO);
    }

    @Test
    void test10(){
        ProtoOrderPO orderPO = protoOrderService.getById(21);
        protoOrderService.notifyAdminCancel(orderPO, "v-huyong", "胡勇");
    }

    @Test
    void test11(){
        for (String mrpType:syncBillConfig.getMrpTypeList() ) {
            protoOrderBOService.checkAccount("2022-08-22", mrpType);
        }
    }

    @Test
    void test12(){
        protoOrderBOService.planProgress("NGJH202208000201");
    }

    @Test
    void test13(){
        protoOrderBOService.incomeProgress("NGJH202208000201");
    }

    @Test
    void test16(){
        BillReqDTO billReqDTO = BillReqDTO.builder().billDate("2022-08-16").mrpType(MrpTypeEnum.AFTER_SALE.getType()).build();
        assetMallClient.payBill(billReqDTO);
    }

    @Test
    void test17(){
        BillReqDTO billReqDTO = BillReqDTO.builder().billDate("2022-08-16").mrpType(MrpTypeEnum.AFTER_SALE.getType()).build();
        assetMallClient.accountBill(billReqDTO);
    }

}
