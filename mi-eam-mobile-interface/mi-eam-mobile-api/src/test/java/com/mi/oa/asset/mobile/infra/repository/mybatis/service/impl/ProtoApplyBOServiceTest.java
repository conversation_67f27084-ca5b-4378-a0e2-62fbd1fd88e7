package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.application.service.ProtoApplyBOService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoApplyService;
import com.xiaomi.core.test.TestWithKeycenter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/2/21
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class ProtoApplyBOServiceTest implements TestWithKeycenter {

    @Autowired
    private ProtoApplyBOService protoApplyBOService;

    @Autowired
    private ProtoApplyService protoApplyService;

    @Test
    void initProtoApplyMakeCnTest(){
        protoApplyBOService.searchAndSetMakeCn(ProtoApplyPO.builder().applyCode("YJFM003871").build());
    }

    @Test
    void test(){
        ProtoApplyPO applyPO = protoApplyService.getByApplyCode("YJFM003455");
        protoApplyBOService.pushSap(applyPO);
    }
}
