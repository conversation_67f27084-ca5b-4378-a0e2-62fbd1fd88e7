package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.mi.oa.asset.mobile.utils.DateUtil;
import org.junit.Test;
import org.junit.jupiter.api.Disabled;

import java.util.Calendar;
import java.util.Date;
/**
 * Copyright (c) 2024 XiaoMi Inc.All Rights Reserved.
 * Description: your description
 *
 * <AUTHOR>
 * @date 2024/11/27 16:07
 */
@Disabled
public class TimeCalcTest {

    @Test
    public void CalcTime() throws InterruptedException {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.add(Calendar.HOUR, 2);
        Date startTime = calendar.getTime();
        calendar.add(Calendar.MINUTE, 1);
        Date endTime = calendar.getTime();

        System.out.println("startTime: " + DateUtil.getDateStr(startTime, "yyyy-MM-dd HH:mm:ss SSS"));
        System.out.println("endTime: " + DateUtil.getDateStr(endTime, "yyyy-MM-dd HH:mm:ss SSS"));
    }
}
