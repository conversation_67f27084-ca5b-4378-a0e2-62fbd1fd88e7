package com.mi.oa.asset.mobile.application.impl;

import com.mi.oa.asset.mobile.api.MobileApiApplication;
import com.mi.oa.asset.mobile.common.enums.RedisFlowNumEnum;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * @Desc: SRM接口单元测试
 * @Author: huy
 * @Date: 2022/1/5
 */
@Slf4j
@SpringBootTest(classes = MobileApiApplication.class)
@Disabled
public class CommonServiceTest {

    @Autowired
    private CommonService commonService;

    @Test
    public void test(){
        String str = "{\"_funID\":\"proto_collect\",\"_pageType\":\"form\",\"_eventCode\":\"save\",\"_message\":\"\",\"_returnValue\":\"\",\"_returnExtData\":\"\",\"_userInfo\":{\"dept_code\":\"IT430204\",\"tenant_id\":\"jxstar\",\"end_date\":\"\",\"add_date\":\"\",\"bus_dept_name\":\"\",\"bus_dept_id\":\"\",\"user_name\":\"陈星宇\",\"psection\":\"\",\"begin_date\":\"\",\"memo\":\"\",\"address_name\":\"\",\"dept_level\":\"4\",\"center_name\":\"研发二队\",\"user_code\":\"chenxingyu\",\"bus_dept_code\":\"\",\"modify_userid\":\"\",\"comp_dept_code\":\"1130\",\"dept_type\":\"\",\"add_userid\":\"\",\"sign_pic\":\"\",\"manager\":\"\",\"long_dept_name\":\"信息技术部-企业效率研发部-研发二组-研发二队\",\"comp_dept_name\":\"北京小米移动软件有限公司\",\"address_id\":\"\",\"dept_name\":\"固资组\",\"fac_dept_code\":\"\",\"user_id\":\"chenxingyu\",\"phone\":\"\",\"center_code\":\"C113010174\",\"is_novalid\":\"0\",\"parent_id\":\"IT4302\",\"is_valid\":\"1\",\"dept_id\":\"IT430204\",\"fac_dept_id\":\"\",\"modify_date\":\"\"},\"_clientInfo\":{\"sec-fetch-mode\":\"cors\",\"referer\":\"http://localhost:3000/main.html?usercode\\u003dchenxingyu\",\"content-length\":\"624\",\"sec-fetch-site\":\"same-origin\",\"cookie\":\"token\\u003deyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJlYW0iLCJzdWIiOiJjaGVueGluZ3l1IiwiaXNzIjoiZWFtIiwiZXhwIjoxNjQxODY0MTUxLCJpYXQiOjE2NDE3Nzc3NTF9.l8GW9LOnCDuLY7ppWaaPQL4-E8-o9EqXCYcgmPlRpT0\",\"accept-language\":\"zh-CN,zh;q\\u003d0.9\",\"origin\":\"http://localhost:3000\",\"usertoken\":\"9239F65126E4AC1062F3B3D2393FC010\",\"request_url\":\"http://localhost:8787/api/jxs/common\",\"accept\":\"*/*\",\"sec-ch-ua\":\"\\u0026quot; Not A;Brand\\u0026quot;;v\\u003d\\u0026quot;99\\u0026quot;, \\u0026quot;Chromium\\u0026quot;;v\\u003d\\u0026quot;96\\u0026quot;, \\u0026quot;Google Chrome\\u0026quot;;v\\u003d\\u0026quot;96\\u0026quot;\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\u0026quot;Windows\\u0026quot;\",\"x-requested-with\":\"XMLHttpRequest\",\"tenantid\":\"jxstar\",\"host\":\"localhost:8787\",\"client_ip\":\"127.0.0.1\",\"content-type\":\"application/x-www-form-urlencoded; charset\\u003dUTF-8\",\"connection\":\"close\",\"max_interval\":\"1800000\",\"accept-encoding\":\"gzip, deflate, br\",\"sec-fetch-dest\":\"empty\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36\"},\"_requestMap\":{\"pagetype\":\"form\",\"eventcode\":\"save\",\"dirtyfields\":\"proto_collect.remark\",\"proto_collect.stage_name\":\"203\",\"dataType\":\"json\",\"keyid\":\"mi-343-3353\",\"proto_collect.modify_date\":\"\",\"proto_collect.remark\":\"dfbfhh\",\"proto_collect.remark\":\"dfbfhh\",\"funid\":\"proto_collect\",\"proto_collect.pm_emp_code\":\"44234\",\"proto_collect.deal_line\":\"2022-01-20 09:29:00\",\"proto_collect.collect_id\":\"mi-343-3353\",\"proto_collect.pm_user_name\":\"欧景翠\",\"user_id\":\"chenxingyu\",\"proto_collect.auditing\":\"0\",\"proto_collect.collect_code\":\"XQ202201000033\",\"fkValue\":\"\",\"appid\":\"jxm\",\"proto_collect.project_name\":\"小米手机5S\",\"proto_collect.pm_user_code\":\"oujingcui\",\"proto_collect.project_code\":\"A7\"},\"_mpSession\":{}}";
        RequestContext requestContext = GsonUtil.toBean(str, RequestContext.class);
        commonService.updateEvent(requestContext);
    }

    @Test
    public void test1(){
        String str = "{\"_funID\":\"proto_collect\",\"_pageType\":\"form\",\"_eventCode\":\"create\",\"_message\":\"\",\"_returnValue\":\"\",\"_returnExtData\":\"\",\"_userInfo\":{\"dept_code\":\"IT430204\",\"tenant_id\":\"jxstar\",\"end_date\":\"\",\"add_date\":\"\",\"bus_dept_name\":\"\",\"bus_dept_id\":\"\",\"user_name\":\"陈星宇\",\"psection\":\"\",\"begin_date\":\"\",\"memo\":\"\",\"address_name\":\"\",\"dept_level\":\"4\",\"center_name\":\"研发二队\",\"user_code\":\"chenxingyu\",\"bus_dept_code\":\"\",\"modify_userid\":\"\",\"comp_dept_code\":\"1130\",\"dept_type\":\"\",\"add_userid\":\"\",\"sign_pic\":\"\",\"manager\":\"\",\"long_dept_name\":\"信息技术部-企业效率研发部-研发二组-研发二队\",\"comp_dept_name\":\"北京小米移动软件有限公司\",\"address_id\":\"\",\"dept_name\":\"固资组\",\"fac_dept_code\":\"\",\"user_id\":\"chenxingyu\",\"phone\":\"\",\"center_code\":\"C113010174\",\"is_novalid\":\"0\",\"parent_id\":\"IT4302\",\"is_valid\":\"1\",\"dept_id\":\"IT430204\",\"fac_dept_id\":\"\",\"modify_date\":\"\"},\"_clientInfo\":{\"sec-fetch-mode\":\"cors\",\"referer\":\"http://localhost:3000/main.html?usercode\\u003dchenxingyu\",\"content-length\":\"590\",\"sec-fetch-site\":\"same-origin\",\"cookie\":\"token\\u003deyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJlYW0iLCJzdWIiOiJjaGVueGluZ3l1IiwiaXNzIjoiZWFtIiwiZXhwIjoxNjQxOTU1MjYzLCJpYXQiOjE2NDE4Njg4NjN9.GzO1pah_HQSwUeBQbjLautSBscE6kOPnIQD_7Zz-hEY\",\"accept-language\":\"zh-CN,zh;q\\u003d0.9\",\"origin\":\"http://localhost:3000\",\"usertoken\":\"CF3B00FB9B13B59E71BE95FEAFC74DEB\",\"request_url\":\"http://localhost:8787/api/jxs/common\",\"accept\":\"*/*\",\"sec-ch-ua\":\"\\u0026quot; Not A;Brand\\u0026quot;;v\\u003d\\u0026quot;99\\u0026quot;, \\u0026quot;Chromium\\u0026quot;;v\\u003d\\u0026quot;96\\u0026quot;, \\u0026quot;Google Chrome\\u0026quot;;v\\u003d\\u0026quot;96\\u0026quot;\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\u0026quot;Windows\\u0026quot;\",\"x-requested-with\":\"XMLHttpRequest\",\"tenantid\":\"jxstar\",\"host\":\"localhost:8787\",\"client_ip\":\"127.0.0.1\",\"content-type\":\"application/x-www-form-urlencoded; charset\\u003dUTF-8\",\"connection\":\"close\",\"max_interval\":\"1800000\",\"accept-encoding\":\"gzip, deflate, br\",\"sec-fetch-dest\":\"empty\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36\"},\"_requestMap\":{\"pagetype\":\"form\",\"eventcode\":\"create\",\"tmpkeyid\":\"tmp-c4f1046c7f968c1e5b7b\",\"proto_collect.stage_name\":\"200\",\"dataType\":\"json\",\"keyid\":\"\",\"proto_collect.modify_date\":\"\",\"proto_collect.remark\":\"hjghj\",\"funid\":\"proto_collect\",\"proto_collect.pm_emp_code\":\"44234\",\"proto_collect.deal_line\":\"2022-01-19 10:43:00\",\"proto_collect.collect_id\":\"\",\"proto_collect.pm_user_name\":\"欧景翠\",\"user_id\":\"chenxingyu\",\"proto_collect.auditing\":\"0\",\"proto_collect.collect_code\":\"\",\"fkValue\":\"\",\"appid\":\"jxm\",\"proto_collect.project_name\":\"小米手机5S\",\"proto_collect.pm_user_code\":\"oujingcui\",\"proto_collect.project_code\":\"A7\"},\"_mpSession\":{}}";
        RequestContext requestContext = GsonUtil.toBean(str, RequestContext.class);
        commonService.createEvent(requestContext);
    }

    @Test
    public void test3(){
        String str = "{\"_funID\":\"proto_project_list\",\"_pageType\":\"editgrid\",\"_eventCode\":\"save_eg\",\"_message\":\"\",\"_returnValue\":\"\",\"_returnExtData\":\"\",\"_userInfo\":{\"dept_code\":\"IT430204\",\"tenant_id\":\"jxstar\",\"end_date\":\"\",\"add_date\":\"\",\"bus_dept_name\":\"\",\"bus_dept_id\":\"\",\"user_name\":\"陈星宇\",\"psection\":\"\",\"begin_date\":\"\",\"memo\":\"\",\"address_name\":\"\",\"dept_level\":\"4\",\"center_name\":\"研发二队\",\"user_code\":\"chenxingyu\",\"bus_dept_code\":\"\",\"modify_userid\":\"\",\"comp_dept_code\":\"1130\",\"dept_type\":\"\",\"add_userid\":\"\",\"sign_pic\":\"\",\"manager\":\"\",\"long_dept_name\":\"信息技术部-企业效率研发部-研发二组-研发二队\",\"comp_dept_name\":\"北京小米移动软件有限公司\",\"address_id\":\"\",\"dept_name\":\"固资组\",\"fac_dept_code\":\"\",\"user_id\":\"chenxingyu\",\"phone\":\"\",\"center_code\":\"C113010174\",\"is_novalid\":\"0\",\"parent_id\":\"IT4302\",\"is_valid\":\"1\",\"dept_id\":\"IT430204\",\"fac_dept_id\":\"\",\"modify_date\":\"\"},\"_clientInfo\":{\"sec-fetch-mode\":\"cors\",\"referer\":\"http://localhost:3000/main.html?usercode\\u003dchenxingyu\",\"content-length\":\"622\",\"sec-fetch-site\":\"same-origin\",\"cookie\":\"token\\u003deyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJlYW0iLCJzdWIiOiJjaGVueGluZ3l1IiwiaXNzIjoiZWFtIiwiZXhwIjoxNjQxOTY3MzYwLCJpYXQiOjE2NDE4ODA5NjB9.4hzOojb_F6eXlf_8aQJkV-PQhfXFpxpxi-6yxlNyoDY\",\"accept-language\":\"zh-CN,zh;q\\u003d0.9\",\"origin\":\"http://localhost:3000\",\"usertoken\":\"B6FF514BC087AD250E99BBD32DC4C42E\",\"request_url\":\"http://localhost:8787/api/jxs/common\",\"accept\":\"*/*\",\"sec-ch-ua\":\"\\u0026quot; Not A;Brand\\u0026quot;;v\\u003d\\u0026quot;99\\u0026quot;, \\u0026quot;Chromium\\u0026quot;;v\\u003d\\u0026quot;96\\u0026quot;, \\u0026quot;Google Chrome\\u0026quot;;v\\u003d\\u0026quot;96\\u0026quot;\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\u0026quot;Windows\\u0026quot;\",\"x-requested-with\":\"XMLHttpRequest\",\"tenantid\":\"jxstar\",\"host\":\"localhost:8787\",\"client_ip\":\"127.0.0.1\",\"content-type\":\"application/x-www-form-urlencoded; charset\\u003dUTF-8\",\"connection\":\"close\",\"max_interval\":\"1800000\",\"accept-encoding\":\"gzip, deflate, br\",\"sec-fetch-dest\":\"empty\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36\"},\"_requestMap\":{\"pagetype\":\"editgrid\",\"eventcode\":\"save_eg\",\"dataType\":\"json\",\"keyid\":[\"mi-011-701\",\"\"],\"proto_project_list.auditing\":[\"0\",\"0\"],\"proto_project_list.project_code\":[\"45\",\"4t\"],\"funid\":\"proto_project_list\",\"proto_project_list.project_name\":[\"4\",\"554\"],\"user_id\":\"chenxingyu\",\"proto_project_list.pm_user_code\":[\"\",\"\"],\"appid\":\"jxm\",\"proto_project_list.pm_user_name\":[\"\",\"\"],\"proto_project_list.pm_emp_code\":[\"\",\"\"],\"proto_project_list.project_list_id\":[\"mi-011-701\",\"\"]},\"_mpSession\":{}}";
        RequestContext requestContext = GsonUtil.toBean(str, RequestContext.class);
        commonService.gridSaveEvent(requestContext);
    }

    @Test
    public void test4(){
        String str = "{\"_funID\":\"proto_collect\",\"_pageType\":\"form\",\"_eventCode\":\"audit\",\"_message\":\"\",\"_returnValue\":\"\",\"_returnExtData\":\"\",\"_userInfo\":{\"dept_code\":\"IT430204\",\"tenant_id\":\"jxstar\",\"end_date\":\"\",\"add_date\":\"\",\"bus_dept_name\":\"\",\"bus_dept_id\":\"\",\"user_name\":\"陈星宇\",\"psection\":\"\",\"begin_date\":\"\",\"memo\":\"\",\"address_name\":\"\",\"dept_level\":\"4\",\"center_name\":\"研发二队\",\"user_code\":\"chenxingyu\",\"bus_dept_code\":\"\",\"modify_userid\":\"\",\"comp_dept_code\":\"1130\",\"dept_type\":\"\",\"add_userid\":\"\",\"sign_pic\":\"\",\"manager\":\"\",\"long_dept_name\":\"信息技术部-企业效率研发部-研发二组-研发二队\",\"comp_dept_name\":\"北京小米移动软件有限公司\",\"address_id\":\"\",\"dept_name\":\"固资组\",\"fac_dept_code\":\"\",\"user_id\":\"chenxingyu\",\"phone\":\"\",\"center_code\":\"C113010174\",\"is_novalid\":\"0\",\"parent_id\":\"IT4302\",\"is_valid\":\"1\",\"dept_id\":\"IT430204\",\"fac_dept_id\":\"\",\"modify_date\":\"\"},\"_clientInfo\":{\"sec-fetch-mode\":\"cors\",\"referer\":\"http://localhost:3000/main.html?usercode\\u003dchenxingyu\",\"content-length\":\"127\",\"sec-fetch-site\":\"same-origin\",\"cookie\":\"token\\u003deyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJlYW0iLCJzdWIiOiJjaGVueGluZ3l1IiwiaXNzIjoiZWFtIiwiZXhwIjoxNjQyNjQ0NDI5LCJpYXQiOjE2NDI1NTgwMjl9.HVChziI1A_VVvu-ENoFvRGW8s2VXrdORBkWLUYKsLN4\",\"accept-language\":\"zh-CN,zh;q\\u003d0.9\",\"origin\":\"http://localhost:3000\",\"usertoken\":\"undefined\",\"request_url\":\"http://localhost:8787/api/jxs/common\",\"accept\":\"*/*\",\"sec-ch-ua\":\"\\u0026quot; Not;A Brand\\u0026quot;;v\\u003d\\u0026quot;99\\u0026quot;, \\u0026quot;Google Chrome\\u0026quot;;v\\u003d\\u0026quot;97\\u0026quot;, \\u0026quot;Chromium\\u0026quot;;v\\u003d\\u0026quot;97\\u0026quot;\",\"sec-ch-ua-mobile\":\"?0\",\"sec-ch-ua-platform\":\"\\u0026quot;Windows\\u0026quot;\",\"x-requested-with\":\"XMLHttpRequest\",\"tenantid\":\"jxstar\",\"host\":\"localhost:8787\",\"client_ip\":\"127.0.0.1\",\"content-type\":\"application/x-www-form-urlencoded; charset\\u003dUTF-8\",\"connection\":\"close\",\"max_interval\":\"1800000\",\"accept-encoding\":\"gzip, deflate, br\",\"sec-fetch-dest\":\"empty\",\"user-agent\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.71 Safari/537.36\"},\"_requestMap\":{\"pagetype\":\"form\",\"eventcode\":\"audit\",\"user_id\":\"chenxingyu\",\"dataType\":\"json\",\"appid\":\"jxstar\",\"keyid\":\"220117000002\",\"auditvalue\":\"1\",\"funid\":\"proto_collect\"},\"_mpSession\":{}}";
        RequestContext requestContext = GsonUtil.toBean(str, RequestContext.class);
        commonService.commonAudit(requestContext);
    }

    @Test
    public void test5(){
        String flowNum = commonService.getFlowNum(RedisFlowNumEnum.PROJECT_CODE_NUM);
        System.out.println(flowNum);
    }
}
