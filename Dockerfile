ARG GROUPID=com.mi.oa.asset.mobile
# build step
FROM hub.pf.xiaomi.com/infoarch/maven-openjdk8-onbuild-artifactory as builder
# java-server-demo
ARG APP
ARG ENV
RUN  mkdir -p  /home/<USER>/data/www
WORKDIR /home/<USER>/data/www
# COPY项目入口
COPY . .
RUN  mvn -U -e -B clean package -pl ${GROUPID}:${APP} -am -DskipTests -P ${ENV}

# 部署 step
FROM hub.pf.xiaomi.com/neo-images/online-app:centos7.3-openjdk1.8
ARG APP
ARG ENV
WORKDIR /home/<USER>/data/www
COPY --from=hub.pf.xiaomi.com/oa/hengyunabc/arthas:latest /opt/arthas /home/<USER>/app/arthas
ADD https://pkgs.d.xiaomi.net/artifactory/releases/io/opentelemetry/javaagent/opentelemetry-javaagent/latest/opentelemetry-javaagent-latest.jar /home/<USER>/app/mitelemetry/agent/opentelemetry-javaagent-all.jar
COPY agentconfig/${APP}/agent_${ENV}.config /home/<USER>/app/mitelemetry/agent/agent.config
COPY --from=builder /home/<USER>/data/www/mi-eam-mobile-interface/${APP}/target/*.jar ./
# set soaagent env
RUN /bin/chown -R work:work /home/<USER>
USER root
CMD java -javaagent:/home/<USER>/app/mitelemetry/agent/opentelemetry-javaagent-all.jar   -Dotel.agent.config.file.enabled=true -jar ./*-SNAPSHOT.jar
