<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <!-- 申明是一个springboot项目 -->
    <parent>
        <groupId>com.mi.oa.infra.oaucf</groupId>
        <artifactId>oaucf-springboot-parent</artifactId>
        <version>1.0.1-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <!-- 指定了当前POM模型的版本，对于Maven2及Maven 3来说，它只能是4.0.0 -->
    <modelVersion>4.0.0</modelVersion>
    <!-- 当前pom的信息-->
    <groupId>com.mi.oa.asset.mobile</groupId>
    <artifactId>mi-eam-mobile-parent</artifactId>
    <packaging>pom</packaging>
    <version>${revision}</version>
    <name>mi-eam-mobile-parent</name>
    <description>oa mobile project</description>

    <!-- 属于当前pom的模块-->
    <modules>
        <module>mi-eam-mobile-application</module>
        <module>mi-eam-mobile-common/mi-eam-mobile-common</module>
        <module>mi-eam-mobile-common/mi-eam-mobile-common-application</module>
        <module>mi-eam-mobile-common/mi-eam-mobile-common-infra</module>
        <module>mi-eam-mobile-infra</module>
        <module>mi-eam-mobile-interface/mi-eam-mobile-api</module>
        <module>mi-eam-mobile-interface/mi-eam-mobile-task</module>
    </modules>

    <!-- 版本参数管理-->
    <properties>
        <revision>1.0-SNAPSHOT</revision>
        <!-- spring相关-->
        <springfox-boot.version>3.0.0</springfox-boot.version>
        <!--基础 build的插件版本 -->
        <java.version>1.8</java.version>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-compiler-plugin.encoding>UTF-8</maven-compiler-plugin.encoding>
        <maven-source-plugin.version>2.4</maven-source-plugin.version>
        <maven-checkstyle-plugin.version>2.17</maven-checkstyle-plugin.version>
        <flatten-maven-plugin.version>1.1.0</flatten-maven-plugin.version>
        <jacoco-maven-plugin.version>0.8.5</jacoco-maven-plugin.version>
        <mybatis-generator-maven-plugin.version>1.4.0</mybatis-generator-maven-plugin.version>
        <!-- 工具包的版本 -->
        <micrometer-registry-prometheus.version>1.7.0</micrometer-registry-prometheus.version>
        <lombok.version>1.18.8</lombok.version>
        <mapstruct.version>1.3.1.Final</mapstruct.version>
        <validation-api.version>2.0.1.Final</validation-api.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <commons-lang3.version>3.10</commons-lang3.version>
        <commons-pool.verison>2.6.1</commons-pool.verison>
        <guava.version>23.0</guava.version>
        <javax.persistence-api.version>2.2</javax.persistence-api.version>
        <assertj-core.version>3.16.1</assertj-core.version>
        <commons-io.version>2.6</commons-io.version>
        <gson.version>2.8.6</gson.version>
        <httpclient5.version>5.0.1</httpclient5.version>
        <zookeeper.version>3.4.14</zookeeper.version>
        <micrometer-core.version>1.5.5</micrometer-core.version>
        <okhttp.version>4.9.1</okhttp.version>
        <mi-asset-x5-version>0.0.1-SNAPSHOT</mi-asset-x5-version>
        <!-- 跟数据源相关的版本 -->
        <hibernate-validator.version>6.0.18.Final</hibernate-validator.version>
        <mybatis-typehandlers-jsr310.version>1.0.2</mybatis-typehandlers-jsr310.version>
        <mybatis-plus.version>3.4.1</mybatis-plus.version>
        <mysql-connector-java.version>8.0.11</mysql-connector-java.version>
        <dynamic-datasource-spring-boot-starter.version>3.2.0</dynamic-datasource-spring-boot-starter.version>
        <!-- 跟单元测试相关的版本 -->
        <junit.version>4.13</junit.version>
        <powermock.version>2.0.2</powermock.version>
        <!-- 小米自己的组件及jar包版本 -->
        <xms-plan-spring-boot-starter.version>4.0.0-RELEASE</xms-plan-spring-boot-starter.version>
        <common-x5protocol-core.version>1.1.9-SNAPSHOT</common-x5protocol-core.version>
        <ums.version>1.9.1</ums.version>
        <oaucf-idm-api-spring-boot-starter.version>1.0.2-SNAPSHOT</oaucf-idm-api-spring-boot-starter.version>
        <oaucf-bpm-spring-boot-starter.version>1.0.8-SNAPSHOT</oaucf-bpm-spring-boot-starter.version>
        <neptune.version>1.1.5-SNAPSHOT</neptune.version>
        <!-- nacos组件-->
        <nacos.version>2.2.1.RELEASE</nacos.version>
        <feign.version>2.2.9.RELEASE</feign.version>
    </properties>


    <dependencyManagement>
        <dependencies>
            <!-- 小米自己的组件及jar-->
            <dependency>
                <groupId>com.mi.xms</groupId>
                <artifactId>xms-plan-spring-boot-starter</artifactId>
                <version>${xms-plan-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.infra.oaucf</groupId>
                <artifactId>oaucf-bpm-spring-boot-starter</artifactId>
                <version>${oaucf-bpm-spring-boot-starter.version}</version>
            </dependency>

            <!-- nacos组件-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
                <version>${nacos.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                <version>${nacos.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>io.github.openfeign</groupId>
                        <artifactId>feign-core</artifactId>
                    </exclusion>
                </exclusions>
                <version>${feign.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.openfeign</groupId>
                <artifactId>feign-httpclient</artifactId>
                <version>${feign.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.github.openfeign</groupId>
                        <artifactId>feign-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 自身的项目jar包注册 -->
            <dependency>
                <groupId>com.mi.oa.asset.mobile</groupId>
                <artifactId>mi-eam-mobile-common</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.asset.mobile</groupId>
                <artifactId>mi-eam-mobile-common-application</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.asset.mobile</groupId>
                <artifactId>mi-eam-mobile-common-infra</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.asset.mobile</groupId>
                <artifactId>mi-eam-mobile-application</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.asset.mobile</groupId>
                <artifactId>mi-eam-mobile-infra</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 工具相关 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${springfox-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons-pool.verison}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>1.10.0</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-core</artifactId>
                <version>${micrometer-core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>${micrometer-registry-prometheus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.asset</groupId>
                <artifactId>mi-asset-x5</artifactId>
                <version>${mi-asset-x5-version}</version>
            </dependency>
            <!-- 测试 -->
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj-core.version}</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>${junit.version}</version>
            </dependency>

            <!-- ORM框架依赖 -->
            <dependency>
                <groupId>javax.persistence</groupId>
                <artifactId>javax.persistence-api</artifactId>
                <version>${javax.persistence-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-typehandlers-jsr310</artifactId>
                <version>${mybatis-typehandlers-jsr310.version}</version>
            </dependency>

            <!-- 数据库驱动依赖 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector-java.version}</version>
            </dependency>

            <!-- 业务周边系统 -->
            <dependency>
                <groupId>com.mioffice.ums</groupId>
                <artifactId>api-sdk</artifactId>
                <version>${ums.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.oa.infra.oaucf</groupId>
                <artifactId>oaucf-idm-api-spring-boot-starter</artifactId>
                <version>${oaucf-idm-api-spring-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mi.info.comb</groupId>
                <artifactId>neptune-client</artifactId>
                <version>${neptune.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents.client5</groupId>
                <artifactId>httpclient5</artifactId>
                <version>${httpclient5.version}</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>${powermock.version}</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>${powermock.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.zookeeper</groupId>
                <artifactId>zookeeper</artifactId>
                <version>${zookeeper.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.mi.oa.infra.oaucf</groupId>
            <artifactId>oaucf-base</artifactId>
            <version>${oaucf-base.version}</version>
        </dependency>
        <!-- 工具相关 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <!-- 测试 -->
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.mi.oa.asset.eam</groupId>-->
        <!--            <artifactId>eam-common</artifactId>-->
        <!--            <version>1.0-SNAPSHOT</version>-->
        <!--        </dependency>-->

        <!--修复安全漏洞升级-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.76</version>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.1.2-mdh2.1.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.xiaomi</groupId>
                    <artifactId>keycenter-agent-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>5.7.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.11.0</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>4.11.0</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>4.11.0</version>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>1.15.10</version>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <version>1.15.10</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.2</version>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.2</version>
        </dependency>

        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.11.0</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>4.11.0</version>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>4.11.0</version>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy</artifactId>
            <version>1.15.10</version>
        </dependency>
        <dependency>
            <groupId>net.bytebuddy</groupId>
            <artifactId>byte-buddy-agent</artifactId>
            <version>1.15.10</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.2</version>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.2</version>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>central</id>
            <name>maven-release-virtual</name>
            <url>https://pkgs.d.xiaomi.net:443/artifactory/maven-release-virtual</url>
        </repository>
        <repository>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>miremote</id>
            <name>maven-remote-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-remote-virtual</url>
        </repository>
        <repository>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>interval:10</updatePolicy>
            </snapshots>
            <id>snapshots</id>
            <name>maven-snapshot-virtual</name>
            <url>https://pkgs.d.xiaomi.net:443/artifactory/maven-snapshot-virtual</url>
        </repository>
    </repositories>
    <distributionManagement>
        <repository>
            <id>central</id>
            <name>maven-release-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-release-virtual</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>maven-snapshot-virtual</name>
            <url>https://pkgs.d.xiaomi.net/artifactory/maven-snapshot-virtual</url>
        </snapshotRepository>
    </distributionManagement>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <env>local</env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <env>test</env>
            </properties>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
            </properties>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources-env/${env}</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>${maven-source-plugin.version}</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <encoding>${maven-compiler-plugin.encoding}</encoding>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <testSource>${java.version}</testSource>
                    <testTarget>${java.version}</testTarget>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>false</skipTests>
                </configuration>
            </plugin>
            <!--            <plugin>-->
            <!--                <groupId>org.apache.maven.plugins</groupId>-->
            <!--                <artifactId>maven-checkstyle-plugin</artifactId>-->
            <!--                <version>${maven-checkstyle-plugin.version}</version>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <id>validate</id>-->
            <!--                        <phase>validate</phase>-->
            <!--                        <configuration>-->
            <!--                            <configLocation>checkstyle/picasso.xml</configLocation>-->
            <!--                            <excludes>com/mi/oa/asset/mobile/infra/repository/**/*.java</excludes>-->
            <!--                            <encoding>UTF-8</encoding>-->
            <!--                            <consoleOutput>true</consoleOutput>-->
            <!--                            <failsOnError>true</failsOnError>-->
            <!--                        </configuration>-->
            <!--                        <goals>-->
            <!--                            <goal>check</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->
        </plugins>
    </build>
</project>
