---
description: JUnit测试代码生成规则
globs: 
alwaysApply: false
---
---
description: 你是一个java程序员，按照下列规则生成单元测试
globs: 
alwaysApply: false
---

阅读此规则前先查看junit-task-rules.mdc

## 1. 规则定义
- **规则名称**: JUnit测试代码生成规则
- **版本**: 4.0
- **创建日期**: 2025-04-01
- **适用语言**: Java

## 2. 目标与范围
- **主要目标**: 快速生成高质量可直接运行的Java单元测试代码
- **适用场景**: Java服务方法的单元测试，Spring组件测试

## 3. 关键优化原则
- **使用mock而非实例**: 优先使用Mockito的mock方法和when-thenReturn模式，避免直接创建实例
- **避免反射**: 不使用ReflectionTestUtils直接设置字段，避免类型不匹配问题
- **关注契约而非实现**: 测试应关注方法契约和行为，而非内部实现细节
- **枚举处理**: 对枚举类型，必须通过查看实际代码获取正确的枚举值，不做猜测

## 4. 代码规范与约束
- **测试框架**:
  - JUnit 5 + Mockito 4
  - 测试类添加`@ExtendWith(MockitoExtension.class)`注解
  - 使用`@InjectMocks`和`@Mock`注入模拟对象

- **命名约定**:
  - 测试方法: `methodName_Scenario_ExpectedBehavior`
  - 测试类: `被测类名Test`

- **单测代码存储约定**:
  - 单测代码应该存储在方法对应module的test目录下，按原方法生成对应的文件路径

- **测试方法结构**:
  - 准备数据(Arrange) -> 执行测试(Act) -> 验证结果(Assert)
  - 每个分支逻辑至少一个测试用例

- **Mock最佳实践**:
  - 使用`mock(Class.class)`创建模拟对象
  - 使用`when(obj.method()).thenReturn(value)`设置行为
  - 对枚举或特殊类型，必须查看实际代码确定正确的值
  - 避免模拟静态方法，除非绝对必要
 
- **行覆盖率(Line Coverage)**: 每个方法的行覆盖率应不低于85%，整体类的行覆盖率应不低于80%。这确保了大部分代码行都被测试执行到。
- **分支覆盖率(Branch Coverage)**: 每个方法的分支覆盖率应不低于80%，整体类的分支覆盖率应不低于75%。特别关注if-else、switch-case、三元运算符等条件分支的覆盖。
- **条件覆盖率(Condition Coverage)**: 复杂条件表达式(如`if(a && b || c)`)中的每个子条件都应被测试到不同取值情况，覆盖率不低于75%。
- **方法覆盖率(Method Coverage)**: 类中的公共方法覆盖率应达到100%，确保每个公共方法至少有一个对应的测试方法。
- **循环覆盖率(Loop Coverage)**: 对于含有循环的代码(for、while、do-while)，应测试以下场景:
  - 循环体一次都不执行(如空集合)
  - 循环体执行一次(边界情况)
  - 循环体执行多次(正常情况)
- **异常覆盖率(Exception Coverage)**: 所有可能抛出异常的代码路径都应被测试，包括:
  - 预期异常的正确抛出
  - 异常处理逻辑的正确执行
  - 边界条件下的异常行为
- **数据路径覆盖**: 对于处理数据流的方法，确保测试覆盖:
  - 数据为空的情况
  - 数据只有一项的情况
  - 数据有多项的情况
  - 数据包含边界值或特殊值的情况
- **无测试盲点**: 不应存在未被任何测试用例覆盖的代码块或逻辑分支。特别关注那些看似简单但容易出错的代码片段，如默认分支、边界条件处理。
- **复杂条件的优化**: 对于条件复杂度(McCabe Cyclomatic Complexity)大于5的方法，应增加测试用例数量，确保覆盖所有可能的执行路径。

## 5. 简化生成流程
1. **快速分析**: 扫描目标方法的主要逻辑、入参和返回值，不需要查看每一个类，针对使用的才进行查看，关注xxxEnum枚举类。
2. **识别依赖**: 确定需要mock的依赖对象和方法
3. **设计测试用例**: 为每条主要逻辑路径设计一个测试方法
4. **生成测试代码**: 使用mock模式编写测试代码
5. **本地执行**: 直接提供可运行的单测代码，无需额外配置

## 6. 测试结果验证
- 测试代码应当在第一次生成后就能成功运行
- 如有错误，根据错误信息调整，优先检查枚举类型和mock对象

## 7. 代码模板
```java
@ExtendWith(MockitoExtension.class)
class ServiceClassTest {

    @InjectMocks
    private ServiceClass serviceClass;

    @Mock
    private DependencyClass dependencyClass;

    @Test
    void methodName_Scenario_ExpectedBehavior() {
        // 准备数据
        when(dependencyClass.method(any())).thenReturn(expectedResult);

        // 执行测试
        ResultType result = serviceClass.methodName(param);

        // 验证结果
        assertEquals(expectedValue, result.getProperty());
        verify(dependencyClass).method(any());
    }

}