<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>mi-eam-mobile-parent</artifactId>
        <groupId>com.mi.oa.asset.mobile</groupId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>mi-eam-mobile-infra</artifactId>

    <dependencies>
        <!-- 自身的jar包 -->
        <dependency>
            <groupId>com.mi.oa.asset.mobile</groupId>
            <artifactId>mi-eam-mobile-common-infra</artifactId>
        </dependency>

        <!-- 数据库驱动依赖 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- springboot mybatis-plus依赖 -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
        </dependency>

        <!-- x5组件 -->
        <dependency>
            <groupId>com.mi.oa.asset</groupId>
            <artifactId>mi-asset-x5</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mioffice.ums</groupId>
            <artifactId>api-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mi.oa.infra.oaucf</groupId>
            <artifactId>oaucf-cache-spring-boot-starter</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.mi.oa.infra.oaucf</groupId>
            <artifactId>oaucf-idm-api-spring-boot-starter</artifactId>


        </dependency>
        <dependency>
            <groupId>com.mi.oa.asset.mobile</groupId>
            <artifactId>mi-eam-mobile-common-application</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20210307</version>
        </dependency>

        <dependency>
            <groupId>com.xiaomi.mit</groupId>
            <artifactId>mit-starter</artifactId>
            <version>1.1.10</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!--自动生成mybatis代码-->
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>${mybatis-generator-maven-plugin.version}</version>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>${mysql-connector-java.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>com.mi.oa.infra.oaucf</groupId>
                        <artifactId>oaucf-mybatis-spring-boot-starter</artifactId>
                        <version>${oaucf-mybatis.version}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>Generate MyBatis Artifacts</id>
                        <phase>deploy</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <!--允许移动生成的文件 -->
                    <verbose>true</verbose>
                    <!-- 是否覆盖 -->
                    <overwrite>true</overwrite>
                    <!-- 自动生成的配置 -->
                    <configurationFile>
                        ../generator/mybatis-generator.xml
                    </configurationFile>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
