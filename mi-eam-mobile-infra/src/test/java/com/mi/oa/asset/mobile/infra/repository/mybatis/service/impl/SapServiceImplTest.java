package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.dto.sap.*;
import com.mi.oa.asset.mobile.infra.remote.sdk.SapClient;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.MdmService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoSapExeLogService;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import feign.Request;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class SapServiceImplTest {

    @InjectMocks
    private SapServiceImpl sapService;

    @Mock
    private ProtoSapExeLogService protoSapExeLogService;

    @Mock
    private SapClient sapClient;

    @Mock
    private MdmService mdmService;

    @Test
    void sap107Json_TVMrpType_Success() {
        // 准备数据
        Sap107JsonDTO jsonDTO = createBasicJsonDTO();
        jsonDTO.setSapFactory("1111");
        String mrpType = MrpTypeEnum.TV.getType();
        
        when(protoSapExeLogService.insertLog(any(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn("logId123");
        
        SapCustomVO expectedResult = new SapCustomVO();
        expectedResult.setSuccess(true);
        expectedResult.setMessage("成功");
        when(sapClient.sap107Json(any(SapMierDTO.class), any(Request.Options.class)))
                .thenReturn(expectedResult);

        // 执行测试
        SapCustomVO result = sapService.sap107Json(jsonDTO, "funId", "orderId", "orderCode", "orderName", "doUser", mrpType);

        // 验证结果
        assertTrue(result.getSuccess());
        assertEquals("成功", result.getMessage());
        verify(protoSapExeLogService).insertLog(any(), eq("funId"), eq("orderId"), eq("orderCode"), eq("orderName"), eq("doUser"));
        verify(protoSapExeLogService).updateLog(eq("logId123"), eq(SapRetEnum.SUCCESS.getKey()), eq("成功"));
        verify(sapClient).sap107Json(any(SapMierDTO.class), any(Request.Options.class));
    }

    @Test
    void sap107Json_WearableMrpType_Success() {
        // 准备数据
        Sap107JsonDTO jsonDTO = createBasicJsonDTO();
        String mrpType = MrpTypeEnum.WEARABLE.getType();
        
        when(protoSapExeLogService.insertLog(any(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn("logId123");
        
        SapCustomVO expectedResult = new SapCustomVO();
        expectedResult.setSuccess(true);
        when(sapClient.sap107Json(any(SapMierDTO.class), any(Request.Options.class)))
                .thenReturn(expectedResult);

        // 执行测试
        SapCustomVO result = sapService.sap107Json(jsonDTO, "funId", "orderId", "orderCode", "orderName", "doUser", mrpType);

        // 验证结果
        assertTrue(result.getSuccess());
        verify(sapClient).sap107Json(any(SapMierDTO.class), any(Request.Options.class));
    }

    @Test
    void sap107Json_EcochainMrpType_MidianFactory_Success() {
        // 准备数据
        Sap107JsonDTO jsonDTO = createBasicJsonDTO();
        jsonDTO.setSapFactory(SapMakeCnEnum.ECO_MIDIAN.getSapFactory());
        String mrpType = MrpTypeEnum.ECOCHAIN.getType();
        
        when(protoSapExeLogService.insertLog(any(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn("logId123");
        
        SapCustomVO expectedResult = new SapCustomVO();
        expectedResult.setSuccess(true);
        when(sapClient.sap107Json(any(SapMierDTO.class), any(Request.Options.class)))
                .thenReturn(expectedResult);

        // 执行测试
        SapCustomVO result = sapService.sap107Json(jsonDTO, "funId", "orderId", "orderCode", "orderName", "doUser", mrpType);

        // 验证结果
        assertTrue(result.getSuccess());
    }

    @Test
    void sap107Json_EcochainMrpType_InvalidFactory_ThrowsException() {
        // 准备数据
        Sap107JsonDTO jsonDTO = createBasicJsonDTO();
        jsonDTO.setSapFactory("invalidFactory");
        String mrpType = MrpTypeEnum.ECOCHAIN.getType();

        // 执行测试并验证异常
        BizException exception = assertThrows(BizException.class, () -> {
            sapService.sap107Json(jsonDTO, "funId", "orderId", "orderCode", "orderName", "doUser", mrpType);
        });

//        assertEquals(ApplicationErrorCodeEnum.EAM_BIZ_ERROR, exception.getMessage());
        assertEquals("{{{生态链SAP工厂错误}}}", exception.getMessage());
    }

    @Test
    void sap107Json_ChangPingFactory_ZDBBsart_Success() {
        // 准备数据
        Sap107JsonDTO jsonDTO = createBasicJsonDTO();
        jsonDTO.setSapFactory(SapFactoryEnum.CHANG_PING.getSapFactory());
        jsonDTO.setBsart(SrmPurchaseTypeEnum.ZDB.getCode());
        jsonDTO.setBusiType(CommonConstant.SapBusiTypeEnum.RECEIPT_GOODS.getType());
        String mrpType = MrpTypeEnum.TV.getType();
        
        when(protoSapExeLogService.insertLog(any(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn("logId123");
        
        SapCustomVO expectedResult = new SapCustomVO();
        expectedResult.setSuccess(true);
        when(sapClient.sap107Json(any(SapMierDTO.class), any(Request.Options.class)))
                .thenReturn(expectedResult);

        // 执行测试
        SapCustomVO result = sapService.sap107Json(jsonDTO, "funId", "orderId", "orderCode", "orderName", "doUser", mrpType);

        // 验证结果
        assertTrue(result.getSuccess());
    }

    @Test
    void sap107Json_MiAutoEaccMrpType_Success() {
        // 准备数据
        Sap107JsonDTO jsonDTO = createBasicJsonDTO();
        jsonDTO.setBusiType(CommonConstant.SapBusiTypeEnum.RECEIPT_GOODS.getType());
        String mrpType = MrpTypeEnum.MI_AUTOEACC.getType();
        
        when(protoSapExeLogService.insertLog(any(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn("logId123");
        
        SapCustomVO expectedResult = new SapCustomVO();
        expectedResult.setSuccess(true);
        when(sapClient.sap107Json(any(SapMierDTO.class), any(Request.Options.class)))
                .thenReturn(expectedResult);

        // 执行测试
        SapCustomVO result = sapService.sap107Json(jsonDTO, "funId", "orderId", "orderCode", "orderName", "doUser", mrpType);

        // 验证结果
        assertTrue(result.getSuccess());
    }

    @Test
    void sap107Json_SapClientReturnsFalse_UpdatesLogWithError() {
        // 准备数据
        Sap107JsonDTO jsonDTO = createBasicJsonDTO();
        String mrpType = MrpTypeEnum.TV.getType();
        
        when(protoSapExeLogService.insertLog(any(), anyString(), anyString(), anyString(), anyString(), anyString()))
                .thenReturn("logId123");
        
        SapCustomVO expectedResult = new SapCustomVO();
        expectedResult.setSuccess(false);
        expectedResult.setMessage("SAP调用失败");
        when(sapClient.sap107Json(any(SapMierDTO.class), any(Request.Options.class)))
                .thenReturn(expectedResult);

        // 执行测试
        SapCustomVO result = sapService.sap107Json(jsonDTO, "funId", "orderId", "orderCode", "orderName", "doUser", mrpType);

        // 验证结果
        assertFalse(result.getSuccess());
        assertEquals("SAP调用失败", result.getMessage());
        verify(protoSapExeLogService).updateLog(eq("logId123"), eq(SapRetEnum.ERROR.getKey()), eq("SAP调用失败"));
    }

    private Sap107JsonDTO createBasicJsonDTO() {
        Sap107JsonDTO jsonDTO = new Sap107JsonDTO();
        jsonDTO.setSapFactory("1111");
        jsonDTO.setBsart("ZNB");
        jsonDTO.setBusiType("YJFF");
        
        List<Sap107JsonDTO.SapItemDTO> itemList = new ArrayList<>();
        Sap107JsonDTO.SapItemDTO item = new Sap107JsonDTO.SapItemDTO();
        item.setMatDescOne("测试物料描述");
        itemList.add(item);
        jsonDTO.setItemDTOList(itemList);
        
        return jsonDTO;
    }
}