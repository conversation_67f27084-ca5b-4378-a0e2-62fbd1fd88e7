package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDelayPO;

/**
 * table_name : proto_delay
 * <AUTHOR>
 * @date 2022/02/17/07:16
 */
public interface ProtoDelayMapper extends BaseMapper<ProtoDelayPO> {
    /**
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String delayId);

    /**
     *
     * @mbg.generated
     */
    ProtoDelayPO selectByPrimaryKey(String delayId);
}