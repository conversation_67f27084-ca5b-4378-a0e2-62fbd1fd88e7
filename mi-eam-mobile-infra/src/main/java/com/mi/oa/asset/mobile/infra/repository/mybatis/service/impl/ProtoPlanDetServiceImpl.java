package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.dto.mall.InnerProductReq;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInnerPurchasePlanPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOrderPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPlanDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoPlanDetMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInnerPurchasePlanService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoPlanDetService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/1
 */
@Service
public class ProtoPlanDetServiceImpl extends ServiceImpl<ProtoPlanDetMapper, ProtoPlanDetPO> implements ProtoPlanDetService {

    @Autowired
    ProtoInnerPurchasePlanService protoInnerPurchasePlanService;

    @Override
    public List<ProtoPlanDetPO> listByPlanId(Integer planId) {
        LambdaQueryWrapper<ProtoPlanDetPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoPlanDetPO::getInnerPurchasePlanId, planId);
        return this.list(wrapper);
    }

    @Override
    public List<ProtoPlanDetPO> listByOrders(List<ProtoOrderPO> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return new ArrayList<>();
        }
        return baseMapper.listByOrders(orderList);
    }

    @Override
    public List<ProtoPlanDetPO> listByPlanNoAndDeviceCode(List<InnerProductReq> innerProductReqs) {
        List<ProtoPlanDetPO> protoPlanDetPOS = new ArrayList<>();
        for (InnerProductReq innerProductReq : innerProductReqs) {
            String planCode = innerProductReq.getPlanCode();
            String deviceCode = innerProductReq.getDeviceCode();
            List<ProtoPlanDetPO> detPOS = this.baseMapper.selectList(new LambdaQueryWrapper<ProtoPlanDetPO>()
                    .eq(ProtoPlanDetPO::getDeviceCode, deviceCode));
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(detPOS)) continue;
            for (ProtoPlanDetPO detPO : detPOS) {
                ProtoInnerPurchasePlanPO protoInnerPurchasePlanPO = protoInnerPurchasePlanService.getById(detPO.getInnerPurchasePlanId());
                if (protoInnerPurchasePlanPO.getOrderNo().equals(planCode)) {
                    protoPlanDetPOS.add(detPO);
                    break;
                }
            }
        }

        return protoPlanDetPOS;
    }

    @Override
    public String getProductConfigByDeviceCode(String deviceCode) {
        List<ProtoPlanDetPO> detPOS = this.baseMapper.selectList(new LambdaQueryWrapper<ProtoPlanDetPO>()
                .eq(ProtoPlanDetPO::getDeviceCode, deviceCode));
        if (CollectionUtils.isNotEmpty(detPOS)) {
            return detPOS.get(0).getPriceDesc();
        }
        return null;
    }
}
