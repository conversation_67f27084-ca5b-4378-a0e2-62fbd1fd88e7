package com.mi.oa.asset.mobile.infra.repository.mybatis.converter;

import com.mi.oa.asset.mobile.infra.dto.BpmExeLogDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.BpmExeLogPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * <AUTHOR>
 * @description: bpm相关属性转换
 * @date 2021/12/7 14:32
 */
@Mapper(componentModel = "spring")
public interface BpmExeLogConverter {
    @Mappings({
            @Mapping(target = "exeBody", ignore = true),
            @Mapping(target = "doDate", ignore = true)
    })
    BpmExeLogPO DTOToPO(BpmExeLogDTO dto);
}
