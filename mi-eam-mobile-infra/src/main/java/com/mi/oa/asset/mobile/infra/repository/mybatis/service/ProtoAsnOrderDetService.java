package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.infra.dto.AsnOrderDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderDetPO;

import java.util.Collection;
import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/2
 */
public interface ProtoAsnOrderDetService extends IService<ProtoAsnOrderDetPO> {

    /**
     * 通过asn单号删除
     * @param asnNo
     */
    void deleteByOrderDetId(String asnNo);

    /**
     * 判断是否收过货
     * @param asnNo
     * @return
     */
    boolean haveReceive(String asnNo);

    /**
     * 通过ASN单号修改详情表状态
     * @param asnNo
     * @param inStatus
     */
    void updateAsnOrderStatus(String asnNo, String inStatus);

    /**
     * 更新收货状态和收货数量
     * @param detId
     * @param inStatus
     * @param receiptNum
     */
    void updateAsnOrderStatusReceiptNum(String detId, String inStatus, Integer receiptNum);

    /**
     * 更新proto_asn_order_det  sapRetType 状态
     */
    void updateSapStatus();

    /**
     * 通过SN查询订单详情
     * @param sn
     * @return
     */
    ProtoAsnOrderDetPO getBySn(String sn);

    /**
     * 通过SN查询订单详情
     * @param snList
     * @return
     */
    List<ProtoAsnOrderDetPO> listBySnList(Collection<String> snList);

    /**
     * 通过SN获取待匹配清单
     * @param sn
     * @return
     */
    AsnOrderDTO getMachAsn(String sn);

    /**
     * 更新收货状态和收货数量
     * @param detId
     */
    void updateNotReceipt(String detId);

    /**
     * 通过AsnNo和状态查询数据
     * @param asnNo
     * @param inStatus
     * @return
     */
    Integer countByAsnAndStatus(String asnNo, String inStatus);

    /**
     * 有ASN收货
     * @param orderCode ASN单号
     * @param opt 操作
     * @param detIds 主键id
     * @param houseName 仓库名称
     * @param houseCode 仓库编码
     * @param houseId 仓库ID
     */
    String confirmReceiving(String orderCode, String opt, String[] detIds, String houseName, String houseCode, String houseId, UserInfo userInfo);

    /**
     *  通过ASN单查询详情
     * @param orderCode
     * @return
     */
    List<ProtoAsnOrderDetPO> listByOrderCode(String orderCode);

    /**
     * 通过SN查询成功抛送SAP的数据
     * @param deviceCode
     * @return
     */
    String getSapSuccessOrderCode(String deviceCode);
}
