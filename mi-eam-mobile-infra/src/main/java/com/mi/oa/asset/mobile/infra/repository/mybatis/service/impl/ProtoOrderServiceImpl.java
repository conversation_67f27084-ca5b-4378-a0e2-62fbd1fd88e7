package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.dto.message.LarkDTO;
import com.mi.oa.asset.mobile.application.dto.order.PlanProgressDTO;
import com.mi.oa.asset.mobile.application.dto.order.PreOrderDTO;
import com.mi.oa.asset.mobile.application.service.MessageService;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.remote.sdk.AssetMallClient;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MailTemplatePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOrderPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoOrderMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/1
 */
@Service
public class ProtoOrderServiceImpl extends ServiceImpl<ProtoOrderMapper, ProtoOrderPO> implements ProtoOrderService {

    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private MailTemplateService mailTemplateService;

    @Autowired
    private SysVarService sysVarService;

    @Autowired
    private ProtoProjectStageService projectStageService;

    @Autowired
    private AssetMallClient assetMallClient;

    @Autowired
    private ProtoPlanDetService planDetService;

    @Override
    public void updateStateByIds(List<Integer> orderIdList, AssetOrderStatusEnum orderStatusEnum) {
        if (CollectionUtils.isNotEmpty(orderIdList)){
            LambdaUpdateWrapper<ProtoOrderPO> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(ProtoOrderPO::getOrderStatus, orderStatusEnum.getState())
                    .in(ProtoOrderPO::getId, orderIdList);
            this.update(wrapper);
        }
    }

    @Override
    public void notifySendLark(ProtoOrderPO orderPO) {
        String dateStr = LocalDateTime.now().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        ProtoCardPO cardPO = new ProtoCardPO();
        BeanUtils.copyProperties(orderPO, cardPO);
        // String table = protoCardService.getLarkTableContentV1(Lists.list(cardPO), "", true);
        // 服务部售后内购
        buildDeviceType(cardPO);
        String table = protoCardService.getLarkTableContentV2(Lists.list(cardPO), "", true, orderPO.getMrpType(), true);
        Map<String, String> valueMap = new HashMap<>(8);
        valueMap.put("userInfo", orderPO.getUserName()+orderPO.getUserCode());
        valueMap.put("date", dateStr);
        valueMap.put("orderCode", orderPO.getSalesOrder());
        valueMap.put("table", table);

        String templateName = MessageTemplateEnum.getTemplateName(orderPO.getMrpType(), MessageTypeEnum.PAY.getCode());
        // String content = mailTemplateService.getMailContent(CommonConstant.PROTO_ORDER_ONLINE_NOTIFY, valueMap);
        String content = mailTemplateService.getMailContent(templateName, valueMap);
        String title = "【" + MrpTypeEnum.valuesOf(orderPO.getMrpType()).getAlias() + "】内购待支付";
        messageService.sendLark(LarkDTO.builder()
                .userNames(Lists.list(orderPO.getUserCode()))
                .content(content)
                .title(title)
                .url(orderPO.getPayLink())
                .buttonName("去支付")
                .botType(CommonConstant.MALL_BOT_TYPE)
                .build());
    }

    @Override
    public void checkNoticeSendLark(List<ProtoOrderPO> orderPOs, PreOrderDTO dto, String houseCode, String templateTag) {
        if(CollectionUtils.isEmpty(orderPOs)) return;
        List<ProtoCardPO> cardPOs = new ArrayList<>();
        for (ProtoOrderPO protoOrderPO : orderPOs) {
            ProtoCardPO cardPO = new ProtoCardPO();
            BeanUtils.copyProperties(protoOrderPO, cardPO);
            cardPOs.add(cardPO);
        }
        ProtoOrderPO orderPO = orderPOs.get(0);

        String address = "";
        String checkInfo = "";
        String productName = "";
        if (dto == null) {
            PreOrderDTO preOrderDTO = assetMallClient.getSKUProductInfo(orderPO.getMrpType(), orderPO.getSkuCode(), houseCode).getData();
            address = preOrderDTO.getAddress();
            checkInfo = preOrderDTO.getCheckInfo();
            productName = preOrderDTO.getBrief();
        } else {
            address = dto.getAddress();
            checkInfo = dto.getCheckInfo();
            productName = dto.getBrief();
        }
        String table = protoCardService.getLarkTableContentV2(cardPOs, productName, true, orderPO.getMrpType(), false);
        Map<String, String> valueMap = new HashMap<>(8);
        valueMap.put("userInfo", orderPO.getUserName()+orderPO.getUserCode());
        valueMap.put("productName", productName);
        valueMap.put("address", address);
        valueMap.put("checkInfo", checkInfo);
        valueMap.put("table", table);
        String content = mailTemplateService.getMailContent(templateTag, valueMap);
        MailTemplatePO templatePO = mailTemplateService.getByTemplateTag(templateTag);
        String title = templatePO.getTemplateName();
        // 手动提醒
        if (CommonConstant.PROTO_ORDER_CHECK_MANUAL.equals(templateTag)) {
            title = "【" + MrpTypeEnum.valuesOf(orderPO.getMrpType()).getAlias() + "】内购订单待验货";
        }
        messageService.sendLark(LarkDTO.builder()
                .userNames(Lists.list(orderPO.getUserCode()))
                .content(content)
                .title(title)
                .buttonName(CommonConstant.NO_BUTTON)
                .botType(CommonConstant.MALL_BOT_TYPE)
                .build());
    }

    @Override
    public void completeSendLark(ProtoOrderPO orderPO) {
        ProtoCardPO cardPO = new ProtoCardPO();
        BeanUtils.copyProperties(orderPO, cardPO);
        // String table = protoCardService.getLarkTableContent(cardPO, true);
        buildDeviceType(cardPO);
        String table = protoCardService.getLarkTableContent(Arrays.asList(cardPO), orderPO.getMrpType(),  true);
        Map<String, String> valueMap = new HashMap<>();
        valueMap.put("userInfo", orderPO.getUserName()+orderPO.getUserCode());
        valueMap.put("orderCode", orderPO.getSalesOrder());
        valueMap.put("table", table);
        String content = mailTemplateService.getMailContent(CommonConstant.PROTO_ORDER_COMPLETE, valueMap);
        String title = "【" + MrpTypeEnum.valuesOf(orderPO.getMrpType()).getAlias() + "】内购订单已完成";
        messageService.sendLark(LarkDTO.builder()
                .userNames(Lists.list(orderPO.getUserCode()))
                .content(content)
                .title(title)
                .buttonName(CommonConstant.NO_BUTTON)
                .botType(CommonConstant.MALL_BOT_TYPE)
                .build());
    }

    private void buildDeviceType(ProtoCardPO cardPO) {
        if (MrpTypeEnum.AFTER_SALE.getType().equals(cardPO.getMrpType())) {
            String deviceCode = cardPO.getDeviceCode();
            String deviceType = planDetService.getProductConfigByDeviceCode(deviceCode);
            cardPO.setDeviceType(deviceType);
        }
    }

    @Override
    public void notifyUpload(ProtoOrderPO orderPO) {
        String dateStr = LocalDateTime.now().plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        ProtoCardPO cardPO = new ProtoCardPO();
        BeanUtils.copyProperties(orderPO, cardPO);
        String table = protoCardService.getLarkTableContent(cardPO, true);
        Map<String, String> valueMap = new HashMap<>(8);
        valueMap.put("userInfo", orderPO.getUserName()+orderPO.getUserCode());
        valueMap.put("date", dateStr);
        valueMap.put("orderCode", orderPO.getSalesOrder());
        valueMap.put("table", table);
        String content = mailTemplateService.getMailContent(CommonConstant.PROTO_NOTIFY_UPLOAD, valueMap);
        String appid = sysVarService.findValByVarCode("mieam.app.apiid");
        String url = "https://applink.feishu.cn/client/mini_program/open?appId="+appid+"&mode=sidebar-semi&path=pages/uploadPaymentRecord/index?taskType=11%26taskId="+orderPO.getSalesOrder();
        messageService.sendLark(LarkDTO.builder()
                .userNames(Lists.list(orderPO.getUserCode()))
                .content(content)
                .title("【工程机】内购待支付")
                .buttonName("去上传")
                .url(url)
                .build());
    }

    @Override
    public void notifyCancel(ProtoOrderPO orderPO) {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        ProtoCardPO cardPO = new ProtoCardPO();
        BeanUtils.copyProperties(orderPO, cardPO);
        // String table = protoCardService.getLarkTableContentV1(Lists.list(cardPO), "", true);
        String table = protoCardService.getLarkTableContentV2(Lists.list(cardPO), "", true, orderPO.getMrpType(), false);
        Map<String, String> valueMap = new HashMap<>(8);
        valueMap.put("userInfo", orderPO.getUserName()+orderPO.getUserCode());
        valueMap.put("date", dateStr);
        valueMap.put("orderCode", orderPO.getSalesOrder());
        valueMap.put("table", table);
        String templateName = MessageTemplateEnum.getTemplateName(orderPO.getMrpType(), MessageTypeEnum.CANCEL.getCode());
        String content = mailTemplateService.getMailContent(/*CommonConstant.PROTO_ORDER_CANCEL*/templateName, valueMap);
        String title = "【" + MrpTypeEnum.valuesOf(orderPO.getMrpType()).getAlias() + "】内购订单已取消";
        messageService.sendLark(LarkDTO.builder()
                .userNames(Lists.list(orderPO.getUserCode()))
                .content(content)
                .title(title)
                .buttonName(CommonConstant.NO_BUTTON)
                .botType(CommonConstant.MALL_BOT_TYPE)
                .build());
    }

    @Override
    public void notifyAdminCancel(ProtoOrderPO orderPO, String userCode, String userName) {
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        ProtoCardPO cardPO = new ProtoCardPO();
        BeanUtils.copyProperties(orderPO, cardPO);
        String table = protoCardService.getLarkTableContent(cardPO, true);
        Map<String, String> valueMap = new HashMap<>(8);
        valueMap.put("userInfo", orderPO.getUserName()+orderPO.getUserCode());
        valueMap.put("dateStr", dateStr);
        valueMap.put("orderCode", orderPO.getSalesOrder());
        valueMap.put("table", table);
        valueMap.put("cancelUserInfo", userName+userCode);

        String content = mailTemplateService.getMailContent(CommonConstant.PROTO_ORDER_ADMIN_CANCEL, valueMap);
        String title = "【" + MrpTypeEnum.valuesOf(orderPO.getMrpType()).getAlias() + "】内购订单已取消";
        messageService.sendLark(LarkDTO.builder()
                .userNames(Lists.list(orderPO.getUserCode()))
                .content(content)
                .title(title)
                .buttonName(CommonConstant.NO_BUTTON)
                .botType(CommonConstant.MALL_BOT_TYPE)
                .build());
    }

    @Override
    public ProtoOrderPO getBySalesOrder(String salesOrder) {
        LambdaQueryWrapper<ProtoOrderPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoOrderPO::getSalesOrder, salesOrder);
        return this.getOne(wrapper);
    }

    @Override
    public ProtoOrderPO getByOrderId(String orderId) {
        LambdaQueryWrapper<ProtoOrderPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoOrderPO::getOrderId, orderId);
        return this.getOne(wrapper);
    }

    @Override
    public List<ProtoOrderPO> listBySalesOrder(List<String> salesOrderList) {
        if (CollectionUtils.isEmpty(salesOrderList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoOrderPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoOrderPO::getSalesOrder, salesOrderList);
        return this.list(wrapper);
    }

    @Override
    public Integer countByStatus(String orderNo, OrderTypeEnum orderTypeEnum, AssetOrderStatusEnum statusEnum) {
        LambdaQueryWrapper<ProtoOrderPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoOrderPO::getPlanCode, orderNo);
        if (null != orderTypeEnum){
            wrapper.eq(ProtoOrderPO::getOrderType, orderTypeEnum.getType());
        }
        if (null != statusEnum){
            wrapper.eq(ProtoOrderPO::getOrderStatus, statusEnum.getState());
        }
        return this.count(wrapper);
    }

    @Override
    public BigDecimal sumEndProfit(String orderNo) {
        QueryWrapper<ProtoOrderPO> wrapper = new QueryWrapper<>();
        wrapper.select("sum(pay_amount) as payAmount")
                .eq("plan_code", orderNo);
        BigDecimal result = baseMapper.sumEndProfit(orderNo);
        return (result == null ? BigDecimal.valueOf(0) : result);
    }

    @Override
    public PlanProgressDTO planProgress(String planCode) {
        return baseMapper.planProgress(planCode);
    }

    @Override
    public List<ProtoOrderPO> listByPlanCode(String planCode, AssetOrderStatusEnum assetOrderStatusEnum) {
        LambdaQueryWrapper<ProtoOrderPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoOrderPO::getPlanCode, planCode);
        if (null != assetOrderStatusEnum){
            wrapper.eq(ProtoOrderPO::getOrderStatus, assetOrderStatusEnum.getState());
        }
        return this.list(wrapper);
    }
}
