package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.application.service.MessageService;
import com.mi.oa.asset.mobile.application.service.ProtoPlanDetSummaryService;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.config.MessageConfig;
import com.mi.oa.asset.mobile.infra.dto.mall.InnerPlanInfo;
import com.mi.oa.asset.mobile.infra.dto.mall.InnerProductDTO;
import com.mi.oa.asset.mobile.infra.dto.mall.InnerProductReq;
import com.mi.oa.asset.mobile.infra.remote.sdk.AssetMallClient;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoInnerPurchasePlanMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoPlanDetSummaryMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/1
 */
@Slf4j
@Service
public class ProtoInnerPurchasePlanServiceImpl extends ServiceImpl<ProtoInnerPurchasePlanMapper, ProtoInnerPurchasePlanPO>
        implements ProtoInnerPurchasePlanService {

    private static final String PROTO_PLAN_DET_SUMMARY = "proto_plan_det_sum";

    @Autowired
    private ProtoOrderService protoOrderService;

    @Autowired
    private ProtoPlanDetService protoPlanDetService;

    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private SystemAttachService systemAttachService;

    @Autowired
    private ProtoPlanDetSummaryService protoPlanDetSummaryService;

    @Autowired
    private ProtoInnerPurchasePlanMapper protoInnerPurchasePlanMapper;

    @Autowired
    private ProtoPlanDetSummaryMapper planDetSummaryMapper;

    @Autowired
    private MessageService messageService;

    @Autowired
    AssetMallClient mallClient;

    @Value("${oaucf.fds.bucketName}")
    private String fdsBucketName;

    @Value("${oaucf.fds.fdsDomain}")
    private String fdsDomain;

    @Autowired
    private MessageConfig messageConfig;

    @Override
    public void calculatePurchase(List<String> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        List<ProtoInnerPurchasePlanPO> planList = listByOrderList(orderList);
        for (ProtoInnerPurchasePlanPO planPO : planList) {
            String orderNo = planPO.getOrderNo();
            String orderType = planPO.getOrderType();

            Integer soldOutNum = 0;
            // 全员内购从mall库 SN表中统计下架数量
            if (PurchaseOrderTypeEnum.ALL_STAFF.getType().equals(orderType)) {
                soldOutNum = mallClient.productOffNumByPlanCode(orderNo).getData();
            } else {
                // 下架数量
                soldOutNum = protoOrderService.countByStatus(orderNo, OrderTypeEnum.SOLD_OUT, null);
            }

            // 已完成数量
            Integer completedNum = protoOrderService.countByStatus(orderNo, null, AssetOrderStatusEnum.COMPLETED);
            // 实际内购收益
            BigDecimal sumEndProfit = protoOrderService.sumEndProfit(orderNo);
            planPO.setCurrentCancelNum(BigDecimal.valueOf(soldOutNum));
            planPO.setCurrentEndNum(BigDecimal.valueOf(completedNum));
            planPO.setCurrentEndProfit(sumEndProfit);
            // 完成比例: （已下架+已完成）/总数
            BigDecimal processPercent = new BigDecimal(soldOutNum + completedNum)
                    .divide(BigDecimal.valueOf(planPO.getInnerPurchaseNum()), 4, BigDecimal.ROUND_HALF_UP);
            planPO.setProcessPercent(processPercent);
        }
        this.updateBatchById(planList);
    }

    private void calculatePurchasePayOff(List<String> orderList, Map<String, Integer> planDeviceNum) {
        if (CollectionUtils.isEmpty(orderList)) {
            return;
        }
        List<ProtoInnerPurchasePlanPO> planList = listByOrderList(orderList);
        for (ProtoInnerPurchasePlanPO planPO : planList) {
            String orderNo = planPO.getOrderNo();
            String orderType = planPO.getOrderType();

            Integer soldOutNum = 0;
            if (PurchaseOrderTypeEnum.ALL_STAFF.getType().equals(orderType)) {
                soldOutNum = (planPO.getCurrentCancelNum() != null ? planPO.getCurrentCancelNum().intValue() : 0) + planDeviceNum.get(orderNo);
            } else {
                // 下架数量
                soldOutNum = protoOrderService.countByStatus(orderNo, OrderTypeEnum.SOLD_OUT, null);
            }

            // 已完成数量
            Integer completedNum = protoOrderService.countByStatus(orderNo, null, AssetOrderStatusEnum.COMPLETED);
            // 实际内购收益
            BigDecimal sumEndProfit = protoOrderService.sumEndProfit(orderNo);
            planPO.setCurrentCancelNum(BigDecimal.valueOf(soldOutNum));
            planPO.setCurrentEndNum(BigDecimal.valueOf(completedNum));
            planPO.setCurrentEndProfit(sumEndProfit);
            // 完成比例: （已下架+已完成）/总数
            BigDecimal processPercent = new BigDecimal(soldOutNum + completedNum)
                    .divide(BigDecimal.valueOf(planPO.getInnerPurchaseNum()), 4, BigDecimal.ROUND_HALF_UP);
            planPO.setProcessPercent(processPercent);
        }
        this.updateBatchById(planList);
    }

    @Override
    public ProtoInnerPurchasePlanPO getInnerPurchasePlanByCode(String planCode) {
        return this.getOne(new LambdaQueryWrapper<ProtoInnerPurchasePlanPO>().eq(ProtoInnerPurchasePlanPO::getOrderNo, planCode));
    }

    private List<ProtoInnerPurchasePlanPO> listByOrderList(List<String> orderList) {
        LambdaQueryWrapper<ProtoInnerPurchasePlanPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoInnerPurchasePlanPO::getOrderNo, orderList);
        return this.list(wrapper);
    }

    @Override
    public List<InnerProductDTO> pullProduct(InnerProductReq req) {
        if (StringUtils.isBlank(req.getPlanCode())) {
            log.warn("参数检查为空, req:{}", req.getPlanCode());
            throw new BizException(ApplicationErrorCodeEnum.PARAM_INVALID_ERROR, req.getPlanCode());
        }
        ProtoInnerPurchasePlanPO planPO = this.getOne(new LambdaQueryWrapper<ProtoInnerPurchasePlanPO>()
                .eq(ProtoInnerPurchasePlanPO::getOrderNo, req.getPlanCode()));
        if (Objects.isNull(planPO)) {
            log.warn("内购计划单不存在:{}", req.getPlanCode());
            throw new BizException(ApplicationErrorCodeEnum.PROTO_INNER_PURCHASE_DB_NO_DATA_ERROR, req.getPlanCode());
        }
        List<ProtoPlanDetPO> planDetPOList = protoPlanDetService.list(new LambdaQueryWrapper<ProtoPlanDetPO>()
                .eq(ProtoPlanDetPO::getInnerPurchasePlanId, planPO.getId()));
        // 封装并返回数据
        return assembleProductResult(planPO, planDetPOList);
    }

    @Override
    public void productOffUpdateAssetCard(List<InnerProductReq> innerProductReqs) {
        List<String> deviceCodes = innerProductReqs.stream().map(InnerProductReq::getDeviceCode).collect(Collectors.toList());
        List<ProtoCardPO> cardList = protoCardService.listByDevices(deviceCodes);

        List<ProtoPlanDetPO> planDetList = protoPlanDetService.listByPlanNoAndDeviceCode(innerProductReqs);
        Map<String, ProtoPlanDetPO> planDetPOMap = planDetList.stream().collect(
                Collectors.toMap(ProtoPlanDetPO::getDeviceCode, Function.identity(), (v1, v2) -> v1));
        if(CollectionUtils.isNotEmpty(cardList)){
            for (ProtoCardPO cardPO : cardList){
                cardPO.setUseState(planDetPOMap.get(cardPO.getDeviceCode()).getOriginCardStatus());
            }
            protoCardService.updateBatchById(cardList);
        }
        // 服务部更新计划明细的原台账状态字段
        List<Integer> afterSalePlanDetIdList = planDetList.stream()
                .filter(det -> MrpTypeEnum.AFTER_SALE.getType().equals(det.getMrpType()))
                .map(ProtoPlanDetPO::getId)
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(afterSalePlanDetIdList)){
            protoPlanDetService.update(new LambdaUpdateWrapper<ProtoPlanDetPO>()
                    .set(ProtoPlanDetPO::getOriginCardStatus, CardStateEnum.IN_LIBRARY.getKey())
                    .in(ProtoPlanDetPO::getId, afterSalePlanDetIdList));
        }

        // 重新计算已下架数量
        List<String> planCodeList = innerProductReqs.stream().map(InnerProductReq::getPlanCode).collect(Collectors.toList());
        Map<String, Integer> planDeviceNum = new HashMap<>();
        for (InnerProductReq innerProductReq : innerProductReqs) {
            planDeviceNum.put(innerProductReq.getPlanCode(), planDeviceNum.getOrDefault(innerProductReq.getPlanCode(), 0) + 1);
        }
        this.calculatePurchasePayOff(planCodeList, planDeviceNum);
    }

    @Override
    public String getMrpType(String planCode) {
        return protoInnerPurchasePlanMapper.getMrpType(planCode);
    }

    @Override
    public InnerPlanInfo getProtoPlanInfo() throws ParseException {
        // 查询当前时间半小时后一分钟内的计划
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        calendar.add(Calendar.HOUR, 2);
        Date startTime = calendar.getTime();
        calendar.add(Calendar.MINUTE, 1);
        Date endTime = calendar.getTime();

        List<ProtoInnerPurchasePlanPO> planPOS = protoInnerPurchasePlanMapper.selectList(new LambdaQueryWrapper<ProtoInnerPurchasePlanPO>()
                .eq(ProtoInnerPurchasePlanPO::getOrderType, PurchaseOrderTypeEnum.ALL_STAFF.getType())
                .eq(ProtoInnerPurchasePlanPO::getOrderStatus, BaselineRecordAuditEnum.FINISHED.getKey())
                .ge(ProtoInnerPurchasePlanPO::getBeginDate, startTime)
                .lt(ProtoInnerPurchasePlanPO::getBeginDate, endTime));
        if (CollectionUtils.isEmpty(planPOS)) return null;

        // 查询计划下待上架机器数量
        List<Integer> planIds = planPOS.stream().map(ProtoInnerPurchasePlanPO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(planIds)) {
            return null;
        }
        List<ProtoPlanDetSummaryPO> protoPlanDetSummaryPOS = planDetSummaryMapper.selectList(new LambdaQueryWrapper<ProtoPlanDetSummaryPO>()
                .in(ProtoPlanDetSummaryPO::getInnerPurchasePlanId, planIds));
        int num = 0;
        for (ProtoPlanDetSummaryPO protoPlanDetSummaryPO : protoPlanDetSummaryPOS) {
            num += protoPlanDetSummaryPO.getNum();
        }
        InnerPlanInfo planInfo = new InnerPlanInfo();
        Date beginDate = planPOS.get(0).getBeginDate();
        planInfo.setStartTime(null != beginDate ? beginDate : startTime);
        planInfo.setNum(num);
        return planInfo;
    }

    @Override
    public void checkInnerPurchaseOrder(String planCode) {
        Date checkDate = new Date();
        LambdaQueryWrapper<ProtoInnerPurchasePlanPO> wrapper = Wrappers.lambdaQuery(ProtoInnerPurchasePlanPO.class);
        wrapper.eq(ProtoInnerPurchasePlanPO::getOrderType, PurchaseOrderTypeEnum.ALL_STAFF.getType())
                .eq(ProtoInnerPurchasePlanPO::getAuditing, CommonConstant.RecordStatus.APPROVED.getStatus())
                .eq(StringUtils.isNotBlank(planCode), ProtoInnerPurchasePlanPO::getOrderNo, planCode)
                .ge(ProtoInnerPurchasePlanPO::getBeginDate, checkDate);
        List<ProtoInnerPurchasePlanPO> pos = protoInnerPurchasePlanMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(pos)) {
            log.info("无内购计划订单，对数校验完成，校验时间：{}", DateUtils.getFormatDate(DateUtils.COMMON_PATTERN));
            return;
        }
        List<String> planCodeList = pos.stream().map(ProtoInnerPurchasePlanPO::getOrderNo).collect(Collectors.toList());
        BaseResp<Map<String, Integer>> resp = mallClient.queryQualityByPlanCodes(planCodeList);
        if (BaseResp.CODE_SUCCESS != resp.getCode()) {
            messageService.sendSMS(messageConfig.getTels(), "内购计划订单对数校验失败，商城统计接口异常");
            return;
        }
        Map<String, Integer> map = resp.getData();
        for (ProtoInnerPurchasePlanPO po : pos) {
            if (map.get(po.getOrderNo()) == null || map.get(po.getOrderNo()) == 0) {
                int detNum = protoPlanDetService.count(Wrappers.lambdaQuery(ProtoPlanDetPO.class)
                        .eq(ProtoPlanDetPO::getInnerPurchasePlanId, po.getId()));
                if (detNum == map.get(po.getOrderNo())) {
                    continue;
                }
                messageService.sendSMS(messageConfig.getTels(),
                        MessageFormat.format("内购计划订单对数校验数量不一致，计划单号：{0}，计划：{1}，商城：{2}",
                                po.getOrderNo(), detNum, map.get(po.getOrderNo())));
            }
        }
    }

    @Override
    public String queryApprovalStatus(String planCode) {
        return protoInnerPurchasePlanMapper.queryApprovalStatus(planCode);
    }

    /**
     * 封装并返回商品信息
     *
     * @param planPO
     * @param planDetPOList
     * @return
     */
    private List<InnerProductDTO> assembleProductResult(ProtoInnerPurchasePlanPO planPO, List<ProtoPlanDetPO> planDetPOList) {
        List<InnerProductDTO> resultList = new ArrayList<>(planDetPOList.size());
        planDetPOList.forEach(item -> {
            List<ProtoPlanDetSummaryPO> summaryPOList = protoPlanDetSummaryService.list(new LambdaUpdateWrapper<ProtoPlanDetSummaryPO>()
                    .eq(ProtoPlanDetSummaryPO::getSkuCode, item.getSkuCode())
                    .eq(ProtoPlanDetSummaryPO::getInnerPurchasePlanId, planPO.getId()));
            if (CollectionUtils.isEmpty(summaryPOList)) {
                throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR,
                        MessageFormat.format("内购汇总信息不存在, planId:{0}, skuCode:{1}", planPO.getId(), item.getSkuCode()));
            }
            List<Integer> summeryIdList = summaryPOList.stream().map(ProtoPlanDetSummaryPO::getId).collect(Collectors.toList());

            // 获取附件
            LambdaQueryWrapper<SystemAttachPO> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(SystemAttachPO::getFunId, PROTO_PLAN_DET_SUMMARY)
                    .ne(SystemAttachPO::getAttachPath, StringUtils.EMPTY)
                    .in(SystemAttachPO::getDataId, summeryIdList);
            List<SystemAttachPO> attachPOList = systemAttachService.list(queryWrapper);

            InnerProductDTO build = InnerProductDTO.builder()
                    .mrpType(item.getMrpType())
                    .deviceCode(item.getDeviceCode())
                    .skuCode(item.getSkuCode())
                    .skuName(item.getSkuName())
                    .houseCode(item.getLocationCode())
                    .houseName(item.getLocationName())
                    .priceCode(item.getPriceCode())
                    .priceDesc(item.getPriceDesc())
                    .planId(planPO.getId())
                    .planCode(planPO.getOrderNo())
                    .planName(planPO.getTopic())
                    .locationService(planPO.getLocationService())
                    .checkInfo(planPO.getCheckInfo())
                    .deliveryMethod(planPO.getDeliveryMethod())
                    .currPrice(item.getActualDamages())
                    .brief(item.getListDesc())
                    .pic(CollectionUtils.isNotEmpty(attachPOList)
                            ? fdsDomain + "/" + fdsBucketName + "/" + attachPOList.get(0).getAttachPath() : StringUtils.EMPTY)
                    .projectCode(item.getProjectCode())
                    .purchaseStatus(0)
                    .snStatus(1)
                    .startTime(planPO.getBeginDate())
                    .endTime(planPO.getEndDate())
                    .build();
            resultList.add(build);
        });
        // 非服务部业务线时从台账获取数据
        if (!MrpTypeEnum.AFTER_SALE.getType().equals(planPO.getMrpType())) {
            List<String> deviceCodeList = planDetPOList.stream().map(ProtoPlanDetPO::getDeviceCode).collect(Collectors.toList());
            List<ProtoCardPO> protoCardPOList = protoCardService.getByDeviceCode(deviceCodeList);
            Map<String, ProtoCardPO> protoCardPOMap = protoCardPOList.stream().collect(
                    Collectors.toMap(ProtoCardPO::getDeviceCode, Function.identity(), (o1, o2) -> o1));
            resultList.forEach(build -> {
                ProtoCardPO protoCardPO = protoCardPOMap.get(build.getDeviceCode());
                build.setLaserCode(protoCardPO.getLaserCode());
                build.setImei(protoCardPO.getImei());
            });
        }
        return resultList;
    }
}
