package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.SapMakeCnEnum;
import com.mi.oa.asset.mobile.infra.dto.CostCenter;
import com.mi.oa.asset.mobile.infra.dto.hrod.EmployeeInfo;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOutDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOutPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoThirdUserPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.asset.x5.common.JacksonUtils;
import com.mi.oa.infra.oaucf.bpm.service.ApprovalService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/11 17:41
 */
@Service
@Slf4j
public class ProtoServiceImpl implements ProtoService {

    @Autowired
    private ProtoThirdUserService thirdUserService;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private ProtoOutService protoOutService;
    @Autowired
    private ProtoOutDetService protoOutDetService;

    @Autowired
    private ApprovalService approvalService;

    public static final String P_PREFIX = "p-";

    @Override
    public boolean qryThirdUser(String userCode){
        return null != thirdUserService.findUserInfo(userCode);
    }

    @Override
    public String getApplyUserType(String userCode) {
        boolean type = this.qryThirdUser(userCode);
        if (!type && userCode.contains(P_PREFIX)) {
            return "";
        }
        if (type) {
            return CommonConstant.ApplyUserTypeEnum.ODM.getValue();
        } else {
            return CommonConstant.ApplyUserTypeEnum.INNER.getValue();
        }
    }

    @Override
    public CostCenter getSAPCenterCode(String userCode, String applyUserType, String makeCn){
        CostCenter costCenter = CostCenter.builder().build();
        String costCode = "";
        String compDeptCode = "";
        //内部员工领用
        if(CommonConstant.ApplyUserTypeEnum.INNER.getValue().equals(applyUserType)) {
            EmployeeInfo empInfo = userInfoService.getEmpInfoByUserName(userCode);
            if(null != empInfo) {
                costCode = empInfo.getCostCenter();
            }
            if (StringUtils.isEmpty(costCode)) {
                //查询第三方配置表
                ProtoThirdUserPO thirdUserInfo =thirdUserService.findUserInfo(userCode);
                if(ObjectUtils.isEmpty(thirdUserInfo)) {
                    //成本中心
                    costCenter.setCenterCode(costCode);
                    //公司代码
                    costCenter.setCompDeptCode(compDeptCode);
                }else {
                    costCode = thirdUserInfo.getCenterCode();
                }
            }

        }else if(CommonConstant.ApplyUserTypeEnum.ODM.getValue().equals(applyUserType)) {
            //ODM+
            //查询第三方配置表
            ProtoThirdUserPO thirdUserInfo =thirdUserService.findUserInfo(userCode);
            if(ObjectUtils.isEmpty(thirdUserInfo)) {
                //成本中心
                costCenter.setCenterCode(costCode);
                //公司代码
                costCenter.setCompDeptCode(compDeptCode);
            }else {
                costCode = thirdUserInfo.getCenterCode();
            }

        }else if(CommonConstant.ApplyUserTypeEnum.PROVIDER.getValue().equals(applyUserType)) {
            //领用类型是供应商的时，成本中心和公司代码都是空，直接扣款。
            log.info("领用类型是供应商的，成本中心和公司代码都是空，直接扣款");
        }

        //样机生产地的 下拉选项规则 xxxx-xxxx 前四位是样机工厂代码，后四位是对应的公司代码
        if(null == costCode || "".equals(costCode)){
            return null;
        }
        if(costCode.length() > 0 && StringUtils.isNotBlank(makeCn)) {
            String makeCnComp = SapMakeCnEnum.CHANG_PING.getSapFactory().equals(makeCn.split("-")[0])
                    ? costCode.substring(1,5).equals(makeCn.split("-")[1])
                    ? makeCn.split("-")[1]
                    : SapMakeCnEnum.INTERNAL.getMakeCn().split("-")[1]
                    : makeCn.split("-")[1];
            // String makeCnComp = makeCn.split("-")[1];
            //截取前1位
            String begin = costCode.substring(0,1);
            //截取后5位
            String end =   costCode.substring(5);
            costCode = begin +  makeCnComp + end ;
            compDeptCode = makeCnComp;
        }
        //成本中心
        costCenter.setCenterCode(costCode);
        //公司代码
        costCenter.setCompDeptCode(compDeptCode);
        return costCenter;
    }

    @Override
    public Map<String, String> tranScanResult(String scanResult) {
        Map<String, String> scanResultMap = new HashMap<>();
        String[] scanResultArray = scanResult.split("\t");
        if (scanResultArray.length < 7) {
            log.info("扫码结果不符合Excel格式！");
            log.info("------------------------------------------------------------------------");

            int scanResultLength = scanResult.length();
            log.info("正在尝试将扫描结果解析成ASN...");
            if (scanResultLength == 10) {
                log.info("扫码结果可以解析为ASN单号");
                log.info("------------------------------------------------------------------------");

                scanResultMap.clear();
                scanResultMap.put("tranType", "ASN");
                scanResultMap.put("order_code", scanResult);

            } else {
                log.info("扫码结果不能解析为ASN单号！");
                log.info("------------------------------------------------------------------------");
            }

            log.info("正在尝试将扫描结果解析成SN...");
            if (scanResultLength == 12) {
                log.info("扫码结果可以解析为SN");
                log.info("------------------------------------------------------------------------");

                scanResultMap.clear();
                scanResultMap.put("tranType", "SN");
                scanResultMap.put("device_code", scanResult);

            } else {
                log.info("扫码结果不能解析为SN！");
                log.info("------------------------------------------------------------------------");
            }

        } else if (scanResultArray.length == 7) {
            log.info("扫码结果符合Excel格式（老版）");
            log.info("------------------------------------------------------------------------");
            String project_code = scanResultArray[0];
            String country = scanResultArray[2];
            project_code = project_code + (StringUtils.isBlank(country) ? "" : "-" + country);

            scanResultMap.clear();
            scanResultMap.put("tranType", "Excel");
            scanResultMap.put("project_code", project_code);
            scanResultMap.put("stage_name", scanResultArray[1]);
            scanResultMap.put("country", country);
            scanResultMap.put("device_type", scanResultArray[3]);
            scanResultMap.put("laser_code", scanResultArray[4]);
            scanResultMap.put("imei", scanResultArray[5]);
            scanResultMap.put("device_code", scanResultArray[6]);

        } else if (scanResultArray.length == 8) {
            log.info("扫码结果符合Excel格式（新版）");
            log.info("------------------------------------------------------------------------");
            String project_code = scanResultArray[0];
            String country = scanResultArray[2];
            project_code = project_code + (StringUtils.isBlank(country) ? "" : "-" + country);

            scanResultMap.clear();
            scanResultMap.put("tranType", "Excel");
            scanResultMap.put("project_code", project_code);
            scanResultMap.put("stage_name", scanResultArray[1]);
            scanResultMap.put("country", country);
            scanResultMap.put("device_type", scanResultArray[3]);
            scanResultMap.put("laser_code", scanResultArray[4]);
            scanResultMap.put("imei", scanResultArray[5]);
            scanResultMap.put("sku_code", scanResultArray[6]);
            scanResultMap.put("device_code", scanResultArray[7]);

        }

        // 如果所有情况都不匹配，则取最后一位为SN
        String tranType = scanResultMap.get("tranType");
        if (StringUtils.isBlank(tranType)) {
            scanResultMap.clear();
            scanResultMap.put("tranType", "SN");
            scanResultMap.put("device_code", scanResultArray[scanResultArray.length - 1]);
        }

        log.info("二维码PDF417【{}】，解析结果【{}】", JacksonUtils.bean2Json(scanResult), JacksonUtils.bean2Json(scanResultMap));

        return scanResultMap;
    }

    @Override
    public List<ProtoOutDetPO> getOutDet(List<String> deviceCodes, String outState, String outType) {

        QueryWrapper<ProtoOutDetPO> queryWrapper =new QueryWrapper<>();
        queryWrapper.lambda().in(ProtoOutDetPO::getDeviceCode,deviceCodes);
        List<ProtoOutDetPO> detList = protoOutDetService.list(queryWrapper);
        if(CollectionUtils.isEmpty(detList)){
            return null;
        }
        LambdaQueryWrapper<ProtoOutPO> outWrapper =new LambdaQueryWrapper<>();
        List<String> outIdList = detList.stream().map(ProtoOutDetPO::getOutId).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        outWrapper.in(ProtoOutPO::getOutId,outIdList)
                .eq(!StringUtils.isBlank(outType),ProtoOutPO::getOutType,outType)
                .eq(!StringUtils.isBlank(outState),ProtoOutPO::getOutStatus,outState);
        List<ProtoOutPO> outList = protoOutService.list(outWrapper);
        if(CollectionUtils.isEmpty(outList)){
            return null;
        }else {
            Map<String,ProtoOutPO> outMap = outList.stream().collect(Collectors.toMap(ProtoOutPO::getOutId, a -> a,(k1, k2)->k1));
            detList.removeIf(det->!outMap.containsKey(det.getOutId()));
        }
        return detList;
    }

    @Override
    public String getBpmUrl(String businessKey, String accUserCode) {
        String result = null;
        try {
            BaseResp<String> taskDetailLink = approvalService.getTaskDetailLink(businessKey, accUserCode);
            result = taskDetailLink.getData();
            if (result == null) {
                log.error("获取跳转链接失败businessKey:{}", businessKey);
            }
        } catch (Exception e){
            log.error("获取跳转链接失败businessKey:{}", businessKey, e);
        }
        return result;
    }

}
