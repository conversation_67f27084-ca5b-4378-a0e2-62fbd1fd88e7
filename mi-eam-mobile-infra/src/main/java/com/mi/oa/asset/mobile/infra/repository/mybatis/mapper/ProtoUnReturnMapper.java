package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoUnReturnPO;

/**
 * table_name : proto_unreturn
 * <AUTHOR>
 * @date 2022/02/25/02:30
 */
public interface ProtoUnReturnMapper extends BaseMapper<ProtoUnReturnPO> {
    /**
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String unreturnId);
}