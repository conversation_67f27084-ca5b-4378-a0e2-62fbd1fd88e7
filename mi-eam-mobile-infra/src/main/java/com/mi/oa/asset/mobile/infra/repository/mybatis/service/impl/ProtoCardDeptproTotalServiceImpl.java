package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.dto.cardusertab.CardSpiltDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardDeptproTotalPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysDeptPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoCardDeptproTotalMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardDeptproTotalService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysDeptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 部门在用报表项目查询
 *
 * <AUTHOR>
 * @date 2022/4/1 10:52
 */
@Service
@Slf4j
public class ProtoCardDeptproTotalServiceImpl extends ServiceImpl<ProtoCardDeptproTotalMapper, ProtoCardDeptproTotalPO> implements ProtoCardDeptproTotalService {

    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private SysDeptService sysDeptService;

    @Override
    public Boolean statisticsDeptpro() {
        Map<CardSpiltDTO, ProtoCardDeptproTotalPO> cardSplitTotalMap = new HashMap<>(8192);
        // 先删除 再重新统计
        this.remove(new QueryWrapper<>());
        List<ProtoCardDeptproTotalPO> totalDeptList = protoCardService.listGroupByDeptproCode();
        this.saveBatch(totalDeptList);
        this.putDeptMap(totalDeptList, cardSplitTotalMap);
        List<String> mrpTypeList = baseMapper.getMrpTypeList();
        for (String mrpType : mrpTypeList){
            // 统计下级部门数据
            for (int deptLevel = 6; deptLevel > 1; deptLevel--) {
                List<ProtoCardDeptproTotalPO> list = baseMapper.statisticsByDeptLevel(deptLevel, mrpType);
                List<ProtoCardDeptproTotalPO> saveList = new ArrayList<>(list.size());
                List<ProtoCardDeptproTotalPO> updateList = new ArrayList<>(list.size());
                for (ProtoCardDeptproTotalPO totalPO : list) {
                    Integer totalNum = totalPO.getDghNum() + totalPO.getGhzNum() + totalPO.getWfghzNum() + totalPO.getZyzNum();
                    totalPO.setTotalNum(totalNum);
                }
                for (ProtoCardDeptproTotalPO totalPO : list) {
                    CardSpiltDTO cardSpiltDTO = new CardSpiltDTO(totalPO.getDeptCode(), totalPO.getProjectCode(), totalPO.getSkuCodeType(), totalPO.getMrpType());
                    ProtoCardDeptproTotalPO deptTotalPO = cardSplitTotalMap.get(cardSpiltDTO);
                    if (null != deptTotalPO) {
                        totalPO.setId(deptTotalPO.getId());
                        totalPO.setTotalNum(deptTotalPO.getTotalNum() + totalPO.getTotalNum());
                        totalPO.setDghNum(deptTotalPO.getDghNum() + totalPO.getDghNum());
                        totalPO.setWfghzNum(deptTotalPO.getWfghzNum() + totalPO.getWfghzNum());
                        totalPO.setGhzNum(deptTotalPO.getGhzNum() + totalPO.getGhzNum());
                        totalPO.setZyzNum(deptTotalPO.getZyzNum() + totalPO.getZyzNum());
                        updateList.add(totalPO);
                    } else {
                        saveList.add(totalPO);
                    }
                }
                this.saveBatch(saveList);
                this.updateBatchById(updateList);
                this.putDeptMap(saveList, cardSplitTotalMap);
                this.putDeptMap(updateList, cardSplitTotalMap);
            }
        }
        // 更新部门名称信息
        List<ProtoCardDeptproTotalPO> allDeptList = new ArrayList<>(cardSplitTotalMap.size());
        for (CardSpiltDTO spiltDTO : cardSplitTotalMap.keySet()) {
            allDeptList.add(cardSplitTotalMap.get(spiltDTO));
        }
        List<String> deptCodeList = allDeptList.stream().map(ProtoCardDeptproTotalPO::getDeptCode).distinct().collect(Collectors.toList());
        Map<String, SysDeptPO> deptMap = sysDeptService.listByDeptCodeList(deptCodeList).stream()
                .collect(Collectors.toMap(SysDeptPO::getDeptId, o -> o));
        for (ProtoCardDeptproTotalPO totalPO : allDeptList) {
            SysDeptPO sysDeptPO = deptMap.get(totalPO.getDeptCode());
            if (null != sysDeptPO) {
                totalPO.setDeptName(sysDeptPO.getDeptName());
                String longDeptName = sysDeptPO.getLongDeptName();
                if (StringUtils.isBlank(longDeptName)) {
                    longDeptName = sysDeptPO.getDeptName();
                }
                totalPO.setDeptLevel(sysDeptPO.getDeptLevel().intValue());
                totalPO.setLongDeptName(longDeptName);
            }
        }
        this.updateBatchById(allDeptList);
        return true;
    }

    private void putDeptMap(List<ProtoCardDeptproTotalPO> list, Map<CardSpiltDTO, ProtoCardDeptproTotalPO> cardSplitTotalMap) {
        for (ProtoCardDeptproTotalPO totalPO : list) {
            CardSpiltDTO cardSpiltDTO = new CardSpiltDTO(totalPO.getDeptCode(), totalPO.getProjectCode(), totalPO.getSkuCodeType(), totalPO.getMrpType());
            cardSplitTotalMap.put(cardSpiltDTO, totalPO);
        }
    }
}
