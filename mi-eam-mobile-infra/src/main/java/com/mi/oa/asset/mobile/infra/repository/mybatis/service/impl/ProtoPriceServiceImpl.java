package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPricePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoPriceMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoPriceService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/14
import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/22
 */
@Service
public class ProtoPriceServiceImpl extends ServiceImpl<ProtoPriceMapper, ProtoPricePO> implements ProtoPriceService {

    @Override
    public boolean havePriceCodeSame(ProtoPricePO protoPrice) {
        LambdaQueryWrapper<ProtoPricePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoPricePO::getPriceCode, protoPrice.getPriceCode())
                .ne(ProtoPricePO::getId, protoPrice.getId());
        return this.count(wrapper) > 0;
    }

    public List<ProtoPricePO> listPrice() {
        LambdaQueryWrapper<ProtoPricePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoPricePO::getStatus, CommonConstant.RecordStatus.COMMITTED.getStatus());
        wrapper.orderByDesc(ProtoPricePO::getId);
        return this.list(wrapper);
    }
}
