package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.mi.oa.asset.mobile.infra.dto.sap.Sap107JsonDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapCustomVO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapPhoneTransferDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapProjectInfoDTO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/12
 */

public interface SapService {

    /**
     * SAP 107接口
     * @return
     */
    SapCustomVO sap107Json(Sap107JsonDTO jsonDTO, String funId, String orderId, String orderCode, String orderName, String doUser, String mrpType);

    /**
     * SAP 小米人发送样机转移申请单至ERP
     * @param request
     * @return
     */
    SapCustomVO enginePhoneTransfer(SapPhoneTransferDTO request, String funId, String orderId, String orderCode, String orderName, String doUser, String mrpType);


    /**
     * 获取项目、阶段信息
     * @param project 项目如 “A7”
     * @return
     */
    List<SapProjectInfoDTO> getProjectInfo(String project);
}
