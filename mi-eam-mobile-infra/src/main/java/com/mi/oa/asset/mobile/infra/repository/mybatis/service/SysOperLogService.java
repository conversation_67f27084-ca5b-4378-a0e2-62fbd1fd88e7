package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysOperLogPO;
import com.mi.oa.asset.x5.common.X5Response;

/**
 * description: your description
 *
 * <AUTHOR>
 * @date 2023/9/20
 */
public interface SysOperLogService extends IService<SysOperLogPO> {

    void saveOrUpdateLog(X5Response x5Response, String paramJson, String orderCode, String doUser);

}
