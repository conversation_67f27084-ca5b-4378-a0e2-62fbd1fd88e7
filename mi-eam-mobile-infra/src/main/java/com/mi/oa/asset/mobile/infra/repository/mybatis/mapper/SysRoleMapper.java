package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysRolePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * table_name : sys_role
 * <AUTHOR>
 * @date 2022/01/25/03:48
 */
@Mapper
public interface SysRoleMapper extends BaseMapper<SysRolePO> {

    List<String> getMrpType(@Param("roleIds") List<String> roleIds);
}