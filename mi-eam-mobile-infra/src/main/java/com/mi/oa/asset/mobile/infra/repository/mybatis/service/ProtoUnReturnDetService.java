package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoUnReturnMatPO;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2022/2/25 14:53
 */
public interface ProtoUnReturnDetService extends IService<ProtoUnReturnMatPO> {
    List<ProtoUnReturnMatPO> findListByParam(ProtoUnReturnMatPO param);

    List<ProtoUnReturnMatPO> listByUnreturnId(String unreturnId);
}
