package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoFactoryListPO;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/14
 */
public interface ProtoFactoryListService extends IService<ProtoFactoryListPO> {

    /**
     * 通过名称查询工厂编号
     * @param factoryName
     * @return
     */
    String getFactoryCodeByName(String factoryName);
}
