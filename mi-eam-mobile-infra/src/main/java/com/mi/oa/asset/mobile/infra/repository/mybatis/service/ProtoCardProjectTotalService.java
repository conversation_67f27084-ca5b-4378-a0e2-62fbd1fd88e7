package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardProjectTotalPO;

import java.util.List;

/**
 * 台账项目汇总
 *
 * <AUTHOR>
 * @date 2022/3/25 9:45
 */
public interface ProtoCardProjectTotalService extends IService<ProtoCardProjectTotalPO> {

    Boolean updCardProjectTotal();

}
