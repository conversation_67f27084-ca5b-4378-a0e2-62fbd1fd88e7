package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.application.dto.order.PlanProgressDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOrderPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;

/**
 * table_name : proto_order
 * <AUTHOR>
 * @date 2022/08/01/10:11
 */
public interface ProtoOrderMapper extends BaseMapper<ProtoOrderPO> {

    @Select("select sum(pay_amount) from proto_order where plan_code = #{orderNo} and order_status = '2'")
    BigDecimal sumEndProfit(@Param("orderNo") String orderNo);

    PlanProgressDTO planProgress(@Param("planCode") String planCode);
}