package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoBaselineRecordDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoBaselineRecordDetMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoBaselineRecordDetService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2022/2/28 16:07
 */
@Service
public class ProtoBaselineRecordDetServiceImpl extends ServiceImpl<ProtoBaselineRecordDetMapper, ProtoBaselineRecordDetPO> implements ProtoBaselineRecordDetService {

    @Override
    public List<ProtoBaselineRecordDetPO> findListByParam(ProtoBaselineRecordDetPO po) {
        QueryWrapper<ProtoBaselineRecordDetPO> queryWrapper = new QueryWrapper(po);
        return list(queryWrapper);
    }

    @Override
    public ProtoBaselineRecordDetPO findOneByParam(ProtoBaselineRecordDetPO po) {
        QueryWrapper<ProtoBaselineRecordDetPO> queryWrapper = new QueryWrapper(po);
        List<ProtoBaselineRecordDetPO> rs = list(queryWrapper);
        return CollectionUtils.isEmpty(rs)?null:rs.get(0);
    }
}
