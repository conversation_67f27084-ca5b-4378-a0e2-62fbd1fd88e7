package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.application.dto.ConditionDTO;
import com.mi.oa.asset.mobile.common.enums.CardStateEnum;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.*;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
public interface ProtoCardService extends IService<ProtoCardPO> {

    /**
     * 更新台账状态 根据deviceCode
     *
     * @param deviceCodeList 设备编号
     * @param useState       使用状态
     * @param mrpType
     */
    void updateState(List<String> deviceCodeList, CardStateEnum useState, String mrpType);

    /**
     * 更新台账状态 根据deviceCode
     *
     * @param deviceCodeList 设备编号
     * @param oldUseState    旧的状态
     * @param useState       使用状态
     */
    void updateState(List<String> deviceCodeList, CardStateEnum oldUseState, CardStateEnum useState);

    /**
     * 根据 deviceCode 更新台账字段
     *
     * @param updateCardList
     */
    void updateByDeviceCode(List<ProtoCardPO> updateCardList);


    /**
     * 查询名下工程机的数量
     *
     * @param usedCode
     * @return
     */
    Integer countByEmpCode(String usedCode);

    /**
     * 查询台账满足条件的记录数量
     *
     * @return
     */
    int qryCardCount(ProtoCardPO cardPO);

    /**
     * 查询台账
     *
     * @param projectCode
     * @param stageName
     * @param skuCode
     * @param useState
     * @param assetNewOld
     * @param isComplete
     * @param occupyApplyCode
     * @param houseCode
     * @param limit
     * @return
     */
    List<ProtoCardPO> findOccupyCardLimit(String projectCode, String stageName
            , String skuCode, String useState, String assetNewOld, String isComplete
            , String occupyApplyCode, String houseCode, int limit);

    /**
     * 查询二手库
     * @param conditionList
     * @param mrpTypeList
     * @return
     */
    List<Map<String, Object>> findSecondNumList(List<ConditionDTO> conditionList, List<String> mrpTypeList);

    /**
     * 根据devicecode查询不属于自己名下的台账
     *
     * @param deviceCodes 设备编号
     * @param userCode    用户code
     * @return
     */
    List<ProtoCardPO> findNotSelfCard(List<String> deviceCodes, String userCode, CommonConstant.ApplyUserTypeEnum applyUserTypeEnum);

    /**
     * 根据devicecode查询
     *
     * @param deviceCodes
     * @return
     */
    List<ProtoCardPO> getByDeviceCode(List<String> deviceCodes);

    /**
     * 查询工程机台账数据
     *
     * @param deviceCodes
     * @param useState
     * @return
     */
    List<ProtoCardPO> getByDeviceCodeAndExcludeUseState(List<String> deviceCodes, String useState);

    /**
     * 通过SN更新数据
     *
     * @param cardPO
     */
    void updateInfoBySn(ProtoCardPO cardPO);

    /**
     * 通过SN查询数据
     *
     * @param sn
     * @return
     */
    ProtoCardPO getBySn(String sn);

    /**
     * 更新旧的发放单号
     *
     * @param oldOutCode
     * @param newOutCode
     */
    void updateOutCode(String oldOutCode, String newOutCode);

    /**
     * 根据SN更新抛送SAP行号
     *
     * @param sn
     * @param outSapItemRowNo
     */
    void updateOutSapItemRowNoBySn(String sn, String outSapItemRowNo);

    /**
     * 修改PS下离职人员工程机的数量
     *
     * @param empCode
     */
    void changePSPhoneNum(String empCode);

    /**
     * 保存台账信息
     *
     * @param orderCode ASN单号
     * @param itemList  收货明细
     * @param inCode    收货编号
     * @param userCode  工号
     * @param houseName 仓库名称
     * @param houseCode 仓库编码
     * @param houseId   仓库主键
     */
    void saveCardInfo(String orderCode, List<ProtoAsnOrderDetPO> itemList, String inCode, String userCode, String houseName, String houseCode, String houseId);

    /**
     * 根据devicecode和状态查询台账
     *
     * @param deviceCodes
     * @param userCode
     * @param useState
     * @param isOpen
     * @param projectCode
     * @return
     */
    List<ProtoCardPO> findCardList(List<String> deviceCodes, String userCode, String useState, String isOpen, String projectCode);

    /**
     * 修改台账状态
     *
     * @param isOpen
     * @param deviceCodes
     * @return
     */
    boolean updProtoCardOpenStatus(String isOpen, List<String> deviceCodes);

    /**
     * 修改台账状态
     *
     * @param isOpen
     * @param userName
     * @param userCode
     * @param empCode
     * @param nowDate
     * @param deviceCodes
     * @return
     */
    boolean updProtoCardOpenStatus(String isOpen, String userName, String userCode, String empCode, List<String> deviceCodes);

    /**
     * 更新台账截止归还时间
     *
     * @param endDate
     * @param deviceCodes
     * @return
     */
    boolean updProtoCardDelayStatus(Date endDate, List<String> deviceCodes);

    /**
     * 释放占用的库存
     *
     * @param skuCode
     * @param houseCode
     * @param releaseCount
     */
    void releaseCard(String skuCode, String houseCode, Integer releaseCount);

    /**
     * 根据多参数查询
     *
     * @param po 查询参数
     * @return
     */
    ProtoCardPO findOneByParam(ProtoCardPO po);

    /**
     * 工程机SN追溯查询
     *
     * @param conditionList
     * @return
     */
    List<ProtoCardPO> getListForSnTrace(List<ConditionDTO> conditionList);

    /**
     * 工程机SN追溯按条件查询数据条数
     *
     * @param conditionList
     * @return
     */
    int countForSnTrace(List<ConditionDTO> conditionList);

    /**
     * 修复有乱码的描述
     */
    void repairAllSkuName();

    /**
     * 更新全量sycFlag字段值
     *
     * @param sycFlag
     */
    void updateSycFlag(String sycFlag);

    /**
     * 查询所有未刷的人员信息
     *
     * @return
     */
    List<String> getNotRefreshUserCode();

    /**
     * 通过账号更新部门信息
     *
     * @param deptId
     * @param longDeptName
     * @param empCode
     * @param userCode
     */
    void updateLongDeptNameByUserCode(String deptId, String longDeptName, String empCode, String userCode);

    /**
     * 根据
     *
     * @param sycFlag
     * @param limit
     * @return
     */
    List<ProtoCardPO> listBySycFlag(String sycFlag, Integer limit);

    /**
     * 查询最近没有入库时间的数据
     *
     * @param limit
     * @return
     */
    List<ProtoCardPO> listNotEntryDate(Integer limit);

    /**
     * 按照部门维度统计
     * @return
     */
    List<ProtoCardDeptTotalPO> listGroupByDeptCode();

    /**
     * 按照部门和项目维度统计
     * @return
     */
    List<ProtoCardDeptproTotalPO> listGroupByDeptproCode();

    /**
     * 根据项目更新上市时间
     * @param projectCode
     * @param listDate
     */
    void updateListDateByProjectCode(String projectCode, Date listDate);

    /**
     * 根据SN删除台账
     * @param oldSn
     */
    void deletedBySn(String oldSn);

    /**
     * 按照条件查询一条工程机台账数据
     */
    ProtoCardPO queryOneProtoCard(LambdaQueryWrapper<ProtoCardPO> queryWrapper);

    /**
     * 按照条件查询工程机台账列表
     */
    List<ProtoCardPO> queryProtoCardList(LambdaQueryWrapper<ProtoCardPO> queryWrapper);

    /**
     * 库存查询报表
     * @return
     */
    List<ProtoCardStorePO> listGroupByStore();

    /**
     * 获取发送飞书表格内容
     * @param cardList
     * @param isCn
     * @return
     */
    String getLarkTableContent(List<ProtoCardPO> cardList, boolean isCn);

    /**
     * 获取发送飞书表格内容
     * @param cardList
     * @param mrpType
     * @param isCn
     * @return
     */
    String getLarkTableContent(List<ProtoCardPO> cardList, String mrpType, boolean isCn);

    /**
     * 获取发送飞书表格内容
     * @param cardPO
     * @param isCn
     * @return
     */
    String getLarkTableContent(ProtoCardPO cardPO, boolean isCn);

    /**
     * 获取发送飞书表格内容
     * 市场名称 项目名 料号 配置 价格 数量
     * @param cardList
     * @param isCn
     */
    String getLarkTableContentV1(List<ProtoCardPO> cardList, String productDesc,  boolean isCn);

    /**
     * 获取发送飞书表格内容
     * 市场名称 项目名 料号 配置 价格 数量
     * @param cardList
     * @param productDesc
     * @param isCn
     * @param mrpType
     * @param hasSn
     * @return
     */
    String getLarkTableContentV2(List<ProtoCardPO> cardList, String productDesc,  boolean isCn, String mrpType, boolean hasSn);

    /**
     * 获取发送邮件表格内容
     * @param cardList
     * @param isCn true: 中文 false:英文
     * @return
     */
    String getEmailTableContent(List<ProtoCardPO> cardList, boolean isCn);

    /**
     * 查询名下工程机数据
     * @param userCode
     * @return
     */
    Integer countByUserCode(String userCode);

    /**
     * 通过SN查询台账
     * @param deviceCodeList
     * @return
     */
    List<ProtoCardPO> listByDevices(List<String> deviceCodeList);

    /**
     * 通过料号查询单个
     * @param skuCode
     * @param projectCode
     * @return
     */
    ProtoCardPO getOneBySkuCode(String skuCode, String projectCode);

    /**
     * 工程机归还提醒
     */
    void protoBackNotifyTask();
}

