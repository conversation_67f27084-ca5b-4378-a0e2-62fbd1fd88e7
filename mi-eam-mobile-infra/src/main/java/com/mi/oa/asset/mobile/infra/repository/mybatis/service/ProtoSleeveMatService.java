package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleeveMatPO;

import java.util.List;

/**
 * 工程机保密套拆除物料明细
 *
 * <AUTHOR>
 * @date 2022/2/7 10:52
 */
public interface ProtoSleeveMatService extends IService<ProtoSleeveMatPO> {

    /**
     * 根据保密套拆除ID和校验状态查询物料信息
     *
     * @param sleeveId
     * @param checkStatus
     * @return
     */
    List<ProtoSleeveMatPO> qryCheckStatus(String sleeveId, String checkStatus);

    /**
     * 连表查询设备编号SN
     *
     * @param sleeveId
     * @return
     */
    List<ProtoSleeveMatPO> qryMatDeviceCodes(String sleeveId);

    /**
     * 根据保密套拆除ID查询物料信息
     *
     * @param sleeveId
     * @return
     */
    List<ProtoSleeveMatPO> qrySleeveMatList(String sleeveId);

    /**
     * 根据保密套拆除ID查询物料信息并根据领用人账号去重
     *
     * @param sleeveId
     * @return
     */
    List<ProtoSleeveMatPO> qrySleeveMatListGroupByApplyUserCode(String sleeveId);

}
