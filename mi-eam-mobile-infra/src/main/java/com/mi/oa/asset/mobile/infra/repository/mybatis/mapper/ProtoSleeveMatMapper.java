package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleeveMatPO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * table_name : proto_sleeve_mat
 * <AUTHOR>
 * @date 2022/02/07/10:48
 */
public interface ProtoSleeveMatMapper extends BaseMapper<ProtoSleeveMatPO> {
    /**
     *
     * @mbg.generated
     */
    ProtoSleeveMatPO selectByPrimaryKey(String sleeveMatId);

    List<ProtoSleeveMatPO> selectDeviceCodes(String sleeveId);
}