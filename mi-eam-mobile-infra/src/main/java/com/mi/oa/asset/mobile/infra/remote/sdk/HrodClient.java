package com.mi.oa.asset.mobile.infra.remote.sdk;

import com.mi.oa.asset.mobile.infra.config.HrodApiDescribe;
import com.mi.oa.asset.mobile.infra.config.HrodClientConfig;
import com.mi.oa.asset.mobile.infra.dto.hrod.HrodConditionBaseDTO;
import com.mi.oa.asset.mobile.infra.dto.hrod.HrodEmployeeVO;
import com.mi.oa.asset.x5.consumer.X5FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/9/9 22:05
 */

@X5FeignClient(value = "hrod", url = "${hrod.host}", appId = "${hrod.appId}", appKey = "${hrod.appKey}", form = true, configuration = HrodClientConfig.class)
public interface HrodClient {

    @PostMapping
    @HrodApiDescribe(apiName = "getEmployeeByOprid", conditionName = "hpcConditionByOprid")
    HrodEmployeeVO getEmployeeByUserName(HrodConditionBaseDTO condition);

    @PostMapping
    @HrodApiDescribe(apiName = "getEmployee", conditionName = "hpcConditionEmployee")
    HrodEmployeeVO getEmployee(HrodConditionBaseDTO condition);

}

