package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCollectPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoCollectMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCollectPlanService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCollectService;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/17
 */
@Service
public class ProtoCollectServiceImpl extends ServiceImpl<ProtoCollectMapper, ProtoCollectPO>
        implements ProtoCollectService {

    @Autowired
    private ProtoCollectPlanService protoCollectPlanService;

    @Override
    public void updateProtoCollectStatus() {
        List<ProtoCollectPO> list = getOverTimeCollect();
        if (CollectionUtils.isEmpty(list)){
            return;
        }
        List<String> codeList = list.stream().map(ProtoCollectPO::getCollectCode).distinct().collect(Collectors.toList());
        protoCollectPlanService.updateAuditingInvalid(codeList);
    }


    private List<ProtoCollectPO> getOverTimeCollect(){
        String nowDateStr = DateUtils.getFormatDate(DateUtils.COMMON_PATTERN);
        return baseMapper.selectList(Wrappers.<ProtoCollectPO>lambdaQuery()
                .lt(ProtoCollectPO::getDealLine, nowDateStr)
                .eq(ProtoCollectPO::getAuditing, CommonConstant.RecordStatus.COMMITTED.getStatus()));

    }
}
