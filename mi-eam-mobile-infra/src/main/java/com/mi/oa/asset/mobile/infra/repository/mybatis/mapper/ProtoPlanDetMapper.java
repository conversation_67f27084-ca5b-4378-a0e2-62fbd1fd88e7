package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOrderPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPlanDetPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * table_name : proto_plan_det
 * <AUTHOR>
 * @date 2022/08/01/10:46
 */
public interface ProtoPlanDetMapper extends BaseMapper<ProtoPlanDetPO> {

    List<ProtoPlanDetPO> listByOrders(@Param("orderList") List<ProtoOrderPO> orderList);
}