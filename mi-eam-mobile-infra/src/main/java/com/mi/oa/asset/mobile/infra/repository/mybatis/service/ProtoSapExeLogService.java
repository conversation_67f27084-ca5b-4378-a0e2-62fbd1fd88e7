package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSapExeLogPO;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/12/10
 */
public interface ProtoSapExeLogService extends IService<ProtoSapExeLogPO> {

    /**
     * 添加抛送SAP日志
     * @param obj
     * @param funId
     * @param orderId
     * @param orderCode
     * @param orderName
     * @param doUser
     * @return
     */
    String insertLog(Object obj, String funId, String orderId, String orderCode, String orderName, String doUser);

    /**
     * 添加抛送SAP日志
     * @param obj
     * @param funId
     * @param orderId
     * @param orderCode
     * @param orderName
     * @param doUser
     * @return
     */
    String insertLog(Object obj, String funId, String orderId, String orderCode, String orderName, String doUser, String exeCode);

    /**
     * 更新执行完的结果
     * @param exelogId
     * @param exeCode
     * @param exeDesc
     */
    void updateLog(String exelogId, String exeCode, String exeDesc);
}
