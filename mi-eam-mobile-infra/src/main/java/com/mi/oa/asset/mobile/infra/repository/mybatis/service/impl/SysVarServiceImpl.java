package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysVarPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysVarMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysVarService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/10 10:24
 */
@Service
@Slf4j
public class SysVarServiceImpl extends ServiceImpl<SysVarMapper, SysVarPO> implements SysVarService {

    private static final String ONE = "1", TRUE = "true", YES = "yes";

    @Override
    public SysVarPO findByVarCode(String varCode) {
        QueryWrapper<SysVarPO> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(SysVarPO::getVarCode, varCode);
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public String findValByVarCode(String varCode) {
        SysVarPO po = findByVarCode(varCode);
        if (!ObjectUtils.isEmpty(po)) {
            return po.getVarValue();
        } else {
            return null;
        }
    }

    @Override
    public String findValByVarCode(String varCode, String defaultVarCode) {
        SysVarPO po = findByVarCode(varCode);
        if (!ObjectUtils.isEmpty(po)) {
            return po.getVarValue();
        } else {
            SysVarPO po1 = findByVarCode(defaultVarCode);
            if (!ObjectUtils.isEmpty(po1)) {
                return po1.getVarValue();
            } else {
                return null;
            }
        }
    }

    @Override
    public Boolean findBoolValByVarCode(String varCode) {
        String val = findValByVarCode(varCode);
        if (StringUtils.isBlank(val)) {
            return null;
        }
        if (ONE.equalsIgnoreCase(val) || TRUE.equalsIgnoreCase(val) || YES.equalsIgnoreCase(val)) {
            return true;
        } else {
            return false;
        }
    }

}
