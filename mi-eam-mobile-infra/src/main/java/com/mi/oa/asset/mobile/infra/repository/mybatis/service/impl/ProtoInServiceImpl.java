package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.dto.AsnOrderDTO;
import com.mi.oa.asset.mobile.infra.dto.BackConfirmDto;
import com.mi.oa.asset.mobile.infra.dto.sap.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoInMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/12/20
 */
@Service
@Slf4j
public class ProtoInServiceImpl extends ServiceImpl<ProtoInMapper, ProtoInPO> implements ProtoInService {

    @Autowired
    private ProtoInDetService protoInDetService;

    @Autowired
    private ProtoAsnOrderDetService protoAsnOrderDetService;

    @Autowired
    private ProtoAsnOrderService protoAsnOrderService;

    @Autowired
    private ProtoApplyService protoApplyService;

    @Autowired
    private ProtoOutDetService protoOutDetService;

    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private SapService sapService;

    private final String ERROR_MESSAGE = "errorMessage";

    private final String WARN_MESSAGE = "warnMessage";

    @Override
    public List<ProtoInPO> listByInIds(Collection<String> inIds) {
        if (CollectionUtils.isEmpty(inIds)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoInPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProtoInPO::getInId, inIds);
        return baseMapper.selectList(queryWrapper);
    }


    @Override
    public List<BackConfirmDto> getbackConfirmByDeviceCode(String deviceCode) {
        return this.getBaseMapper().getbackConfirmByDeviceCode(deviceCode);
    }

    @Override
    public void updateBackConfirmToUser(String inId, String userName, String userCode, String userEmp) {
        LambdaUpdateWrapper<ProtoInPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .eq(ProtoInPO::getInId,inId).isNull(ProtoInPO::getUserName)
                .set(ProtoInPO::getUserName,userName).set(ProtoInPO::getUserCode,userCode).set(ProtoInPO::getEmpCode,userEmp);
        this.update(updateWrapper);
    }

    @Override
    public List<ProtoInPO> findListByParam(ProtoInPO po) {
        QueryWrapper<ProtoInPO> queryWrapper = new QueryWrapper(po);
        return this.list(queryWrapper);
    }

    @Override
    public ProtoInPO findOneByParam(ProtoInPO po) {
        QueryWrapper<ProtoInPO> queryWrapper = new QueryWrapper(po);
        return this.getOne(queryWrapper);
    }
    @Override
    public List<ProtoInPO> getNotPushSAPList(Integer pageNum, Integer pageSize) {
        Page page = new Page(pageNum, pageSize);
        LambdaQueryWrapper<ProtoInPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProtoInPO::getInType, InTypeEnum.NOT_ASN_DELIVERY.getType(), InTypeEnum.HAVE_ASN_DELIVERY.getType());
        queryWrapper.eq(ProtoInPO::getInStatus, InStatusEnum.HAVE_DELIVERY.getState());
        queryWrapper.ne(ProtoInPO::getMrpType, MrpTypeEnum.PROTOTYPE.getType());
        return this.page(page, queryWrapper).getRecords();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void noAsnOrderPushSAP(ProtoInSapDTO notAsnPushSAP, ProtoInPO protoInPO) {
        String orderCode = notAsnPushSAP.getOrderCode();
        String sapFactory = notAsnPushSAP.getSapFactory();
        String inCode = notAsnPushSAP.getInCode();
        if (StringUtils.isBlank(inCode)) {
            // 亦庄
            if (SapFactoryEnum.YI_ZHUANG.getSapFactory().equals(sapFactory)) {
                inCode = commonService.getBatchIdUnique(SapBatchIdEnum.YI_ZHUANG);
            } else if (SapFactoryEnum.CHANG_PING.getSapFactory().equals(sapFactory)) {
                if (SrmPurchaseTypeEnum.ZDB.getCode().equals(notAsnPushSAP.getBsart())) {
                    inCode = commonService.getBatchIdUnique(SapBatchIdEnum.ASN_RECEIVE_GOODS);
                } else {
                    inCode = commonService.getBatchIdUnique(SapBatchIdEnum.CHANG_PING);
                }
            } else if (SapFactoryEnum.CHANG_PING2.getSapFactory().equals(sapFactory)) {
                inCode = commonService.getBatchIdUnique(SapBatchIdEnum.CHANG_PING2);
            } else if (SapFactoryEnum.ROBOT.getSapFactory().equals(sapFactory)) {
                inCode = commonService.getBatchIdUnique(SapBatchIdEnum.ROBOT);
            } else {
                inCode = commonService.getBatchIdUnique(SapBatchIdEnum.ASN_RECEIVE_GOODS);
            }
            notAsnPushSAP.setInCode(inCode);
            // 更新收货记录编号
            protoInDetService.updateInCode(inCode, protoInPO.getInId(), orderCode);
        }
        // 添加SAP明细
        List<ProtoInNoAsnDTO> notAsnDetPushSAPList = protoInDetService.getNotAsnDetPushSAPList(protoInPO.getInId(), orderCode);
        pushSAP(notAsnPushSAP, notAsnDetPushSAPList, protoInPO.getInId(), protoInPO.getInType(), protoInPO.getUserName(), protoInPO.getMrpType());
    }

    /**
     * 更新主单的状态
     * @param inId
     * @param inType
     */
    private void updateSapRetType(String inId, String inType, boolean sapRetType) {
        int count = protoInDetService.countNotSuccess(inId);
        if (count == 0){
            // 更新in_status
            LambdaUpdateWrapper<ProtoInPO> wrapper2 = new LambdaUpdateWrapper<>();
            wrapper2.set(ProtoInPO::getInStatus, AsnInStatusEnum.HAVE_DELIVERY.getState());
            wrapper2.eq(ProtoInPO::getInId, inId);
            this.update(wrapper2);
        }
        if (InTypeEnum.NOT_ASN_DELIVERY.getType().equals(inType) && count > 0){
            return;
        }
        String sapRetTypeStr = (sapRetType ? SapRetEnum.SUCCESS.getKey() : SapRetEnum.ERROR.getKey());
        LambdaUpdateWrapper<ProtoInPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProtoInPO::getSapRetType, sapRetTypeStr);
        wrapper.set(ProtoInPO::getPostingDate, new Date());
        wrapper.eq(ProtoInPO::getInId, inId);
        this.update(wrapper);
    }


    private SapCustomVO pushSAP(ProtoInSapDTO sapMainDTO, List<ProtoInNoAsnDTO> notAsnPushSapList, String inId, String inType, String userName, String mrpType) {
        // 创建SAP主表
        Sap107JsonDTO sapMain = new Sap107JsonDTO();
        sapMain.setBusiType(CommonConstant.SapBusiTypeEnum.RECEIPT_GOODS.getType());
        sapMain.setBatchId(sapMainDTO.getInCode());
        sapMain.setMidocNum(sapMainDTO.getOrderCode());
        sapMain.setVndocNum(sapMainDTO.getOrderCode());
        sapMain.setSapFactory(sapMainDTO.getSapFactory());
        sapMain.setBsart(sapMainDTO.getBsart());
        List<Sap107JsonDTO.SapItemDTO> sapItems = new ArrayList<>();
        for (ProtoInNoAsnDTO proto : notAsnPushSapList) {
            String skuCode = proto.getSkuCode();
            String midocRow = Integer.parseInt(proto.getItemRowNo())+"";
            Sap107JsonDTO.SapItemDTO sapItem = new Sap107JsonDTO.SapItemDTO();
            sapItem.setBatchRow(midocRow);
            sapItem.setMidocRow(midocRow);

            // 添加ASN单信息
            sapItem.setVndocNum(sapMainDTO.getOrderCode());
            sapItem.setVndocRow(midocRow);

            sapItem.setMaterialOne(skuCode);
            sapItem.setMatDescOne(proto.getSkuName());
            sapItem.setQuantityOne(proto.getCnt()+"");
            sapItems.add(sapItem);
            // 更新明细SAP行号
            protoInDetService.updateSapItemRowNo(midocRow, inId, sapMainDTO.getInCode());
        }
        sapMain.setItemDTOList(sapItems);
        // 推送SAP，107接口
        String orderName = "收货记录-ASN收货";
        if (InTypeEnum.NOT_ASN_DELIVERY.getType().equals(inType)) {
            orderName = "收货记录-无ASN收货";
        }
        try {
            SapCustomVO sapCustomVO = sapService.sap107Json(sapMain, "proto_in", inId, sapMainDTO.getInCode(), orderName, userName, mrpType);
            protoInDetService.updateSapRetType(inId, sapMainDTO.getOrderCode(), sapCustomVO.getSuccess());
            // 更新发送SAP状态和收货状态
            this.updateSapRetType(inId, inType, sapCustomVO.getSuccess());
            return sapCustomVO;
        } catch (Exception e) {
            log.error("sapService.sap107Json exception", e);
            return new SapCustomVO(null, false, "推送SAP107接口失败");
        }
    }

    /**
     * 获取待匹配清单
     * @param pageNum
     * @return
     */
    public List<ProtoInPO> getUnMachList(int pageNum){
        LambdaQueryWrapper<ProtoInPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProtoInPO::getInType, InTypeEnum.NOT_ASN_DELIVERY.getType());
        queryWrapper.eq(ProtoInPO::getInStatus, InStatusEnum.HAVE_DELIVERY.getState());
        Page page = new Page(pageNum, CommonConstant.DEFAULT_PAGE_SIZE);
        return this.page(page, queryWrapper).getRecords();
    }

    /**
     * 处理待匹配清单
     * @param protoInPO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealUnMach(ProtoInPO protoInPO){
        String inId = protoInPO.getInId();
        List<ProtoInDetPO> detList = protoInDetService.listByInId(inId);
        if (CollectionUtils.isEmpty(detList)){
            return;
        }
        if (!InTypeEnum.NOT_ASN_DELIVERY.getType().equals(protoInPO.getInType())
                || !InStatusEnum.HAVE_DELIVERY.getState().equals(protoInPO.getInStatus())){
            return;
        }
        for (ProtoInDetPO detPO : detList){
            if (SapRetEnum.SUCCESS.getKey().equals(detPO.getSapRetType())){
                continue;
            }
            if (MachAuditingEnum.HANDLE_MACH.getState().equals(detPO.getAuditing())){
                continue;
            }
            String sn = detPO.getDeviceCode();
            AsnOrderDTO machAsn = protoAsnOrderDetService.getMachAsn(sn);
            if (null == machAsn){
                continue;
            }
            // 处理匹配度
            Map<String, String> compareMap = this.compareAsn(machAsn, detPO);
            String errorMessage = compareMap.get(ERROR_MESSAGE);
            detPO.setOrderDetId(machAsn.getDetId());
            detPO.setOrderCode(machAsn.getOrderCode());
            detPO.setSkuName(machAsn.getSkuName());
            detPO.setItemRowNo(machAsn.getItemRowNo());
            detPO.setAuditDate(DateUtils.getFormatDate(DateUtils.COMMON_PATTERN));
            FeedBackEnum feedBackEnum = null;
            // 完全匹配
            if (StringUtils.isEmpty(errorMessage)){
                detPO.setAuditing(MachAuditingEnum.HAVE_MACH.getState());
                detPO.setErrorMsg(compareMap.get(WARN_MESSAGE));
                feedBackEnum =  FeedBackEnum.HAVE_FEED_BACK;
            } else {
                detPO.setAuditing(MachAuditingEnum.FAIL_MACH.getState());
                detPO.setErrorMsg(compareMap.get(ERROR_MESSAGE));
                feedBackEnum = FeedBackEnum.NOT_FEED_BACK;
            }
            detPO.setIsFeekback(feedBackEnum.getState());
            // 更新子表的状态
            protoInDetService.updateById(detPO);
            // 更新ASN订单状态为已收货
            protoAsnOrderDetService.updateAsnOrderStatusReceiptNum(machAsn.getDetId(), AsnInStatusEnum.HAVE_DELIVERY.getState(), 1);
            protoAsnOrderService.feedBackReceivingToAsnOrder(protoInPO, machAsn.getOrderCode());
            updateMachIsFeedBackByDet(inId, feedBackEnum);
            // 更新台账数据
            ProtoCardPO cardPO = new ProtoCardPO();
            cardPO.setDeviceCode(sn);
            cardPO.setSkuName(detPO.getSkuName());
            cardPO.setDeviceType(detPO.getDeviceType());
            cardPO.setLaserCode(detPO.getLaserCode());
            cardPO.setImei(detPO.getImei());
            cardPO.setOrderCode(detPO.getOrderCode());
            cardPO.setInCode(detPO.getInCode());
            cardPO.setSapFactory(machAsn.getSapFactory());
            cardPO.setGoodsId(machAsn.getGoodsId());
            cardPO.setMakeCn(SapMakeCnEnum.getMakeCn(cardPO.getSapFactory()));
            protoCardService.updateInfoBySn(cardPO);
        }
    }

    @Override
    public List<ProtoInPO> listByInCodeList(Collection<String> inCodes) {
        if (CollectionUtils.isEmpty(inCodes)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoInPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoInPO::getInCode, inCodes);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public String confirmReceiving(ProtoAsnOrderPO orderPO, List<ProtoAsnOrderDetPO> itemList, String inType, String userCode, String houseName, String houseCode, String houseId) {
        // 保存收货主表
        String inId = commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_IN);
        ProtoInPO protoInPO = new ProtoInPO();
        BeanUtils.copyProperties(orderPO, protoInPO);
        if (SapFactoryEnum.YI_ZHUANG.getSapFactory().equals(orderPO.getSapFactory())) {
            protoInPO.setInCode(commonService.getBatchIdUnique(SapBatchIdEnum.YI_ZHUANG));
        } else if (SapFactoryEnum.CHANG_PING.getSapFactory().equals(orderPO.getSapFactory())) {
            if (SrmPurchaseTypeEnum.ZDB.getCode().equals(orderPO.getBsart())) {
                protoInPO.setInCode(commonService.getBatchIdUnique(SapBatchIdEnum.ASN_RECEIVE_GOODS));
            } else {
                protoInPO.setInCode(commonService.getBatchIdUnique(SapBatchIdEnum.CHANG_PING));
            }
        } else if (SapFactoryEnum.CHANG_PING2.getSapFactory().equals(orderPO.getSapFactory())) {
            protoInPO.setInCode(commonService.getBatchIdUnique(SapBatchIdEnum.CHANG_PING2));
        } else if (SapFactoryEnum.ROBOT.getSapFactory().equals(orderPO.getSapFactory())) {
            protoInPO.setInCode(commonService.getBatchIdUnique(SapBatchIdEnum.ROBOT));
        } else {
            protoInPO.setInCode(commonService.getBatchIdUnique(SapBatchIdEnum.ASN_RECEIVE_GOODS));
        }
        protoInPO.setAddUserid(userCode);
        protoInPO.setAddDate(new Date());
        protoInPO.setModifyDate(new Date());
        protoInPO.setInStatus(InStatusEnum.HAVE_DELIVERY.getState());
        protoInPO.setInDate(new Date());
        protoInPO.setInType(inType);
        protoInPO.setInId(inId);
        this.save(protoInPO);

        // 保存收货明细表
        protoInDetService.saveAsnDet(itemList, inId, userCode);
        // 保存台账
        protoCardService.saveCardInfo(orderPO.getOrderCode(), itemList, protoInPO.getInCode(), userCode, houseName, houseCode, houseId);
        return inId;
    }

    @Override
    public List<ProtoInPO> getByInIdsAndInType(List<String> inIds, String... inType) {
        if (CollectionUtils.isEmpty(inIds)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoInPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProtoInPO::getInId, inIds);
        queryWrapper.in(ProtoInPO::getInType, inType);
        return this.list(queryWrapper);
    }

    @Override
    public void pushSAP(String inId, String userName) {
        ProtoInPO protoInPO = this.getById(inId);
        String inStatus = protoInPO.getInStatus();
        if (InStatusEnum.DUMPED_SAP.getState().equals(inStatus)){
            return;
        }
        if (!InStatusEnum.HAVE_DELIVERY.getState().equals(inStatus)){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_IN_003);
        }
        String orderCode = protoInPO.getOrderCode();
        if (StringUtils.isBlank(orderCode)){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_IN_004);
        }
        ProtoAsnOrderPO asnOrderPO = protoAsnOrderService.getAsnOrderByCode(orderCode);
        List<ProtoPushSapDTO> pushSapList = protoInDetService.getPushSapList(inId);

        Sap107JsonDTO sapMain = new Sap107JsonDTO();
        sapMain.setBusiType(CommonConstant.SapBusiTypeEnum.RECEIPT_GOODS.getType());
        sapMain.setBatchId(protoInPO.getInCode());
        sapMain.setMidocNum(protoInPO.getOrderCode());
        sapMain.setVndocNum(protoInPO.getOrderCode());
        sapMain.setSapFactory(asnOrderPO.getSapFactory());
        sapMain.setBsart(asnOrderPO.getBsart());
        List<Sap107JsonDTO.SapItemDTO> sapItems = new ArrayList<>(pushSapList.size());
        for (ProtoPushSapDTO sapItemsDTO : pushSapList) {

            String skuCode = sapItemsDTO.getSkuCode();
            int midocRow = Integer.parseInt(sapItemsDTO.getItemRowNo());

            Sap107JsonDTO.SapItemDTO sapItem = new Sap107JsonDTO.SapItemDTO();
            sapItem.setBatchRow(String.valueOf(midocRow));
            sapItem.setMidocRow(String.valueOf(midocRow));

            // 添加ASN单信息
            sapItem.setVndocNum(orderCode);
            sapItem.setVndocRow(String.valueOf(midocRow));

            sapItem.setMaterialOne(skuCode);
            sapItem.setMatDescOne(sapItemsDTO.getSkuName());
            sapItem.setQuantityOne(String.valueOf(sapItemsDTO.getCountSku()));
            sapItems.add(sapItem);
            // 更新明细SAP行号
            protoInDetService.updateProtoInDetToSapItemRowNo(inId, skuCode, String.valueOf(midocRow));
        }

        sapMain.setItemDTOList(sapItems);
        String inType =protoInPO.getInType();
        String orderName = (InTypeEnum.NOT_ASN_DELIVERY.getType().equals(inType) ? "收货记录-无ASN收货" : "收货记录-ASN收货");
        SapCustomVO sapCustomVO = sapService.sap107Json(sapMain, "proto_in", inId, protoInPO.getInCode(), orderName, userName, protoInPO.getMrpType());
        if (!sapCustomVO.getSuccess()){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_SAP_001, sapCustomVO.getMessage());
        }
        // 抛送SAP成功时更新sap_ret_type状态
        this.updateSapRetType(inId, inType, sapCustomVO.getSuccess());
        // 更新收货单状态为：已抛SAP
        this.updateInStatus(inId, InStatusEnum.DUMPED_SAP.getState(), null);
    }

    /**
     * 更新收货主单状态
     * @param inId
     * @param inStatus
     * @param userInfo
     */
    private void updateInStatus(String inId, String inStatus, UserInfo userInfo){
        LambdaUpdateWrapper<ProtoInPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProtoInPO::getInStatus, inStatus);
        if (null != userInfo){
            wrapper.set(ProtoInPO::getUserName, userInfo.getUserName());
            wrapper.set(ProtoInPO::getUserCode, userInfo.getUserCode());
            wrapper.set(ProtoInPO::getEmpCode, userInfo.getEmpCode());
        }
        wrapper.eq(ProtoInPO::getInId, inId);
        this.update(wrapper);
    }

    /**
     * 如果子表全部匹配成功 则标记主表为匹配成功
     * @param inID
     * @param backEnum
     */
    private void updateMachIsFeedBackByDet(String inID, FeedBackEnum backEnum) {
        if (FeedBackEnum.HAVE_FEED_BACK.equals(backEnum)){
            int count = protoInDetService.countInIdAndFeedBack(inID, FeedBackEnum.NOT_FEED_BACK);
            if (count == 0){
                this.updateInFeedBack(inID, backEnum);
            }
        }
    }

    /**
     * 对不SN
     * @param asnOrderDTO
     * @param protoInDetPO
     * @return
     */
    public Map<String, String> compareAsn(AsnOrderDTO asnOrderDTO, ProtoInDetPO protoInDetPO) {
        Map<String, String> result = new HashMap<>(4);
        StringBuffer warnBuffer = new StringBuffer();
        StringBuffer errorBuffer = new StringBuffer();
        if (!equalsStr(asnOrderDTO.getDeviceType(), protoInDetPO.getDeviceType())){
            errorBuffer.append("配置、");
        }
        if (!equalsStr(asnOrderDTO.getLaserCode(), protoInDetPO.getLaserCode())) {
            errorBuffer.append("镭雕号、");
        }
        if (!equalsStr(asnOrderDTO.getImei(), protoInDetPO.getImei())){
            errorBuffer.append("IMEI号、");
        }
        if (!equalsStr(asnOrderDTO.getSkuCode(), protoInDetPO.getSkuCode())){
            errorBuffer.append("小米料号、");
        }
        String applyCode = protoOutDetService.getApplyCode(protoInDetPO.getDeviceCode());
        ProtoApplyPO protoApply = protoApplyService.getByApplyCode(applyCode);
        String machSapFactory = (Objects.isNull(protoApply) ? "" : protoApply.getMakeCn());
        if (StringUtils.isNotBlank(machSapFactory) && !equalsStr(asnOrderDTO.getSapFactory(), (StringUtils.isNotEmpty(machSapFactory) && machSapFactory.length() >= 4) ?
                machSapFactory.substring(0, 4) : machSapFactory)) {
            errorBuffer.append("样机生产地、");
        }
        if (!equalsStr(asnOrderDTO.getProjectCode(), protoInDetPO.getProjectCode())
                || !equalsStr(asnOrderDTO.getStageName(), protoInDetPO.getStageName())){
            warnBuffer.append("警告：项目或阶段不一致，但不影响过账，匹配");
        }
        result.put(WARN_MESSAGE, warnBuffer.toString());
        if (errorBuffer.length() > 0) {
            // SN相同，镭雕号不通，make_cn不同，IMEI，配置，料号不同
            String errorMessage = errorBuffer.delete(errorBuffer.length() - 1, errorBuffer.length()).toString();
            errorMessage = "SN相同，" + errorMessage + "不同";
            if (warnBuffer.length() > 0) {
                errorMessage += "；" + warnBuffer.toString();
            }
            result.put(ERROR_MESSAGE, errorMessage);
        }
        return result;
    }

    /**
     * 更新proto_in表is_feekback为未收货
     * @param inId
     */
    private void updateInFeedBack(String inId, FeedBackEnum backEnum){
        LambdaUpdateWrapper<ProtoInPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProtoInPO::getIsFeekback, backEnum.getState());
        updateWrapper.eq(ProtoInPO::getInId, inId);
        this.update(updateWrapper);
    }

    /**
     * 比较两个人字符串是否相等  null和""等价 去掉前后空格
     * @param str1
     * @param str2
     * @return
     */
    private boolean equalsStr(String str1, String str2){
        str1 = (Objects.isNull(str1) ? "" : StringUtils.trim(str1));
        str2 = (Objects.isNull(str2) ? "" : StringUtils.trim(str2));
        return str1.equals(str2);
    }

    /**
     * 查询empCode为null的小米内部员工信息的收货记录
     *
     * @return
     */
    @Override
    public List<ProtoInPO> getInByEmpCode() {
        LambdaQueryWrapper<ProtoInPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(ProtoInPO::getEmpCode);
        queryWrapper.apply("LOCATE('p-',user_code) = 0");
        queryWrapper.groupBy(ProtoInPO::getUserCode);
        return this.list(queryWrapper);
    }

    /**
     * 修改empCode为null的小米内部员工信息的收货记录
     */
    @Override
    public void updateInByEmpCode(String empCode, String userCode){
        LambdaUpdateWrapper<ProtoInPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProtoInPO::getEmpCode, empCode);
        updateWrapper.isNull(ProtoInPO::getEmpCode);
        updateWrapper.eq(ProtoInPO::getUserCode, userCode);
        this.update(updateWrapper);
    }

    @Override
    public List<ProtoInPO> getAsnLastSevenDay() {
        LocalDateTime localDateTime = LocalDateTime.now().plusDays(-7);
        LambdaQueryWrapper<ProtoInPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(ProtoInPO::getInDate, Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant()));
        wrapper.eq(ProtoInPO::getInType, InTypeEnum.HAVE_ASN_DELIVERY.getType());
        return this.list(wrapper);
    }

    @Override
    public List<Map<String, Object>> getEntryDate(List<String> snList) {
        if (CollectionUtils.isEmpty(snList)){
            return new ArrayList<>();
        }
        return baseMapper.getEntryDate(snList);
    }
}
