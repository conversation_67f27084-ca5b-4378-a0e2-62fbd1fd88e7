package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPayAccountPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoPayAccountMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoPayAccountService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/23
 */
@Service
public class ProtoPayAccountServiceImpl extends ServiceImpl<ProtoPayAccountMapper, ProtoPayAccountPO> implements ProtoPayAccountService {

    @Override
    public Integer countByAccountDate(String mrpType, Date accountDate) {
        LambdaQueryWrapper<ProtoPayAccountPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoPayAccountPO::getMrpType, mrpType);
        wrapper.eq(ProtoPayAccountPO::getAccountDate, accountDate);
        return this.count(wrapper);
    }
}
