package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mi.oa.asset.mobile.application.dto.message.EmailDTO;
import com.mi.oa.asset.mobile.application.service.MessageService;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.RedisCachePrefixKeyEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoThirdUserPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoThirdUserMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoThirdUserService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysVarService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.UserInfoService;
import com.mi.oa.asset.mobile.utils.DateUtil;
import com.mi.oa.infra.oaucf.idm.api.enums.AccountStatusEnum;
import com.mi.oa.infra.oaucf.idm.api.rep.AccountInfoDto;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 第三方人员配置
 *
 * <AUTHOR>
 * @date 2022/1/6 16:55
 */
@Service
@Slf4j
public class ProtoThirdUserServiceImpl extends ServiceImpl<ProtoThirdUserMapper, ProtoThirdUserPO> implements ProtoThirdUserService {

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private MessageService messageService;

    @Value("${thirdUser-cc}")
    private String reminderEmailCc;

    @Autowired
    private SysVarService sysVarService;

    @Override
    public ProtoThirdUserPO findUserInfo(String userCode) {
        return findUserInfo(userCode, "");
    }

    @Override
    public ProtoThirdUserPO findUserInfoCache(String userCode) {
        String cacheKey = RedisCachePrefixKeyEnum.THIRD_USER_CACHE.getPrefixKey() + userCode;
        Object obj = RedisUtils.get(cacheKey);
        if (null != obj) {
            return (ProtoThirdUserPO) obj;
        }
        ProtoThirdUserPO thirdUserPO = this.findUserInfo(userCode, CommonConstant.RecordStatus.COMMITTED.getStatus());
        if (null != thirdUserPO) {
            RedisUtils.setEx(cacheKey, thirdUserPO, 5, TimeUnit.MINUTES);
        }
        return thirdUserPO;
    }

    private ProtoThirdUserPO findUserInfo(String userCode, String auditing) {
        QueryWrapper<ProtoThirdUserPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProtoThirdUserPO::getUserCode, userCode);
        if (!StringUtils.isBlank(auditing)) {
            queryWrapper.lambda().eq(ProtoThirdUserPO::getAuditing, auditing);
        }
        queryWrapper.lambda().last("limit 1");
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    /**
     * 根据主键查询用户信息
     *
     * @param userId
     * @return
     */
    @Override
    public ProtoThirdUserPO findUserById(String userId) {
        QueryWrapper<ProtoThirdUserPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProtoThirdUserPO::getUserId, userId);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    public void synValidDate() {
        // 查询当前时间31天前的，超过这个时间IDM为禁用状态无需考虑
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.COMMON_PATTERN);
        List<ProtoThirdUserPO> list = this.list(Wrappers.lambdaQuery(ProtoThirdUserPO.class)
                .ge(ProtoThirdUserPO::getValidDate, dateTimeFormatter.format(LocalDateTime.now().plusDays(-31)))
                .or().isNull(ProtoThirdUserPO::getValidDate));
        for (ProtoThirdUserPO userPO : list) {
            String userCode = userPO.getUserCode();
            try {
                AccountInfoDto infoDto = userInfoService.getAccountInfoByUserName(userCode);
                if (infoDto == null) {
                    continue;
                }
                Date validDate = infoDto.getExpireTime() != null ?
                        Date.from(infoDto.getExpireTime().toInstant()) : null;
                //P账号在IDM状态为关闭，EAM状态已失效，有效期不再更新；
                if (AccountStatusEnum.CLOSE.equals(infoDto.getAccountStatus())) {
                    userPO.setAuditing(CommonConstant.RecordStatus.INVALID.getStatus());
                }else if(AccountStatusEnum.DISABLE.equals(infoDto.getAccountStatus())){
                    //P账号在IDM状态为禁用，EAM状态已失效，有效期每日更新；
                    userPO.setAuditing(CommonConstant.RecordStatus.INVALID.getStatus());
                    userPO.setValidDate(validDate);
                }else if(AccountStatusEnum.ENABLE.equals(infoDto.getAccountStatus())){
                    //P账号在IDM状态为已启用，EAM状态已提交，有效期每日更新；
                    userPO.setAuditing(CommonConstant.RecordStatus.COMMITTED.getStatus());
                    userPO.setValidDate(validDate);
                }
            } catch (Exception e) {
                log.error("get_userAccount_error_userCode:{}, e:{}", userCode, e);
            }
        }
        List<ProtoThirdUserPO> collect = list.stream().filter(o -> null != o.getValidDate()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        this.updateBatchById(collect);
    }

    @Override
    public List<ProtoThirdUserPO> listByAuditing(CommonConstant.RecordStatus recordStatus) {
        LambdaQueryWrapper<ProtoThirdUserPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoThirdUserPO::getAuditing, recordStatus.getStatus());
        return this.list(wrapper);
    }


    @Override
    public void sendReminderEmail() {
        List<ProtoThirdUserPO> list = this.listByAuditing(CommonConstant.RecordStatus.COMMITTED);
        Date nowDate = new Date();
        for (ProtoThirdUserPO userPO : list) {
            Date validDate = userPO.getValidDate();
            if (null == validDate) {
                continue;
            }
            try {
                String varCode = "proto.leave.user_" + userPO.getMrpType();
                String emailStr = sysVarService.findValByVarCode(varCode);

                int diffDay = DateUtil.dayDiff(validDate, nowDate);
                // 到期前15天发送一次提醒，到期前一周每天都发送一次
                if (diffDay > 0 && (diffDay == 15 || diffDay <= 7)) {
                    // 发送到期提醒邮件
                    Integer count = protoCardService.countByUserCode(userPO.getUserCode());
                    String content = getSendEmailContent(userPO, count);
                    EmailDTO emailDTO = EmailDTO.builder().emails(Lists.newArrayList(userPO.getEmail())).content(content).title("【工程机】三方借用P账号即将到期提醒").build();
                    messageService.sendEmail(emailDTO);
                    if (StringUtils.isNotBlank(emailStr)) {
                        List<String> emails = new ArrayList<>();
                        for (String str : emailStr.split(";")) {
                            emails.add(str + "@xiaomi.com");
                        }
                        emailDTO = EmailDTO.builder().emails(emails).content(content).title("【工程机】三方借用P账号即将到期提醒").build();
                        messageService.sendEmail(emailDTO);
                    }
                }
            } catch (Exception e) {
                log.error("sendReminderEmail_error_userCode:{}, e:{}", userPO.getUserCode(), e);
            }
        }
    }

    private String getSendEmailContent(ProtoThirdUserPO userPO, Integer count) {
        StringBuffer content = new StringBuffer();
        content.append("<span style\"color: rgb(33, 33, 33); font-family: &quot;Microsoft YaHei UI&quot;, &quot;Microsoft YaHei&quot;, 微软雅黑, SimSun, 宋体, sans-serif, serif, EmojiFont;\">以下第三方P账号即将到期，请及时进行相应处理</span><br><br>");
        content.append("<table border=\"1px\" bordercolor=\"#000000\" cellspacing=\"0px\" style=\"border-collapse:collapse;text-align: center;width: 820px;\">")
                .append("<tr>")
                .append("<td width=\"40px\">账号</td>")
                .append("<td width=\"130px\">姓名</td>")
                .append("<td width=\"80px\">供应商名称</td>")
                .append("<td width=\"50px\">供应商编码</td>")
                .append("<td width=\"130px\">工程机数量</td>")
                .append("<td width=\"50px\">账号有效期</td>")
                .append("</tr>")
                .append("<tr>")
                .append("<td>" + userPO.getUserCode() + "</td>")
                .append("<td>" + userPO.getUserName() + "</td>")
                .append("<td>" + userPO.getProviderName() + "</td>")
                .append("<td>" + userPO.getProviderCode() + "</td>")
                .append("<td>" + count + "</td>")
                .append("<td>" + DateUtil.getDateStr(userPO.getValidDate(), "yyyy-MM-dd") + "</td>")
                .append("</tr>")
                .append("</table>");
        return content.toString();
    }

}
