package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MailTemplateParamPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.MailTemplateParamMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.MailTemplateParamService;
import com.mi.oa.asset.mobile.utils.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
@Service
@Slf4j
public class MailTemplateParamServiceImpl extends ServiceImpl<MailTemplateParamMapper, MailTemplateParamPO> implements MailTemplateParamService {

    @Override
    public List<MailTemplateParamPO> listByTemplateId(String templateId) {
        LambdaQueryWrapper<MailTemplateParamPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MailTemplateParamPO::getTemplateId, templateId);
        return this.list(wrapper);
    }

    @Override
    public String getTemplateContent(String templateId, String templateCont, Map<String, String> dataMap) {
        // 查询模板参数
        List<MailTemplateParamPO> paramList = this.listByTemplateId(templateId);

        // 替换模板参数
        for (MailTemplateParamPO mailTemplateParamPO : paramList) {
            String paramName = mailTemplateParamPO.getParamName();
            String paramValue = mailTemplateParamPO.getParamValue();
            templateCont = templateCont.replace("{" + paramName + "}", MapUtil.getValue(dataMap, paramName, paramValue));
        }

        return templateCont;
    }
}
