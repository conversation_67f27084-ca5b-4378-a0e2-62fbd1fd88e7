package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.dto.BpmExeLogDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.BpmExeLogPO;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
public interface BpmExeLogService extends IService<BpmExeLogPO> {
    void save(BpmExeLogDTO bpmExeLogDTO);

    void insertBpmlog(String funId, String orderName, String keyId, String code, String userName, boolean success, String desc, Object body);
}
