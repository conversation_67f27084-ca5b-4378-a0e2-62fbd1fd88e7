package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysTableCodePO;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/10
 */
public interface SysTableCodeService extends IService<SysTableCodePO> {

    Integer getMaxSysTableCode(String tabName, String codeExt);

    SysTableCodePO getByTabName(String tableName, String codeExt);
}
