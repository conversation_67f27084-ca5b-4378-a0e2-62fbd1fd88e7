package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysUserRolePO;

import java.util.List;

/**
 * 用户拥有角色
 *
 * <AUTHOR>
 * @date 2022/1/25 17:25
 */
public interface SysUserRoleService extends IService<SysUserRolePO> {

    /**
     * 添加用户角色
     *
     * @param
     * @param roleNo
     * @param userCode
     * @return
     */
    void addSysUserRole(String roleNo, String userCode);

    /**
     * 查询用户是否授权这个角色
     *
     * @param roleNo
     * @param userCode
     * @return
     */
    Integer countByRoleNoAndUserCode(String roleNo, String userCode);

    /**
     * @param userId
     * @param funId
     * @return
     */
    List<String> getMrpType(String userId, String funId);

    /**
     * 根据用户角色查询授权用户
     *
     * @param roleNo
     * @return
     */
    List<String> getUserIdsByRoleNo(String roleNo);
}
