package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SystemAttachPO;

import java.util.List;

/**
 * Copyright (c) 2022 XiaoMi Inc.All Rights Reserved.
 * Description:
 *
 * <AUTHOR>
 * @date 2022/7/11 09:47
 */
public interface SystemAttachService extends IService<SystemAttachPO> {

    /**
     * 在附件表查找文件名
     * @param funId
     * @param dataId
     * @return
     */
    List<SystemAttachPO> getFileName(String funId, String dataId);

    /**
     * 批量保存附件信息
     *
     * @param attachList
     * @return
     */
    boolean saveAttachByBatch(List<SystemAttachPO> attachList);


    /**
     * 删除图文附件
     *
     * @param funId
     * @param dataIds
     * @return
     */
    int batchDeleteSystemAttach(String funId, List<String> dataIds);
}
