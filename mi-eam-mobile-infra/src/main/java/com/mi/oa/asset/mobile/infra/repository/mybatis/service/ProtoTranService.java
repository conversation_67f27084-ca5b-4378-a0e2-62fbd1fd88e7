package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.application.dto.ConditionDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoTranSapDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranPO;

import java.util.List;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
public interface ProtoTranService extends IService<ProtoTranPO> {
    ProtoTranPO getByTranId(String tranId);
    ProtoTranPO getByBusinessKey(String businessKey);

    /**
     * 找出所有未抛送SAP的转移单（由于关联了明细表，所以会有重复单，后续程序过滤）
     * @return
     */
    List<ProtoTranSapDTO> getNoAsnOrderTranList();

    /**
     * 转移7-15天未确认到期提醒
     * @return
     */
    List<ProtoTranPO> listToRemind();

    /**
     * 推送消息提醒
     * @param tranPO
     */
    void remindMessage(ProtoTranPO tranPO);

    /**
     * 查询转移记录
     * @param userCode
     * @param conditionList
     * @param start
     * @param limit
     * @return
     */
    List<ProtoTranPO> getTranList(String userCode, List<ConditionDTO> conditionList, Integer start, Integer limit, Integer count, List<String> deptCodeList, String whereSql);

    /**
     * 查询转移记录总数
     * @param userCode
     * @param conditionList
     * @return
     */
    Integer countTranList(String userCode, List<ConditionDTO> conditionList, Integer count, List<String> deptCodeList, String whereSql);
}
