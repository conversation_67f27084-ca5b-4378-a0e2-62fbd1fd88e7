package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleeveMatPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoSleeveMatMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoSleeveMatService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 工程机保密套拆除物料明细
 *
 * <AUTHOR>
 * @date 2022/2/7 10:57
 */
@Service
public class ProtoSleeveMatServiceImpl extends ServiceImpl<ProtoSleeveMatMapper, ProtoSleeveMatPO> implements ProtoSleeveMatService {

    /**
     * 根据保密套拆除ID和校验状态查询物料信息
     *
     * @param sleeveId
     * @param checkStatus
     * @return
     */
    @Override
    public List<ProtoSleeveMatPO> qryCheckStatus(String sleeveId, String checkStatus) {
        LambdaQueryWrapper<ProtoSleeveMatPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProtoSleeveMatPO::getSleeveId, sleeveId);
        queryWrapper.eq(ProtoSleeveMatPO::getCheckStatus, checkStatus);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 连表查询设备编号SN
     *
     * @param sleeveId
     * @return
     */
    @Override
    public List<ProtoSleeveMatPO> qryMatDeviceCodes(String sleeveId){
        return baseMapper.selectDeviceCodes(sleeveId);
    }

    /**
     * 根据保密套拆除ID查询物料信息
     *
     * @param sleeveId
     * @return
     */
    @Override
    public List<ProtoSleeveMatPO> qrySleeveMatList(String sleeveId){
        LambdaQueryWrapper<ProtoSleeveMatPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProtoSleeveMatPO::getSleeveId, sleeveId);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 根据保密套拆除ID查询物料信息并根据领用人账号去重
     *
     * @param sleeveId
     * @return
     */
    @Override
    public List<ProtoSleeveMatPO> qrySleeveMatListGroupByApplyUserCode(String sleeveId){
        LambdaQueryWrapper<ProtoSleeveMatPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProtoSleeveMatPO::getSleeveId, sleeveId);
        queryWrapper.groupBy(ProtoSleeveMatPO::getApplyUserCode);
        return baseMapper.selectList(queryWrapper);
    }

}
