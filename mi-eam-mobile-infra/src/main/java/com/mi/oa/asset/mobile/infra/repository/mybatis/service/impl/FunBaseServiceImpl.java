package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.dto.FunRepeatValDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.FunBasePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.FunBaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.FunBaseService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/7
 */
@Service
public class FunBaseServiceImpl extends ServiceImpl<FunBaseMapper, FunBasePO> implements FunBaseService {


    @Override
    public List<FunRepeatValDTO> getRepeatCol(String funId) {
        return baseMapper.getRepeatCol(funId);
    }

    @Override
    public int getRepeatValue(String tableName, String colCode, String colName, String keyId, String fkColName, String fkValue) {
        return baseMapper.getRepeatValue(tableName, colCode, colName, keyId, fkColName, fkValue);
    }

    @Override
    public void updateCommonValue(String tableName, Map<String, String> valueMap, String sKeyValue, String pkCol) {
        baseMapper.updateCommonValue(tableName, valueMap, sKeyValue, pkCol);
    }

    @Override
    public void insertCommonValue(String tableName, Map<String, String> valueMap) {
        baseMapper.insertCommonValue(tableName, valueMap);
    }

    @Override
    public Integer getHaveAuditCount(String tableName, String auditCol, String auditValue, String pkCol, String keyId) {
        return baseMapper.getHaveAuditCount(tableName, auditCol, auditValue, pkCol, keyId);
    }

    @Override
    public Integer getCount(String tableName, String keyCol, String keyValue) {
        return baseMapper.getCount(tableName, keyCol, keyValue);
    }
}
