package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysUserRolePO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * table_name : sys_user_role
 * <AUTHOR>
 * @date 2022/01/25/05:19
 */
public interface SysUserRoleMapper extends BaseMapper<SysUserRolePO> {
    /**
     *
     * @mbg.generated
     */
    SysUserRolePO selectByPrimaryKey(String userRoleId);

    @Select("select distinct role_id from sys_user_role where user_id = #{userId}")
    List<String> getRoleIdByUserId(@Param("userId") String userId);

    /**
     * 根据角色编码查询授权用户
     *
     * @param roleNo
     * @return
     */
    List<String> getUserIdsByRoleNo(@Param("roleNo") String roleNo);
}