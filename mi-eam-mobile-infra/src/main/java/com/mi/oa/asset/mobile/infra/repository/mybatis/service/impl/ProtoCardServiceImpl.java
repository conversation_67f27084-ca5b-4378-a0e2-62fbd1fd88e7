package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.dto.ConditionDTO;
import com.mi.oa.asset.mobile.application.dto.message.EmailDTO;
import com.mi.oa.asset.mobile.application.dto.message.LarkDTO;
import com.mi.oa.asset.mobile.application.service.MessageService;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.config.message.ProtoBackWhitelistConfig;
import com.mi.oa.asset.mobile.infra.dto.idm.ReportLineDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoCardMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
@Service
@Slf4j
public class ProtoCardServiceImpl extends ServiceImpl<ProtoCardMapper, ProtoCardPO> implements ProtoCardService {

    @Autowired
    private ProtoSapExeLogService protoSapExeLogService;

    @Autowired
    private EmployeeService employeeService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProtoProjectStageService projectStageService;

    @Autowired
    private ProtoApplyService protoApplyService;

    @Autowired
    private MessageService messageService;

    @Autowired
    MailTemplateService mailTemplateService;

    @Autowired
    private MailTemplateParamService mailTemplateParamService;

    @Autowired
    private ProtoBackWhitelistConfig protoBackWhitelistConfig;

    private static final String PROTO_BACK_NOTIFY_EMAIL = "proto_back_notify_email";
    private static final String PROTO_BACK_NOTIFY_FLY = "proto_back_notify_fly";
    private static final String PROTO_BACK_URGE_NOTIFY_EMAIL = "proto_back_urge_notify_email";
    private static final String PROTO_BACK_URGE_NOTIFY_FLY = "proto_back_urge_notify_fly";

    @Override
    public void updateState(List<String> deviceCodeList, CardStateEnum useState, String mrpType) {
        if (CollectionUtils.isEmpty(deviceCodeList)){
            return;
        }
        LambdaUpdateWrapper<ProtoCardPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProtoCardPO::getUseState, useState.getKey());
        wrapper.set(ProtoCardPO::getModifyDate, new Date());
        // 工程机归还 设置为最近入库时间
        if (CardStateEnum.IN_LIBRARY.equals(useState)){
            wrapper.set(ProtoCardPO::getEntryDate, new Date());
        }
        // 已内购清空使用人和部门
        if (CardStateEnum.HAVE_PURCHASED.equals(useState)){
            wrapper.set(ProtoCardPO::getUserCode, "")
                    .set(ProtoCardPO::getUserName, "")
                    .set(ProtoCardPO::getEmpCode, "")
                    .set(ProtoCardPO::getDeptCode, "")
                    .set(ProtoCardPO::getLongDeptName, "");
        }
        wrapper.in(ProtoCardPO::getDeviceCode, deviceCodeList);
        wrapper.ne(MrpTypeEnum.PROTOTYPE.getType().equals(mrpType), ProtoCardPO::getUseState, CardStateEnum.UNABLE_TO_RETURN.getKey());
        this.update(wrapper);
    }

    @Override
    public void updateState(List<String> deviceCodeList, CardStateEnum oldUseState, CardStateEnum useState) {
        if (CollectionUtils.isEmpty(deviceCodeList)){
            return;
        }
        LambdaUpdateWrapper<ProtoCardPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProtoCardPO::getUseState, useState.getKey());
        wrapper.set(ProtoCardPO::getModifyDate, new Date());
        wrapper.eq(ProtoCardPO::getUseState, oldUseState.getKey());
        wrapper.in(ProtoCardPO::getDeviceCode, deviceCodeList);
        this.update(wrapper);
    }

    @Override
    public void updateByDeviceCode(List<ProtoCardPO> updateCardList) {
        List<ProtoCardPO> protoCardPOList = list(Wrappers.<ProtoCardPO>lambdaQuery()
                .in(ProtoCardPO::getDeviceCode, updateCardList.stream().map(ProtoCardPO::getDeviceCode).collect(Collectors.toList())));
        if (ObjectUtils.isEmpty(protoCardPOList)) {
            return;
        }
        Map<String, ProtoCardPO> idMap = protoCardPOList.stream().collect(Collectors.toMap(ProtoCardPO::getDeviceCode, a -> a, (k1, k2) -> k1));
        List<ProtoCardPO> finalList = updateCardList.stream().filter(t -> idMap.containsKey(t.getDeviceCode())).collect(Collectors.toList());
        finalList.forEach(t -> {
            t.setDeviceId(idMap.get(t.getDeviceCode()).getDeviceId());
            t.setModifyDate(new Date());
        });
        this.updateBatchById(finalList);
    }

    @Override
    public Integer countByEmpCode(String usedCode) {
        return baseMapper.countByEmpCode(usedCode);
    }

    @Override
    public int qryCardCount(ProtoCardPO cardPO) {
        QueryWrapper<ProtoCardPO> cardPOQueryWrapper = new QueryWrapper<>(cardPO);
        return this.count(cardPOQueryWrapper);
    }

    @Override
    public List<ProtoCardPO> findOccupyCardLimit(String projectCode, String stageName, String skuCode, String useState, String assetNewOld, String isComplete, String occupyApplyCode, String houseCode, int limit) {
        QueryWrapper<ProtoCardPO> cardPOQueryWrapper = new QueryWrapper<>();
        cardPOQueryWrapper.lambda()
                .eq(ProtoCardPO::getProjectCode, projectCode)
                .eq(ProtoCardPO::getStageName, stageName)
                .eq(ProtoCardPO::getSkuCode, skuCode)
                .eq(ProtoCardPO::getUseState, useState)
                .eq(ProtoCardPO::getAssetNewold, assetNewOld)
                .eq(ProtoCardPO::getIsComplete, isComplete)
                .eq(ProtoCardPO::getOccupyApplyCode, occupyApplyCode)
                .eq(ProtoCardPO::getHouseCode, houseCode);
        cardPOQueryWrapper.last("limit " + limit);
        return this.list(cardPOQueryWrapper);
    }

    @Override
    public List<Map<String, Object>> findSecondNumList(List<ConditionDTO> conditionList, List<String> mrpTypeList) {
        QueryWrapper<ProtoCardPO> queryWrapper = new QueryWrapper();
        // 增加商品id
        queryWrapper.select("mrp_type", "project_code", "stage_name", "sku_code", "sku_name", "is_complete, house_code, goods_id, count(1) as num");
        commonService.appendQueryWrapper(queryWrapper, conditionList);
        //状态为在库
        queryWrapper.lambda().eq(ProtoCardPO::getUseState, CardStateEnum.IN_LIBRARY.getKey());
        //新旧属性为二手
        queryWrapper.lambda().eq(ProtoCardPO::getAssetNewold, CommonConstant.ProtoCardAssetNewOld.SECONDHAND.getType());
        // 非占用
        queryWrapper.lambda().eq(ProtoCardPO::getOccupyApplyCode, CommonConstant.ProtoCardOccupyApplyCode.NOTOCCUPY.getType());
        //is_complete = '1' -- 是否良品为良品
        queryWrapper.lambda().eq(ProtoCardPO::getIsComplete, CommonConstant.ProtoCardIsComplete.GOOD.getType());
        if (CollectionUtils.isNotEmpty(mrpTypeList)){
            queryWrapper.lambda().in(ProtoCardPO::getMrpType, mrpTypeList);
        }
        queryWrapper.lambda().groupBy(ProtoCardPO::getMrpType, ProtoCardPO::getProjectCode, ProtoCardPO::getStageName, ProtoCardPO::getSkuCode, ProtoCardPO::getHouseCode);
        queryWrapper.lambda().having("count(1) > 0");
        return this.listMaps(queryWrapper);
    }

    @Override
    public List<ProtoCardPO> findNotSelfCard(List<String> deviceCodes, String userCode, CommonConstant.ApplyUserTypeEnum applyUserTypeEnum) {
        QueryWrapper<ProtoCardPO> cardPOQueryWrapper = new QueryWrapper<>();
        cardPOQueryWrapper.lambda()
                .in(ProtoCardPO::getDeviceCode, deviceCodes)
                .and(queryWrapper -> queryWrapper.ne(ProtoCardPO::getUserCode, userCode)
                        .or().ne(ProtoCardPO::getUseState, CardStateEnum.TO_BE_RETURNED.getKey())
                        .or(null != applyUserTypeEnum).eq(null != applyUserTypeEnum, ProtoCardPO::getApplyUserType, null != applyUserTypeEnum ? applyUserTypeEnum.getValue() : ""));
        return this.list(cardPOQueryWrapper);
    }

    @Override
    public List<ProtoCardPO> getByDeviceCode(List<String> deviceCodes) {
        QueryWrapper<ProtoCardPO> cardPOQueryWrapper = new QueryWrapper<>();
        cardPOQueryWrapper.lambda()
                .in(ProtoCardPO::getDeviceCode, deviceCodes);
        return this.list(cardPOQueryWrapper);
    }

    @Override
    public List<ProtoCardPO> getByDeviceCodeAndExcludeUseState(List<String> deviceCodes, String excludeUseState) {
        return this.list(Wrappers.lambdaQuery(ProtoCardPO.class).in(ProtoCardPO::getDeviceCode, deviceCodes)
                .ne(StringUtils.isNotEmpty(excludeUseState), ProtoCardPO::getUseState, excludeUseState));
    }

    @Override
    public void updateInfoBySn(ProtoCardPO cardPO) {
        ProtoCardPO dbCard = getBySn(cardPO.getDeviceCode());
        if (null != dbCard) {
            cardPO.setDeviceId(dbCard.getDeviceId());
            baseMapper.updateById(cardPO);
        }
    }

    @Override
    public ProtoCardPO getBySn(String sn) {
        LambdaQueryWrapper<ProtoCardPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProtoCardPO::getDeviceCode, sn);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public void updateOutCode(String oldOutCode, String newOutCode) {
        if (!StringUtils.equals(oldOutCode, newOutCode)) {
            protoSapExeLogService.insertLog(new Object(), "updateOutCode", oldOutCode, oldOutCode, "更新发货单单号", newOutCode);
            baseMapper.updateOutCode(oldOutCode, newOutCode);
        }
    }

    @Override
    public void updateOutSapItemRowNoBySn(String sn, String outSapItemRowNo) {
        LambdaUpdateWrapper<ProtoCardPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoCardPO::getDeviceCode, sn);
        updateWrapper.set(ProtoCardPO::getOutSapItemRowNo, outSapItemRowNo);
        this.update(updateWrapper);
    }

    @Override
    public void changePSPhoneNum(String empCode) {
        Integer count = countByEmpCode(empCode);
        // 名下没有工程机时调用PS接口
        if (count == 0) {
            try {
                log.info("change_ps_phone_count_empCode:{}", empCode);
                boolean result = employeeService.leaveCheckPhoneNum(empCode);
                if (result) {
                    RedisUtils.sRemove(RedisCachePrefixKeyEnum.PS_LEAVE_CHECK.getPrefixKey(), empCode);
                }
            } catch (Exception e) {
                log.error("change_ps_phone_count_error_empCode:{}, e:{}", empCode, e);
            }
        }
    }

    @Override
    public void saveCardInfo(String orderCode, List<ProtoAsnOrderDetPO> itemList, String inCode, String userCode, String houseName, String houseCode, String houseId) {
        List<ProtoCardPO> list = new ArrayList<>(itemList.size());
        for (ProtoAsnOrderDetPO detPO : itemList) {
            ProtoCardPO cardPO = new ProtoCardPO();
            BeanUtils.copyProperties(detPO, cardPO);
            cardPO.setInCode(inCode);
            cardPO.setOrderCode(orderCode);
            cardPO.setUseState(CardStateEnum.IN_LIBRARY.getKey());
            cardPO.setIsOpen(IsOpenEnum.NOT_DISMANTLE.getType());
            cardPO.setAssetNewold(CommonConstant.ProtoCardAssetNewOld.ONEHAND.getType());
            cardPO.setHouseName(houseName);
            cardPO.setHouseCode(houseCode);
            cardPO.setHouseId(houseId);
            cardPO.setAddUserid(userCode);
            cardPO.setAddDate(DateUtils.getFormatDate(DateUtils.COMMON_PATTERN));
            cardPO.setDeviceId(commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_CARD));
            list.add(cardPO);
        }
        this.saveBatch(list);
    }

    /**
     * 根据devicecode和状态查询台账
     *
     * @param deviceCodes
     * @param userCode
     * @param useState
     * @param isOpen
     * @param projectCode
     * @return
     */
    @Override
    public List<ProtoCardPO> findCardList(List<String> deviceCodes, String userCode, String useState, String isOpen, String projectCode) {
        QueryWrapper<ProtoCardPO> cardPOQueryWrapper = new QueryWrapper<>();
        cardPOQueryWrapper.lambda()
                .in(ProtoCardPO::getDeviceCode, deviceCodes)
                .and(queryWrapper -> queryWrapper.ne(ProtoCardPO::getUserCode, userCode)
                        .or().ne(ProtoCardPO::getUseState, useState)
                        .or().ne(ProtoCardPO::getIsOpen, isOpen)
                        .or().ne(ProtoCardPO::getProjectCode, projectCode));
        return this.list(cardPOQueryWrapper);
    }

    /**
     * 修改台账状态
     *
     * @param isOpen
     * @param deviceCodes
     * @return
     */
    @Override
    public boolean updProtoCardOpenStatus(String isOpen, List<String> deviceCodes) {
        LambdaUpdateWrapper<ProtoCardPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProtoCardPO::getDeviceCode, deviceCodes)
                .set(ProtoCardPO::getIsOpen, isOpen)
                .set(ProtoCardPO::getModifyDate, new Date());
        return this.update(updateWrapper);
    }

    /**
     * 修改台账状态
     *
     * @param isOpen
     * @param userName
     * @param userCode
     * @param empCode
     * @param deviceCodes
     * @return
     */
    @Override
    public boolean updProtoCardOpenStatus(String isOpen, String userName, String userCode, String empCode, List<String> deviceCodes) {
        Date nowDate = new Date();
        LambdaUpdateWrapper<ProtoCardPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProtoCardPO::getDeviceCode, deviceCodes)
                .set(ProtoCardPO::getIsOpen, isOpen)
                .set(ProtoCardPO::getOpenUserName, userName)
                .set(ProtoCardPO::getOpenUserCode, userCode)
                .set(ProtoCardPO::getOpenEmpCode, empCode)
                .set(ProtoCardPO::getOpenDate, nowDate)
                .set(ProtoCardPO::getLastDate, nowDate)
                .set(ProtoCardPO::getModifyDate, nowDate);
        return this.update(updateWrapper);
    }

    /**
     * 更新台账截止归还时间
     *
     * @param endDate
     * @param deviceCodes
     * @return
     */
    @Override
    public boolean updProtoCardDelayStatus(Date endDate, List<String> deviceCodes) {
        LambdaUpdateWrapper<ProtoCardPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProtoCardPO::getDeviceCode, deviceCodes)
                .set(ProtoCardPO::getEndDate, endDate)
                .set(ProtoCardPO::getModifyDate, new Date());
        return this.update(updateWrapper);
    }

    @Override
    public void releaseCard(String skuCode, String houseCode, Integer releaseCount) {
        List<String> list = listBySkuHouseRelease(skuCode, houseCode, releaseCount).stream().map(ProtoCardPO::getDeviceCode).collect(Collectors.toList());
        this.updateOccupyApplyCodeBySnList(list);
    }

    @Override
    public ProtoCardPO findOneByParam(ProtoCardPO po) {
        QueryWrapper<ProtoCardPO> queryWrapper = new QueryWrapper(po);
        return this.getOne(queryWrapper);
    }

    /**
     * 更新被占用的库存
     * @param snList
     */
    private void updateOccupyApplyCodeBySnList(List<String> snList){
        if (CollectionUtils.isEmpty(snList)){
            return;
        }
        LambdaUpdateWrapper<ProtoCardPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProtoCardPO::getOccupyApplyCode, "0") // 未被占用
                .in(ProtoCardPO::getDeviceCode, snList);
        this.update(wrapper);
    }

    /**
     * 查询在库 已被占用的库存
     * @param skuCode
     * @param houseCode
     * @param releaseCount
     * @return
     */
    private List<ProtoCardPO> listBySkuHouseRelease(String skuCode, String houseCode, Integer releaseCount){
        LambdaQueryWrapper<ProtoCardPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ProtoCardPO::getDeviceCode)
                .eq(ProtoCardPO::getSkuCode, skuCode)
                .eq(ProtoCardPO::getHouseCode, houseCode)
                .eq(ProtoCardPO::getOccupyApplyCode, "1") //已占用的库存
                .eq(ProtoCardPO::getUseState, CardStateEnum.IN_LIBRARY.getKey())
                .eq(ProtoCardPO::getAssetNewold, CommonConstant.ProtoCardAssetNewOld.SECONDHAND.getType())
                .last("limit " +releaseCount);
        return this.list(wrapper);
    }

    /**
     * 工程机SN追溯查询
     *
     * @param conditionList
     * @return
     */
    @Override
    public List<ProtoCardPO> getListForSnTrace(List<ConditionDTO> conditionList){
        QueryWrapper queryWrapper = new QueryWrapper();
        commonService.appendQueryWrapper(queryWrapper, conditionList);
        return this.list(queryWrapper);
    }

    /**
     * 工程机SN追溯按条件查询数据条数
     *
     * @param conditionList
     * @return
     */
    public int countForSnTrace(List<ConditionDTO> conditionList){
        QueryWrapper queryWrapper = new QueryWrapper();
        commonService.appendQueryWrapper(queryWrapper, conditionList);
        return this.count(queryWrapper);
    }

    /**
     * 修复有乱码的描述
     */
    @Override
    public void repairAllSkuName() {
        //查询有乱码的描述
        List<ProtoCardPO> list = findProtoCardAbnormalList();
        for (ProtoCardPO protoCardPO : list) {
            String deviceId = protoCardPO.getDeviceId();
            String skuCode = protoCardPO.getSkuCode();
            ProtoCardPO cardPO = findSkuNameBySkuCode(skuCode);
            if (null != cardPO && StringUtils.isNotBlank(cardPO.getSkuName())) {
                updProtoCardSkuNameById(deviceId, cardPO.getSkuName());
            }
        }
    }

    @Override
    public void updateSycFlag(String sycFlag) {
        LambdaUpdateWrapper<ProtoCardPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProtoCardPO::getSycFlag, sycFlag);
        this.update(wrapper);
    }

    @Override
    public List<String> getNotRefreshUserCode() {
        return baseMapper.getNotRefreshUserCode();
    }

    @Override
    public void updateLongDeptNameByUserCode(String deptId, String longDeptName, String empCode, String userCode) {
        LambdaUpdateWrapper<ProtoCardPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(StringUtils.isNotBlank(deptId), ProtoCardPO::getDeptCode, deptId)
                .set(StringUtils.isNotBlank(longDeptName), ProtoCardPO::getLongDeptName, longDeptName)
                .set(StringUtils.isNotBlank(empCode), ProtoCardPO::getEmpCode, empCode)
                .eq(ProtoCardPO::getUserCode, userCode);
        this.update(updateWrapper);
    }

    @Override
    public List<ProtoCardPO> listBySycFlag(String sycFlag, Integer limit) {
        LambdaQueryWrapper<ProtoCardPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ProtoCardPO::getDeviceId, ProtoCardPO::getDeviceCode, ProtoCardPO::getOriProjectCode)
                .eq(ProtoCardPO::getSycFlag, sycFlag)
                .last("limit "+limit);
        return this.list(wrapper);
    }

    @Override
    public List<ProtoCardPO> listNotEntryDate(Integer limit) {
        LambdaQueryWrapper<ProtoCardPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(ProtoCardPO::getDeviceId, ProtoCardPO::getInCode)
                .isNotNull(ProtoCardPO::getInCode).isNull(ProtoCardPO::getEntryDate);
        wrapper.last("limit "+ limit);
        return this.list(wrapper);
    }

    @Override
    public List<ProtoCardDeptTotalPO> listGroupByDeptCode() {
        return baseMapper.listGroupByDeptCode();
    }

    @Override
    public List<ProtoCardDeptproTotalPO> listGroupByDeptproCode() {
        return baseMapper.listGroupByDeptproCode();
    }

    @Override
    public void updateListDateByProjectCode(String projectCode, Date listDate) {
        if (StringUtils.isBlank(projectCode) || null == listDate){
            return;
        }
        LambdaUpdateWrapper<ProtoCardPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ProtoCardPO::getProjectCode, projectCode);
        wrapper.set(ProtoCardPO::getListDate, listDate);
        this.update(wrapper);
    }

    @Override
    public List<ProtoCardStorePO> listGroupByStore() {
        return baseMapper.listGroupByStore();
    }

    @Override
    public String getLarkTableContent(List<ProtoCardPO> cardList, boolean isCn) {
        StringBuffer buffer = new StringBuffer();
        if (isCn) {
            buffer.append("项目名 ")
                    .append("阶段 ")
                    .append("料号 ")
                    .append("配置 ")
                    .append("镭雕号 ")
                    .append("IMEI号 ")
                    .append("SN号").append("\\r\\n");
        } else {
            buffer.append("Project ")
                    .append("Stage ")
                    .append("Material ")
                    .append("Configuration ")
                    .append("Radium Carving ")
                    .append("IMEI ")
                    .append("SN").append("\\r\\n");
        }
        for (ProtoCardPO cardPO : cardList){
            buffer.append(this.getStr(cardPO.getProjectCode())).append(" ")
                    .append(this.getStr(cardPO.getStageName())).append(" ")
                    .append(this.getStr(cardPO.getSkuCode())).append(" ")
                    .append(this.getStr(cardPO.getDeviceType())).append(" ")
                    .append(this.getStr(cardPO.getLaserCode())).append(" ")
                    .append(this.getStr(cardPO.getImei())).append(" ")
                    .append(this.getStr(cardPO.getDeviceCode())).append("\\r\\n");

        }
        return buffer.toString();
    }

    @Override
    public String getLarkTableContent(List<ProtoCardPO> cardList, String mrpType, boolean isCn) {
        StringBuffer buffer = new StringBuffer();
        if (isCn) {
            if (MrpTypeEnum.MOBILE.getType().equals(mrpType) || MrpTypeEnum.TV.getType().equals(mrpType)) {
                buffer.append("项目名 ")
                        .append("阶段 ")
                        .append("料号 ")
                        .append("配置 ")
                        .append("镭雕号 ")
                        .append("IMEI号 ")
                        .append("SN号").append("\\r\\n");
            } else if (MrpTypeEnum.AFTER_SALE.getType().equals(mrpType)) {
                buffer.append("市场名称 ")
                        .append("SKU ")
                        .append("配置 ")
                        .append("SN号").append("\\r\\n");
            }

        } else {
            if (MrpTypeEnum.MOBILE.getType().equals(mrpType) || MrpTypeEnum.TV.getType().equals(mrpType)) {
                buffer.append("Project ")
                        .append("Stage ")
                        .append("Material ")
                        .append("Configuration ")
                        .append("Radium Carving ")
                        .append("IMEI ")
                        .append("SN").append("\\r\\n");
            } else if (MrpTypeEnum.AFTER_SALE.getType().equals(mrpType)) {
                buffer.append("Product name ")
                        .append("SKU ")
                        .append("Configuration ")
                        .append("SN").append("\\r\\n");
            }
        }
        for (ProtoCardPO cardPO : cardList){
            if (MrpTypeEnum.MOBILE.getType().equals(mrpType) || MrpTypeEnum.TV.getType().equals(mrpType)) {
                buffer.append(this.getStr(cardPO.getProjectCode())).append(" ")
                        .append(this.getStr(cardPO.getStageName())).append(" ")
                        .append(this.getStr(cardPO.getSkuCode())).append(" ")
                        .append(this.getStr(cardPO.getDeviceType())).append(" ")
                        .append(this.getStr(cardPO.getLaserCode())).append(" ")
                        .append(this.getStr(cardPO.getImei())).append(" ")
                        .append(this.getStr(cardPO.getDeviceCode())).append("\\r\\n");
            } else if (MrpTypeEnum.AFTER_SALE.getType().equals(mrpType)) {
                buffer.append(this.getStr(cardPO.getSkuName())).append(" ")
                        .append(this.getStr(cardPO.getSkuCode())).append(" ")
                        .append(this.getStr(cardPO.getDeviceType())).append(" ")
                        .append(this.getStr(cardPO.getDeviceCode())).append("\\r\\n");
            }
        }
        return buffer.toString();
    }

    @Override
    public String getLarkTableContent(ProtoCardPO cardPO, boolean isCn) {
        return getLarkTableContent(Lists.list(cardPO), isCn);
    }

    @Override
    public String getLarkTableContentV1(List<ProtoCardPO> cardList, String productDesc, boolean isCn) {
        StringBuffer buffer = new StringBuffer();
        if (isCn) {
            buffer.append("市场名称    ")
                    .append("项目名    ")
                    .append("阶段    ")
                    .append("料号    ")
                    .append("配置    ")
                    .append("镭雕号    ")
                    .append("IMEI号    ")
                    .append("SN号").append("\\r\\n");
        } else {
            buffer.append("product name    ")
                    .append("Project    ")
                    .append("Stage    ")
                    .append("Material    ")
                    .append("Configuration    ")
                    .append("Radium Carving    ")
                    .append("IMEI    ")
                    .append("SN").append("\\r\\n");
        }
        for (ProtoCardPO cardPO : cardList){
            String listDesc = productDesc;
            if (StringUtils.isBlank(listDesc)) {
                listDesc = projectStageService.getListDescBySkuCodeAndMrpType(cardPO.getSkuCode(), cardPO.getMrpType());
            }
            buffer.append(listDesc).append("    ")
                    .append(this.getStr(cardPO.getProjectCode())).append("    ")
                    .append(this.getStr(cardPO.getStageName())).append("    ")
                    .append(this.getStr(cardPO.getSkuCode())).append("    ")
                    .append(this.getStr(cardPO.getDeviceType())).append("    ")
                    .append(this.getStr(cardPO.getLaserCode())).append("    ")
                    .append(this.getStr(cardPO.getImei())).append("    ")
                    .append(this.getStr(cardPO.getDeviceCode())).append("\\r\\n");

        }
        return buffer.toString();
    }

    @Override
    public String getLarkTableContentV2(List<ProtoCardPO> cardList, String productDesc, boolean isCn, String mrpType, boolean hasSn) {
        StringBuffer buffer = new StringBuffer();
        if (isCn) {
            if (MrpTypeEnum.MOBILE.getType().equals(mrpType) || MrpTypeEnum.TV.getType().equals(mrpType)) {
                buffer.append("市场名称    ")
                        .append("项目名    ")
                        .append("阶段    ")
                        .append("料号    ")
                        .append("配置    ")
                        .append("镭雕号    ")
                        .append("IMEI号    ")
                        .append("SN号").append("\\r\\n");
            } else if (MrpTypeEnum.AFTER_SALE.getType().equals(mrpType)) {
                buffer.append("市场名称    ")
                        .append("SKU    ")
                        .append("配置    ");
                if (hasSn) {
                    buffer.append("SN号");
                }
                buffer.append("\\r\\n");
            }
        } else {
            if (MrpTypeEnum.MOBILE.getType().equals(mrpType) || MrpTypeEnum.TV.getType().equals(mrpType)) {
                buffer.append("product name    ")
                        .append("Project    ")
                        .append("Stage    ")
                        .append("Material    ")
                        .append("Configuration    ")
                        .append("Radium Carving    ")
                        .append("IMEI    ")
                        .append("SN").append("\\r\\n");
            } else if (MrpTypeEnum.AFTER_SALE.getType().equals(mrpType)) {
                buffer.append("product name    ")
                        .append("SKU    ")
                        .append("Configuration    ");
                if (hasSn) {
                    buffer.append("SN");
                }
                buffer.append("\\r\\n");
            }
        }
        for (ProtoCardPO cardPO : cardList){
            String listDesc = productDesc;
            if (StringUtils.isBlank(listDesc)) {
                listDesc = projectStageService.getListDescBySkuCodeAndMrpType(cardPO.getSkuCode(), cardPO.getMrpType());
            }
            if (MrpTypeEnum.MOBILE.getType().equals(mrpType) || MrpTypeEnum.TV.getType().equals(mrpType) ) {
                buffer.append(listDesc).append("    ")
                        .append(this.getStr(cardPO.getProjectCode())).append("    ")
                        .append(this.getStr(cardPO.getStageName())).append("    ")
                        .append(this.getStr(cardPO.getSkuCode())).append("    ")
                        .append(this.getStr(cardPO.getDeviceType())).append("    ")
                        .append(this.getStr(cardPO.getLaserCode())).append("    ")
                        .append(this.getStr(cardPO.getImei())).append("    ")
                        .append(this.getStr(cardPO.getDeviceCode())).append("\\r\\n");
            } else if (MrpTypeEnum.AFTER_SALE.getType().equals(mrpType)) {
                buffer.append(cardPO.getSkuName()).append("    ")
                        .append(this.getStr(cardPO.getSkuCode())).append("    ")
                        .append(this.getStr(cardPO.getDeviceType())).append("    ");
                if (hasSn) {
                    buffer.append(this.getStr(cardPO.getDeviceCode()));
                }
                buffer.append("\\r\\n");
            }
        }
        return buffer.toString();
    }

    @Override
    public String getEmailTableContent(List<ProtoCardPO> cardList, boolean isCn) {
        StringBuffer buffer = new StringBuffer();
        if (isCn) {
            //项目名   阶段  料号 配置  镭雕号  IMEI号   SN号
            buffer.append("<table border=\"1px\" bordercolor=\"#000000\" cellspacing=\"0px\" style=\"border-collapse:collapse;text-align: center;width: 820px;\">")
                    .append("<tr>")
                    .append("<td width=\"40px\">项目名</td>")
                    .append("<td width=\"40px\">阶段</td>")
                    .append("<td width=\"80px\">料号</td>")
                    .append("<td width=\"50px\">配置</td>")
                    .append("<td width=\"130px\">镭雕号</td>")
                    .append("<td width=\"50px\">IMEI号</td>")
                    .append("<td width=\"100px\">SN号</td>")
                    .append("</tr>");
        } else {
            buffer.append("<table border=\"1px\" bordercolor=\"#000000\" cellspacing=\"0px\" style=\"border-collapse:collapse;text-align: center;width: 820px;\">")
                    .append("<tr>")
                    .append("<td width=\"40px\">Project</td>")
                    .append("<td width=\"40px\">Stage</td>")
                    .append("<td width=\"80px\">Material</td>")
                    .append("<td width=\"50px\">Configuration</td>")
                    .append("<td width=\"130px\"> Radium Carving</td>")
                    .append("<td width=\"50px\">IMEI</td>")
                    .append("<td width=\"100px\">SN</td>")
                    .append("</tr>");
        }
        for (ProtoCardPO cardPO : cardList){
            buffer.append("<tr>")
                    .append("<td>"+this.getStr(cardPO.getProjectCode())+"</td>")
                    .append("<td>"+this.getStr(cardPO.getStageName())+"</td>")
                    .append("<td>"+this.getStr(cardPO.getSkuCode())+"</td>")
                    .append("<td>"+this.getStr(cardPO.getDeviceType())+"</td>")
                    .append("<td>"+this.getStr(cardPO.getLaserCode())+"</td>")
                    .append("<td>"+this.getStr(cardPO.getImei())+"</td>")
                    .append("<td>"+this.getStr(cardPO.getDeviceCode())+"</td>")
                    .append("</tr>");
        }
        buffer.append("</table>");
        return buffer.toString();
    }

    @Override
    public Integer countByUserCode(String userCode) {
        return baseMapper.countByUserCode(userCode);
    }

    @Override
    public List<ProtoCardPO> listByDevices(List<String> deviceCodeList) {
        if (CollectionUtils.isEmpty(deviceCodeList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoCardPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoCardPO::getDeviceCode, deviceCodeList);
        return this.list(wrapper);
    }

    @Override
    public ProtoCardPO getOneBySkuCode(String skuCode, String projectCode) {
        LambdaQueryWrapper<ProtoCardPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoCardPO::getSkuCode, skuCode);
        wrapper.eq(ProtoCardPO::getProjectCode, projectCode)
                .last("limit 1");
        return this.getOne(wrapper);
    }

    @Override
    public void protoBackNotifyTask() {
        log.info("工程机归还提醒通知开始.......");
        // 查询所有待归还且有应归还时间的台账（目前手机管控）
        LambdaQueryWrapper<ProtoCardPO> cardPOQueryWrapper = new LambdaQueryWrapper<>();
        cardPOQueryWrapper.likeRight(ProtoCardPO::getDeptCode, "HW");
        cardPOQueryWrapper.eq(ProtoCardPO::getUseState, CardStateEnum.TO_BE_RETURNED.getKey());
        cardPOQueryWrapper.eq(ProtoCardPO::getMrpType, MrpTypeEnum.MOBILE.getType());
        cardPOQueryWrapper.select(ProtoCardPO::getDeviceId, ProtoCardPO::getDeviceCode, ProtoCardPO::getUserCode,
                ProtoCardPO::getEndDate, ProtoCardPO::getUserName, ProtoCardPO::getLastApplyCode,
                ProtoCardPO::getLastApplyDate);
        List<ProtoCardPO> protoCardPOList = this.list(cardPOQueryWrapper);
        if (CollectionUtils.isEmpty(protoCardPOList)) {
            log.info("没有需要归还提醒的工程机，任务完成......." );
            return;
        }

        long time = 1693497600000L;  // 2023-09-01 00:00:00
        protoCardPOList.removeIf(protoCardPO ->
                Objects.isNull(protoCardPO.getEndDate())
                        || (Objects.nonNull(protoCardPO.getEndDate()) && protoCardPO.getEndDate().getTime() < time)
                        || StringUtils.isBlank(protoCardPO.getLastApplyCode())
                        || Objects.isNull(protoCardPO.getLastApplyDate())
                        || (Objects.nonNull(protoCardPO.getLastApplyDate()) && protoCardPO.getLastApplyDate().getTime() < time)
        );
        if (CollectionUtils.isEmpty(protoCardPOList)) {
            log.info("没有需要归还提醒的工程机，任务完成......." );
            return;
        }

        // 预期提醒清单
        Map<String, List<String>> expectedReminderList = new HashMap<>();

        // 超期提醒清单
        Map<String, List<String>> overdueReminderList = new HashMap<>();

        // 记录领用单是否设置了归还时间
        Map<String, Boolean> isContainReturnTimeMap = new HashMap<>();

        // 过滤掉领用单没有归还时间的台账
        for (ProtoCardPO protoCardPO : protoCardPOList) {
            String applyCode = protoCardPO.getLastApplyCode();
            if (isContainReturnTimeMap.containsKey(applyCode)) {
                if (isContainReturnTimeMap.get(applyCode)) {
                    isOverdueReminder(expectedReminderList, overdueReminderList, protoCardPO);
                }
            } else {
                ProtoApplyPO protoApplyPO = protoApplyService.getByApplyCode(applyCode);
                if (null != protoApplyPO && StringUtils.isNotBlank(protoApplyPO.getReturnTime())) {
                    isContainReturnTimeMap.put(applyCode, true);
                    isOverdueReminder(expectedReminderList, overdueReminderList, protoCardPO);
                } else {
                    isContainReturnTimeMap.put(applyCode, false);
                }
            }
        }

        List<String> whitelist = protoBackWhitelistConfig.getProtoBackWhitelist();

        // 发送预期提醒消息
        log.info("预期归还清单："+expectedReminderList);
        sendBackMessage(expectedReminderList, PROTO_BACK_NOTIFY_EMAIL, PROTO_BACK_NOTIFY_FLY, whitelist);

        // 发送超期提醒消息
        log.info("超期归还清单："+overdueReminderList);
        sendBackMessage(overdueReminderList, PROTO_BACK_URGE_NOTIFY_EMAIL, PROTO_BACK_URGE_NOTIFY_FLY, whitelist);

        log.info("工程机归还提醒通知完成.......");
    }

    // 判断预期提醒还是延期提醒
    private void isOverdueReminder(Map<String, List<String>> expectedReminderList, Map<String, List<String>> overdueReminderList, ProtoCardPO protoCardPO) {
        Date nowDate = new Date();
        String deviceCode = protoCardPO.getDeviceCode();
        Date endDate = protoCardPO.getEndDate();
        String userCode = protoCardPO.getUserCode();
        String userName = protoCardPO.getUserName();
        String key = userName + "(" + userCode;
        int day = 0;
        try {
            day = (int) ((endDate.getTime() - nowDate.getTime()) / 24 / 60 / 60 / 1000);
        } catch (Exception e) {
            log.error("归还提醒时间差计算异常，endDate {}，nowDate {}", endDate, nowDate);
        }
        if (day == 5) {
            List<String> deviceCodeList = expectedReminderList.getOrDefault(key, new ArrayList<>());
            deviceCodeList.add(deviceCode);
            expectedReminderList.put(key, deviceCodeList);
        } else if (day < 0) {
            List<String> deviceCodeList = overdueReminderList.getOrDefault(key, new ArrayList<>());
            deviceCodeList.add(deviceCode);
            overdueReminderList.put(key, deviceCodeList);
        }
    }

    private void sendBackMessage(Map<String, List<String>> reminderList, String templateTagEmail, String templateTagFly, List<String> whitelist){
        MailTemplatePO mailTemplatePO = mailTemplateService.getByTemplateTag(templateTagFly);
        String templateNameFly = mailTemplatePO.getTemplateName();
        String templateContFly = mailTemplatePO.getTemplateCont();
        String templateIdFly = mailTemplatePO.getTemplateId();
        mailTemplatePO = mailTemplateService.getByTemplateTag(templateTagEmail);
        String templateNameEmail = mailTemplatePO.getTemplateName();
        String templateContEmail = mailTemplatePO.getTemplateCont();
        String templateIdEmail = mailTemplatePO.getTemplateId();

        if(CollectionUtils.isNotEmpty(reminderList)){
            for (Map.Entry<String, List<String>> entry : reminderList.entrySet()) {
                String key = entry.getKey();
                if (StringUtils.isBlank(key)) continue;
                String[] split = key.split("\\(" );
                if (split.length != 2) continue;
                String userCode = key.split("\\(")[1];
                String userName = key + ")";
                if(StringUtils.isBlank(userCode)) continue;

                // 如果在白名单，跳过
                if(whitelist.contains(userCode)) continue;

                // 过滤高职级的
                List<ReportLineDTO> reportLines = employeeService.findReportLineByUserName(userCode);
                if (CollectionUtils.isEmpty(reportLines)) continue;
                // 主岗
                long mainPostCount = reportLines.stream().filter(reportLine -> PostTypeEnum.MAIN.getType().equals(reportLine.getPostType())).count();
                // 兼岗
                long concurrentPostCount = reportLines.stream().filter(reportLine -> PostTypeEnum.CONCURRENT.getType().equals(reportLine.getPostType())).count();
                if ((0 < mainPostCount && mainPostCount <= 4) || (0 < concurrentPostCount && concurrentPostCount <= 4)) {
                    log.info("{}为部门领导，不发送归还消息", userCode);
                    continue;
                }

                Map<String, String> paramMap = new HashMap<>();
                paramMap.put("userName", userName);
                paramMap.put("deviceCodeList", entry.getValue().toString());

                // 发送邮件
                String emailContent = mailTemplateParamService.getTemplateContent(templateIdEmail, templateContEmail, paramMap);
                messageService.sendEmail(EmailDTO.builder()
                        .emails(Collections.singletonList(userCode + "@xiaomi.com"))
                        .title(templateNameEmail)
                        .content(emailContent)
                        .build());

                // 发送飞书
                String flyContent = mailTemplateParamService.getTemplateContent(templateIdFly, templateContFly, paramMap);
                messageService.sendLark(LarkDTO.builder()
                        .userNames(Collections.singletonList(userCode))
                        .title(templateNameFly)
                        .url("https://eam.asset.mioffice.cn/")
                        .content(flyContent)
                        .buttonName("立即归还")
                        .build());
            }
        }
    }

    private String getStr(String str){
        return null == str ? "" : str;
    }

    @Override
    public void deletedBySn(String oldSn) {
        LambdaQueryWrapper<ProtoCardPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoCardPO::getDeviceCode, oldSn);
        this.remove(wrapper);
    }

    @Override
    public ProtoCardPO queryOneProtoCard(LambdaQueryWrapper<ProtoCardPO> queryWrapper) {
        return baseMapper.selectOne(queryWrapper.last("limit 1"));
    }

    @Override
    public List<ProtoCardPO> queryProtoCardList(LambdaQueryWrapper<ProtoCardPO> queryWrapper) {
        return baseMapper.selectList(queryWrapper);
    }

    private List<ProtoCardPO> findProtoCardAbnormalList() {
        LambdaQueryWrapper<ProtoCardPO> cardPOQueryWrapper = new LambdaQueryWrapper<>();
        cardPOQueryWrapper.like(ProtoCardPO::getSkuName, '�');
        return this.list(cardPOQueryWrapper);
    }

    private ProtoCardPO findSkuNameBySkuCode(String skuCode) {
        QueryWrapper<ProtoCardPO> cardPOQueryWrapper = new QueryWrapper<>();
        cardPOQueryWrapper.lambda()
                .eq(ProtoCardPO::getSkuCode, skuCode)
                .notLike(ProtoCardPO::getSkuName, '�');
        cardPOQueryWrapper.last("limit 1");
        return this.getOne(cardPOQueryWrapper);
    }

    public boolean updProtoCardSkuNameById(String deviceId, String skuName) {
        LambdaUpdateWrapper<ProtoCardPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoCardPO::getDeviceId, deviceId)
                .set(ProtoCardPO::getSkuName, skuName);
        return this.update(updateWrapper);
    }

}
