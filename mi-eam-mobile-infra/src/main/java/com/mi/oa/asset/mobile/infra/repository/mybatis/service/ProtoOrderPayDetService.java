package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOrderPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOrderPayDetPO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/3
 */
public interface ProtoOrderPayDetService extends IService<ProtoOrderPayDetPO> {


    List<ProtoOrderPayDetPO> listByTradeDate(String mrpType, String dateStr);

    List<ProtoOrderPayDetPO> listByOrderCodes(List<String> orderCodeList);

    /**
     * 通过交易流水号查询
     * @param tradeCodeList
     * @return
     */
    List<ProtoOrderPayDetPO> listByTradeOrderCodeList(List<String> tradeCodeList);
}
