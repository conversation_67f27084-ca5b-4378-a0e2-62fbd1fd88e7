package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoOutSapDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOutDetPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * table_name : proto_out_det
 * <AUTHOR>
 * @date 2022/01/20/02:34
 */
public interface ProtoOutDetMapper extends BaseMapper<ProtoOutDetPO> {


    int getProtoOutSapSuccess(@Param("sn") String sn);

    String getApplyCode(@Param("sn") String sn);

    List<ProtoOutSapDTO> getProtoOutDetSAPList(@Param("outId") String outId);

    List<String> selectProtoOutDetDeviceCodes(List<String> deviceCodes);
}