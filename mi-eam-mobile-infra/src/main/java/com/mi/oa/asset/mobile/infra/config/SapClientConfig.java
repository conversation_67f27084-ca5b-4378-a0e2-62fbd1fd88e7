package com.mi.oa.asset.mobile.infra.config;


import com.mi.oa.asset.mobile.infra.dto.sap.SapBaseVO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapCustomVO;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import com.mi.oa.asset.x5.common.X5Response;
import com.mi.oa.asset.x5.consumer.X5FeignClientsConfiguration;
import feign.RequestInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;

import java.lang.reflect.Type;
import java.util.Base64;

/**
 * @Desc SAP 接口请求 feign 客户端配置
 * <AUTHOR>
 * @Date 2021/8/30 20:52
 * @see
 */

@Slf4j
public class SapClientConfig extends X5FeignClientsConfiguration {

    @Value("${sap.account}")
    private String account;

    @Value("${sap.password}")
    private String password;

    @Override
    protected Object responseHandler(String resDataString, X5Response res, Type type) {
        // 自定义返回值 返回值
        if (type.equals(SapCustomVO.class)){
            SapCustomVO data = new SapCustomVO();
            data.setResponseBody(res.getBody().toString());
            data.setSuccess(X5_RESPONSE_SUCCESS_CODE.equals(res.getHeader().getCode()));
            data.setMessage(res.getHeader().getDesc());
            return data;
        }
        SapBaseVO data = GsonUtil.toBean(GsonUtil.toJsonString(res.getBody()), type);
        if (null == data) {
            data = new SapBaseVO();
        }
        data.setSuccess(X5_RESPONSE_SUCCESS_CODE.equals(res.getHeader().getCode()));
        data.setMessage(res.getHeader().getDesc());
        return data;
    }

    @Bean
    protected RequestInterceptor requestInterceptor() {
        return requestTemplate -> {
            String authString = String.format("%s:%s", account, password);
            authString = Base64.getEncoder().encodeToString(authString.getBytes());
            requestTemplate.header("Authorization", "Basic " + authString);
        };
    }
}


