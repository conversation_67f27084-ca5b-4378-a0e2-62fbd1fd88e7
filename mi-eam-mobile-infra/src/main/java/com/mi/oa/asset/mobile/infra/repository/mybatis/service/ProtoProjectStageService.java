package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.dto.mier.MaterialDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectStagePO;

import java.util.List;

/**
 * 试产阶段
 *
 * <AUTHOR>
 * @date 2022/1/27 11:12
 */
public interface ProtoProjectStageService extends IService<ProtoProjectStagePO> {

    /**
     * 通过项目查询
     * @param projectCodeList
     * @return
     */
    List<ProtoProjectStagePO> listByProjectCode(List<String> projectCodeList);

    /**
     * 通过料号查询
     * @param skuCodeList
     * @return
     */
    List<ProtoProjectStagePO> listBySkuCodeListAndMrpType(List<String> skuCodeList, String mrpType);

    /**
     * 通过料号查询上市描述
     * @param skuCode
     * @return
     */
    String getListDescBySkuCodeAndMrpType(String skuCode, String mrpType);

    /**
     * 同步工程机电视物料数据
     *
     * @param dtoList
     */
    List<MaterialDTO> syncTvData(List<MaterialDTO> dtoList);
}
