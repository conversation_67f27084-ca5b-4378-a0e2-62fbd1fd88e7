package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardSkuTotalPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectListPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoCardSkuTotalMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardSkuTotalService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 台账物料汇总
 *
 * <AUTHOR>
 * @date 2022/3/25 10:08
 */
@Service
@Slf4j
public class ProtoCardSkuTotalServiceImpl extends ServiceImpl<ProtoCardSkuTotalMapper, ProtoCardSkuTotalPO> implements ProtoCardSkuTotalService {

    @Autowired
    private ProtoProjectListService protoProjectListService;

    @Override
    public List<ProtoCardSkuTotalPO> countCardProjectList() {
        return baseMapper.countCardSku();
    }


    @Override
    public Boolean statistics() {
        Date nowDate = new Date();
        //清空项目汇总台账表数据
        this.remove(new QueryWrapper<>());

        //项目汇总台账数据去项目配置里获取项目经理信息
        List<ProtoCardSkuTotalPO> cardSkuTotalList = this.countCardProjectList();
        List<ProtoProjectListPO> projectList = protoProjectListService.getAllProjectList();

        if (CollectionUtils.isNotEmpty(cardSkuTotalList) && CollectionUtils.isNotEmpty(projectList)) {
            for (ProtoCardSkuTotalPO cardSkuTotalPO : cardSkuTotalList) {
                String projectCode = cardSkuTotalPO.getProjectCode();
                BigDecimal zkysNum = cardSkuTotalPO.getZkysNum();
                BigDecimal zkesNum = cardSkuTotalPO.getZkesNum();
                BigDecimal yszyNum = cardSkuTotalPO.getYszyNum();
                BigDecimal eszyNum = cardSkuTotalPO.getEszyNum();
                BigDecimal wfghNum = cardSkuTotalPO.getWfghNum();
                BigDecimal ybfNum = cardSkuTotalPO.getYbfNum();
                BigDecimal ykkNum = cardSkuTotalPO.getYkkNum();
                BigDecimal totalNum = zkysNum.add(zkesNum).add(yszyNum).add(eszyNum).add(wfghNum).add(ybfNum).add(ykkNum);
                cardSkuTotalPO.setTotalNum(totalNum);
                cardSkuTotalPO.setModifyDate(nowDate);
                if (StringUtils.isNotBlank(projectCode)) {
                    for (ProtoProjectListPO projectListPO : projectList) {
                        String listProCode = projectListPO.getProjectCode();
                        if (projectCode.equals(listProCode)) {
                            String pmUserName = projectListPO.getPmUserName();
                            String pmUserCode = projectListPO.getPmUserCode();
                            if (StringUtils.isNotBlank(pmUserName)) {
                                cardSkuTotalPO.setPm(pmUserName + "(" + pmUserCode + ")");
                            }
                            break;
                        }
                    }
                }
            }
        }
        //项目汇总台账数据批量入库
        this.saveBatch(cardSkuTotalList);
        return null;
    }

}
