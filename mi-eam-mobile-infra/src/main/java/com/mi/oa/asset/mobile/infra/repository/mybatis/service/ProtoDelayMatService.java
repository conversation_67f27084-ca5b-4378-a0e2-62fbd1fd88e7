package com.mi.oa.asset.mobile.infra.repository.mybatis.service;


import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDelayMatPO;

import java.util.List;

/**
 * 工程机延期归还物料明细
 *
 * <AUTHOR>
 * @date 2022/2/18 17:45
 */
public interface ProtoDelayMatService {

    /**
     * 根据延期归还申请ID查询延期归还物料明细
     *
     * @param delayId
     * @return
     */
    List<ProtoDelayMatPO> getListByDelayId(String delayId);

    /**
     * 根据延期归还申请ID和当前归还时间查询延期归还物料明细
     *
     * @param delayId
     * @param endDate
     * @return
     */
    List<ProtoDelayMatPO> getListByDelayIdAndEndDate(String delayId, String endDate);

    List<ProtoDelayMatPO> findListByParam(ProtoDelayMatPO param);
}
