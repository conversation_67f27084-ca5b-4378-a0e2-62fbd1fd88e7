package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDevicePlanPO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/23
 */
public interface ProtoDevicePlanService extends IService<ProtoDevicePlanPO> {

    /**
     * 盘点提前30分钟发送邮件提醒
     * @param planId
     */
    void sendPlanMessage(String planId);

    /**
     * 盘点提交时发送飞书、邮件提醒
     * @param planId
     */
    void submitSendMessage(String planId);

    /**
     *
     * @return
     */
    List<ProtoDevicePlanPO> listPlaning();
}
