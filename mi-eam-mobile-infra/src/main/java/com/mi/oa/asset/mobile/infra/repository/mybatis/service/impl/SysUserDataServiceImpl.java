package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysUserDataPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysUserDataMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysDataTypeService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysUserDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/6
 */
@Service
public class SysUserDataServiceImpl extends ServiceImpl<SysUserDataMapper, SysUserDataPO> implements SysUserDataService {

    @Autowired
    private SysDataTypeService sysDataTypeService;

    @Autowired
    private CommonService commonService;

    @Override
    public void addDataAuthority(String userCode, String dataType, String dataValue) {
        String dataTypeId = sysDataTypeService.getDataTypeIdByField(dataType);
        Integer count = this.countByUserIdAndType(userCode, dataTypeId, dataValue);
        if (count != 0){
            return;
        }
        SysUserDataPO sysUserDataPO = new SysUserDataPO();
        sysUserDataPO.setUserDataId(commonService.getUniqueId(RedisUniqueKeyEnum.SYS_USER_DATA));
        sysUserDataPO.setUserId(userCode);
        sysUserDataPO.setDtypeId(dataTypeId);
        sysUserDataPO.setAddUserid(userCode);
        sysUserDataPO.setAddDate(new Date());
        sysUserDataPO.setModifyUserid(userCode);
        sysUserDataPO.setModifyDate(new Date());
        sysUserDataPO.setDtypeData(dataValue);
        // 包含下级
        sysUserDataPO.setHasSub("1");
        sysUserDataPO.setDisplay(dataValue);
        this.save(sysUserDataPO);
    }

    @Override
    public void deleteDataAuthority(String userCode, String dataType, String dataValue) {
        String dataTypeId = sysDataTypeService.getDataTypeIdByField(dataType);
        LambdaQueryWrapper<SysUserDataPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserDataPO::getUserId, userCode)
                .eq(SysUserDataPO::getDtypeId, dataTypeId)
                .eq(SysUserDataPO::getDtypeData, dataValue);
        this.remove(wrapper);
    }

    @Override
    public List<SysUserDataPO> getByUserCodeAndDataType(String userCode, String dataType) {
        String dataTypeId = sysDataTypeService.getDataTypeIdByField(dataType);
        LambdaQueryWrapper<SysUserDataPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserDataPO::getUserId, userCode)
                .eq(SysUserDataPO::getDtypeId, dataTypeId);
        return this.list(wrapper);
    }

    private Integer countByUserIdAndType(String userId, String dataTypeId, String dataValue){
        LambdaQueryWrapper<SysUserDataPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserDataPO::getUserId, userId)
                .eq(SysUserDataPO::getDtypeId, dataTypeId)
                .eq(SysUserDataPO::getDtypeData, dataValue);
        return this.count(wrapper);
    }
}
