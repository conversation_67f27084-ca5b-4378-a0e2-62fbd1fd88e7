package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyMatPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoApplyMatMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoApplyMatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/5 16:03
 */
@Service
public class ProtoApplyMatServiceImpl extends ServiceImpl<ProtoApplyMatMapper, ProtoApplyMatPO> implements ProtoApplyMatService {

    @Override
    public boolean updateProtoApplyMatToSapItemRowNo(String applyId, String skuCode, Integer sapItemRowNo) {
        LambdaUpdateWrapper<ProtoApplyMatPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoApplyMatPO::getApplyId,applyId).eq(ProtoApplyMatPO::getSkuCode,skuCode)
                .set(ProtoApplyMatPO::getSapItemRowNo,String.valueOf(sapItemRowNo));
        return this.update(updateWrapper);
    }

    @Override
    public List<ProtoApplyMatPO> getProtoApplyDetSAPList(String keyId) {
        QueryWrapper<ProtoApplyMatPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProtoApplyMatPO::getApplyId,keyId);
        queryWrapper.lambda().gt(ProtoApplyMatPO::getApplyNum,0);
        queryWrapper.lambda().orderByAsc(ProtoApplyMatPO::getSkuCode);
        return this.list(queryWrapper);
    }

    @Override
    public Integer countByApplyIdAndNotFinish(String applyId) {
        return baseMapper.countByApplyIdAndNotFinish(applyId);
    }

    @Override
    public ProtoApplyMatPO queryOneProtoApplyMat(LambdaQueryWrapper<ProtoApplyMatPO> queryWrapper) {
        return baseMapper.selectOne(queryWrapper.last("limit 1"));
    }

    @Override
    public String getApplyItemid(String applyId, String skuCode) {
        LambdaQueryWrapper<ProtoApplyMatPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoApplyMatPO::getApplyId, applyId)
                .eq(ProtoApplyMatPO::getSkuCode, skuCode)
                .isNotNull(ProtoApplyMatPO::getSapItemRowNo);
        ProtoApplyMatPO applyMatPO = this.getOne(wrapper, false);
        return (null == applyMatPO) ? null : applyMatPO.getSapItemRowNo();
    }

}
