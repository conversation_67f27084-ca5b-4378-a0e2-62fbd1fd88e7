package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDeviceScanDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoDeviceScanDetMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoDeviceScanDetService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/23
 */
@Service
public class ProtoDeviceScanDetImpl extends ServiceImpl<ProtoDeviceScanDetMapper, ProtoDeviceScanDetPO> implements ProtoDeviceScanDetService {
    @Override
    public List<ProtoDeviceScanDetPO> listByPlanId(String planId) {
        LambdaQueryWrapper<ProtoDeviceScanDetPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoDeviceScanDetPO::getPlanId, planId);
        return this.list(wrapper);
    }

    @Override
    public List<String> getProjectCodeByPlanId(String planId) {
        return baseMapper.getProjectCodeByPlanId(planId);
    }

}
