package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.mi.oa.asset.mobile.application.dto.AssetTaskDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.TodoTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/1
 */
@Service
@Slf4j
public class TodoTaskServiceImpl implements TodoTaskService {


    @Autowired
    RocketMQTemplate rocketMQTemplate;

    /**
     * 发送MQ消息
     * @param orderTrace
     */
    @Value("${rocketmq.labOrderTask.topic}")
    private String orderTopic;

    @Override
    public void sendMessage(List<AssetTaskDTO> taskDTO) {
        if (CollectionUtils.isEmpty(taskDTO)) {
            log.info("send_lab_task_is_invalid");
            return;
        }
        try {
            rocketMQTemplate.asyncSendOrderly(orderTopic, taskDTO, taskDTO.get(0).getTaskId(), new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("async_lab_task_onSuccess_SendResult: {}", sendResult);
                }

                @Override
                public void onException(Throwable throwable) {
                    log.error("async_lab_task_onException_Throwable: {}", throwable);
                }
            });
        } catch (Exception e) {
            log.error("async_onException_Throwable: {}", e);
        }
    }
}
