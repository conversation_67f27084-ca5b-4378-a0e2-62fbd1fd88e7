package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDelayPO;

/**
 * 工程机延期归还
 *
 * <AUTHOR>
 * @date 2022/2/18 15:04
 */
public interface ProtoDelayService {

    /**
     * 根据主键查询
     *
     * @param delayId
     * @return
     */
    ProtoDelayPO findByDelayId(String delayId);

    /**
     * 更新领用方类型
     *
     * @param keyId
     * @param applyUserType
     * @return
     */
    boolean updateApplyUserType(String keyId, String applyUserType);

    /**
     * 修改流程信息
     *
     * @param delayId
     * @param auditing
     * @param modifyDate
     * @param businessKey
     * @return
     */
    boolean updProtoDelayStatusByDelayId(String delayId, String auditing, String modifyDate, String businessKey);

    /**
     * 修改流程信息
     *
     * @param delayId
     * @param auditing
     * @param modifyDate
     * @return
     */
    boolean updProtoDelayStatusByDelayId(String delayId, String auditing, String modifyDate);

    /**
     * 根据bpm唯一标识来查询工程机延期归还信息
     *
     * @param businessKey
     * @return
     */
    ProtoDelayPO findByBusinessKey(String businessKey);
}
