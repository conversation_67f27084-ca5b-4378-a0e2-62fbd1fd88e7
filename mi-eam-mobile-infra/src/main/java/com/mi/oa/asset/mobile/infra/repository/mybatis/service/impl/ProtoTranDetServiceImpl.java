package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.dto.tran.TranSapSplitDTO;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.SapRetEnum;
import com.mi.oa.asset.mobile.common.enums.TranTypeEnum;
import com.mi.oa.asset.mobile.infra.dto.CostCenter;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOutPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoTranDetMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
@Service
public class ProtoTranDetServiceImpl extends ServiceImpl<ProtoTranDetMapper, ProtoTranDetPO> implements ProtoTranDetService {

    @Autowired
    private ProtoOutService protoOutService;

    @Autowired
    private ProtoApplyMatService protoApplyMatService;

    @Autowired
    private ProtoApplyService protoApplyService;

    @Autowired
    private ProtoService protoService;

    @Autowired
    private ProtoTranService protoTranService;

    /**
     * select project_code,center_code,make_cn,sap_tran_code,batch_id
     * from proto_tran_det
     * where tran_id = ? and asset_newold = ? and (sap_ret_type = 'E' or sap_ret_type = 'N') and (is_success = 'E' or is_success = 'N')
     * group by project_code,center_code,make_cn
     *
     * @param tranId 转移单id
     * @return
     */
    @Override
    public List<ProtoTranDetPO> getDetGroupBy(String tranId) {
        return baseMapper.selectList(Wrappers.<ProtoTranDetPO>lambdaQuery()
                .groupBy(ProtoTranDetPO::getProjectCode, ProtoTranDetPO::getCenterCode,
                        ProtoTranDetPO::getMakeCn)
                .select(ProtoTranDetPO::getProjectCode, ProtoTranDetPO::getCenterCode,
                        ProtoTranDetPO::getMakeCn, ProtoTranDetPO::getSapTranCode,
                        ProtoTranDetPO::getBatchId, ProtoTranDetPO::getAssetNewold)
                .eq(ProtoTranDetPO::getTranId, tranId)
                .eq(ProtoTranDetPO::getAssetNewold, CommonConstant.ProtoCardAssetNewOld.ONEHAND.getType())
                .and(queryWrapper -> queryWrapper.eq(ProtoTranDetPO::getSapRetType, "E").or().eq(ProtoTranDetPO::getSapRetType, "N"))
                .and(queryWrapper -> queryWrapper.eq(ProtoTranDetPO::getIsSuccess, "E").or().eq(ProtoTranDetPO::getIsSuccess, "N"))
        );


    }

    @Override
    public List<ProtoTranDetPO> getByTranId(String tranId) {
        return baseMapper.selectList( Wrappers.<ProtoTranDetPO>lambdaQuery()
                .eq(ProtoTranDetPO::getTranId, tranId));
    }

    @Override
    public List<ProtoTranDetPO> findListByParam(ProtoTranDetPO param) {
        QueryWrapper<ProtoTranDetPO> queryWrapper = new QueryWrapper(param);
        return this.list(queryWrapper);
    }

    /**
     * String sql = "select project_code,center_code,make_cn,sap_tran_code,batch_id " +
     *                 " from proto_tran_det " +
     *                 " where tran_id = ? and asset_newold = ? and sap_ret_type = 'S' and (is_success = 'E' or is_success = 'N') " +
     *                 " group by project_code,center_code,make_cn";
     * @param tranId
     * @return
     */
    @Override
    public List<ProtoTranDetPO> list107FailByTranId(String tranId) {
        return baseMapper.selectList(Wrappers.<ProtoTranDetPO>lambdaQuery()
                .groupBy(ProtoTranDetPO::getProjectCode, ProtoTranDetPO::getCenterCode,
                        ProtoTranDetPO::getMakeCn)
                .select(ProtoTranDetPO::getProjectCode, ProtoTranDetPO::getCenterCode,
                        ProtoTranDetPO::getMakeCn, ProtoTranDetPO::getSapTranCode,
                        ProtoTranDetPO::getBatchId, ProtoTranDetPO::getAssetNewold)
                .eq(ProtoTranDetPO::getTranId, tranId)
                .eq(ProtoTranDetPO::getAssetNewold, CommonConstant.ProtoCardAssetNewOld.ONEHAND.getType())
                .eq(ProtoTranDetPO::getSapRetType, SapRetEnum.SUCCESS.getKey())
                .and(queryWrapper -> queryWrapper.eq(ProtoTranDetPO::getIsSuccess, "E").or().eq(ProtoTranDetPO::getIsSuccess, "N")));
    }

    @Override
    public List<ProtoTranDetPO> listByTranInfo(String tranId, String assetNewold, String projectCode, String centerCode, String makeCn) {
        return baseMapper.selectList(Wrappers.<ProtoTranDetPO>lambdaQuery()
                .eq(ProtoTranDetPO::getTranId, tranId)
                .eq(ProtoTranDetPO::getAssetNewold, assetNewold)
                .eq(ProtoTranDetPO::getProjectCode, projectCode)
                .eq(ProtoTranDetPO::getCenterCode, centerCode)
                .eq(ProtoTranDetPO::getMakeCn, makeCn)
                );
    }


    @Override
    public Map<TranSapSplitDTO, List<ProtoTranDetPO>> splitTranDetMap(List<ProtoTranDetPO> list) {
        Map<TranSapSplitDTO, List<ProtoTranDetPO>> splitTranDetMap = new HashMap<>();
        for (ProtoTranDetPO detPO : list) {
            //只处理一手
            if (!CommonConstant.ProtoCardAssetNewOld.ONEHAND.getType().equals(detPO.getAssetNewold())) {
                continue;
            }
            TranSapSplitDTO key = new TranSapSplitDTO(detPO.getProjectCode(), detPO.getCenterCode(), detPO.getMakeCn());
            List<ProtoTranDetPO> splitTranDetPOList = splitTranDetMap.get(key);
            if (ObjectUtils.isEmpty(splitTranDetPOList)) {
                splitTranDetPOList = new ArrayList<>();
            }
            splitTranDetPOList.add(detPO);
            splitTranDetMap.put(key, splitTranDetPOList);
        }
        return splitTranDetMap;
    }

    @Override
    public List<ProtoTranDetPO> listByTranId(String tranId) {
        LambdaQueryWrapper<ProtoTranDetPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoTranDetPO::getTranId, tranId);
        return this.list(wrapper);
    }

    @Override
    public void updateMakeCnByTranIds(List<String> tranIdList) {
        if (CollectionUtils.isEmpty(tranIdList)){
            return;
        }
        LambdaQueryWrapper<ProtoTranDetPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoTranDetPO::getTranId, tranIdList);
        List<ProtoTranDetPO> detList = this.list(wrapper);
        Map<String, String> outCodeMakeCnMap = new HashMap<>();
        for (ProtoTranDetPO detPO : detList){
            String outCode = detPO.getOutCode();
            String makeCn = outCodeMakeCnMap.get(outCode);
            ProtoTranPO tranPO = protoTranService.getByTranId(detPO.getTranId());
            // 如果没有样机生产地就去发放单的样机生产地
            if (StringUtils.isBlank(makeCn)){
                ProtoOutPO outPO = protoOutService.getByOutCode(outCode);
                if (null == outPO){
                    continue;
                }
                makeCn = outPO.getMakeCn();
                // 查询领用申请单样机
                if (StringUtils.isBlank(makeCn)){
                    ProtoApplyPO applyPO = protoApplyService.getByApplyCode(outPO.getApplyCode());
                    if (null != applyPO){
                        makeCn = applyPO.getMakeCn();
                    }
                }
                outCodeMakeCnMap.put(outCode, makeCn);
            }
            // 设置成本中心
            CostCenter centerCode = protoService.getSAPCenterCode(tranPO.getUserCode(),
                    CommonConstant.ApplyUserTypeEnum.INNER.getValue(), detPO.getMakeCn());
            if (null == centerCode){
                continue;
            }

            detPO.setCenterCode(centerCode.getCenterCode());
            detPO.setCompDeptCode(centerCode.getCompDeptCode());
            // 如果是小米转odm，成本中心不发生变化
            if (TranTypeEnum.INNER_TO_ODM.getKey().equals(tranPO.getTranType())){
                detPO.setInCenterCode(detPO.getCenterCode());
                detPO.setInCompDeptCode(detPO.getCompDeptCode());
            } else {
                centerCode = protoService.getSAPCenterCode(tranPO.getAccUserCode(),
                        CommonConstant.ApplyUserTypeEnum.INNER.getValue(), detPO.getMakeCn());

                if (centerCode == null){
                    continue;
                }
                detPO.setInCenterCode(centerCode.getCenterCode());
                detPO.setInCompDeptCode(centerCode.getCompDeptCode());
            }
            detPO.setMakeCn(makeCn);
        }
        this.updateBatchById(detList);
    }

    @Override
    public void updateUpdateApplyItemid(List<String> tranIdList) {
        if (CollectionUtils.isEmpty(tranIdList)){
            return;
        }
        LambdaQueryWrapper<ProtoTranDetPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoTranDetPO::getTranId, tranIdList)
                .apply("(apply_itemid is null or apply_itemid = '')");
        List<ProtoTranDetPO> detList = this.list(wrapper);
        for (ProtoTranDetPO detPO : detList){
            String applyCode = detPO.getApplyCode();
            ProtoApplyPO applyPO = protoApplyService.getByApplyCode(applyCode);
            if (null != applyPO){
                String applyItemid = protoApplyMatService.getApplyItemid(applyPO.getApplyId(), detPO.getSkuCode());
                detPO.setApplyItemid(applyItemid);
            }
        }
        this.updateBatchById(detList);
    }
}
