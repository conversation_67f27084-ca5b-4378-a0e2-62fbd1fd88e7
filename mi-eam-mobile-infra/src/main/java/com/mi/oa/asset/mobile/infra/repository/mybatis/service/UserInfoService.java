package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.mi.oa.asset.mobile.infra.dto.hrod.EmployeeInfo;
import com.mi.oa.infra.oaucf.idm.api.rep.AccountInfoDto;
import com.mi.oa.infra.oaucf.idm.api.rep.UserBaseInfoDto;

/**
 * @Desc 用户信息相关接口
 * <AUTHOR>
 * @Date 2021/8/27 1:21
 */

public interface UserInfoService {

    EmployeeInfo getEmpInfoByUserName(String userName);

    /**
     * 查询idm 根据username查询
     * @param userName
     * @return
     */
    UserBaseInfoDto getUserInfoByUserName(String userName);

    /**
     * 查询idm 根据username查询 缓存半个小时
     * @param userName
     * @return
     */
    UserBaseInfoDto getCacheUserInfoByUserName(String userName);

    /**
     * 查询idm 根据username查询 缓存半个小时 没查询到  返回空的对象
     * @param userName
     * @return
     */
    UserBaseInfoDto getNullCacheUserInfoByUserName(String userName);

    /**
     * 根据用户name获取email 支持p账号查询
     * @param userName
     * @return
     */
    String getEmailByName(String userName);

    EmployeeInfo getEmpInfoByEmpId(String empId);

    AccountInfoDto getAccountInfoByUserName(String userCode);
}



