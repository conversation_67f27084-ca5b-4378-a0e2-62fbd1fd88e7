package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoInNoAsnDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoInSapDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoPushSapDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoInDetMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoOutDetService;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/12/17
 */
@Service
public class ProtoInDetServiceImpl extends ServiceImpl<ProtoInDetMapper, ProtoInDetPO> implements  ProtoInDetService{

    @Autowired
    private ProtoInService protoInService;

    @Autowired
    private ProtoOutDetService protoOutDetService;

    @Autowired
    private CommonService commonService;


    @Override
    public List<ProtoInDetPO> listBySnList(Collection<String> snList) {
        if (CollectionUtils.isEmpty(snList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoInDetPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProtoInDetPO::getDeviceCode, snList);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void updateDetAuditing() {
        this.updateReceivingPosting();
        int pageNum = 1;
        while (true) {
            Page page = new Page(pageNum++, CommonConstant.DEFAULT_PAGE_SIZE);
            LambdaQueryWrapper<ProtoInDetPO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ProtoInDetPO::getAuditing, MachAuditingEnum.RECEIVING_POSTING.getState());
            List<ProtoInDetPO> records = this.page(page, queryWrapper).getRecords();
            this.dealDetAuditing(records);
            if (records.size() < CommonConstant.DEFAULT_PAGE_SIZE) {
                return;
            }
        }
    }

    @Override
    public List<ProtoInDetPO> listByInId(String inId) {
        LambdaQueryWrapper<ProtoInDetPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProtoInDetPO::getInId, inId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void clearProtoInDetMach(String detId) {
        LambdaUpdateWrapper<ProtoInDetPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProtoInDetPO::getIsFeekback, FeedBackEnum.NOT_FEED_BACK.getState());
        updateWrapper.set(ProtoInDetPO::getOrderDetId, null);
        updateWrapper.set(ProtoInDetPO::getOrderCode, null);
        updateWrapper.set(ProtoInDetPO::getSkuName, null);
        updateWrapper.set(ProtoInDetPO::getItemRowNo, null);
        updateWrapper.set(ProtoInDetPO::getAuditing, MachAuditingEnum.NOT_MACH.getState());
        updateWrapper.set(ProtoInDetPO::getAddDate, null);
        updateWrapper.eq(ProtoInDetPO::getDetId, detId);
        this.update(updateWrapper);
    }

    @Override
    public List<ProtoInDetPO> findListByParam(ProtoInDetPO po) {
        QueryWrapper<ProtoInDetPO> queryWrapper = new QueryWrapper(po);
        return this.list(queryWrapper);
    }

    @Override
    public ProtoInDetPO findOneByParam(ProtoInDetPO po) {
        QueryWrapper<ProtoInDetPO> queryWrapper = new QueryWrapper(po);
        return this.getOne(queryWrapper);
    }

    @Override
    public int countDet(String inId, ProtoInDetIsBackEnum isBackEnum, CommonConstant.ProtoCardIsComplete isComplete) {
        QueryWrapper<ProtoInDetPO> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(ProtoInDetPO::getInId, inId);
        if (null != isBackEnum) {
            queryWrapper.lambda().eq( ProtoInDetPO::getIsBack, isBackEnum.getCode());
        }
        if (null != isComplete) {
            queryWrapper.lambda().eq(ProtoInDetPO::getIsComplete, isComplete.getType());
        }
        return this.count(queryWrapper);
    }

    @Override
    public List<ProtoInDetPO> findIsComplete(String inId) {
        QueryWrapper<ProtoInDetPO> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(ProtoInDetPO::getInId,inId).in(ProtoInDetPO::getIsComplete,CommonConstant.ProtoCardIsComplete.GOOD.getType()
                ,CommonConstant.ProtoCardIsComplete.NO_GOOD.getType(),CommonConstant.ProtoCardIsComplete.TEMPORARY_GOOD_PRODUCT.getType()
                ,CommonConstant.ProtoCardIsComplete.RETURN_NOT_ALLOWED.getType());
        return this.list(queryWrapper);
    }

    @Override
    public void updateProtoInDetToIsBack(String inId, ProtoInDetIsBackEnum isBackEnum, CommonConstant.ProtoCardIsComplete isComplete) {
        LambdaUpdateWrapper<ProtoInDetPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProtoInDetPO::getIsBack, isBackEnum.getCode()).eq(ProtoInDetPO::getInId, inId);
        if (null != isComplete) {
            updateWrapper.eq(ProtoInDetPO::getIsComplete, isComplete.getType());
        }
        this.update(updateWrapper);
    }

    @Override
    public Integer countInIdAndFeedBack(String inID, FeedBackEnum backEnum) {
        LambdaQueryWrapper<ProtoInDetPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProtoInDetPO::getInId, inID);
        queryWrapper.eq(ProtoInDetPO::getIsFeekback, backEnum.getState());
        return this.count(queryWrapper);
    }

    @Override
    public List<ProtoInSapDTO> getNotAsnPushSap(String inId) {
        return baseMapper.getNotAsnPushSap(inId);
    }

    @Override
    public void updateInCode(String inCode, String inId, String orderCode) {
        LambdaUpdateWrapper<ProtoInDetPO> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.set(ProtoInDetPO::getInCode, inCode);
        updateWrapper.eq(ProtoInDetPO::getInId, inId);
        updateWrapper.eq(ProtoInDetPO::getOrderCode, orderCode);
        this.update(updateWrapper);
    }

    @Override
    public void updateSapRetType(String inId, String orderCode, Boolean sapRetType) {
        LambdaUpdateWrapper<ProtoInDetPO> updateWrapper = new LambdaUpdateWrapper();
        String sapRetTypeStr = (sapRetType ? SapRetEnum.SUCCESS.getKey() : SapRetEnum.ERROR.getKey());
        updateWrapper.set(ProtoInDetPO::getSapRetType, sapRetTypeStr);
        updateWrapper.set(ProtoInDetPO::getPostingDate, DateUtils.getFormatDate(DateUtils.COMMON_PATTERN));
        updateWrapper.eq(ProtoInDetPO::getInId, inId);
        updateWrapper.eq(ProtoInDetPO::getOrderCode, orderCode);
        this.update(updateWrapper);
    }

    @Override
    public void updateSapItemRowNo(String sapItemRowNo, String inId, String skuCode) {
        LambdaUpdateWrapper<ProtoInDetPO> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.set(ProtoInDetPO::getSapItemRowNo, sapItemRowNo);
        updateWrapper.eq(ProtoInDetPO::getInId, inId);
        updateWrapper.eq(ProtoInDetPO::getSkuCode, skuCode);
        this.update(updateWrapper);
    }

    @Override
    public List<ProtoInNoAsnDTO> getNotAsnDetPushSAPList(String inId, String orderCode) {
        return baseMapper.getNotAsnDetPushSAPList(inId, orderCode);
    }

    @Override
    public Integer countNotSuccess(String inId) {
        return baseMapper.countNotSuccess(inId);
    }

    @Override
    public void saveAsnDet(List<ProtoAsnOrderDetPO> itemList, String inId, String userCode) {
        List<ProtoInDetPO> list = new ArrayList<>(itemList.size());
        for (ProtoAsnOrderDetPO detPO : itemList){
            this.checkOtherReceiving(detPO.getDeviceCode());
            ProtoInDetPO inDetPO = new ProtoInDetPO();
            BeanUtils.copyProperties(detPO, inDetPO);
            inDetPO.setAuditing(MachAuditingEnum.HAVE_MACH.getState());
            inDetPO.setAddUserid(userCode);
            inDetPO.setAddDate(DateUtils.getFormatDate(DateUtils.COMMON_PATTERN));
            inDetPO.setInId(inId);
            inDetPO.setDetId(commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_IN_DET));
            list.add(inDetPO);
        }
        this.saveBatch(list);
    }

    @Override
    public List<ProtoPushSapDTO> getPushSapList(String inId) {
        return baseMapper.getPushSapList(inId);
    }

    @Override
    public void updateProtoInDetToSapItemRowNo(String inId, String skuCode, String sapItemRowNo) {
        LambdaUpdateWrapper<ProtoInDetPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProtoInDetPO::getSapItemRowNo, sapItemRowNo);
        wrapper.eq(ProtoInDetPO::getInId, inId);
        wrapper.eq(ProtoInDetPO::getSkuCode, skuCode);
        this.update(wrapper);
    }

    /**
     * 检测是否在其他地方收货
     * @param sn
     */
    private void checkOtherReceiving(String sn){
        LambdaQueryWrapper<ProtoInDetPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProtoInDetPO::getDeviceCode, sn);
        List<ProtoInDetPO> list = this.list(queryWrapper);
        List<String> detIdList = list.stream().map(ProtoInDetPO::getInId).collect(Collectors.toList());
        List<ProtoInPO> inList = protoInService.getByInIdsAndInType(detIdList, InTypeEnum.NOT_ASN_DELIVERY.getType(), InTypeEnum.HAVE_ASN_DELIVERY.getType());
        if (!CollectionUtils.isEmpty(inList)){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_IN_001, sn, inList.get(0).getInCode());
        }
    }

    /**
     * 手工匹配、已匹配并且抛送SAP成功时 状态改成抛送收货过账
     */
    private void updateReceivingPosting(){
        LambdaUpdateWrapper<ProtoInDetPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProtoInDetPO::getAuditing, MachAuditingEnum.RECEIVING_POSTING.getState());
        updateWrapper.in(ProtoInDetPO::getAuditing, MachAuditingEnum.HANDLE_MACH.getState(), MachAuditingEnum.HAVE_MACH.getState());
        updateWrapper.eq(ProtoInDetPO::getSapRetType, SapRetEnum.SUCCESS.getKey());
        this.update(updateWrapper);
    }

    /**
     * 处理抛送SAP状态
     * @param records
     */
    private void dealDetAuditing(List<ProtoInDetPO> records){
        if (CollectionUtils.isEmpty(records)){
            return;
        }
        List<String> detIdList = new ArrayList<>(records.size());
        // 0 无ASN收货
        for (ProtoInDetPO detPO : records) {
            String sn = detPO.getDeviceCode();
            String inId = detPO.getInId();
            ProtoInPO inPO = protoInService.getById(inId);
            // 不是无ASN收货
            if (null == inPO || !InTypeEnum.NOT_ASN_DELIVERY.getType().equals(inPO.getInType())){
                continue;
            }
            int count = protoOutDetService.getProtoOutSapSuccess(sn);
            if (count > 0){
                detIdList.add(detPO.getDetId());
            }
        }
        if (!detIdList.isEmpty()){
            LambdaUpdateWrapper<ProtoInDetPO> updateWrapper = new LambdaUpdateWrapper();
            updateWrapper.set(ProtoInDetPO::getAuditing, MachAuditingEnum.HAVE_COMPLETE.getState());
            updateWrapper.in(ProtoInDetPO::getDetId, detIdList);
            this.update(updateWrapper);
        }
    }

    /**
     * 通过inId和归还状态查询明细
     * @param inId
     * @return
     */
    public List<ProtoInDetPO> listByInIdAndIsComplete(String inId, String isComplete) {
        LambdaQueryWrapper<ProtoInDetPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProtoInDetPO::getInId, inId);
        queryWrapper.ne(ProtoInDetPO::getIsComplete, isComplete);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void deletedByInId(String inId) {
        LambdaQueryWrapper<ProtoInDetPO> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ProtoInDetPO::getInId, inId);
        this.remove(wrapper);
    }

}
