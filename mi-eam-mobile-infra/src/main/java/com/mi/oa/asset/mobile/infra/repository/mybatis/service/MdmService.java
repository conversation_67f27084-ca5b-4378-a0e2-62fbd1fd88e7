package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.mi.oa.asset.mobile.infra.dto.mdm.MdmBaseDTO;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmBaseVO;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmProjectVO;
import com.mi.oa.asset.x5.common.X5Response;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/12
 */
public interface MdmService {

    /**
     * 创建成本中心
     * @param createId 创建人
     * @param ccCode 成本中心
     * @return
     */
    X5Response createCostCenter(String createId, String ccCode);

    /**
     * 从MDM获取项目
     * @param projectCode
     * @param updateTime
     * @param pageNum
     * @param pageSize
     * @return
     */
    MdmBaseVO<MdmProjectVO> getProject(String projectCode, String updateTime, Integer pageNum, Integer pageSize);
}
