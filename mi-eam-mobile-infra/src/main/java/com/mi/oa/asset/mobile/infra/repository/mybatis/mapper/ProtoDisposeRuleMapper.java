package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDisposeRulePO;

/**
 * table_name : proto_dispose_rule
 * <AUTHOR>
 * @date 2022/06/15/03:51
 */
public interface ProtoDisposeRuleMapper extends BaseMapper<ProtoDisposeRulePO> {
    /**
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     *
     * @mbg.generated
     */
    int insert(ProtoDisposeRulePO record);

    /**
     *
     * @mbg.generated
     */
    int insertSelective(ProtoDisposeRulePO record);
}