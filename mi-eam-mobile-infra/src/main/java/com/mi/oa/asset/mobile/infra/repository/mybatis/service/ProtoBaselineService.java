package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.common.enums.BaselineStateEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoBaselinePO;

/**
 *
 * <AUTHOR>
 * @date 2022/2/28 15:57
 */
public interface ProtoBaselineService extends IService<ProtoBaselinePO> {

    ProtoBaselinePO findOneByParam(ProtoBaselinePO param);
    /**
     * 更新基线状态
     */
    void updateBaselineStateByCode(String baselineCode, BaselineStateEnum stateEnum);
    /**
     * 更新基线状态
     */
    void updateBaselineStateByRecordId(String baselineRecordId, BaselineStateEnum stateEnum);

    /**
     * 更新基线接口人部门信息
     */
    void refreshAllDeptName();
}
