package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MrpMachGrantRevertPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.MrpMachGrantRevertMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.MrpMachGrantRevertService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/3/30
 */
@Service
public class MrpMachGrantRevertServiceImpl extends ServiceImpl<MrpMachGrantRevertMapper, MrpMachGrantRevertPO>
            implements MrpMachGrantRevertService {


    @Override
    public Map<String, Date> getGrantTimeMap(List<String> snList) {
        LambdaQueryWrapper<MrpMachGrantRevertPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(MrpMachGrantRevertPO::getSn, snList)
                .select(MrpMachGrantRevertPO::getGrantTime, MrpMachGrantRevertPO::getSn)
                .isNotNull(MrpMachGrantRevertPO::getGrantTime)
                .orderByAsc(MrpMachGrantRevertPO::getId);

        Map<String, Date> result = new HashMap<>();
        List<MrpMachGrantRevertPO> grantTimeList = this.list(queryWrapper);
        for (MrpMachGrantRevertPO revertPO : grantTimeList){
            result.putIfAbsent(revertPO.getSn(), revertPO.getGrantTime());
        }
        return result;
    }

    @Override
    public Map<String, Date> getRevertTimeMap(List<String> snList){
        LambdaQueryWrapper<MrpMachGrantRevertPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(MrpMachGrantRevertPO::getSn, snList)
                .select(MrpMachGrantRevertPO::getRevertTime, MrpMachGrantRevertPO::getSn)
                .isNotNull(MrpMachGrantRevertPO::getRevertTime)
                .orderByDesc(MrpMachGrantRevertPO::getId);
        List<MrpMachGrantRevertPO> list = this.list(queryWrapper);
        Map<String, Date> result = new HashMap<>(snList.size()*2);
        for (MrpMachGrantRevertPO revertPO : list){
            result.putIfAbsent(revertPO.getSn(), revertPO.getRevertTime());
        }
        return result;
    }

    @Override
    public Map<String, String> getGrantUserMap(List<String> snList) {
        LambdaQueryWrapper<MrpMachGrantRevertPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(MrpMachGrantRevertPO::getSn, snList)
                .select(MrpMachGrantRevertPO::getGrantUser, MrpMachGrantRevertPO::getSn)
                .isNotNull(MrpMachGrantRevertPO::getGrantUser)
                .orderByAsc(MrpMachGrantRevertPO::getId);
        List<MrpMachGrantRevertPO> list = this.list(queryWrapper);
        Map<String, String> result = new HashMap<>(snList.size()*2);
        for (MrpMachGrantRevertPO revertPO : list){
            result.putIfAbsent(revertPO.getSn(), revertPO.getGrantUser());
        }
        return result;
    }
}
