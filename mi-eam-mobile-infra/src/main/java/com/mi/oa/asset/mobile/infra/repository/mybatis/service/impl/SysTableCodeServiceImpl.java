package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysTableCodePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysTableCodeMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysTableCodeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/10
 */
@Service
public class SysTableCodeServiceImpl extends ServiceImpl<SysTableCodeMapper, SysTableCodePO>
        implements SysTableCodeService {

    @Override
    @Transactional(propagation= Propagation.REQUIRES_NEW)
    public Integer getMaxSysTableCode(String tabName, String codeExt) {
        SysTableCodePO sysTableCodePO = this.getByTabName(tabName, codeExt);
        if (null == sysTableCodePO){
            sysTableCodePO = new SysTableCodePO();
            sysTableCodePO.setTableName(tabName);
            sysTableCodePO.setCodeExt(codeExt);
            sysTableCodePO.setMaxValue(1);
            baseMapper.insert(sysTableCodePO);
        } else {
            baseMapper.updateMaxValue(tabName, codeExt);
        }
        return getByTabName(tabName, codeExt).getMaxValue();
    }

    @Override
    public SysTableCodePO getByTabName(String tableName, String codeExt) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("table_name", tableName);
        queryWrapper.eq("code_ext", codeExt);
        return baseMapper.selectOne(queryWrapper);
    }


}
