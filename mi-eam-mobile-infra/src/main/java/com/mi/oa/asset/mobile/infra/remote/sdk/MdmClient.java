package com.mi.oa.asset.mobile.infra.remote.sdk;

import com.mi.oa.asset.mobile.infra.dto.mdm.MdmBaseDTO;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmBaseVO;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmProjectDTO;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmProjectVO;
import com.mi.oa.asset.x5.consumer.X5FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/12
 */
@X5FeignClient(value = "mdm", url = "${mdm.host}", appId = "${mdm.appId}", appKey = "${mdm.appKey}", form = true)
public interface MdmClient {

    @PostMapping
    MdmBaseVO<MdmProjectVO> getProject(MdmProjectDTO mdmProjectDTO);
}
