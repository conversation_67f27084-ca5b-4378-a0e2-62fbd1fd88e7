package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.application.dto.ConditionDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyMatPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSecondCardPO;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/7 14:09
 */
public interface ProtoSecondCardService extends IService<ProtoSecondCardPO> {
    /**
     * 在二手非良品库清单找到该不良品记录，根据申请数量进行扣减或增加
     * @param matList
     * @param projectCode
     * @param stageName
     * @param houseCode
     * @param flag
     * @return
     */
    public boolean isReduce(List<ProtoApplyMatPO> matList, String projectCode, String stageName, String houseCode, boolean flag);
    /**
     * TODO 查询二手非良品库清单满足条件的记录数量
     * @param projectCode
     * @param stageName
     * @param skuCode
     * @param houseCode
     * @return
     */
    int qrySecondCardCount(String projectCode, String stageName, String skuCode,String houseCode);
    /**
     *
     * @param conditionList
     * @return
     */
    List<Map<String, Object>> findSecondNoGoodNumList(List<ConditionDTO> conditionList, List<String> mrpTypeList);
    /**
     * 根据多参数查询
     * @param po 查询参数
     * @return
     */
    List<ProtoSecondCardPO> findListByParam(ProtoSecondCardPO po);
    /**
     * 根据多参数查询
     * @param po 查询参数
     * @return
     */
    ProtoSecondCardPO findOneByParam(ProtoSecondCardPO po);

    /**
     * 释放占用的库存
     * @param skuCode
     * @param houseCode
     * @param releaseCount
     */
    void releaseSecondCard(String skuCode, String houseCode, Integer releaseCount);
}
