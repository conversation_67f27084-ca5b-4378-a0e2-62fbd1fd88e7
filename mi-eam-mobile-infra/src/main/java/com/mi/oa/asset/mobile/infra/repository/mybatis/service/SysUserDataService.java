package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysUserDataPO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/6
 */
public interface SysUserDataService extends IService<SysUserDataPO> {

    /**
     * 添加数据权限
     * @param userCode
     * @param dataType
     * @param dataValue
     */
    void addDataAuthority(String userCode, String dataType, String dataValue);

    /**
     * 删除数据权限
     * @param userCode
     * @param dataType
     */
    void deleteDataAuthority(String userCode, String dataType, String dataValue);

    /**
     * 查询权限
     * @param userCode
     * @param dataType
     * @return
     */
    List<SysUserDataPO> getByUserCodeAndDataType(String userCode, String dataType);
}
