package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysRolePO;

import java.util.List;

/**
 * 操作角色
 *
 * <AUTHOR>
 * @date 2022/1/25 15:52
 */
public interface SysRoleService extends IService<SysRolePO> {

    /**
     * 根据角色编号查询角色
     *
     * @param roleNo
     * @return
     */
    SysRolePO getSysRoleByRoleNo(String roleNo);

    /**
     * 查询角色
     * @param roleIds
     * @return
     */
    List<String> getMrpType(List<String> roleIds);
}
