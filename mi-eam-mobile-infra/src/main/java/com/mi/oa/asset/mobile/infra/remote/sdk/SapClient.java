package com.mi.oa.asset.mobile.infra.remote.sdk;


import com.mi.oa.asset.mobile.infra.config.SapClientConfig;
import com.mi.oa.asset.mobile.infra.dto.sap.SapBaseVO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapCustomVO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapMierDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapPhoneTransferDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapProjectDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapProjectInfoDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapSkuDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapSkuDetailVO;
import com.mi.oa.asset.x5.consumer.X5FeignClient;
import feign.Request;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * @Desc SAP 远程接口定义
 * <AUTHOR>
 * @Date 2021/8/30 20:43
 */

@X5FeignClient(value = "sap", url = "${sap.host}/RESTAdapter/sap/mieam", appId = "${sap.appId}", appKey = "${sap.appKey}", configuration = SapClientConfig.class)
public interface SapClient {


    /**
     * 发送样机转移申请单至ERP接口X5 REST HTTP POST服务  106接口
     * @param param
     * @return
     */
    @PostMapping("/engine-phone/transfer")
    SapCustomVO enginePhoneTransfer(SapPhoneTransferDTO param);

    /**
     * SAP 107接口
     * @param sapMierDTO
     * @return
     */
    @PostMapping("/107-transfer/push")
    SapCustomVO sap107Json(SapMierDTO sapMierDTO, Request.Options options);

    /**
     * 获取项目、阶段信息  105接口
     * @param param
     * @return
     */
    @PostMapping("/project-info/query")
    SapBaseVO<SapProjectInfoDTO> getProjectInfo(SapProjectDTO param);

    /**
     * 4、获取小米SAP工厂物料清单公用X5同步接口 (生态链半成品物料同步/查询)
     * https://xiaomi.f.mioffice.cn/docx/doxk40zwcBMHUTA3ZEimTiyiy6e
     * @param sapSkuDTO
     * @return
     */
    @PostMapping("/factory-material-list/query")
    SapBaseVO<SapSkuDetailVO> queryFactoryMaterialList(SapSkuDTO sapSkuDTO);

}
