package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.RedisCachePrefixKeyEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysDeptPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysDeptMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysDeptService;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/5 15:24
 */
@Service
public class SysDeptServiceImpl extends ServiceImpl<SysDeptMapper, SysDeptPO> implements SysDeptService {



    @Override
    public SysDeptPO getCacheByDeptId(String deptId) {
        // 暂时去掉缓存
        String cacheKey = RedisCachePrefixKeyEnum.SYS_DEPT_DEPT_ID.getPrefixKey() + deptId;
        Object obj = RedisUtils.get(cacheKey);
        if (null != obj){
            return (SysDeptPO)obj;
        }
        SysDeptPO deptPO = this.getById(deptId);
        if (null != deptPO){
            String longDeptName = deptPO.getLongDeptName();
            if (StringUtils.isBlank(longDeptName)){
                deptPO.setLongDeptName(deptPO.getDeptName());
            }
            RedisUtils.setEx(cacheKey, deptPO, 30, TimeUnit.MINUTES);
        }
        return deptPO;
    }

    @Override
    public List<SysDeptPO> listByDeptCodeList(List<String> deptCodeList) {
        if (CollectionUtils.isEmpty(deptCodeList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SysDeptPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SysDeptPO::getDeptId, deptCodeList);
        return this.list(wrapper);
    }
}
