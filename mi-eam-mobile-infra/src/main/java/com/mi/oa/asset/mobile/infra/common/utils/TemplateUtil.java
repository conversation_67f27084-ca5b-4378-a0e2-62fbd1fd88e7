package com.mi.oa.asset.mobile.infra.common.utils;

import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.StringWriter;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/11/1 17:10
 */

@Slf4j
@Service
public class TemplateUtil {

    @Autowired
    private Configuration fileTplConfiguration;

    public String compose(String templateName, Object data) {
        if(null == templateName || templateName.isEmpty()) {
            log.error("template is empty");

            return "";
        }

        String content = "";

        try {
            Template template = fileTplConfiguration.getTemplate(templateName);
            StringWriter stringWriter = new StringWriter();
            template.process(data, stringWriter);

            content = stringWriter.toString();
        } catch (Exception e) {
            log.error("template {} compose failed", templateName, e);
        }

        return content;
    }

    public String composeStrTpl(String tpl, Object data) {
        Configuration configuration = new Configuration(Configuration.VERSION_2_3_30);
        configuration.setDefaultEncoding("UTF-8");
        configuration.setTemplateLoader(new StringTemplateLoader());

        String content = "";

        try {
            Template template = new Template("strtpl", tpl, configuration);
            StringWriter stringWriter = new StringWriter();
            template.process(data, stringWriter);

            content = stringWriter.toString();
        } catch (Exception e) {
            log.error("template {} compose failed", tpl, e);
        }

        return content;
    }
}
