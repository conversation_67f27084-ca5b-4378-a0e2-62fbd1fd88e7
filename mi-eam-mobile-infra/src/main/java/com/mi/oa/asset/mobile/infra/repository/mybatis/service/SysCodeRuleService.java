package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysCodeRulePO;

import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/10
 */
public interface SysCodeRuleService extends IService<SysCodeRulePO> {

    /**
     * 获取流水号
     * @param funId
     * @return
     */
    String getBusCode(String funId, Map<String, String> mpValue);

    /**
     * 获取流水号
     * @param funId
     * @return
     */
    String getBusCode(String funId);

    SysCodeRulePO getByFunId(String funId);
}
