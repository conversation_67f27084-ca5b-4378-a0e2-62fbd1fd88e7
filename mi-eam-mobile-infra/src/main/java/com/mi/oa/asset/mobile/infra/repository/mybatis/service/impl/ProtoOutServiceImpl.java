package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.service.ProtoInBoService;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoOutSapDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.Sap107JsonDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.SapCustomVO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoOutMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
@Service
public class ProtoOutServiceImpl extends ServiceImpl<ProtoOutMapper, ProtoOutPO> implements ProtoOutService{

    @Autowired
    private ProtoOutDetService protoOutDetService;
    @Autowired
    private ProtoInBoService protoInBoService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private SapService sapService;

    @Autowired
    private ProtoApplyService protoApplyService;

    @Autowired
    private ProtoApplyMatService protoApplyMatService;

    @Override
    public List<ProtoOutPO> getOutPO(List<String> outCodes, OutStateEnum outStateEnum) {
        return this.baseMapper.selectList(Wrappers.<ProtoOutPO>lambdaQuery()
                .in(ProtoOutPO::getOutCode, outCodes)
                .eq(ProtoOutPO::getOutStatus, outStateEnum.getState()));
    }

    @Override
    public List<ProtoOutPO> findListByParam(ProtoOutPO po) {
        QueryWrapper<ProtoOutPO> queryWrapper = new QueryWrapper(po);
        return this.list(queryWrapper);
    }

    @Override
    public List<ProtoOutPO> getProtoOutPushSAPList() {
        LambdaQueryWrapper<ProtoOutPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProtoOutPO::getOutType, OutTypeEnum.FAST_OUT.getType());
        queryWrapper.ne(ProtoOutPO::getSapRetType, SapRetEnum.SUCCESS.getKey());
        queryWrapper.in(ProtoOutPO::getOutStatus, OutStateEnum.TO_BE_CONFIRMED.getState(), OutStateEnum.ISSUED.getState());
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SapCustomVO pushSAP(String outId, String userName) {
        ProtoOutPO outPO = baseMapper.selectById(outId);
        if (null == outPO){
            return null;
        }
        if (!OutTypeEnum.FAST_OUT.getType().equals(outPO.getOutType())){
            return null;
        }
        if (SapRetEnum.SUCCESS.getKey().equals(outPO.getSapRetType())){
            return null;
        }
        // 如果有存在没用抛送SAP收货数据时 则发放时也不抛
        if (protoOutDetService.countNotPushSap(outPO.getOutId()) > 0){
            return null;
        }
        ProtoApplyPO applyPO = protoApplyService.getByApplyCode(outPO.getApplyCode());
        if (null == applyPO){
            return null;
        }
        // 领用申请单未抛送SAP
        if (!SapRetEnum.SUCCESS.getKey().equals(applyPO.getSapRetType())){
            return null;
        }
        String makeCn = outPO.getMakeCn();
        if(StringUtils.isBlank(makeCn)){
            makeCn = protoInBoService.getMakeCn(outPO.getApplyCode());
            if(StringUtils.isBlank(makeCn)){
                return null;
            }
            this.updateMakeCn(outPO.getOutId(), makeCn);
        }

        List<ProtoOutDetPO> outDetPOList = protoOutDetService.listByOutId(outId);
        // 更新发放子表和台账 领用申请单行号
        this.updateApplyItemRow(applyPO, outDetPOList);

        String bsart = outPO.getBsart();
        if (StringUtils.isBlank(bsart)) {
            //bsart为空说明是无ASN收货，取发放明细第一条的bsart
            bsart = outDetPOList.get(0).getBsart();
        }
        Sap107JsonDTO sapMain = new Sap107JsonDTO();
        String outCode = outPO.getOutCode();
        final String oldOutCode = outCode;
        String apply_code = outPO.getApplyCode();
        if (CommonConstant.ApplyUserTypeEnum.PROVIDER.getValue().equals(outPO.getApplyUserType())) {
            sapMain.setBusiType(CommonConstant.SapBusiTypeEnum.MRP_PROVIDE.getType());
        } else {
            sapMain.setBusiType(CommonConstant.SapBusiTypeEnum.PROVIDE.getType());
        }
        sapMain.setMidocNum(apply_code);
        sapMain.setVndocNum(apply_code);

        sapMain.setSapFactory(makeCn.split("-")[0]);
        sapMain.setBsart(bsart);
        sapMain.setWareType("TP");
        // 无ASN 更新单号 修复前期make_cn为空造成的数据错误
        if (StringUtils.startsWith(outCode, SapBatchIdEnum.NO_ASN.getBillType())) {
            if (SapFactoryEnum.YI_ZHUANG.getSapFactory().equals(sapMain.getSapFactory())) {
                outCode = commonService.getBatchIdUnique(SapBatchIdEnum.YI_ZHUANG);
            } else if (SapFactoryEnum.CHANG_PING.getSapFactory().equals(sapMain.getSapFactory())) {
                if (SrmPurchaseTypeEnum.ZDB.getCode().equals(bsart)) {
                    outCode = commonService.getBatchIdUnique(SapBatchIdEnum.ASN_RECEIVE_GOODS);
                } else {
                    outCode = commonService.getBatchIdUnique(SapBatchIdEnum.CHANG_PING);
                }
            } else if (SapFactoryEnum.CHANG_PING2.getSapFactory().equals(sapMain.getSapFactory())) {
                outCode = commonService.getBatchIdUnique(SapBatchIdEnum.CHANG_PING2);
            } else if (SapFactoryEnum.ROBOT.getSapFactory().equals(sapMain.getSapFactory())) {
                outCode = commonService.getBatchIdUnique(SapBatchIdEnum.ROBOT);
            } else {
                outCode = commonService.getBatchIdUnique(SapBatchIdEnum.ASN_RECEIVE_GOODS);
            }
            // 更新发放单号
            updateOutCode(outId, outCode);
            // 更新台账 出库单单号
            protoCardService.updateOutCode(oldOutCode, outCode);
        }
        sapMain.setBatchId(outCode);
        // 添加SAP明细
        List<ProtoOutSapDTO> protoOutDetList = protoOutDetService.getProtoOutDetSAPList(outId);
        List<Sap107JsonDTO.SapItemDTO> sapItems = new ArrayList<>(protoOutDetList.size());
        int batchRow = 0;
        for (ProtoOutSapDTO protoOutDet : protoOutDetList) {
            Sap107JsonDTO.SapItemDTO sapItem = new Sap107JsonDTO.SapItemDTO();
            batchRow += 10;
            String outSapItemRowNo = protoOutDet.getOutSapItemRowNo();
            if (StringUtils.isEmpty(outSapItemRowNo)){
                outSapItemRowNo = batchRow+"";
            }
            sapItem.setBatchRow(outSapItemRowNo);
            sapItem.setMidocRow(protoOutDet.getSapItemRowNo());
            sapItem.setMaterialOne(protoOutDet.getSkuCode());

            // 添加领用申请单信息
            sapItem.setVndocNum(apply_code);
            sapItem.setVndocRow(protoOutDet.getSapItemRowNo());

            sapItem.setQuantityOne(protoOutDet.getPushCount()+"");
            sapItem.setMiProjOne(applyPO.getProjectCode());
            sapItem.setCommItm(protoOutDet.getOrderCode());
            sapItems.add(sapItem);
            // 保存明细出库SAP行号
            protoOutDetService.updateDetOutSapRow(outId, protoOutDet.getSkuCode(), protoOutDet.getOrderCode(), outSapItemRowNo);
        }
        sapMain.setItemDTOList(sapItems);
        // 推送SAP，107接口
        SapCustomVO sapCustomVO = sapService.sap107Json(sapMain, "proto_out", outId, outCode, "工程机快速发放", userName, outPO.getMrpType());
        Boolean isSuccess = sapCustomVO.getSuccess();
        // 记录SAP是否抛送成功
        this.updatePushSap(outId, isSuccess);
        // 保存台账出库SAP行号
        for (ProtoOutDetPO detPO : outDetPOList){
            protoCardService.updateOutSapItemRowNoBySn(detPO.getDeviceCode(), detPO.getOutSapItemRowNo());
        }
        return sapCustomVO;
    }

    /**
     * 更新发放子表和台账 领用申请单行号
     * @param applyPO
     * @param outDetPOList
     */
    private void updateApplyItemRow(ProtoApplyPO applyPO, List<ProtoOutDetPO> outDetPOList){
        List<ProtoApplyMatPO> protoApplyDetSAPList = protoApplyMatService.getProtoApplyDetSAPList(applyPO.getApplyId());
        List<ProtoOutDetPO> applyNotItemRowList = outDetPOList.stream()
                .filter(o -> StringUtils.isBlank(o.getSapItemRowNo())).collect(Collectors.toList());
        // 料号-领用申请行号
        Map<String, String> skuItemRowMap = new HashMap<>();

        for (ProtoApplyMatPO matPO : protoApplyDetSAPList){
            skuItemRowMap.put(matPO.getSkuCode(), matPO.getSapItemRowNo());
            for (ProtoOutDetPO detPO : applyNotItemRowList){
                if (detPO.getSkuCode().equals(matPO.getSkuCode())){
                    detPO.setSapItemRowNo(matPO.getSapItemRowNo());
                }
            }
        }

        for (ProtoOutDetPO detPO : applyNotItemRowList){
            for (ProtoApplyMatPO matPO : protoApplyDetSAPList){
                if (detPO.getSkuCode().equals(matPO.getSapItemRowNo())){
                    detPO.setSapItemRowNo(matPO.getSapItemRowNo());
                }
            }
        }
        // 更新SAP收货行号
        protoOutDetService.updateBatchById(applyNotItemRowList);
        // 更新台账领用申请单行号
        List<String> snList = outDetPOList.stream().map(ProtoOutDetPO::getDeviceCode).collect(Collectors.toList());
        List<ProtoCardPO> cardPOList = protoCardService.listByDevices(snList);
        for (ProtoCardPO cardPO : cardPOList){
            cardPO.setApplyItemid(skuItemRowMap.get(cardPO.getSkuCode()));
        }
        protoCardService.updateBatchById(cardPOList);
    }

    @Override
    public void updatePushSap(String outId, boolean success) {
        LambdaUpdateWrapper<ProtoOutPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoOutPO::getOutId, outId);
        String sapRetType = (success ? SapRetEnum.SUCCESS.getKey() : SapRetEnum.ERROR.getKey());
        updateWrapper.set(ProtoOutPO::getSapRetType, sapRetType);
        updateWrapper.set(ProtoOutPO::getPostingDate, new Date());
        this.update(updateWrapper);
    }

    @Override
    public void updateCancelStatus() {
        LambdaUpdateWrapper<ProtoOutPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProtoOutPO::getOutStatus, CommonConstant.RecordStatus.INVALID.getStatus());
        wrapper.eq(ProtoOutPO::getOutStatus, CommonConstant.RecordStatus.UNCOMMITTED.getStatus());
        this.update(wrapper);
    }

    private void updateMakeCn(String outId,String makeCn) {
        LambdaUpdateWrapper<ProtoOutPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProtoOutPO::getMakeCn, makeCn);
        wrapper.eq(ProtoOutPO::getOutId,outId);
        this.update(wrapper);
    }
    @Override
    public void timeOutAutoConfirm() {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.COMMON_PATTERN);
        Date date = new Date();
        String dateStr = dateTimeFormatter.format(LocalDateTime.now().plusDays(-3));
        LambdaQueryWrapper<ProtoOutPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoOutPO::getOutStatus, OutStateEnum.TO_BE_CONFIRMED.getState());
        wrapper.le(ProtoOutPO::getOutDate, dateStr);
        List<ProtoOutPO> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)){
            return;
        }
        for (ProtoOutPO outPO : list){
            String applyCode = outPO.getApplyCode();
            outPO.setOutStatus(OutStateEnum.ISSUED.getState());
            // 根据领用申请单获取所有待确认的发放单
            Integer count = this.countByApplyCodeAndOutStatus(OutStateEnum.TO_BE_CONFIRMED.getState(), applyCode);
            // 如果该领用单仍有发放单处于待确认状态，不更新单据状态；
            if (count == 0){
                continue;
            }
            ProtoApplyPO applyPO = protoApplyService.getByApplyCode(applyCode);
            if (null == applyPO){
                continue;
            }

            // 存在当天创建的新发放单，需要再次校验是否还有待确认的发放单
            QueryWrapper<ProtoOutPO> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("apply_code");
            queryWrapper.eq("out_status", OutStateEnum.TO_BE_CONFIRMED.getState());
            queryWrapper.eq("apply_code", applyCode);
            queryWrapper.gt("out_date", dateStr);
            List<ProtoOutPO> protoOutPOS = this.list(queryWrapper);
            String applyStatus = "";
            if (!CollectionUtils.isEmpty(protoOutPOS) && protoOutPOS.size() > 0) {
                applyStatus = CommonConstant.ProtoCardApplyStatus.TOBECONFIRMED.getType();
            } else {
                // 明细全部发放完，更新主表的单据状态为已完成  没发放完则更新发放中
                Integer protoApplyCount = protoApplyMatService.countByApplyIdAndNotFinish(applyPO.getApplyId());
                applyStatus = protoApplyCount == 0 ?
                        CommonConstant.ProtoCardApplyStatus.SHIPMENTCOMPLETED.getType() : CommonConstant.ProtoCardApplyStatus.SHIPPING.getType();
            }
            protoApplyService.updateApplyStatusByApplyId(applyStatus, applyPO.getApplyId());

            //更新出库表确认收货时间
            if (applyStatus.equals(CommonConstant.ProtoCardApplyStatus.SHIPMENTCOMPLETED.getType())) {
                outPO.setCheckDate(date);
            }
        }
        this.updateBatchById(list);
    }

    @Override
    public Integer countByApplyCodeAndStatus(String applyCode, String outStatus) {
        LambdaQueryWrapper<ProtoOutPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoOutPO::getApplyCode, applyCode);
        wrapper.eq(ProtoOutPO::getOutStatus, outStatus);
        return this.count(wrapper);
    }

    @Override
    public List<ProtoOutPO> listByApplyCode(String applyCode) {
        LambdaQueryWrapper<ProtoOutPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoOutPO::getApplyCode, applyCode)
                .in(ProtoOutPO::getOutStatus, OutStateEnum.TO_BE_CONFIRMED.getState()
                        , OutStateEnum.ISSUED.getState(), OutStateEnum.DUMPED_SAP.getState());
        return this.list(wrapper);
    }

    @Override
    public ProtoOutPO getByOutCode(String outCode) {
        LambdaQueryWrapper<ProtoOutPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoOutPO::getOutCode, outCode);
        return this.getOne(wrapper);
    }

    /**
     * 更新单号
     * @param outId
     * @param outCode
     */
    private void updateOutCode(String outId, String outCode){
        LambdaUpdateWrapper<ProtoOutPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoOutPO::getOutId, outId);
        updateWrapper.set(ProtoOutPO::getOutCode, outCode);
        this.update(updateWrapper);
    }

    /**
     * 通过单号和状态查询数据
     * @param outStatus
     * @param applyCode
     * @return
     */
    private Integer countByApplyCodeAndOutStatus(String outStatus, String applyCode){
        LambdaQueryWrapper<ProtoOutPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoOutPO::getOutStatus, outStatus);
        wrapper.eq(ProtoOutPO::getApplyCode, applyCode);
        return baseMapper.selectCount(wrapper);
    }
}
