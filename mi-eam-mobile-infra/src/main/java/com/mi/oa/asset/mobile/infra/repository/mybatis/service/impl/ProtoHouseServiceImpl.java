package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoHousePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoHouseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoHouseService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/12/7
 */
@Service
public class ProtoHouseServiceImpl extends ServiceImpl<ProtoHouseMapper, ProtoHousePO> implements ProtoHouseService {

    @Override
    public String getHoseNameByCode(String houseCode) {
        LambdaQueryWrapper<ProtoHousePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoHousePO::getHouseCode, houseCode)
                .last("limit 1");
        ProtoHousePO housePO = this.getOne(wrapper);
        return (housePO == null ? "" : housePO.getHouseName());
    }

    @Override
    public List<ProtoHousePO> listByHouseCode(List<String> houseCodeList) {
        if (CollectionUtils.isEmpty(houseCodeList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoHousePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoHousePO::getHouseCode, houseCodeList);
        return this.list(wrapper);
    }
}
