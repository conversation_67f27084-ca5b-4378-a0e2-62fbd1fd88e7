package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.dto.FunRepeatValDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.FunBasePO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * table_name : fun_base
 * <AUTHOR>
 * @date 2022/01/07/10:56
 */
public interface FunBaseMapper extends BaseMapper<FunBasePO> {

    @Select("select fun_col.col_code colCode, fun_col.col_name colName from fun_col, fun_colext where fun_col.col_id = fun_colext.col_id and fun_colext.is_repeatval = '1' and fun_col.fun_id = #{funId}")
    List<FunRepeatValDTO> getRepeatCol(@Param("funId") String funId);

    int getRepeatValue(@Param("tableName") String tableName, @Param("colCode") String colCode,
                       @Param("colName") String colName, @Param("keyId") String keyId,
                       @Param("fkColName") String fkColName, @Param("fkValue") String fkValue);


    void updateCommonValue(@Param("tableName")String tableName, @Param("valueMap") Map<String, String> valueMap, @Param("sKeyValue") String sKeyValue, @Param("pkCol") String pkCol);

    void insertCommonValue(@Param("tableName") String tableName, @Param("valueMap") Map<String, String> valueMap);

    Integer getHaveAuditCount(@Param("tableName") String tableName, @Param("auditCol")String auditCol, @Param("auditValue")String auditValue, @Param("pkCol")String pkCol, @Param("keyId")String keyId);

    Integer getCount(@Param("tableName") String tableName, @Param("keyCol")String keyCol, @Param("keyValue")String keyValue);
}