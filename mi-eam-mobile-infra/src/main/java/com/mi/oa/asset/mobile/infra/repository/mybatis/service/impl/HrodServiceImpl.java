package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.mi.oa.asset.mobile.infra.dto.hrod.HrodEmployeeConditionDTO;
import com.mi.oa.asset.mobile.infra.dto.hrod.HrodEmployeeVO;
import com.mi.oa.asset.mobile.infra.remote.sdk.HrodClient;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.HrodService;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/9/10 20:00
 */

@Service
@ConfigurationProperties(prefix = "whitelist")
public class HrodServiceImpl implements HrodService {

    @Getter
    @Setter
    private List<String> emailWhiteList; // 邮件白名单

    @Getter
    @Setter
    private List<HrodEmployeeVO> employeeWhiteList; // 员工白名单

    @Autowired
    private HrodClient hrodClient;


    @Override
    public HrodEmployeeVO getEmpByUserName(String userName) {
        HrodEmployeeConditionDTO condition = HrodEmployeeConditionDTO.builder().userName(userName).build();
        condition.setEmpIds(Arrays.asList("id1","id2"));
        condition.setSearchEmpId("searchempid");

        return hrodClient.getEmployeeByUserName(condition);
    }

    @Override
    public HrodEmployeeVO getEmpByEmpId(String empId) {
        HrodEmployeeConditionDTO condition = HrodEmployeeConditionDTO.builder().empId(empId).build();

        return hrodClient.getEmployee(condition);
    }

}


