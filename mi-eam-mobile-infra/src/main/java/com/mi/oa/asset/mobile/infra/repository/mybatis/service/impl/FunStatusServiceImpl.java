package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.FunStatusPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.FunStatusMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.FunStatusService;
import org.springframework.stereotype.Service;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/18
 */
@Service
public class FunStatusServiceImpl extends ServiceImpl<FunStatusMapper, FunStatusPO> implements FunStatusService {


    @Override
    public FunStatusPO getByFunId(String funId) {
        LambdaQueryWrapper<FunStatusPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FunStatusPO::getFunId, funId);
        return this.getOne(queryWrapper);
    }
}
