package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDeviceScanPO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/23
 */
public interface ProtoDeviceScanService extends IService<ProtoDeviceScanPO> {

    /**
     * 通过盘点计划id查询
     * @param planId
     * @return
     */
    List<ProtoDeviceScanPO> listByPlanId(String planId);
}
