package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.dto.AsnOrderDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderDetPO;
import org.apache.ibatis.annotations.Param;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/2
 */
public interface ProtoAsnOrderDetMapper extends BaseMapper<ProtoAsnOrderDetPO> {

    AsnOrderDTO getMachAsn(@Param("sn") String sn);
}
