package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSecondCardPO;

/**
 * table_name : proto_second_card
 * <AUTHOR>
 * @date 2022/01/07/02:08
 */
public interface ProtoSecondCardMapper extends BaseMapper<ProtoSecondCardPO> {
    /**
     *
     * @mbg.generated
     */
    ProtoSecondCardPO selectByPrimaryKey(String deviceId);
}