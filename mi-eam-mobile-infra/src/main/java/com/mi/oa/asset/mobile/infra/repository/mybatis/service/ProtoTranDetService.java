package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.application.dto.tran.TranSapSplitDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranDetPO;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
public interface ProtoTranDetService extends IService<ProtoTranDetPO> {

    /**
     * 查询转移接口和107接口都失败的数据
     * 根据项目、成本中心、样机生产地进行拆单
     *
     * @param tranId 转移单id
     * @return
     */
    List<ProtoTranDetPO> getDetGroupBy(String tranId);

    /**
     * 根据tranId查询全部数据
     *
     * @param tranId
     * @return
     */
    List<ProtoTranDetPO> getByTranId(String tranId);

    List<ProtoTranDetPO> findListByParam(ProtoTranDetPO param);

    /**
     * 查询所有转移接口成功 107接口失败的数据
     * @param tranId
     * @return
     */
    List<ProtoTranDetPO> list107FailByTranId(String tranId);

    /**
     * 查询详情
     * @param tranId 主单id
     * @param assetNewold 新旧属性
     * @param projectCode 项目编号
     * @param centerCode 成本中心
     * @param makeCn 样机生产地
     * @return
     */
    List<ProtoTranDetPO> listByTranInfo(String tranId, String assetNewold, String projectCode, String centerCode, String makeCn);

    /**
     * 根据项目、成本中心、样机生产地进行拆单
     * @param list
     * @return
     */
    Map<TranSapSplitDTO, List<ProtoTranDetPO>> splitTranDetMap(List<ProtoTranDetPO> list);

    /**
     * 通过转移Id查询转移详情
     * @param tranId
     * @return
     */
    List<ProtoTranDetPO> listByTranId(String tranId);

    /**
     * 更新样机生产地和成本中心
     * @param tranIdList
     */
    void updateMakeCnByTranIds(List<String> tranIdList);


    /**
     * 处理领用申请单行号
     * @param tranIdList
     */
    void updateUpdateApplyItemid(List<String> tranIdList);
}
