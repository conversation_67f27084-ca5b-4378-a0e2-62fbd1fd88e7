package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.dto.srm.ProtoAsnOrderListDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInPO;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/1
 */

public interface ProtoAsnOrderService extends IService<ProtoAsnOrderPO> {

    /**
     * 保存主单和子单数据
     * @param orderDTO
     */
    void saveMainOrder(ProtoAsnOrderListDTO.ProtoAsnOrderDTO orderDTO);

    /**
     *
     * @param orderCode
     * @return
     */
    ProtoAsnOrderPO getAsnOrderByCode(String orderCode);

    /**
     * 更新主单的状态
     * @param asnNo
     * @param orderStatus 0：未收货 1：部分收货 2：完全收货 5: 已取消
     */
    void updateAsnOrderStatus(String asnNo, String orderStatus);

    /**
     * 反馈收货信息到ASN订单收货
     * @param protoInPO
     * @param orderCode
     */
    void feedBackReceivingToAsnOrder(ProtoInPO protoInPO, String orderCode);

    /**
     * 校验收货数量不能大于发货数量
     * @param orderCode
     */
    void checkOutNum(String orderCode);


}
