package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.mi.oa.asset.mobile.common.enums.ErrorCodeEnum;
import com.mi.oa.asset.mobile.infra.common.utils.TemplateUtil;
import com.mi.oa.asset.mobile.infra.common.utils.XmlUtil;
import com.mi.oa.asset.mobile.infra.config.SoapConfig;
import com.mi.oa.asset.mobile.infra.dto.idm.ReportLineDTO;
import com.mi.oa.asset.mobile.infra.remote.sdk.IdmClient;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.EmployeeService;
import com.mi.oa.asset.mobile.utils.HttpClientUtil;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/26
 */
@Service
@Slf4j
public class EmployeeServiceImpl implements EmployeeService {

    @Autowired
    private SoapConfig.LeaveCheckPhoneConfig leaveCheckPhoneConfig;

    @Autowired
    private TemplateUtil templateUtil;

    @Autowired
    private IdmClient idmClient;

    @Override
    public Boolean leaveCheckPhoneNum(String emplId) {
        Map<String, String> params = new HashMap<>();
        params.put("account", leaveCheckPhoneConfig.getAccount());
        params.put("password", leaveCheckPhoneConfig.getPassword());
        params.put("emplId", emplId);
        String soapMsg = templateUtil.compose("leave-check-phone.ftl", params);
        String xmlRes = HttpClientUtil.doSoapPost(leaveCheckPhoneConfig.getUrl(), soapMsg, leaveCheckPhoneConfig.getAction());
        log.info("leave_check_phone_num_emplId:{}, response:{}", emplId, xmlRes);
        if (StringUtils.isBlank(xmlRes)){
            throw new BizException(ErrorCodeEnum.UNKNOWN_ERROR);
        }
        Object data =  XmlUtil.xmlToMap(xmlRes, "root").get("STATUS");
        if (data == null){
            throw new BizException(ErrorCodeEnum.UNKNOWN_ERROR);
        }
        return "Y".equals(String.valueOf(data));
    }

    @Override
    public List<ReportLineDTO> findReportLineByUserName(String userName) {
        try {
            return idmClient.findReportLineByUserName(userName);
        } catch (Exception e) {
            return null;
        }
    }
}
