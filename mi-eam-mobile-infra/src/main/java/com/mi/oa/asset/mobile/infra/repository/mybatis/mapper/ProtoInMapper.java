package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.dto.BackConfirmDto;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * table_name : proto_in
 * <AUTHOR>
 * @date 2021/12/10/03:30
 */
public interface ProtoInMapper extends BaseMapper<ProtoInPO> {
    @Select("select p_in.in_id as inId,p_in.in_status as inStatus, det.det_id as detId, det.is_back as isBack from proto_in p_in, proto_in_det det " +
            "where det.device_code = #{deviceCode} and det.in_id = p_in.in_id and p_in.in_type = '2' and p_in.in_status in ('2', '3') " +
            "order by p_in.in_status")
    List<BackConfirmDto> getbackConfirmByDeviceCode(@Param("deviceCode") String deviceCode);

    List<Map<String, Object>> getEntryDate(@Param("snList") List<String> snList);
}