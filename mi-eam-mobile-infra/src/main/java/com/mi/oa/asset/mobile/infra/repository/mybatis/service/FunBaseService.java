package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.dto.FunRepeatValDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.FunBasePO;

import java.util.List;
import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/7
 */
public interface FunBaseService extends IService<FunBasePO> {

    List<FunRepeatValDTO> getRepeatCol(String funId);

    /**
     * 查询是否有重复的值
     * @param tableName
     * @param colCode
     * @param colName
     * @param keyId
     * @param fkColName
     * @param fkValue
     * @return
     */
    int getRepeatValue(String tableName, String colCode, String colName, String keyId, String fkColName, String fkValue);

    /**
     * 根据主键更新值
     * @param valueMap
     * @param sKeyValue
     * @param pkCol
     */
    void updateCommonValue(String tableName, Map<String, String> valueMap, String sKeyValue, String pkCol);

    /**
     * 通用插入操作
     * @param tableName 表名
     * @param valueMap
     */
    void insertCommonValue(String tableName, Map<String, String> valueMap);

    /**
     * 根据主键id和状态值查询数量
     * @param tableName 表名
     * @param auditCol 状态字段名
     * @param auditValue 状态字段值
     * @param pkCol 主键名
     * @param keyId 主键值
     * @return
     */
    Integer getHaveAuditCount(String tableName, String auditCol, String auditValue, String pkCol, String keyId);

    /**
     * 查询数量
     * @param tableName 表名
     * @param keyCol 字段名
     * @param keyValue 字段值
     * @return
     */
    Integer getCount(String tableName, String keyCol, String keyValue);
}
