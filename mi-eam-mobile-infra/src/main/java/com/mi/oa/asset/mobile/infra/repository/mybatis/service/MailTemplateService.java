package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MailTemplatePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MailTemplateParamPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranPO;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
public interface MailTemplateService extends IService<MailTemplatePO> {

    /**
     * 通过模板标识获取模板
     * @param templateTag
     * @return
     */
    MailTemplatePO getByTemplateTag(String templateTag);

    /**
     * 通过模板标识和模板参数获取模板内容
     * @param templateTag
     * @param valueMap
     * @return
     */
    String getMailContent(String templateTag, Map<String, String> valueMap);
}
