package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysAttachPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysAttachMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysAttachService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/7/11
 */
@Service
public class SysAttachServiceImpl extends ServiceImpl<SysAttachMapper, SysAttachPO> implements SysAttachService {

    @Override
    public void updateAttachField(String attachField, String funId, String dataId, String attachPath) {
        LambdaUpdateWrapper<SysAttachPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(SysAttachPO::getAttachField, attachField)
                .eq(SysAttachPO::getFunId, funId)
                .eq(SysAttachPO::getDataId, dataId)
                .eq(SysAttachPO::getAttachPath, attachPath);
        this.update(wrapper);
    }

    @Override
    public List<SysAttachPO> getByTableDataId(String tableName, String dataId) {
        LambdaQueryWrapper<SysAttachPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysAttachPO::getTableName, tableName)
                .eq(SysAttachPO::getDataId, dataId);
        return this.list(wrapper);
    }
}
