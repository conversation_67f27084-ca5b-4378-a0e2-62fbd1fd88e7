package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPriceDetPO;

/**
 * table_name : proto_price_det
 * <AUTHOR>
 * @date 2022/06/22/03:43
 */
public interface ProtoPriceDetMapper extends BaseMapper<ProtoPriceDetPO> {
    /**
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     *
     * @mbg.generated
     */
    int insert(ProtoPriceDetPO record);

    /**
     *
     * @mbg.generated
     */
    int insertSelective(ProtoPriceDetPO record);
}