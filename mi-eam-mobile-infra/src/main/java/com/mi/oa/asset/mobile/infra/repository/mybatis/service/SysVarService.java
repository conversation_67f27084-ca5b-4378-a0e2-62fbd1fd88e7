package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysVarPO;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/10 10:24
 */
public interface SysVarService extends IService<SysVarPO> {
    SysVarPO findByVarCode(String varCode);

    String findValByVarCode(String varCode);

    String findValByVarCode(String varCode, String defaultVarCode);

    Boolean findBoolValByVarCode(String varCode);
}
