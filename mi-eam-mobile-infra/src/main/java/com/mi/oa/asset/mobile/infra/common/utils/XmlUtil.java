package com.mi.oa.asset.mobile.infra.common.utils;

import com.mi.oa.asset.mobile.utils.GsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.json.XML;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/11/1 17:57
 */

public class XmlUtil {

    public static Map<String, Object> xmlToMap(String xml, String extractNode) {
        if (StringUtils.isNotBlank(extractNode)) {
            xml = extractNestedNode(xml, extractNode);
        }

        JSONObject jsonObject = XML.toJSONObject(xml);

        if(StringUtils.isNotBlank(extractNode)) {
            jsonObject = jsonObject.getJSONObject(extractNode);
        }

        return GsonUtil.toMap(jsonObject.toString());
    }

    /**
     * 正则提取 xml 中的指定节点
     * 请务必清楚被提取的 xml 的结构
     * 如果有多个相同名字的节点，只能提取第一个
     * 定制化功能，慎用！！！
     * @param xml
     * @param nodeName
     * @return
     */
    public static String extractNestedNode(String xml, String nodeName) {
        String start = "<" + nodeName + "[\\s\\S]*>";
        String end = "</" + nodeName + ">";

        Pattern pattern = Pattern.compile(start + "([\\s\\S]*)" + end);
        Matcher m = pattern.matcher(xml);

        if (m.find()) {
            return m.group();
        }

        return null;
    }
}


