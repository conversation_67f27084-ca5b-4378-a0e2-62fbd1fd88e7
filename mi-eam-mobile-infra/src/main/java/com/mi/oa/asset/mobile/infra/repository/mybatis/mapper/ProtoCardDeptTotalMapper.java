package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardDeptTotalPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * table_name : proto_card_dept_total
 * <AUTHOR>
 * @date 2022/03/31/11:30
 */
public interface ProtoCardDeptTotalMapper extends BaseMapper<ProtoCardDeptTotalPO> {

    /**
     * 按照部门维度进行统计
     * @param deptLevel
     * @return
     */
    List<ProtoCardDeptTotalPO> statisticsByDeptLevel(@Param("deptLevel") Integer deptLevel, @Param("mrpType") String mrpType);

    @Select("select distinct mrp_type from proto_card_dept_total")
    List<String> getMrpTypeList();
}