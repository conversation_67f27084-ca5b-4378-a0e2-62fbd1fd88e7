package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmProjectVO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectFactoryPO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/14
 */
public interface ProtoProjectFactoryService extends IService<ProtoProjectFactoryPO> {

    /**
     * 保存项目
     * @param factoryList
     * @param cfgId 配置Id
     */
    void saveProjectFactory(List<MdmProjectVO.TrialProductionFactoryDTO> factoryList, String cfgId);

    /**
     * 通过项目Id删除
     * @param projectId
     */
    void deleteByCfgId(String projectId);

    Integer countByCfgId(String cfgId);

    List<ProtoProjectFactoryPO> listByCfgId(String cfgId);
}
