package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardSkuTotalPO;

import java.util.List;

/**
 * table_name : proto_card_sku_total
 * <AUTHOR>
 * @date 2022/03/24/06:51
 */
public interface ProtoCardSkuTotalMapper extends BaseMapper<ProtoCardSkuTotalPO> {

    List<ProtoCardSkuTotalPO> countCardSku();

}