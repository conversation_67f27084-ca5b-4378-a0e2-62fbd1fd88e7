package com.mi.oa.asset.mobile.infra.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/09 14:16
 */
@Component
@ConfigurationProperties(prefix = "message.sms")
public class MessageConfig {
    @Getter
    @Setter
    private List<String> tels;
}
