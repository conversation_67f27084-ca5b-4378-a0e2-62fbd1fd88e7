package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardProjectTotalPO;

import java.util.List;

/**
 * table_name : proto_card_project_total
 * <AUTHOR>
 * @date 2022/03/24/06:50
 */
public interface ProtoCardProjectTotalMapper extends BaseMapper<ProtoCardProjectTotalPO> {

    List<ProtoCardProjectTotalPO> countCardProject();

}