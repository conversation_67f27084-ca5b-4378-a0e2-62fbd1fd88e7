package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmProcessCompletedDTO;
import com.mi.oa.asset.mobile.application.dto.bpm.ProtoSleeveBizDTO;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.common.enums.BmpV3ProcessEnum;
import com.mi.oa.asset.mobile.common.enums.TranAuditEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleeveInfoPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleeveMatPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleevePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoSleeveMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.BpmExeLogService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoSleeveMatService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoSleeveService;
import com.mi.oa.asset.mobile.utils.DateUtil;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import com.mi.oa.infra.oaucf.bpm.enums.ProcessInstanceStatus;
import com.mi.oa.infra.oaucf.bpm.enums.UserTaskOperation;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 工程机保密套拆除
 *
 * <AUTHOR>
 * @date 2022/1/29 10:17
 */
@Slf4j
@Service
public class ProtoSleeveServiceImpl extends ServiceImpl<ProtoSleeveMapper, ProtoSleevePO> implements ProtoSleeveService {

    private static final String FUNID = "proto_sleeve";
    private static final String ORDER_NAME = "工程机保密套拆除-BPM回调";
    private static final Gson GSON = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();

    @Autowired
    private BpmExeLogService bpmExeLogService;

    @Autowired
    private ProtoSleeveMatService protoSleeveMatService;

    @Autowired
    private ProtoCardService protoCardService;

    /**
     * 根据主键查询
     *
     * @param keyId
     * @return
     */
    @Override
    public ProtoSleevePO findProtoSleeve(String keyId) {
        QueryWrapper<ProtoSleevePO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProtoSleevePO::getSleeveId, keyId);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    /**
     * 连表查询工程机保密套拆除和物料信息
     *
     * @param sleeveId
     * @return
     */
    @Override
    public List<ProtoSleeveInfoPO> selectSleeveInfoById(String sleeveId) {
        return baseMapper.selectSleeveInfoById(sleeveId);
    }

    /**
     * 更新领用方类型
     *
     * @param applyUserType
     * @param keyId
     * @param userId
     * @return
     */
    @Override
    public boolean updateType(String applyUserType,String keyId,String userId){
        LambdaUpdateWrapper<ProtoSleevePO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoSleevePO::getSleeveId, keyId)
                .set(ProtoSleevePO::getApplyUserType, applyUserType)
                .set(ProtoSleevePO::getModifyUserid, userId)
                .set(ProtoSleevePO::getModifyDate, DateUtil.getTodaySec());
        return this.update(updateWrapper);
    }

    /**
     * 修改流程信息
     *
     * @param sleeveId
     * @param auditing
     * @param modifyDate
     * @param businessKey
     * @return
     */
    @Override
    public boolean updProtoSleeveStatusBySleeveId(String sleeveId, String auditing, String modifyDate, String businessKey){
        LambdaUpdateWrapper<ProtoSleevePO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoSleevePO::getSleeveId, sleeveId)
                .set(ProtoSleevePO::getAuditing, auditing)
                .set(ProtoSleevePO::getModifyDate, modifyDate)
                .set(ProtoSleevePO::getBusinessKey, businessKey);
        return this.update(updateWrapper);
    }

    /**
     * 修改流程信息
     *
     * @param sleeveId
     * @param auditing
     * @param modifyDate
     * @return
     */
    @Override
    public boolean updProtoSleeveStatusBySleeveId(String sleeveId, String auditing, String modifyDate){
        LambdaUpdateWrapper<ProtoSleevePO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoSleevePO::getSleeveId, sleeveId)
                .set(ProtoSleevePO::getAuditing, auditing)
                .set(ProtoSleevePO::getModifyDate, modifyDate);
        return this.update(updateWrapper);
    }

    /**
     * 根据bpm唯一标识来查询工程机保密套拆除信息
     *
     * @param businessKey
     * @return
     */
    @Override
    public ProtoSleevePO findByBusinessKey(String businessKey){
        QueryWrapper<ProtoSleevePO> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(ProtoSleevePO::getBusinessKey,businessKey);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bpmCallback(BpmProcessCompletedDTO param) {
        log.info("========正在处理工程机保密套拆除Bpm回调事件");

        if (Objects.isNull(param.getFormData())) {
            log.warn(String.format(BmpV3ProcessEnum.PROTO_TRAN_APPLY.getProcessInstanceName(), "回调无表单数据"));
            bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, null,
                    null, null, false, "param参数为空", param);
            throw new BizException(ApplicationErrorCodeEnum.PARAM_INVALID_ERROR, "param中表单为空");
        }

        ProtoSleeveBizDTO protoSleeveBizDTO = GSON.fromJson(GsonUtil.toJsonString(param.getFormData()), ProtoSleeveBizDTO.class);
        ProtoSleevePO protoSleevePO = this.getOne(new LambdaQueryWrapper<ProtoSleevePO>().eq(ProtoSleevePO::getBusinessKey,
                param.getBusinessKey()));

        if (null == protoSleevePO) {
            log.debug("业务数据找不到【{0}】", protoSleeveBizDTO.getApplyCode());
            bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, null, null,
                    null, false, "找不到单号为：" + protoSleeveBizDTO.getApplyCode() + "的业务数据。businessKey:{}" + param.getBusinessKey(), protoSleeveBizDTO);
            return false;
        }

        // 流程结束
        ProcessInstanceStatus processStatus = param.getProcessInstanceStatus();
        if (processStatus == ProcessInstanceStatus.COMPLETED) {
            doTranProcessCompleted(protoSleevePO, protoSleeveBizDTO);
            bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, protoSleevePO.getSleeveId(), protoSleevePO.getApplyCode(),
                    protoSleevePO.getApplyUserName(), true, "工程机保密套拆除Bpm审批完成！", param);
        }

        // 流程终止（撤回）
        if (processStatus == ProcessInstanceStatus.TERMINATED) {
            doTranProcessRejectedOrTerminated(protoSleevePO, protoSleeveBizDTO, "TERMINATED");
            bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, protoSleevePO.getSleeveId(), protoSleevePO.getApplyCode(),
                    protoSleevePO.getApplyUserName(), true, "工程机保密套拆除Bpm审批被终止（撤回）！", param);
        }

        // 流程审批事件
        String status = param.getVariables().get("status").toString();
        if (StringUtils.isNotBlank(status)) {
            UserTaskOperation userTaskOperation = UserTaskOperation.findByCode(status);
            switch (userTaskOperation) {
                case REJECT:
                    doTranProcessRejectedOrTerminated(protoSleevePO, protoSleeveBizDTO, "REJECT");
                    bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, protoSleevePO.getSleeveId(), protoSleevePO.getApplyCode(),
                            protoSleevePO.getApplyUserName(), true, "工程机保密套拆除Bpm审批被拒绝！", param);
                    break;
                default:
                    log.debug("其他状态不处理...");
            }
        }

        log.info("========处理工程机保密套拆除Bpm回调事件结束");
        return true;
    }

    private void doTranProcessRejectedOrTerminated(ProtoSleevePO mainData, ProtoSleeveBizDTO protoSleeveBizDTO, String status) {
        String keyId = mainData.getSleeveId();
        String applyCode = mainData.getApplyCode();
        String applyUserName = mainData.getApplyUserName();

        if (status.equals("TERMINATED")) {
            try {
                this.updProtoSleeveStatusBySleeveId(mainData.getSleeveId(), TranAuditEnum.NOT_SUBMIT.getKey(), DateUtil.getTodaySec());
            } catch (Exception e) {
                log.info("更新记录状态失败！");
                bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, keyId, applyCode, applyUserName, false, "更新记录状态失败！", protoSleeveBizDTO);
                throw new BizException(ApplicationErrorCodeEnum.UPDATE_DATA_ERROR, "更新记录状态失败");
            }
        } else if (status.equals("REJECT")) {
            try {
                this.updProtoSleeveStatusBySleeveId(keyId, TranAuditEnum.CANCELLED.getKey(), DateUtil.getTodaySec());
            } catch (Exception e) {
                log.info("更新记录状态失败！");
                bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, keyId, applyCode, applyUserName, false, "更新记录状态失败！", protoSleeveBizDTO);
                throw new BizException(ApplicationErrorCodeEnum.UPDATE_DATA_ERROR, "更新记录状态失败");
            }
        }

        log.info("更新保密套是否拆除为：否...");
        // 更新工程机台账对应记录保密套是否拆除为否
        if (!updProtoCardInfo(keyId, "0")) {
            log.info("更新工程机台账保密套是否拆除字段失败！");
            bpmExeLogService.insertBpmlog(FUNID, ORDER_NAME, keyId, applyCode, applyUserName,
                    false, "更新工程机台账保密套是否拆除字段失败！", protoSleeveBizDTO);
            throw new BizException(ApplicationErrorCodeEnum.UPDATE_DATA_ERROR, "更新工程机台账保密套是否拆除字段失败！");
        }
    }

    private boolean doTranProcessCompleted(ProtoSleevePO mainData, ProtoSleeveBizDTO protoSleeveBizDTO) {
        log.info("单据审批完成...");

        String auditing = mainData.getAuditing();
        String keyId = mainData.getSleeveId();
        String applyCode = mainData.getApplyCode();
        String applyUserName = mainData.getApplyUserName();

        // 检测流程是否已审批
        if ("3".equals(auditing)) {
            log.info("单据已审批完成，无需再审...");
            return true;
        }
        // 更新记录状态
        // 已审批
        this.updProtoSleeveStatusBySleeveId(mainData.getSleeveId(), TranAuditEnum.CHECK_OK.getKey(), DateUtil.getTodaySec());

        log.info("更新保密套是否拆除为：是...");
        //更新工程机台账保密套拆除信息
        if (!updProtoCardInfo(mainData.getSleeveId(), "1", mainData.getApplyUserName(), mainData.getApplyUserCode(), mainData.getApplyEmpCode())) {
            log.info("更新工程机台账保密套拆除信息失败！");
            bpmExeLogService.insertBpmlog(FUNID, "工程机保密套拆除-BPM回调", keyId, applyCode, applyUserName,
                    false, "更新工程机台账保密套拆除信息失败！", protoSleeveBizDTO);
            throw new BizException(ApplicationErrorCodeEnum.UPDATE_DATA_ERROR, "更新工程机台账保密套拆除信息失败！");
        }
        return true;
    }

    /**
     * 修改台账状态
     *
     * @param fKeyId 报废ID
     * @param isOpen 保密套是否拆除
     * @return
     */
    private boolean updProtoCardInfo(String fKeyId, String isOpen, String userName, String userCode, String empCode) {
        List<ProtoSleeveMatPO> matList = protoSleeveMatService.qrySleeveMatList(fKeyId);
        if (null != matList && !matList.isEmpty()) {
            List<String> deviceCodes = matList.stream().map(ProtoSleeveMatPO::getDeviceCode).collect(Collectors.toList());
            return protoCardService.updProtoCardOpenStatus(isOpen, userName, userCode, empCode, deviceCodes);
        }
        return true;
    }
    /**
     * 修改台账状态
     *
     * @param fKeyId 报废ID
     * @param isOpen 保密套是否拆除
     * @return
     */
    private boolean updProtoCardInfo(String fKeyId, String isOpen) {
        List<ProtoSleeveMatPO> matList = protoSleeveMatService.qrySleeveMatList(fKeyId);
        if (null != matList && !matList.isEmpty()) {
            List<String> deviceCodes = matList.stream().map(ProtoSleeveMatPO::getDeviceCode).collect(Collectors.toList());
            return protoCardService.updProtoCardOpenStatus(isOpen, deviceCodes);
        }
        return true;
    }

}
