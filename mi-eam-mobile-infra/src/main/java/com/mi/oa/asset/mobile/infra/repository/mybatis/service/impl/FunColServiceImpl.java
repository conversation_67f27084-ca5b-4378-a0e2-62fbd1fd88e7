package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.FunColPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.FunColMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.FunColService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/7
 */
@Service
public class FunColServiceImpl extends ServiceImpl<FunColMapper, FunColPO> implements FunColService {

    @Override
    public List<FunColPO> listByFunID(String funId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("fun_id", funId);
        queryWrapper.orderByAsc("col_index");
        return baseMapper.selectList(queryWrapper);
    }
}
