package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyMatPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCollectPlanPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoCollectPlanMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCollectPlanService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/17
 */
@Service
public class ProtoCollectPlanImpl extends ServiceImpl<ProtoCollectPlanMapper, ProtoCollectPlanPO>
        implements ProtoCollectPlanService {


    @Override
    public void updateAuditingInvalid(List<String> collectCodeList) {
        if (CollectionUtils.isEmpty(collectCodeList)){
            return;
        }
        LambdaUpdateWrapper<ProtoCollectPlanPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProtoCollectPlanPO::getAuditing, CommonConstant.RecordStatus.INVALID.getStatus());
        updateWrapper.in(ProtoCollectPlanPO::getCollectCode, collectCodeList);
        updateWrapper.eq(ProtoCollectPlanPO::getAuditing, CommonConstant.RecordStatus.UNCOMMITTED.getStatus());
        this.update(updateWrapper);
    }
}
