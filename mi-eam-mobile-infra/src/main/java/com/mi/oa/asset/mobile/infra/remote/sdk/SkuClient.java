package com.mi.oa.asset.mobile.infra.remote.sdk;


import com.mi.oa.asset.mobile.infra.dto.upc.*;
import com.mi.oa.asset.x5.consumer.X5FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * @Desc
 * <AUTHOR>
 * @Date 2021/10/29 16：13
 */
@X5FeignClient(value = "upc", url = "${upc.host}/sku/api", appId = "${upc.appId}", appKey = "${upc.appKey}", form = true)
public interface SkuClient {

    /**
     * 同步手机sku
     * @param req  参数level 1 一级 2二级
     * @return
     */
    @PostMapping(value = "/skuRecord/getPhoneSkuProjectInfo")
    List<MobileSkuInfoDTO> getPhoneSkuProjectInfo(SkuReq req);


    /**
     * 通过sku查询sku详情
     * @param req
     * @return
     */
    @PostMapping(value = "/sku/getSkuList")
    List<MobileSkuDetailDTO> getMobileSkuList(MobileSkuDetailReq req);


    /**
     * 同步电视Project
     *
     * @param req skuReqDTO  参数level 0 所有 1 一级 2 二级
     * @return
     */
    @PostMapping(value = "/tvCode/getTvProjectInfo")
    List<TvSkuDTO> getTvProjectInfo(SkuReq req);
}
