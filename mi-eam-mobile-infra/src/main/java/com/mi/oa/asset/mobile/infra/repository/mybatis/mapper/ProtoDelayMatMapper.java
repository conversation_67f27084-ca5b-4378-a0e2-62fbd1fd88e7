package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDelayMatPO;

/**
 * table_name : proto_delay_mat
 * <AUTHOR>
 * @date 2022/02/18/05:43
 */
public interface ProtoDelayMatMapper extends BaseMapper<ProtoDelayMatPO> {
    /**
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String delayMatId);

    /**
     *
     * @mbg.generated
     */
    ProtoDelayMatPO selectByPrimaryKey(String delayMatId);
}