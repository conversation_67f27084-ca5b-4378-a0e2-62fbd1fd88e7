package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.FunAllControlPO;

/**
 * table_name : funall_control
 * <AUTHOR>
 * @date 2022/01/20/05:20
 */
public interface FunAllControlMapper extends BaseMapper<FunAllControlPO> {
    /**
     *
     * @mbg.generated
     */
    FunAllControlPO selectByPrimaryKey(String controlId);
}