package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.mi.oa.asset.mobile.infra.dto.CostCenter;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOutDetPO;

import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/11 17:40
 */
public interface ProtoService {
    boolean qryThirdUser(String userCode);
    String getApplyUserType(String userCode);
    /**
     * 根据申请类型，转换对应的成本中心，公司代码1111
     * @param userCode      员工工号
     * @param applyUserType 申请类型  inner：内部领用  odm ： ODM ， provider ： 供应商领用
     * @param makeCn        样机生产地 xxx-xxxx，前四位是样机生产地代码，后四位是对应的公司代码
     * @return  map : centerCode , compDeptCode
     */
    CostCenter getSAPCenterCode(String userCode, String applyUserType, String makeCn);
    /**
     * 解析扫码结果：tranType [Excel, ASN, SN]
     * <AUTHOR>
     * @param scanResult
     * @return
     */
    Map<String, String> tranScanResult(String scanResult);

    /**
     * 找出未出库的设备编号SN
     * @param deviceCodes
     * @param outState
     * @param outType
     * @return
     */
    List<ProtoOutDetPO> getOutDet(List<String> deviceCodes, String outState, String outType);

    /**
     * 获取bpm跳链接
     * @param businessKey
     * @return
     */
    String getBpmUrl(String businessKey, String accUserCode);
}
