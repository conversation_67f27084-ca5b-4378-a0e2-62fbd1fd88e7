package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardProjectTotalPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectListPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoCardProjectTotalMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardProjectTotalService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectListService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 台账项目汇总
 *
 * <AUTHOR>
 * @date 2022/3/25 9:48
 */
@Service
@Slf4j
public class ProtoCardProjectTotalServiceImpl extends ServiceImpl<ProtoCardProjectTotalMapper, ProtoCardProjectTotalPO> implements ProtoCardProjectTotalService {

    @Autowired
    private ProtoProjectListService protoProjectListService;

    @Override
    public Boolean updCardProjectTotal() {
        this.remove(new QueryWrapper<>());
        //项目汇总台账数据去项目配置里获取项目经理信息
        List<ProtoCardProjectTotalPO> cardProjectTotalList = baseMapper.countCardProject();
        List<ProtoProjectListPO> projectList = protoProjectListService.getAllProjectList();
        Date nowDate = new Date();
        if (CollectionUtils.isNotEmpty(cardProjectTotalList) && CollectionUtils.isNotEmpty(projectList)) {
            for (ProtoCardProjectTotalPO cardProjectTotalPO : cardProjectTotalList) {
                String projectCode = cardProjectTotalPO.getProjectCode();
                BigDecimal zkysNum = cardProjectTotalPO.getZkysNum();
                BigDecimal zkesNum = cardProjectTotalPO.getZkesNum();
                BigDecimal yszyNum = cardProjectTotalPO.getYszyNum();
                BigDecimal eszyNum = cardProjectTotalPO.getEszyNum();
                BigDecimal wfghNum = cardProjectTotalPO.getWfghNum();
                BigDecimal ybfNum = cardProjectTotalPO.getYbfNum();
                BigDecimal ykkNum = cardProjectTotalPO.getYkkNum();
                BigDecimal totalNum = zkysNum.add(zkesNum).add(yszyNum).add(eszyNum).add(wfghNum).add(ybfNum).add(ykkNum);
                cardProjectTotalPO.setTotalNum(totalNum);
                cardProjectTotalPO.setModifyDate(nowDate);
                if (StringUtils.isNotBlank(projectCode)) {
                    for (ProtoProjectListPO projectListPO : projectList) {
                        String listProCode = projectListPO.getProjectCode();
                        if (projectCode.equals(listProCode)) {
                            String pmUserName = projectListPO.getPmUserName();
                            String pmUserCode = projectListPO.getPmUserCode();
                            if (StringUtils.isNotBlank(pmUserName)) {
                                cardProjectTotalPO.setPm(pmUserName + "(" + pmUserCode + ")");
                            }
                            break;
                        }
                    }
                }
            }
        }
        //项目汇总台账数据批量入库
        this.saveBatch(cardProjectTotalList);
        return true;
    }
}
