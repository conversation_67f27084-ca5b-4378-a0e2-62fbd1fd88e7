package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPricePO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/22
 */
public interface ProtoPriceService extends IService<ProtoPricePO> {

    List<ProtoPricePO> listPrice();

    /**
     * 定价配置编码 是否相同
     * @param protoPrice
     * @return
     */
    boolean havePriceCodeSame(ProtoPricePO protoPrice);
}
