package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDeviceScanDetPO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/23
 */
public interface ProtoDeviceScanDetService extends IService<ProtoDeviceScanDetPO> {

    /**
     * 通过盘点计划Id查询盘点详情
     * @param planId
     * @return
     */
    List<ProtoDeviceScanDetPO> listByPlanId(String planId);

    /**
     * 通过盘点计划查询项目编号
     * @param planId
     * @return
     */
    List<String> getProjectCodeByPlanId(String planId);
}
