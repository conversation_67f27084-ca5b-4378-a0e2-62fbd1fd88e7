package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.PriceTypeEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOrderPayDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoOrderPayDetMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoOrderPayDetService;
import com.mi.oa.asset.mobile.utils.DateUtil;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/3
 */
@Service
public class ProtoOrderPayDetServiceImpl extends ServiceImpl<ProtoOrderPayDetMapper, ProtoOrderPayDetPO> implements ProtoOrderPayDetService {

    @Override
    public List<ProtoOrderPayDetPO> listByTradeDate(String mrpType, String dateStr) {
        LambdaQueryWrapper<ProtoOrderPayDetPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(ProtoOrderPayDetPO::getTradeDate, DateUtil.getZeroDate(dateStr, DateUtils.YEAR_MONTH_DATE))
                .le(ProtoOrderPayDetPO::getTradeDate, DateUtil.getLastDate(dateStr, DateUtils.YEAR_MONTH_DATE))
                .eq(ProtoOrderPayDetPO::getMrpType, mrpType)
                .eq(ProtoOrderPayDetPO::getBusType, PriceTypeEnum.IN_PURCHA.getType());
        return this.list(wrapper);
    }

    @Override
    public List<ProtoOrderPayDetPO> listByOrderCodes(List<String> orderCodeList) {
        if (CollectionUtils.isEmpty(orderCodeList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoOrderPayDetPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoOrderPayDetPO::getOrderCode, orderCodeList);
        return this.list(wrapper);
    }

    @Override
    public List<ProtoOrderPayDetPO> listByTradeOrderCodeList(List<String> tradeCodeList) {
        if (CollectionUtils.isEmpty(tradeCodeList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoOrderPayDetPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoOrderPayDetPO::getTradeOrderCode, tradeCodeList);
        return this.list(wrapper);
    }
}
