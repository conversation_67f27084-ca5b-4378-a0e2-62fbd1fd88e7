package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.common.enums.OutStateEnum;
import com.mi.oa.asset.mobile.infra.dto.sap.SapCustomVO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOutPO;

import java.util.List;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
public interface ProtoOutService extends IService<ProtoOutPO> {
    List<ProtoOutPO> getOutPO(List<String> outCodes, OutStateEnum outStateEnum);

    /**
     * 根据多参数查询
     * @param po 查询参数
     * @return
     */
    List<ProtoOutPO> findListByParam(ProtoOutPO po);

    /**
     * 获取无ASN收货，且没有抛送SAP的发放单
     * @return
     */
    List<ProtoOutPO> getProtoOutPushSAPList();

    /**
     * 快速发放抛送SAP
     * @param outId
     * @param userName
     */
    SapCustomVO pushSAP(String outId, String userName);

    /**
     * 更新抛送SAP状态
     * @param outId
     * @param success
     */
    void updatePushSap(String outId, boolean success);

    /**
     * 将所有未提交的数据标记成已取消(定时任务中执行)
     */
    void updateCancelStatus();

    /**
     * 超时自动确认定时任务
     */
    void timeOutAutoConfirm();

    /**
     * 通过申请单号和状态查询数量
     * @param applyCode
     * @param outStatus
     * @return
     */
    Integer countByApplyCodeAndStatus(String applyCode, String outStatus);

    /**
     * 查询发放列表
     * @param applyCode
     * @return
     */
    List<ProtoOutPO> listByApplyCode(String applyCode);

    /**
     * 通过发放单号查询
     * @param outCode
     */
    ProtoOutPO getByOutCode(String outCode);
}
