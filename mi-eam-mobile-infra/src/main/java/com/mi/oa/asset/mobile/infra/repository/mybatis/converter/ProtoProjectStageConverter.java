package com.mi.oa.asset.mobile.infra.repository.mybatis.converter;

import com.mi.oa.asset.mobile.infra.dto.mier.MaterialDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectStagePO;
import org.mapstruct.Mapper;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ProtoProjectStageConverter {

    ProtoProjectStagePO protoProjectStageDTO2PO(MaterialDTO tvDTO);

    List<ProtoProjectStagePO> protoProjectStageDTO2POList(List<MaterialDTO> tvDTOList);

    MaterialDTO protoProjectStagePO2DTO(ProtoProjectStagePO po);

    List<MaterialDTO> protoProjectStagePO2DTOList(List<ProtoProjectStagePO> poList);
}
