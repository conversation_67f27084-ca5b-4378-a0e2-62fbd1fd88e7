package com.mi.oa.asset.mobile.infra.remote.sdk;

import com.mi.oa.asset.mobile.infra.config.IdmClientConfig;
import com.mi.oa.asset.mobile.infra.dto.idm.ReportLineDTO;
import com.mi.oa.asset.x5.consumer.X5FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/10/9 14:28
 */

@X5FeignClient(value = "idm", url = "${oaucf.idm-api.url}/api", appId = "${oaucf.idm-api.app-id}", appKey = "${oaucf.idm-api.app-secret}", form = true, configuration = IdmClientConfig.class)
public interface IdmClient {


    /**
     * 根据员工账号查询汇报线
     * @param userName
     * @return
     */
    @PostMapping(value = "/reportLine/findReportLineByUserName")
    List<ReportLineDTO> findReportLineByUserName(@RequestParam("userName") String userName);
}


