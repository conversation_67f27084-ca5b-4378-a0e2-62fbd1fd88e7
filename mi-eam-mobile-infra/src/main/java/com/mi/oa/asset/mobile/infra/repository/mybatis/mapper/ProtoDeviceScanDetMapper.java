package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDeviceScanDetPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * table_name : proto_device_scan_det
 * <AUTHOR>
 * @date 2022/06/23/11:20
 */
public interface ProtoDeviceScanDetMapper extends BaseMapper<ProtoDeviceScanDetPO> {

    @Select("select distinct project_code from proto_device_scan_det where plan_id = #{planId}")
    List<String> getProjectCodeByPlanId(@Param("planId") String planId);

    @Select("select distinct project_code from proto_device_scan_det where plan_id = #{planId}")
    List<String> getSendUserCodeByPlanId(String planId);
}