package com.mi.oa.asset.mobile.infra.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/10/23 22:24
 */

@Configuration
public class SoapConfig {

    @Bean
    public LeaveCheckPhoneConfig leaveCheckPhoneConfig() {
        return new LeaveCheckPhoneConfig();
    };

    @Data
    public static class BasicConfig {
        String url;
        String action;
    }

    @Data
    @ConfigurationProperties(prefix = "soap.leave-check-phone")
    public static class LeaveCheckPhoneConfig extends BasicConfig {
        String account;
        String password;
    }

}


