package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyMatPO;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/5 16:02
 */
public interface ProtoApplyMatService extends IService<ProtoApplyMatPO> {

    /**
     * 更新SAP行号
     * @param applyId
     * @param skuCode
     * @param sapItemRowNo
     * @return
     */
    boolean updateProtoApplyMatToSapItemRowNo(String applyId, String skuCode, Integer sapItemRowNo);
    /**
     * 获取SAP明细数据
     *
     * @param keyId
     * @return
     */
    List<ProtoApplyMatPO> getProtoApplyDetSAPList(String keyId);

    /**
     * 检查明细表是否存在尚未发放完的记录
     * @param applyId
     * @return
     */
    Integer countByApplyIdAndNotFinish(String applyId);

    /**
     * 查询物资
     *
     * @param queryWrapper
     * @return
     */
    ProtoApplyMatPO queryOneProtoApplyMat(LambdaQueryWrapper<ProtoApplyMatPO> queryWrapper);

    /**
     * 获取抛SAP领用申请单行号
     * @param applyId
     * @param skuCode
     * @return
     */
    String getApplyItemid(String applyId, String skuCode);
}
