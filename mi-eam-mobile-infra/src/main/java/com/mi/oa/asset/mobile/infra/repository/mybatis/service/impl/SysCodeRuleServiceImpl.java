package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.FunBasePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysCodeRulePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysVarPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysCodeRuleMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.FunBaseService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysCodeRuleService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysTableCodeService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysVarService;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/10
 */
@Service
public class SysCodeRuleServiceImpl extends ServiceImpl<SysCodeRuleMapper, SysCodeRulePO>
            implements SysCodeRuleService {

    @Autowired
    private SysVarService sysVarService;

    @Autowired
    private FunBaseService funBaseService;

    @Autowired
    private SysTableCodeService sysTableCodeService;

    @Override
    public String getBusCode(String funId, Map<String, String> mpValue) {
        SysCodeRulePO codeRulePO = getByFunId(funId);
        FunBasePO basePO = funBaseService.getById(funId);
        Integer codeLength = 6;
        String extValue = "";
        String codePrefix = (basePO.getCodePrefix() == null ? "" : basePO.getCodePrefix());
        if (null == codeRulePO || StringUtils.isBlank(codeRulePO.getCodeExt())){
            // 默认规则
            String format = CommonConstant.DEFAULT_CODE_RULE_FORMAT;
            String codeVar = sysVarService.findValByVarCode(CommonConstant.DEFAULT_CODE_RULE);
            format = (StringUtils.isBlank(codeVar) ? format : codeVar);
            if (format.length() > 0) {
                extValue = DateUtils.getFormatDate(format);
            }
            SysVarPO codeRule = sysVarService.findByVarCode(CommonConstant.DEFAULT_CODE_RULE_SERIAL);
            if (null != codeRule && StringUtils.isNotBlank(codeRule.getVarValue())){
                codeLength = codeRule.getVarValue().length();
            }
            Integer nextValue = sysTableCodeService.getMaxSysTableCode(basePO.getTableName(), extValue);
            return codePrefix + extValue + StringUtils.leftPad(nextValue+"", codeLength, "0");
        }
        // 扩展规则
        codeLength = codeRulePO.getCodeLength();
        String format = codeRulePO.getCodeExt();
        extValue = this.getCodeExtend(format, mpValue) + codeRulePO.getCodeNo();
        Integer nextValue = sysTableCodeService.getMaxSysTableCode(basePO.getTableName(), extValue);
        return codePrefix + extValue + StringUtils.leftPad(nextValue+"", codeLength, "0");
    }

    @Override
    public String getBusCode(String funId) {
        return getBusCode(funId, new HashMap<>());
    }


    private String getCodeExtend(String format, Map<String, String> mpValue) {
        String retVal = "";
        if (format != null && format.length() != 0) {
            format = format.trim();
            if (format.charAt(0) == ':') {
                retVal = DateUtils.getFormatDate(format.substring(1));
            } else {
                retVal = mpValue.get(format);
                if (StringUtils.isBlank(retVal)) {
                    String field = getNoTableCol(format);
                    retVal = (mpValue.get(field) == null ? "" : mpValue.get(field));
                }
            }
            return retVal;
        } else {
            return retVal;
        }
    }

    @Override
    public SysCodeRulePO getByFunId(String funId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("fun_id", funId);
        return baseMapper.selectOne(queryWrapper);
    }

    public static String getNoTableCol(String colName) {
        if (colName == null) {
            return colName;
        } else {
            if (colName.indexOf(".") >= 0) {
                colName = colName.substring(colName.indexOf(".") + 1, colName.length());
            }

            return colName;
        }
    }
}
