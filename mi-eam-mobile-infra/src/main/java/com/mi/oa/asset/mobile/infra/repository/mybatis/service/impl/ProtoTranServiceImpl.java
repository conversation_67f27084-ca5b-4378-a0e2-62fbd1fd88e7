package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.dto.ConditionDTO;
import com.mi.oa.asset.mobile.application.dto.message.EmailDTO;
import com.mi.oa.asset.mobile.application.dto.message.LarkDTO;
import com.mi.oa.asset.mobile.application.service.MessageService;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.RedisCachePrefixKeyEnum;
import com.mi.oa.asset.mobile.common.enums.TranConfirmStatusEnum;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoTranSapDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoThirdUserPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoTranMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.asset.mobile.utils.BeanUtil;
import com.mi.oa.asset.mobile.utils.DateUtil;
import com.mi.oa.infra.oaucf.idm.api.rep.UserBaseInfoDto;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
@Service
public class ProtoTranServiceImpl extends ServiceImpl<ProtoTranMapper, ProtoTranPO> implements ProtoTranService {

    @Autowired
    private ProtoThirdUserService protoThirdUserService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private ProtoTranDetService protoTranDetService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private MailTemplateService mailTemplateService;

    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private ProtoService protoService;

    @Value("${bpm.approvalUrlPrefix}")
    private String approvalUrlPrefix;

    @Override
    public ProtoTranPO getByTranId(String tranId) {
        return this.baseMapper.selectOne(Wrappers.<ProtoTranPO>lambdaQuery()
                .eq(ProtoTranPO::getTranId, tranId));
    }

    @Override
    public ProtoTranPO getByBusinessKey(String businessKey) {
        return baseMapper.selectOne(Wrappers.<ProtoTranPO>lambdaQuery()
                .eq(ProtoTranPO::getBusinessKey, businessKey));
    }

    @Override
    public List<ProtoTranSapDTO> getNoAsnOrderTranList() {
        return baseMapper.getNoAsnOrderTranList();
    }

    @Override
    public List<ProtoTranPO> listToRemind() {
        LambdaQueryWrapper<ProtoTranPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProtoTranPO::getConfirmStatus, TranConfirmStatusEnum.TO_BE_CONFIRMED.getKey());
        List<ProtoTranPO> list = this.list(queryWrapper);
        Date nowDate = new Date();
        list = list.stream()
                .filter(o-> DateUtil.dayDiff(nowDate, o.getApplyDate()) >= 7 && DateUtil.dayDiff(nowDate, o.getApplyDate()) <= 15)
                .collect(Collectors.toList());
        return list;
    }

    @Override
    public void remindMessage(ProtoTranPO tranPO) {
        List<ProtoTranDetPO> list = protoTranDetService.listByTranId(tranPO.getTranId());
        List<ProtoCardPO> cardList = BeanUtil.copyListProperties(list, ProtoCardPO.class);
        String businessKey = tranPO.getBusinessKey();

        // 申请人
        String userCode = tranPO.getUserCode();
        String userCodeEmail = this.getEmailByUserCode(userCode);
        List<String> applicantEmail = new ArrayList<>(1);
        List<String> userCodeList = new ArrayList<>(1);
        userCodeList.add(userCode);
        applicantEmail.add(userCodeEmail);
        String applyUserUrl = protoService.getBpmUrl(businessKey, userCode);
        Map<String, String> valueMap = new HashMap<>(32);
        valueMap.put("userName", tranPO.getUserName());
        valueMap.put("userCode", tranPO.getUserCode());
        valueMap.put("applicantDate", DateUtils.dateToStringByType(tranPO.getApplyDate(), DateUtils.YEAR_MONTH_DATE));
        valueMap.put("accUserName", tranPO.getAccUserName());
        valueMap.put("accUserCode", tranPO.getAccUserCode());
        valueMap.put("tranCode", tranPO.getTranCode());
        valueMap.put("proto_tran_det_table_en", protoCardService.getEmailTableContent(cardList, false));
        valueMap.put("proto_tran_det_table_cn", protoCardService.getEmailTableContent(cardList, true));
        valueMap.put("proto_tran_det_en", protoCardService.getLarkTableContent(cardList, false));
        valueMap.put("proto_tran_det_cn", protoCardService.getLarkTableContent(cardList, true));
        valueMap.put("proto_tran_url", applyUserUrl);
        // 转移超7天给申请人邮件消息模板
        String applicantMailContent = mailTemplateService.getMailContent(CommonConstant.PROTO_TRAN_OVER_TIME_APPLICANT, valueMap);
        // 转移超7天给申请人飞书提醒
        String applicantLarkContent = mailTemplateService.getMailContent(CommonConstant.PROTO_TRAN_OVER_TIME_APPLICANT_LARK, valueMap);
        applicantLarkContent = applicantLarkContent.replaceAll("[\\t\\n\\r]", "   ");

        // 接受人
        String accUserCode = tranPO.getAccUserCode();
        String accUserCodeEmail = this.getEmailByUserCode(accUserCode);
        List<String> receiverEmailList = new ArrayList<>(1);
        receiverEmailList.add(accUserCodeEmail);
        List<String> receiverUserCodeList = new ArrayList<>(1);
        receiverUserCodeList.add(accUserCode);
        String title = "《工程机转移超7天未确认提醒》《The engineering machine transfer has not been confirmed for more than 7 days.》";
        String acceptUserUrl = protoService.getBpmUrl(businessKey, accUserCode);
        valueMap.put("proto_tran_url", acceptUserUrl);

        // 转移超7天给接受人邮件消息模板
        String receiverMailContent = mailTemplateService.getMailContent(CommonConstant.PROTO_TRAN_OVER_TIME_RECEIVER, valueMap);
        // 转移超7天给接受人飞书提醒
        String receiverLarkContent = mailTemplateService.getMailContent(CommonConstant.PROTO_TRAN_OVER_TIME_RECEIVER_LARK, valueMap);

//        // 给申请人邮件提醒
        messageService.sendEmail(EmailDTO.builder()
                .emails(applicantEmail)
                .content(applicantMailContent)
                .title(title)
                .build());

        // 给申请人飞书提醒
        messageService.sendLark(LarkDTO.builder()
                            .userNames(userCodeList)
                            .content(applicantLarkContent)
                            .title(title)
                            .url(applyUserUrl)
                            .build());

        // 给接受人邮件提醒
        messageService.sendEmail(EmailDTO.builder()
                .emails(receiverEmailList)
                .content(receiverMailContent)
                .title(title)
                .build());

        // 给接受人飞书提醒
        messageService.sendLark(LarkDTO.builder()
                .userNames(receiverUserCodeList)
                .content(receiverLarkContent)
                .title(title)
                .url(acceptUserUrl)
                .build());
    }

    private String getEmailByUserCode(String userCode){
        String cacheKey = RedisCachePrefixKeyEnum.TRAN_EMAIL_USER_CODE.getPrefixKey()+userCode;
        Object obj = RedisUtils.get(cacheKey);
        if (obj != null){
            return obj.toString();
        }
        // P账号
        if (StringUtils.startsWith(userCode, "p-")){
            ProtoThirdUserPO userInfo = protoThirdUserService.findUserInfo(userCode);
            if (null == userInfo){
                return null;
            }
            RedisUtils.setEx(cacheKey, userInfo.getEmail(), 10, TimeUnit.MINUTES);
            return userInfo.getEmail();
        }
        UserBaseInfoDto info = userInfoService.getUserInfoByUserName(userCode);
        RedisUtils.setEx(cacheKey, info.getEmail(), 10, TimeUnit.MINUTES);
        return info.getEmail();
    }

    /**
     * 查询转移记录
     * @param conditionList
     * @param start
     * @param limit
     * @return
     */
    @Override
    public List<ProtoTranPO> getTranList(String userCode, List<ConditionDTO> conditionList, Integer start, Integer limit, Integer count, List<String> deptCodeList, String whereSql) {
        return baseMapper.getTranList(userCode, conditionList, start, limit, count, deptCodeList, whereSql);
    }

    @Override
    public Integer countTranList(String userCode, List<ConditionDTO> conditionList, Integer count, List<String> deptCodeList, String whereSql) {
        return baseMapper.countTranList(userCode, conditionList, count, deptCodeList, whereSql);
    }

}
