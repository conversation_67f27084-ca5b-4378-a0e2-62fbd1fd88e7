package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mi.oa.asset.mobile.application.dto.CommonConditionDTO;
import com.mi.oa.asset.mobile.application.dto.ConditionDTO;
import com.mi.oa.asset.mobile.common.enums.RedisFlowNumEnum;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.common.enums.SapBatchIdEnum;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/1
 */
public interface CommonService {

    /**
     * 15位 唯一ID
     * @param keyEnum
     * @return
     */
    String getUniqueId(RedisUniqueKeyEnum keyEnum);

    /**
     * @return
     */
    String getAutoIncId(RedisUniqueKeyEnum keyEnum, int size);

    /**
     * 获取唯一的流水号
     * @param keyEnum
     * @return
     */
    String getBatchIdUnique(SapBatchIdEnum keyEnum);

    /**
     * 获取流水号
     * @param flowNumEnum
     * @return
     */
    String getFlowNum(RedisFlowNumEnum flowNumEnum);

    /**
     * 通用单个保存
     * @param requestContext
     * @return
     */
    String updateEvent(RequestContext requestContext);

    /**
     * 通用单个保存接口
     * @param requestContext
     * @return
     */
    String createEvent(RequestContext requestContext);

    /**
     * 表格编辑保存
     * @param requestContext
     * @return
     */
    String gridSaveEvent(RequestContext requestContext);

    /**
     * 通用保存修改接口
     * @param requestContext
     * @return
     */
    String commonSave(RequestContext requestContext);

    /**
     * 通用提交方法
     * @param requestContext
     * @return
     */
    String commonAudit(RequestContext requestContext);


    /**
     * 通用条件
     * @param conditionDTO
     * @return
     */
    List<ConditionDTO> getConditionList(CommonConditionDTO conditionDTO);

    /**
     * 追加条件
     * @param queryWrapper
     * @param conditionList
     */
    void appendQueryWrapper(QueryWrapper queryWrapper, List<ConditionDTO> conditionList);
}
