package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCollectPO;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/17
 */
public interface ProtoCollectService extends IService<ProtoCollectPO> {

    /**
     * 将已超过截止日期的需求计划下达记录生成的需求计划采集记录失效
     */
    void updateProtoCollectStatus();
}
