package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.dto.cardusertab.CardDeptDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardDeptTotalPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysDeptPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoCardDeptTotalMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardDeptTotalService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysDeptService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/3/31
 */
@Service
public class ProtoCardDeptTotalServiceImpl extends ServiceImpl<ProtoCardDeptTotalMapper, ProtoCardDeptTotalPO>
        implements ProtoCardDeptTotalService {

    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private SysDeptService sysDeptService;

    @Override
    public Boolean statisticsDept() {
        Map<CardDeptDTO, ProtoCardDeptTotalPO> deptTotalMap = new HashMap<>(4096);
        // 先删除 再重新统计
        this.remove(new QueryWrapper<>());
        List<ProtoCardDeptTotalPO> totalDeptList = protoCardService.listGroupByDeptCode();
        this.saveBatch(totalDeptList);
        this.putDeptMap(totalDeptList, deptTotalMap);
        List<String> mrpList = baseMapper.getMrpTypeList();
        // 统计下级部门数据
        for (String mrpType : mrpList){
            for (int deptLevel = 6; deptLevel > 1; deptLevel--) {
                List<ProtoCardDeptTotalPO> list = baseMapper.statisticsByDeptLevel(deptLevel, mrpType);
                List<ProtoCardDeptTotalPO> saveList = new ArrayList<>(list.size());
                List<ProtoCardDeptTotalPO> updateList = new ArrayList<>(list.size());
                for (ProtoCardDeptTotalPO totalPO : list) {
                    Integer totalNum = totalPO.getDghNum() + totalPO.getGhzNum() + totalPO.getWfghzNum() + totalPO.getZyzNum();
                    totalPO.setTotalNum(totalNum);
                }
                for (ProtoCardDeptTotalPO totalPO : list) {
                    CardDeptDTO cardDeptDTO = new CardDeptDTO(totalPO.getDeptCode(), totalPO.getMrpType());
                    ProtoCardDeptTotalPO deptTotalPO = deptTotalMap.get(cardDeptDTO);
                    if (null != deptTotalPO) {
                        totalPO.setId(deptTotalPO.getId());
                        totalPO.setTotalNum(deptTotalPO.getTotalNum() + totalPO.getTotalNum());
                        totalPO.setDghNum(deptTotalPO.getDghNum() + totalPO.getDghNum());
                        totalPO.setWfghzNum(deptTotalPO.getWfghzNum() + totalPO.getWfghzNum());
                        totalPO.setGhzNum(deptTotalPO.getGhzNum() + totalPO.getGhzNum());
                        totalPO.setZyzNum(deptTotalPO.getZyzNum() + totalPO.getZyzNum());
                        updateList.add(totalPO);
                    } else {
                        saveList.add(totalPO);
                    }
                }
                this.saveBatch(saveList);
                this.updateBatchById(updateList);
                this.putDeptMap(saveList, deptTotalMap);
                this.putDeptMap(updateList, deptTotalMap);
            }
        }

        // 更新部门名称信息
        List<ProtoCardDeptTotalPO> allDeptList = new ArrayList<>(deptTotalMap.size());
        for (CardDeptDTO cardDeptDTO : deptTotalMap.keySet()) {
            allDeptList.add(deptTotalMap.get(cardDeptDTO));
        }
        List<String> deptCodeList = allDeptList.stream().map(ProtoCardDeptTotalPO::getDeptCode).distinct().collect(Collectors.toList());
        Map<String, SysDeptPO> deptMap = sysDeptService.listByDeptCodeList(deptCodeList).stream()
                .collect(Collectors.toMap(SysDeptPO::getDeptId, o -> o));
        for (ProtoCardDeptTotalPO totalPO : allDeptList) {
            SysDeptPO sysDeptPO = deptMap.get(totalPO.getDeptCode());
            if (null != sysDeptPO) {
                totalPO.setDeptName(sysDeptPO.getDeptName());
                String longDeptName = sysDeptPO.getLongDeptName();
                if (StringUtils.isBlank(longDeptName)) {
                    longDeptName = sysDeptPO.getDeptName();
                }
                totalPO.setDeptLevel(sysDeptPO.getDeptLevel().intValue());
                totalPO.setLongDeptName(longDeptName);
            }
        }
        this.updateBatchById(allDeptList);
        return true;
    }

    private void putDeptMap(List<ProtoCardDeptTotalPO> list, Map<CardDeptDTO, ProtoCardDeptTotalPO> deptTotalMap) {
        for (ProtoCardDeptTotalPO totalPO : list) {
            CardDeptDTO cardDeptDTO = new CardDeptDTO(totalPO.getDeptCode(), totalPO.getMrpType());
            deptTotalMap.put(cardDeptDTO, totalPO);
        }
    }
}
