package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.dto.apply.UserInfo;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.dto.AsnOrderDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoAsnOrderDetMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoAsnOrderDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoAsnOrderService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInService;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/2
 */
@Service
public class ProtoAsnOrderDetServiceImpl extends ServiceImpl<ProtoAsnOrderDetMapper, ProtoAsnOrderDetPO> implements ProtoAsnOrderDetService {

    @Autowired
    private ProtoInDetService protoInDetService;

    @Autowired
    private ProtoInService protoInService;

    @Autowired
    private ProtoAsnOrderService protoAsnOrderService;

    @Override
    public void deleteByOrderDetId(String asnNo) {
        LambdaQueryWrapper<ProtoAsnOrderDetPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProtoAsnOrderDetPO::getAsnNo, asnNo);
        baseMapper.delete(queryWrapper);
    }

    @Override
    public boolean haveReceive(String asnNo) {
        LambdaQueryWrapper<ProtoAsnOrderDetPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProtoAsnOrderDetPO::getAsnNo, asnNo);
        queryWrapper.eq(ProtoAsnOrderDetPO::getIsDelete, 0);
        queryWrapper.gt(ProtoAsnOrderDetPO::getReceiptNum, 0);
        return baseMapper.selectCount(queryWrapper) > 0;
    }

    @Override
    public void updateAsnOrderStatus(String asnNo, String inStatus) {
        LambdaUpdateWrapper<ProtoAsnOrderDetPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProtoAsnOrderDetPO::getInStatus, inStatus);
        updateWrapper.set(ProtoAsnOrderDetPO::getSapRetType, SapRetEnum.ERROR.getKey());
        updateWrapper.eq(ProtoAsnOrderDetPO::getAsnNo, asnNo);
        this.update(updateWrapper);
    }

    @Override
    public void updateAsnOrderStatusReceiptNum(String detId, String inStatus, Integer receiptNum) {
        LambdaUpdateWrapper<ProtoAsnOrderDetPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProtoAsnOrderDetPO::getInStatus, inStatus);
        updateWrapper.set(ProtoAsnOrderDetPO::getReceiptNum, receiptNum);
        updateWrapper.eq(ProtoAsnOrderDetPO::getDetId, detId);
        this.update(updateWrapper);
    }

    @Override
    public void updateSapStatus() {
        int pageNum = 1;
        while (true) {
            List<ProtoAsnOrderDetPO> notPushSapList = getNotPushSapList(pageNum, CommonConstant.DEFAULT_PAGE_SIZE);
            dealAsnOrderDetSapStatus(notPushSapList);
            pageNum++;
            if (notPushSapList.size() < CommonConstant.DEFAULT_PAGE_SIZE){
                return;
            }
        }
    }

    @Override
    public ProtoAsnOrderDetPO getBySn(String sn) {
        LambdaQueryWrapper<ProtoAsnOrderDetPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProtoAsnOrderDetPO::getDeviceCode, sn);
        queryWrapper.ne(ProtoAsnOrderDetPO::getInStatus,  AsnInStatusEnum.CANCEL_DELIVERY.getState());
        return this.getOne(queryWrapper, false);
    }

    @Override
    public List<ProtoAsnOrderDetPO> listBySnList(Collection<String> snList) {
        if (CollectionUtils.isEmpty(snList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoAsnOrderDetPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ProtoAsnOrderDetPO::getDeviceCode, snList);
        queryWrapper.ne(ProtoAsnOrderDetPO::getInStatus, AsnInStatusEnum.CANCEL_DELIVERY.getState());
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public AsnOrderDTO getMachAsn(String sn) {
        return baseMapper.getMachAsn(sn);
    }

    @Override
    public void updateNotReceipt(String detId) {
        LambdaUpdateWrapper<ProtoAsnOrderDetPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProtoAsnOrderDetPO::getInStatus, InStatusEnum.NOT_DELIVERY.getState());
        updateWrapper.set(ProtoAsnOrderDetPO::getReceiptNum, 0);
        this.update(updateWrapper);
    }

    @Override
    public Integer countByAsnAndStatus(String asnNo, String inStatus) {
        LambdaQueryWrapper<ProtoAsnOrderDetPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProtoAsnOrderDetPO::getAsnNo, asnNo);
        queryWrapper.eq(ProtoAsnOrderDetPO::getInStatus, inStatus);
        return this.count(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String confirmReceiving(String orderCode, String opt, String[] detIds, String houseName, String houseCode, String houseId, UserInfo userInfo) {
        ProtoAsnOrderPO asnOrderPO = protoAsnOrderService.getAsnOrderByCode(orderCode);
        if (null == asnOrderPO){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_ASN_ORDER_001, orderCode);
        }
        if (SrmAsnStatusEnum.HAVE_RECEIVE.getEamStatus().equals(asnOrderPO.getOrderStatus())){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_ASN_ORDER_002);
        }
        if (SrmAsnStatusEnum.HAVE_CANCEL.getEamStatus().equals(asnOrderPO.getOrderStatus())){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_ASN_ORDER_003, orderCode);
        }
        // 更新仓库
        if ("no_house".equals(opt)){
            asnOrderPO.setHouseName(houseName);
            asnOrderPO.setHouseCode(houseCode);
            asnOrderPO.setHouseId(houseId);
            protoAsnOrderService.updateById(asnOrderPO);
        }
        // 更新为待收货状态
        this.updateInStatus(detIds);
        // 查询待收货清单
        LambdaQueryWrapper<ProtoAsnOrderDetPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProtoAsnOrderDetPO::getAsnNo, orderCode);
        queryWrapper.eq(ProtoAsnOrderDetPO::getInStatus, AsnInStatusEnum.SECTION_DELIVERY.getState());
        List<ProtoAsnOrderDetPO> list = this.list(queryWrapper);
        if (CollectionUtils.isEmpty(list)){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_ASN_ORDER_004);
        }
        String inId = protoInService.confirmReceiving(asnOrderPO, list, InTypeEnum.HAVE_ASN_DELIVERY.getType(), userInfo.getUserCode(), houseName, houseCode, houseId);
        List<String> detIdList = list.stream().map(ProtoAsnOrderDetPO::getDetId).collect(Collectors.toList());
        this.feedBackReceivingToAsnOrderDet(detIdList, inId);
        ProtoInPO protoInPO = protoInService.getById(inId);
        // 反馈收货信息到ASN订单收货
        protoAsnOrderService.feedBackReceivingToAsnOrder(protoInPO, orderCode);
        // 校验收货数量
        protoAsnOrderService.checkOutNum(orderCode);
        // 抛SAP
        protoInService.pushSAP(inId, userInfo.getUserName());
        return inId;
    }

    @Override
    public List<ProtoAsnOrderDetPO> listByOrderCode(String orderCode) {
        LambdaQueryWrapper<ProtoAsnOrderDetPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoAsnOrderDetPO::getAsnNo, orderCode);
        return this.list(wrapper);
    }

    @Override
    public String getSapSuccessOrderCode(String deviceCode) {
        ProtoAsnOrderDetPO orderDetPO = this.getBySn(deviceCode);
        // 抛送SAP成功
        if (null != orderDetPO && SapRetEnum.SUCCESS.getKey().equals(orderDetPO.getSapRetType())){
            return orderDetPO.getAsnNo();
        }
        return null;
    }


    /**
     * 反馈收货信息到ASN订单明细
     * @param detIds
     * @param inId
     * @return
     */
    private void feedBackReceivingToAsnOrderDet(List<String> detIds, String inId) {
        LambdaUpdateWrapper<ProtoAsnOrderDetPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProtoAsnOrderDetPO::getInId, inId);
        wrapper.set(ProtoAsnOrderDetPO::getInStatus, AsnInStatusEnum.HAVE_DELIVERY.getState());
        wrapper.set(ProtoAsnOrderDetPO::getReceiptNum, 1);
        wrapper.in(ProtoAsnOrderDetPO::getDetId, detIds);
        this.update(wrapper);
    }

    /**
     * 更新收货状态
     * @param detIds
     */
    private void updateInStatus(String[] detIds){
        LambdaUpdateWrapper<ProtoAsnOrderDetPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProtoAsnOrderDetPO::getInStatus, AsnInStatusEnum.SECTION_DELIVERY.getState());
        wrapper.eq(ProtoAsnOrderDetPO::getInStatus, AsnInStatusEnum.NOT_DELIVERY.getState());
        wrapper.in(ProtoAsnOrderDetPO::getDetId, detIds);
        this.update(wrapper);
    }

    /**
     * 处理AsnOrderDet表抛送SAP状态
     * @param notPushSapList
     */
    private void dealAsnOrderDetSapStatus(List<ProtoAsnOrderDetPO> notPushSapList){
        if (notPushSapList.size() == 0){
            return;
        }
        List<String> snList = notPushSapList.stream().map(ProtoAsnOrderDetPO::getDeviceCode).collect(Collectors.toList());
        List<ProtoInDetPO> haveSapSnList = protoInDetService.listBySnList(snList);
        if (CollectionUtils.isEmpty(haveSapSnList)){
            return;
        }
        Map<String, List<ProtoInDetPO>> protoMap = new HashMap<>(haveSapSnList.size()*2);
        for (ProtoInDetPO proto: haveSapSnList){
            String sn = proto.getDeviceCode();
            List<ProtoInDetPO> inDetPOList = protoMap.get(sn);
            if (null == inDetPOList){
                inDetPOList = new ArrayList<>();
            }
            inDetPOList.add(proto);
            protoMap.put(sn, inDetPOList);
        }
        List<String> inIdList = haveSapSnList.stream().map(ProtoInDetPO::getInId).distinct().collect(Collectors.toList());
        // 查询主表
        Map<String, ProtoInPO> protoInMap = protoInService.listByInIds(inIdList).stream()
                .collect(Collectors.toMap(ProtoInPO::getInId, o->o));
        for (ProtoAsnOrderDetPO detPO : notPushSapList){
            String sn = detPO.getDeviceCode();
            List<ProtoInDetPO> protoInDetList = protoMap.get(sn);
            if (CollectionUtils.isEmpty(protoInDetList)){
                continue;
            }
            for (ProtoInDetPO protoInDet : protoInDetList){
                ProtoInPO protoIn = protoInMap.get(protoInDet.getInId());
                if (protoIn == null){
                    continue;
                }
                String inType = protoIn.getInType();
                // 无ASN收货
                if (InTypeEnum.NOT_ASN_DELIVERY.getType().equals(inType)){
                    if (SapRetEnum.SUCCESS.getKey().equals(protoInDet.getSapRetType())){
                        detPO.setSapRetType(SapRetEnum.SUCCESS.getKey());
                    }
                } else if (InTypeEnum.HAVE_ASN_DELIVERY.getType().equals(inType)){
                    // 有ASN收货
                    if (SapRetEnum.SUCCESS.getKey().equals(protoIn.getSapRetType())){
                        detPO.setSapRetType(SapRetEnum.SUCCESS.getKey());
                    }
                }
            }
        }
        List<ProtoAsnOrderDetPO> updateList = notPushSapList.stream()
                .filter(o -> StringUtils.equals(o.getSapRetType(), SapRetEnum.SUCCESS.getKey())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(updateList)){
            this.updateBatchById(updateList);
        }
    }

    private List<ProtoAsnOrderDetPO> getNotPushSapList(int pageNum, int pageSize){
        Page page = new Page(pageNum, pageSize);
        LambdaQueryWrapper<ProtoAsnOrderDetPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(ProtoAsnOrderDetPO::getSapRetType, SapRetEnum.SUCCESS.getKey());
        queryWrapper.eq(ProtoAsnOrderDetPO::getInStatus, InStatusEnum.DUMPED_SAP.getState());
        return baseMapper.selectPage(page, queryWrapper).getRecords();
    }
}
