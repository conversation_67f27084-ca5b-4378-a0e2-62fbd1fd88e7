package com.mi.oa.asset.mobile.infra.remote.sdk;

import com.mi.oa.asset.mobile.application.dto.order.*;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/1
 */
@FeignClient(value = "asset-mall")
public interface AssetMallClient {

    /**
     * 创建订单
     * @param orderReq
     * @return
     */
    @PostMapping(value = "/api/v1/order/create", consumes = MediaType.APPLICATION_JSON_VALUE)
    BaseResp<MallOrderReq> orderCreate(@RequestBody MallOrderReq orderReq);

    /**
     * 批量创建订单
     * @param orderReqList
     * @return
     */
    @PostMapping(value = "/api/v1/order/batch-create", consumes = MediaType.APPLICATION_JSON_VALUE)
    BaseResp<List<MallOrderReq>> orderCreateByBatch(@RequestBody List<MallOrderReq> orderReqList);

    /**
     * 取消接口
     * @param orderId
     * @return
     */
    @PostMapping(value = "/api/v1/order/cancel/{orderId}", consumes = MediaType.APPLICATION_JSON_VALUE)
    BaseResp orderCancel(@PathVariable("orderId") String orderId);

    /**
     * 批量取消接口
     * @param orderIdList
     * @return
     */
    @PostMapping(value = "/api/v1/order/batch-cancel", consumes = MediaType.APPLICATION_JSON_VALUE)
    BaseResp batchCancel(@RequestBody List<String> orderIdList);

    /**
     * 查询支付订单接口
     * @param billReqDTO
     * @return
     */
    @PostMapping(value = "/api/v1/merchant/pay-bill", consumes = MediaType.APPLICATION_JSON_VALUE)
    BaseResp<List<PayBillResp>> payBill(@RequestBody BillReqDTO billReqDTO);

    /**
     * 查询账务提现流水
     * @param billReqDTO
     * @return
     */
    @PostMapping(value = "/api/v1/merchant/account-bill", consumes = MediaType.APPLICATION_JSON_VALUE)
    BaseResp<List<AccountBillResp>> accountBill(@RequestBody BillReqDTO billReqDTO);

    @GetMapping(value = "/api/v1/sku/product-info")
    BaseResp<PreOrderDTO> getSKUProductInfo(@RequestParam("mrpType") String mrpType, @RequestParam("skuCode") String skuCode, @RequestParam("houseCode") String houseCode);

    /**
     * 统计下架SN数量
     * @param orderNo  内购计划号
     * @return
     */
    @GetMapping(value = "/api/v1/product-off/num", consumes = MediaType.APPLICATION_JSON_VALUE)
    BaseResp<Integer> productOffNumByPlanCode(@RequestParam("planCode") String orderNo);

    /**
     * 统计下架SN数量
     * @param planCodes  内购计划号
     * @return
     */
    @GetMapping(value = "/api/v1/sn/quality", consumes = MediaType.APPLICATION_JSON_VALUE)
    BaseResp<Map<String, Integer>> queryQualityByPlanCodes(@RequestParam("planCode") List<String> planCodes);

    /**
     * 查询售后订单
     * @param orderId
     * @return
     */
    @GetMapping(value = "/api/v1/order/after-sale/query", consumes = MediaType.APPLICATION_JSON_VALUE)
    BaseResp<AfterSaleApplyResp> queryAfterSaleOrder(@RequestParam("orderId") String orderId);
}
