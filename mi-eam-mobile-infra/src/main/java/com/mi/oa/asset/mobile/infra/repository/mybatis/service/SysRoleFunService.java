package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysRoleFunPO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/11/21
 */
public interface SysRoleFunService extends IService<SysRoleFunPO> {

    /**
     * 查询有权限的角色Id
     * @param funId
     * @param roleIdList
     * @return
     */
    List<String> getHaveAuthorityRoleId(String funId, List<String> roleIdList);
}
