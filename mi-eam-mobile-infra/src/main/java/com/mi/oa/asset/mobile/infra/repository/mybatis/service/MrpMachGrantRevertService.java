package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MrpMachGrantRevertPO;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/3/30
 */
public interface MrpMachGrantRevertService extends IService<MrpMachGrantRevertPO> {

    /**
     * 发放时间
     * @param snList
     * @return
     */
    Map<String, Date> getGrantTimeMap(List<String> snList);

    /**
     * 归还时间
     * @param snList
     * @return
     */
    Map<String, Date> getRevertTimeMap(List<String> snList);

    /**
     * 发放人map
     * @param snList
     * @return
     */
    Map<String, String> getGrantUserMap(List<String> snList);
}
