package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.dto.ConditionDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyMatPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSecondCardPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoSecondCardMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoSecondCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/7 14:10
 */
@Service
public class ProtoSecondCardServiceImpl extends ServiceImpl<ProtoSecondCardMapper, ProtoSecondCardPO> implements ProtoSecondCardService {

    @Autowired
    private CommonService commonService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean isReduce(List<ProtoApplyMatPO> matList, String projectCode, String stageName, String houseCode, boolean flag) {
        for(ProtoApplyMatPO matMap : matList){
            LambdaUpdateWrapper<ProtoSecondCardPO> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper
                    .eq(ProtoSecondCardPO::getProjectCode,projectCode)
                    .eq(ProtoSecondCardPO::getStageName,stageName)
                    .eq(ProtoSecondCardPO::getSkuCode,matMap.getSkuCode())
                    .eq(ProtoSecondCardPO::getHouseCode,houseCode)
                    .setSql(flag,"num = num - "+matMap.getApplyNum())
                    .setSql(!flag,"num = num + "+matMap.getApplyNum());
            this.update(updateWrapper);
        }
        return true;
    }

    @Override
    public int qrySecondCardCount(String projectCode, String stageName, String skuCode, String houseCode) {
        QueryWrapper<ProtoSecondCardPO> cardPOQueryWrapper=new QueryWrapper<>();
        cardPOQueryWrapper.lambda()
                .eq(ProtoSecondCardPO::getProjectCode,projectCode)
                .eq(ProtoSecondCardPO::getStageName,stageName)
                .eq(ProtoSecondCardPO::getSkuCode,skuCode)
                .eq(ProtoSecondCardPO::getHouseCode,houseCode);
        return this.count(cardPOQueryWrapper);
    }

    @Override
    public List<Map<String, Object>> findSecondNoGoodNumList(List<ConditionDTO> conditionList, List<String> mrpTypeList) {
        QueryWrapper<ProtoSecondCardPO> queryWrapper = new QueryWrapper();
        queryWrapper.select("mrp_type, project_code","stage_name","sku_code","sku_name","is_complete","house_code","sum(num) as num");
        commonService.appendQueryWrapper(queryWrapper, conditionList);
        queryWrapper.lambda().gt(ProtoSecondCardPO::getNum,0);
        if (CollectionUtils.isNotEmpty(mrpTypeList)){
            queryWrapper.lambda().in(ProtoSecondCardPO::getMrpType, mrpTypeList);
        }
        queryWrapper.lambda().groupBy(ProtoSecondCardPO::getMrpType,ProtoSecondCardPO::getProjectCode,ProtoSecondCardPO::getStageName,ProtoSecondCardPO::getSkuCode,ProtoSecondCardPO::getHouseCode);
        queryWrapper.lambda().having("count(1) > 0");
        return this.listMaps(queryWrapper);
    }

    @Override
    public List<ProtoSecondCardPO> findListByParam(ProtoSecondCardPO po) {
        QueryWrapper<ProtoSecondCardPO> queryWrapper = new QueryWrapper(po);
        return this.list(queryWrapper);
    }

    @Override
    public ProtoSecondCardPO findOneByParam(ProtoSecondCardPO po){
        QueryWrapper<ProtoSecondCardPO> queryWrapper = new QueryWrapper(po);
        return this.getOne(queryWrapper);
    }

    @Override
    public void releaseSecondCard(String skuCode, String houseCode, Integer releaseCount) {
        ProtoSecondCardPO cardPO = findOneByParam(ProtoSecondCardPO.builder().skuCode(skuCode).houseCode(houseCode).build());
        if (null == cardPO){
            return;
        }
        Integer num = cardPO.getNum().intValue()+releaseCount;
        cardPO.setNum(BigDecimal.valueOf(num));
        this.updateById(cardPO);
    }
}
