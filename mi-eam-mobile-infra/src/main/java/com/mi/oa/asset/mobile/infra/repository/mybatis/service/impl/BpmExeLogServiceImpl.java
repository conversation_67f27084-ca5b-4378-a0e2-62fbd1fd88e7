package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.infra.dto.BpmExeLogDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.converter.BpmExeLogConverter;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.BpmExeLogPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.BpmExeLogMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.BpmExeLogService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
@Service
@Slf4j
public class BpmExeLogServiceImpl extends ServiceImpl<BpmExeLogMapper, BpmExeLogPO> implements BpmExeLogService {

    //表名
    public static final String TABLE_NAME = "bpm_exelog";

    //最大长度
    public static final Integer MAX_SIZE = 2000;

    @Autowired
    private BpmExeLogConverter converter;

    @Autowired
    private CommonService commonService;

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void save(BpmExeLogDTO bpmExeLogDTO) {
        BpmExeLogPO bpmExeLogPO = converter.DTOToPO(bpmExeLogDTO);
        bpmExeLogPO.setExelogId(commonService.getUniqueId(RedisUniqueKeyEnum.BPM_EXELOG));
        bpmExeLogPO.setExeCode(bpmExeLogDTO.getExeCode() ? "S" : "E");
        if (bpmExeLogDTO.getExeBodyStr().length() > MAX_SIZE) {
            bpmExeLogPO.setExeBody(bpmExeLogDTO.getExeBodyStr().substring(0, MAX_SIZE));
        } else {
            bpmExeLogPO.setExeBody(bpmExeLogDTO.getExeBodyStr());
        }
        bpmExeLogPO.setAddDate(new Date());
        baseMapper.insert(bpmExeLogPO);
    }

    @Override
    public void insertBpmlog(String funId, String orderName, String keyId, String code, String userName, boolean success, String desc, Object body) {
        BpmExeLogDTO protoBpmExeLogDTO = BpmExeLogDTO.builder()
                .funId(funId)
                .orderId(keyId)
                .orderCode(code)
                .orderName(orderName)
                .doUser(userName)
                .doDate(DateUtils.getFormatDate(DateUtils.COMMON_PATTERN))
                .exeCode(success)
                .exeDesc(desc)
                .exeBody(body)
                .build();
        this.save(protoBpmExeLogDTO);
    }
}
