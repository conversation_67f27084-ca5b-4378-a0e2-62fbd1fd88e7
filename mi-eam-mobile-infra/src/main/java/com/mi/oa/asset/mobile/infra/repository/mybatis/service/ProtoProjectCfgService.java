package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmProjectVO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectCfgPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectListPO;

import java.util.List;

/**
 * 项目配置
 *
 * <AUTHOR>
 * @date 2022/1/21 10:40
 */
public interface ProtoProjectCfgService extends IService<ProtoProjectCfgPO> {

    /**
     * 项目信息转为项目配置并初始化项目配置信息
     *
     * @param projectListPO
     * @return
     */
    ProtoProjectCfgPO getAndInitProtoProjectCfgPO(ProtoProjectListPO projectListPO);

    /**
     * 保存或者更新配置信息
     * @param projectListPO
     * @param factoryList
     */
    void saveOrUpdateCfg(ProtoProjectListPO projectListPO, List<MdmProjectVO.TrialProductionFactoryDTO> factoryList);

    /**
     * 通过项目编号查询配置
     * @param projectCodeList
     * @return
     */
    List<ProtoProjectCfgPO> listByProjectCodeList(List<String> projectCodeList);

    /**
     * 通过项目编号查询
     * @param projectCodeList
     * @return
     */
    List<ProtoProjectCfgPO> listProjectCodeList(List<String> projectCodeList);
}
