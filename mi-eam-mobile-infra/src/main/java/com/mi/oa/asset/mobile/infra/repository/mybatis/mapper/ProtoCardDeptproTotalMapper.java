package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardDeptproTotalPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * table_name : proto_card_deptpro_total
 * <AUTHOR>
 * @date 2022/04/01/10:21
 */
public interface ProtoCardDeptproTotalMapper extends BaseMapper<ProtoCardDeptproTotalPO> {
    /**
     * 按照部门维度进行统计
     * @param deptLevel
     * @return
     */
    List<ProtoCardDeptproTotalPO> statisticsByDeptLevel(@Param("deptLevel") Integer deptLevel, @Param("mrpType") String mrpType);

    /**
     * 查询所有类型
     * @return
     */
    @Select("select distinct mrp_type from proto_card_deptpro_total")
    List<String> getMrpTypeList();
}