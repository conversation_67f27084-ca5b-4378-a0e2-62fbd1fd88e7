package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.application.dto.bpm.BpmProcessCompletedDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleeveInfoPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleevePO;

import java.util.List;

/**
 * 工程机保密套拆除
 *
 * <AUTHOR>
 * @date 2022/1/29 10:13
 */
public interface ProtoSleeveService extends IService<ProtoSleevePO> {

    /**
     * 根据主键查询
     *
     * @param keyId
     * @return
     */
    ProtoSleevePO findProtoSleeve(String keyId);

    /**
     * 连表查询工程机保密套拆除和物料信息
     *
     * @param sleeveId
     * @return
     */
    List<ProtoSleeveInfoPO> selectSleeveInfoById(String sleeveId);

    /**
     * 更新领用方类型
     *
     * @param applyUserType
     * @param keyId
     * @param userId
     * @return
     */
    boolean updateType(String applyUserType,String keyId,String userId);

    /**
     * 修改流程信息
     *
     * @param sleeveId
     * @param auditing
     * @param modifyDate
     * @param businessKey
     * @return
     */
    boolean updProtoSleeveStatusBySleeveId(String sleeveId, String auditing, String modifyDate, String businessKey);

    /**
     * 修改流程信息
     *
     * @param sleeveId
     * @param auditing
     * @param modifyDate
     * @return
     */
    boolean updProtoSleeveStatusBySleeveId(String sleeveId, String auditing, String modifyDate);

    /**
     * 根据bpm唯一标识来查询工程机保密套拆除信息
     *
     * @param businessKey
     * @return
     */
    ProtoSleevePO findByBusinessKey(String businessKey);

    /**
     * 工程机保密套拆除回调
     *
     * @param param
     */
    boolean bpmCallback(BpmProcessCompletedDTO param);
}
