package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDeviceScanPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoDeviceScanMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoDeviceScanService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/23
 */
@Service
public class ProtoDeviceScanServiceImpl extends ServiceImpl<ProtoDeviceScanMapper, ProtoDeviceScanPO> implements ProtoDeviceScanService {

    @Override
    public List<ProtoDeviceScanPO> listByPlanId(String planId) {
        LambdaQueryWrapper<ProtoDeviceScanPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoDeviceScanPO::getPlanId, planId);
        return this.list(wrapper);
    }
}
