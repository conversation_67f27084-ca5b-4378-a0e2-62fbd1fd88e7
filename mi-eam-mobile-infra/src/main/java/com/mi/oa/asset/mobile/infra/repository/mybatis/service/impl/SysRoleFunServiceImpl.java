package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysRoleFunPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysRoleFunMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysRoleFunService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/11/21
 */
@Service
public class SysRoleFunServiceImpl extends ServiceImpl<SysRoleFunMapper, SysRoleFunPO> implements SysRoleFunService {

    @Override
    public List<String> getHaveAuthorityRoleId(String funId, List<String> roleIdList) {
        if (CollectionUtils.isEmpty(roleIdList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SysRoleFunPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRoleFunPO::getFunId, funId)
                .in(SysRoleFunPO::getRoleId, roleIdList);
        return this.list(wrapper).stream().map(SysRoleFunPO::getRoleId).collect(Collectors.toList());
    }
}
