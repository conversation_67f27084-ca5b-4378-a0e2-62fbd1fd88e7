package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInnerPurchasePlanPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * table_name : proto_inner_purchase_plan
 * <AUTHOR>
 * @date 2022/08/01/10:42
 */
public interface ProtoInnerPurchasePlanMapper extends BaseMapper<ProtoInnerPurchasePlanPO> {
    @Select({
            "select mrp_type ",
            "from proto_plan_det ",
            "where inner_purchase_plan_id in (",
            "   select id ",
            "   from proto_inner_purchase_plan ",
            "   where order_no = #{planCode})",
            "limit 1",
    })
    String getMrpType(@Param("planCode") String planCode);

    @Select({
            "select auditing ",
            "from proto_inner_purchase_plan ",
            "where order_no = #{planCode}",
            "limit 1",
    })
    String queryApprovalStatus(String planCode);
}