package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.BaselineStateEnum;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoBaselinePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoBaselineRecordDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysDeptPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoBaselineMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoBaselineRecordDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoBaselineService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysDeptService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.UserInfoService;
import com.mi.oa.infra.oaucf.idm.api.rep.UserBaseInfoDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 * @date 2022/2/28 15:58
 */
@Service
public class ProtoBaselineServiceImpl extends ServiceImpl<ProtoBaselineMapper, ProtoBaselinePO> implements ProtoBaselineService {

    @Autowired
    private ProtoBaselineRecordDetService protoBaselineRecordDetService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private SysDeptService sysDeptService;

    private static final BigDecimal BASELINE_CODE_SERNO = new BigDecimal(1);

    @Override
    public ProtoBaselinePO findOneByParam(ProtoBaselinePO param) {
        QueryWrapper<ProtoBaselinePO> queryWrapper = new QueryWrapper<>(param);
        return this.getOne(queryWrapper);
    }

    @Override
    public void updateBaselineStateByCode(String baselineCode, BaselineStateEnum stateEnum) {
        if(StringUtils.isEmpty(baselineCode)){
            return;
        }
        Optional<ProtoBaselinePO> line = Optional.ofNullable(this
                .findOneByParam(ProtoBaselinePO.builder().baselineCode(baselineCode)
                        .build()));
        line.ifPresent(t->{
            t.setState(stateEnum.getKey());
            this.updateById(t);
        });
    }

    @Override
    public void updateBaselineStateByRecordId(String baselineRecordId, BaselineStateEnum stateEnum) {
        if(StringUtils.isEmpty(baselineRecordId)){
            return;
        }
        List<ProtoBaselineRecordDetPO> dets = protoBaselineRecordDetService.findListByParam(ProtoBaselineRecordDetPO.builder().baselineRecordId(baselineRecordId).build());
        Optional<ProtoBaselineRecordDetPO> codeCol = dets.stream().filter(det->BASELINE_CODE_SERNO.compareTo(det.getBaselineTypeSerno())==0).findFirst();
        codeCol.ifPresent(t->updateBaselineStateByCode(codeCol.get().getBaselineUpdAfter(),stateEnum));
    }

    @Override
    public void refreshAllDeptName() {
        Integer pageNum = 1;
        while (true) {
            Page<ProtoBaselinePO> page = new Page<>(pageNum, CommonConstant.DEFAULT_PAGE_SIZE);
            LambdaQueryWrapper<ProtoBaselinePO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(ProtoBaselinePO::getBaselineId, ProtoBaselinePO::getCpCode,
                    ProtoBaselinePO::getCpDeptName);
            List<ProtoBaselinePO> records = this.page(page, queryWrapper).getRecords();
            for (ProtoBaselinePO baselinePO : records){
                String cpCode = baselinePO.getCpCode();
                try {
                    UserBaseInfoDto info = userInfoService.getCacheUserInfoByUserName(cpCode);
                    String deptId = info.getDeptId();
                    baselinePO.setCpDeptCode(deptId);
                    SysDeptPO deptPO = sysDeptService.getCacheByDeptId(deptId);
                    if (null != deptPO){
                        baselinePO.setCpDeptName(deptPO.getLongDeptName());
                    }
                } catch (Exception e){
                }
            }
            pageNum++;
            this.updateBatchById(records);
            if (records.size() < CommonConstant.DEFAULT_PAGE_SIZE){
                return;
            }
        }
    }
}
