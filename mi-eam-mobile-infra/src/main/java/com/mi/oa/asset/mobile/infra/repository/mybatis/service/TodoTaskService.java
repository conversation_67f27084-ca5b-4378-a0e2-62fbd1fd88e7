package com.mi.oa.asset.mobile.infra.repository.mybatis.service;



import com.mi.oa.asset.mobile.application.dto.AssetTaskDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @title: LabTodoTaskService
 * @projectName mi-eam-lab-parent
 * @description: TODO
 * @date 2022/6/8下午3:47
 **/
public interface TodoTaskService {
    /**
     　　* @description 发送代办消息到员工端
     */
    void sendMessage(List<AssetTaskDTO> taskDTO);
}
