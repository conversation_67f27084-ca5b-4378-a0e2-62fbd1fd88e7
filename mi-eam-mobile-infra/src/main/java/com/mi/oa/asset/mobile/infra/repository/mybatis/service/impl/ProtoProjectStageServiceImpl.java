package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mi.oa.asset.mobile.common.enums.MrpTypeEnum;
import com.mi.oa.asset.mobile.infra.dto.mier.MaterialDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.converter.ProtoProjectStageConverter;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectStagePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoProjectStageMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectStageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum.PROTO_PROJECT_STAGE;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/27 11:16
 */
@Slf4j
@Service
public class ProtoProjectStageServiceImpl extends ServiceImpl<ProtoProjectStageMapper, ProtoProjectStagePO> implements ProtoProjectStageService {

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProtoProjectStageConverter protoProjectStageConverter;

    @Override
    public List<ProtoProjectStagePO> listByProjectCode(List<String> projectCodeList) {
        if (CollectionUtils.isEmpty(projectCodeList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoProjectStagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoProjectStagePO::getProjectCode, projectCodeList);
        return this.list(wrapper);
    }

    @Override
    public List<ProtoProjectStagePO> listBySkuCodeListAndMrpType(List<String> skuCodeList, String mrpType) {
        if (CollectionUtils.isEmpty(skuCodeList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoProjectStagePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoProjectStagePO::getSkuCode, skuCodeList);
        wrapper.eq(StringUtils.isNotBlank(mrpType), ProtoProjectStagePO::getMrpType, mrpType);
        return this.list(wrapper);
    }

    @Override
    public String getListDescBySkuCodeAndMrpType(String skuCode, String mrpType) {
        if (StringUtils.isBlank(skuCode)){
            return null;
        }
        List<ProtoProjectStagePO> stagePOList = listBySkuCodeListAndMrpType(Lists.newArrayList(skuCode), mrpType);
        return CollectionUtils.isEmpty(stagePOList) ? "" : stagePOList.get(0).getListDesc();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MaterialDTO> syncTvData(List<MaterialDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            log.info("sync tv data, origin list is empty.");
            return Collections.EMPTY_LIST;
        }
        // 过滤TV物料数据
        dtoList.forEach(item -> item.setMrpType(item.getMrpType().toLowerCase(Locale.ROOT)));
        List<MaterialDTO> tvList = dtoList.stream()
                .filter(item -> MrpTypeEnum.TV.getType().equals(item.getMrpType()))
                .collect(Collectors.toList());
        if (tvList.isEmpty()) {
            log.info("sync tv data, filter tv list is empty.");
            return Collections.EMPTY_LIST;
        }
        List<ProtoProjectStagePO> poList = protoProjectStageConverter.protoProjectStageDTO2POList(tvList);
        // 批量保存或更新
        saveOrUpdateBatchList(poList);
        // 返回入库或更新数据
        return protoProjectStageConverter.protoProjectStagePO2DTOList(poList);
    }


    private void saveOrUpdateBatchList(List<ProtoProjectStagePO> poList) {
        List<String> skuCodeList = poList.stream().map(ProtoProjectStagePO::getSkuCode).collect(Collectors.toList());
        List<ProtoProjectStagePO> existList = this.listBySkuCodeListAndMrpType(skuCodeList, poList.get(0).getMrpType());
        List<ProtoProjectStagePO> saveList = poList.stream()
                .filter(item -> !existList.stream()
                        .map(ele -> ele.getSkuCode())
                        .collect(Collectors.toList())
                        .contains(item.getSkuCode()))
                .collect(Collectors.toList());
        log.info("sync tv data, filterList:{}, saveList:{}, updateList:{}", poList.size(), saveList.size(), existList.size());
        // 已存在，更新记录
        if (!CollectionUtils.isEmpty(existList)) {
            Map<String, ProtoProjectStagePO> mapPO =
                    poList.stream().collect(Collectors.toMap(ProtoProjectStagePO::getSkuCode, ele -> ele));
            existList.forEach(item -> item.setSkuName(mapPO.get(item.getSkuCode()).getSkuName()));
            this.updateBatchById(existList);
        }
        // 不存在，新增记录
        if (!CollectionUtils.isEmpty(saveList)) {
            saveList.forEach(item -> item.setStageId(commonService.getUniqueId(PROTO_PROJECT_STAGE)));
            this.saveBatch(saveList);
        }
    }
}
