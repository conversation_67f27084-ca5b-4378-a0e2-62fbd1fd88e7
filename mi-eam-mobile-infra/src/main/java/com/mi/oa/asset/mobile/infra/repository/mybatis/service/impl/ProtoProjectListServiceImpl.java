package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmProjectVO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectListPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoProjectListMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectCfgService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectListService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/24 17:34
 */
@Service
public class ProtoProjectListServiceImpl extends ServiceImpl<ProtoProjectListMapper, ProtoProjectListPO> implements ProtoProjectListService {

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProtoProjectCfgService protoProjectCfgService;

    /**
     * 更新项目目录表PM信息
     *
     * @param pmUserName
     * @param pmUserCode
     * @param pmEmpCode
     * @param modifyDate
     * @param modifyUserid
     * @param projectCode
     * @return
     */
    @Override
    public boolean updateProjectListPM(String pmUserName, String pmUserCode, String pmEmpCode, String modifyDate, String modifyUserid, String projectCode) {
        LambdaUpdateWrapper<ProtoProjectListPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoProjectListPO::getProjectCode, projectCode)
                .set(ProtoProjectListPO::getPmUserName, pmUserName)
                .set(ProtoProjectListPO::getPmUserCode, pmUserCode)
                .set(ProtoProjectListPO::getPmEmpCode, pmEmpCode)
                .set(ProtoProjectListPO::getModifyDate, modifyDate)
                .set(ProtoProjectListPO::getModifyUserid, modifyUserid);
        return this.update(updateWrapper);
    }

    /**
     * 批量更新项目目录表PM信息
     *
     * @param pmUserName
     * @param pmUserCode
     * @param pmEmpCode
     * @param modifyDate
     * @param modifyUserid
     * @param projectCodes
     * @return
     */
    @Override
    public boolean batchUpdateProjectListPM(String pmUserName, String pmUserCode, String pmEmpCode, String modifyDate, String modifyUserid, String[] projectCodes) {
        LambdaUpdateWrapper<ProtoProjectListPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(ProtoProjectListPO::getProjectCode, projectCodes)
                .set(ProtoProjectListPO::getPmUserName, pmUserName)
                .set(ProtoProjectListPO::getPmUserCode, pmUserCode)
                .set(ProtoProjectListPO::getPmEmpCode, pmEmpCode)
                .set(ProtoProjectListPO::getModifyDate, modifyDate)
                .set(ProtoProjectListPO::getModifyUserid, modifyUserid);
        return this.update(updateWrapper);
    }

    /**
     * 查询所有项目列表
     *
     * @return
     */
    @Override
    public List<ProtoProjectListPO> getAllProjectList() {
        QueryWrapper<ProtoProjectListPO> queryWrapper = new QueryWrapper<>();
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateProject(ProtoProjectListPO projectListPO, List<MdmProjectVO.TrialProductionFactoryDTO> factoryList) {
        String projectCode = projectListPO.getProjectCode();
        ProtoProjectListPO project = getByProjectCode(projectCode);
        projectListPO.setModifyDate(new Date());
        if (null != project){
            projectListPO.setProjectListId(project.getProjectListId());
        } else {
            projectListPO.setProjectListId(commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_PROJECT_LIST));
            projectListPO.setAddDate(new Date());
        }
        this.saveOrUpdate(projectListPO);
        // 保存或者更新配置
        protoProjectCfgService.saveOrUpdateCfg(projectListPO, factoryList);
    }

    /**
     * 通过项目编码查询项目
     * @param projectCode
     * @return
     */
    @Override
    public ProtoProjectListPO getByProjectCode(String projectCode) {
        LambdaQueryWrapper<ProtoProjectListPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoProjectListPO::getProjectCode, projectCode);
        return this.getOne(wrapper, false);
    }

    @Override
    public ProtoProjectListPO getByProjectCodeAndMrpType(String projectCode, String mrpType) {
        LambdaQueryWrapper<ProtoProjectListPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoProjectListPO::getProjectCode, projectCode);
        wrapper.eq(ProtoProjectListPO::getMrpType, mrpType);
        return this.getOne(wrapper, false);
    }

    @Override
    public List<ProtoProjectListPO> getByProjectCodes(List<String> projectCodes) {
        LambdaQueryWrapper<ProtoProjectListPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoProjectListPO::getProjectCode, projectCodes);
        return this.list(wrapper);
    }
}
