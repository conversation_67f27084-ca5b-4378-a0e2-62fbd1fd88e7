package com.mi.oa.asset.mobile.infra.config;

import com.mi.info.comb.neptune.client.web.NeptuneFilter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2021/12/7 10:23
 */
@Configuration
public class NeptuneConfig {
    @Bean
    public FilterRegistrationBean neptuneFilterRegistration() {
        FilterRegistrationBean registrationBean = new FilterRegistrationBean();
        registrationBean.setFilter(new NeptuneFilter());
        registrationBean.addUrlPatterns("/*");
        //保证该Filter的排序靠前
        registrationBean.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return registrationBean;
    }
}
