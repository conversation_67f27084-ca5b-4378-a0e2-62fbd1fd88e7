package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysRolePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysUserRolePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysUserRoleMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysRoleFunService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysRoleService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysUserRoleService;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户拥有角色
 *
 * <AUTHOR>
 * @date 2022/1/25 17:27
 */
@Service
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRolePO> implements SysUserRoleService {

    @Autowired
    private CommonService commonService;

    @Autowired
    private SysRoleService sysRoleService;

    @Autowired
    private SysRoleFunService sysRoleFunService;

    /**
     * 添加用户角色
     *
     * @param roleNo
     * @param userCode
     * @return
     */
    @Override
    public void addSysUserRole(String roleNo, String userCode) {
        SysRolePO rolePO = sysRoleService.getSysRoleByRoleNo(roleNo);
        if (null == rolePO){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_ROLE_NO, roleNo);
        }
        String roleId = rolePO.getRoleId();
        Integer count = this.countByUserRole(roleId, userCode);
        if (count == 0){
            SysUserRolePO sysUserRolePO = new SysUserRolePO();
            sysUserRolePO.setUserRoleId(commonService.getUniqueId(RedisUniqueKeyEnum.SYS_USER_ROLE));
            sysUserRolePO.setRoleId(roleId);
            sysUserRolePO.setUserId(userCode);
            baseMapper.insert(sysUserRolePO);
        }
    }

    @Override
    public Integer countByRoleNoAndUserCode(String roleNo, String userCode) {
        SysRolePO rolePO = sysRoleService.getSysRoleByRoleNo(roleNo);
        return countByUserRole(rolePO.getRoleId(), userCode);
    }

    @Override
    public List<String> getMrpType(String userId, String funId) {
        List<String> roleIdList = baseMapper.getRoleIdByUserId(userId);
        List<String> haveRoleIdList = sysRoleFunService.getHaveAuthorityRoleId(funId, roleIdList);
        return sysRoleService.getMrpType(haveRoleIdList);
    }

    @Override
    public List<String> getUserIdsByRoleNo(String roleNo) {
        return baseMapper.getUserIdsByRoleNo(roleNo);
    }

    private Integer countByUserRole(String roleId, String userCode){
        LambdaQueryWrapper<SysUserRolePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUserRolePO::getUserId, userCode)
                .eq(SysUserRolePO::getRoleId, roleId);
        return this.count(wrapper);
    }
}
