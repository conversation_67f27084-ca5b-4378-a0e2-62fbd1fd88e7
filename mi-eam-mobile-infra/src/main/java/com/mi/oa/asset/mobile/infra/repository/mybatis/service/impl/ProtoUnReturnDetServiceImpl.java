package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoUnReturnMatPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoUnReturnMatMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoUnReturnDetService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2022/2/25 14:56
 */
@Service
public class ProtoUnReturnDetServiceImpl extends ServiceImpl<ProtoUnReturnMatMapper, ProtoUnReturnMatPO> implements ProtoUnReturnDetService {
    @Override
    public List<ProtoUnReturnMatPO> findListByParam(ProtoUnReturnMatPO param) {
        QueryWrapper<ProtoUnReturnMatPO> queryWrapper = new QueryWrapper<>(param);
        return list(queryWrapper);
    }

    @Override
    public List<ProtoUnReturnMatPO> listByUnreturnId(String unreturnId) {
        LambdaQueryWrapper<ProtoUnReturnMatPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoUnReturnMatPO::getUnreturnId, unreturnId);
        return this.list(wrapper);
    }
}
