package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.mi.oa.asset.mobile.infra.dto.mdm.MdmBaseVO;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmCostDTO;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmProjectDTO;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmProjectVO;
import com.mi.oa.asset.mobile.infra.remote.sdk.MdmClient;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.MdmService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysOperLogService;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import com.mi.oa.asset.mobile.utils.HttpClientUtil;
import com.mi.oa.asset.x5.common.X5Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/12
 */
@Service
@Slf4j
public class MdmServiceImpl implements MdmService {

    @Value("${mierMdm.host}")
    private String mierMdmHost;

    @Value("${mierMdm.appId}")
    private String mierMdmAppId;

    @Value("${mierMdm.appKey}")
    private String mierMdmAppKey;

    @Autowired
    private MdmClient mdmClient;

    @Value("${mdm.queryKey.project}")
    private String projectQueryKey;
    @Autowired
    private SysOperLogService sysOperLogService;

    @Override
    public X5Response createCostCenter(String createId, String ccCode) {
        if (StringUtils.isBlank(createId) || StringUtils.isBlank(ccCode)) {
            return null;
        }
        MdmCostDTO request = new MdmCostDTO();
        request.setCreateId(createId);
        request.setCcCode(ccCode);
        // code = 200  创建成功
        // code = 0 成本中心编码校验不合规
        // code = 2 成本中心编码在系统中已存在
        // code = 3 成本中心编码在流程中存在
        // 其他可能 公司主体代码（成本中心1-5位）MDM那边主数据库中查不到会报错返回 https://xiaomi.f.mioffice.cn/docx/doxk4FI7fd9yV1oYBJ5PebpZL4d
        X5Response x5Response = HttpClientUtil.doX5Post(mierMdmHost + "/integrate/x5", mierMdmAppId, mierMdmAppKey, request, "");
        log.info("create cost center createId:{},ccCode:{}, response:{}", createId,ccCode, GsonUtil.toJsonString(x5Response));
        sysOperLogService.saveOrUpdateLog(x5Response, GsonUtil.toJsonString(request), ccCode, createId);
        return x5Response;
    }


    @Override
    public MdmBaseVO<MdmProjectVO> getProject(String projectCode, String updateTime, Integer pageNum, Integer pageSize) {
        MdmProjectDTO projectDTO = MdmProjectDTO.builder().projectName(projectCode).queryKey(projectQueryKey)
                .modelPubDate(updateTime).pageNum(pageNum).pageSize(pageSize).build();
        return mdmClient.getProject(projectDTO);
    }
}
