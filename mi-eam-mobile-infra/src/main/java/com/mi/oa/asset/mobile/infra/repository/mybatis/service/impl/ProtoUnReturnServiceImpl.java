package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.DisposalTypeEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoUnReturnPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoUnReturnMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoUnReturnService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2022/2/25 14:33
 */
@Service
public class ProtoUnReturnServiceImpl extends ServiceImpl<ProtoUnReturnMapper, ProtoUnReturnPO> implements ProtoUnReturnService {
    @Override
    public List<ProtoUnReturnPO> findListByParam(ProtoUnReturnPO param) {
        QueryWrapper<ProtoUnReturnPO> queryWrapper = new QueryWrapper<>(param);
        return this.list(queryWrapper);
    }

    @Override
    public ProtoUnReturnPO getByUnreturnCode(String unreturnCode) {
        if (StringUtils.isBlank(unreturnCode)){
            return null;
        }
        LambdaQueryWrapper<ProtoUnReturnPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoUnReturnPO::getUnreturnCode, unreturnCode);
        return this.getOne(wrapper);
    }

    @Override
    public List<ProtoUnReturnPO> offline() {
        LambdaQueryWrapper<ProtoUnReturnPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoUnReturnPO::getDisposalType, DisposalTypeEnum.LOSE.getType())
                .in(ProtoUnReturnPO::getOrderStatus, "2", "3");
        return this.list(wrapper);
    }

    @Override
    public List<ProtoUnReturnPO> listByCodeList(List<String> orderCodeList) {
        if (CollectionUtils.isEmpty(orderCodeList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoUnReturnPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoUnReturnPO::getUnreturnCode, orderCodeList);
        return this.list(wrapper);
    }
}
