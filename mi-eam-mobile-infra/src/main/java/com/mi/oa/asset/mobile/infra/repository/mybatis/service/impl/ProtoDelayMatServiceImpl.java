package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDelayMatPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoDelayMatMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoDelayMatService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 工程机延期归还物料明细
 *
 * <AUTHOR>
 * @date 2022/2/18 17:45
 */
@Service
public class ProtoDelayMatServiceImpl extends ServiceImpl<ProtoDelayMatMapper, ProtoDelayMatPO>  implements ProtoDelayMatService {

    /**
     * 根据延期归还申请ID查询延期归还物料明细
     *
     * @param delayId
     * @return
     */
    @Override
    public List<ProtoDelayMatPO> getListByDelayId(String delayId){
        LambdaQueryWrapper<ProtoDelayMatPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProtoDelayMatPO::getDelayId, delayId);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 根据延期归还申请ID和当前归还时间查询延期归还物料明细
     *
     * @param delayId
     * @param endDate
     * @return
     */
    @Override
    public List<ProtoDelayMatPO> getListByDelayIdAndEndDate(String delayId, String endDate){
        LambdaQueryWrapper<ProtoDelayMatPO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(ProtoDelayMatPO::getDelayId, delayId);
        queryWrapper.gt(ProtoDelayMatPO::getEndDate, endDate);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<ProtoDelayMatPO> findListByParam(ProtoDelayMatPO param) {
        LambdaQueryWrapper<ProtoDelayMatPO> queryWrapper = new LambdaQueryWrapper(param);
        return baseMapper.selectList(queryWrapper);
    }

}
