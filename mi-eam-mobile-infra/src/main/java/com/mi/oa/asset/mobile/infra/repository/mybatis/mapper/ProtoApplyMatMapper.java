package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyMatPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * table_name : proto_apply_mat
 * <AUTHOR>
 * @date 2022/01/05/03:58
 */
public interface ProtoApplyMatMapper extends BaseMapper<ProtoApplyMatPO> {

    @Select("select count(1) from proto_apply_mat where apply_id = #{applyId} and apply_num != out_num")
    Integer countByApplyIdAndNotFinish(@Param("applyId") String applyId);
}