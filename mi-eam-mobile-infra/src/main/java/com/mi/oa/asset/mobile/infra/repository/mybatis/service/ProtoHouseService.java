package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoHousePO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/12/7
 */
public interface ProtoHouseService extends IService<ProtoHousePO> {

    String getHoseNameByCode(String houseCode);

    List<ProtoHousePO> listByHouseCode(List<String> houseCodeList);
}
