package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.FunColPO;

/**
 * table_name : fun_col
 * <AUTHOR>
 * @date 2022/01/07/11:00
 */
public interface FunColMapper extends BaseMapper<FunColPO> {
    /**
     *
     * @mbg.generated
     */
    FunColPO selectByPrimaryKey(String colId);
}