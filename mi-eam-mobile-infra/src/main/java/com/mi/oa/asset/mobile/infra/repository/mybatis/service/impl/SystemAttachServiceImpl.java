package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SystemAttachPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SystemAttachMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SystemAttachService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SystemAttachServiceImpl extends ServiceImpl<SystemAttachMapper, SystemAttachPO> implements SystemAttachService {

    @Autowired
    SystemAttachMapper systemAttachMapper;

    @Override
    public List<SystemAttachPO> getFileName(String funId, String dataId) {
        return this.list(new LambdaUpdateWrapper<SystemAttachPO>().eq(SystemAttachPO::getDataId, dataId).eq(SystemAttachPO::getFunId, funId));
    }

    @Override
    public boolean saveAttachByBatch(List<SystemAttachPO> attachList) {
        return this.saveBatch(attachList);
    }

    @Override
    public int batchDeleteSystemAttach(String funId, List<String> dataIds) {
        return systemAttachMapper.delete(new LambdaQueryWrapper<SystemAttachPO>().in(SystemAttachPO::getDataId, dataIds).eq(SystemAttachPO::getFunId, funId));
    }
}
