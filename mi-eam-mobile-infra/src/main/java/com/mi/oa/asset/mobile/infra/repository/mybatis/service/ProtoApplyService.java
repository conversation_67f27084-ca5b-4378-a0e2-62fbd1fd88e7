package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyPO;

import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/5 15:59
 */
public interface ProtoApplyService extends IService<ProtoApplyPO> {
    /**
     * 查找
     * @param applyId
     * @return
     */
    ProtoApplyPO findByApplyId(String applyId);

    /**
     * 根据businessKey查找
     * @param businessKey
     * @return
     */
    ProtoApplyPO findByBusinessKey(String businessKey);
    /**
     * 更新成本中心
     * @param applyId
     * @param centerCode
     * @param compDeptCode
     * @return
     */
    boolean updateCostCenter(String applyId,String centerCode,String compDeptCode);

    /**
     * 更新更新人员及时间
     * @param applyId
     * @param applyUserType
     * @param userId
     * @param date
     * @return
     */
    boolean updateApplyUserTypeModifyUseridModifyDate(String applyId, String applyUserType, String userId,Date date);

    /**
     * 保存流程信息
     * @param applyId
     * @param auditing
     * @param modifyDate
     * @param businessKey
     * @return
     */
    boolean updateAuditingModifyDateBusinessKeyByApplyId(String applyId,String auditing,Date modifyDate,String businessKey);

    /**
     * 通过单号查询详情
     * @param applyCode
     * @return
     */
    ProtoApplyPO getByApplyCode(String applyCode);

    /**
     * 更新单据状态
     * @param applyStatus
     * @param applyId
     */
    void updateApplyStatusByApplyId(String applyStatus, String applyId);

    List<ProtoApplyPO> listToPushSap();

    /**
     * 查询已审批 样机生产地是空的数据
     * @return
     */
    List<ProtoApplyPO> listMackCnIsBlank();
}
