package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MailTemplateParamPO;

/**
 * table_name : mail_template_param
 * <AUTHOR>
 * @date 2022/01/06/02:51
 */
public interface MailTemplateParamMapper extends BaseMapper<MailTemplateParamPO> {
    /**
     *
     * @mbg.generated
     */
    MailTemplateParamPO selectByPrimaryKey(String paramId);
}