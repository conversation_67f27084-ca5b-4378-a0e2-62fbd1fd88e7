package com.mi.oa.asset.mobile.infra.config;

import com.mi.oa.asset.mobile.infra.config.interceptor.LocationInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/10/11 15:43
 */

@Configuration
@EnableWebMvc
public class InterceptorConfigurer implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry){
        registry.addInterceptor(new LocationInterceptor());
    }
}


