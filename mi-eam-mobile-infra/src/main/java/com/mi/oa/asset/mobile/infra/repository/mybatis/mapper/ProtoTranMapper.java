package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.application.dto.ConditionDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoTranSapDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/2
 */
public interface ProtoTranMapper extends BaseMapper<ProtoTranPO> {

    List<ProtoTranSapDTO> getNoAsnOrderTranList();

    /**
     * 查询权限
     * @param conditionList 条件集合
     * @param start
     * @param limit
     * @return
     */
    List<ProtoTranPO> getTranList(@Param("userCode") String userCode, @Param("conditionList") List<ConditionDTO> conditionList,
                                  @Param("start") Integer start, @Param("limit") Integer limit,
                                  @Param("count") Integer count, @Param("deptCodeList") List<String> deptCodeList,
                                  @Param("whereSql") String whereSql);

    /**
     * 查询权限
     * @param userCode
     * @param conditionList 条件集合
     * @param count
     * @param deptCodeList
     * @return
     */
    Integer countTranList(@Param("userCode") String userCode, @Param("conditionList") List<ConditionDTO> conditionList,
                          @Param("count")Integer count, @Param("deptCodeList") List<String> deptCodeList,
                          @Param("whereSql") String whereSql);
}
