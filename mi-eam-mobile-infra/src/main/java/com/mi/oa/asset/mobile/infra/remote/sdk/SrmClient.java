package com.mi.oa.asset.mobile.infra.remote.sdk;

import com.mi.oa.asset.mobile.infra.config.SrmClientConfig;
import com.mi.oa.asset.mobile.infra.config.X5FeignMethod;
import com.mi.oa.asset.mobile.infra.dto.srm.*;
import com.mi.oa.asset.x5.consumer.X5FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/10/29
 */
@X5FeignClient(value = "srm", url = "${srm.host}", appId = "${srm.appId}", appKey = "${srm.appKey}", configuration = SrmClientConfig.class)
public interface SrmClient {



    /**
     * 资产跨公司调拨至ECC系统 ZEAMFI02
     *
     * @param
     * @return
     */
    @PostMapping("")
    @X5FeignMethod("SRM.VDATA.XM959")
    ProtoAsnOrderListDTO synMobile(AsnMobileDTO mobileDTO);


    @PostMapping("")
    @X5FeignMethod("SRM.VDATA.XM958")
    PoDetailListDTO getPODetail(PoRequestDTO requestDTO);

    @PostMapping("")
    @X5FeignMethod("SRM.SN.XM936")
    SnDetailListDTO getSnDetail(SnRequestDTO requestDTO);

}
