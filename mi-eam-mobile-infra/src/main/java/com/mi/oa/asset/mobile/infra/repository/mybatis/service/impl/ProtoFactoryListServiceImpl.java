package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoFactoryListPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoFactoryListMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoFactoryListService;
import org.springframework.stereotype.Service;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/14
 */
@Service
public class ProtoFactoryListServiceImpl extends ServiceImpl<ProtoFactoryListMapper, ProtoFactoryListPO>
        implements ProtoFactoryListService {


    @Override
    public String getFactoryCodeByName(String factoryName) {
        LambdaQueryWrapper<ProtoFactoryListPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoFactoryListPO::getFactoryName, factoryName);
        ProtoFactoryListPO listPO = this.getOne(wrapper, false);
        return (null == listPO ? null : listPO.getFactoryCode());
    }
}
