package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoUnReturnMatPO;

/**
 * table_name : proto_unreturn_mat
 * <AUTHOR>
 * @date 2022/02/25/02:55
 */
public interface ProtoUnReturnMatMapper extends BaseMapper<ProtoUnReturnMatPO> {
    /**
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String unreturnMatId);
}