package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.application.dto.UnReturnDTO;
import com.mi.oa.asset.mobile.application.dto.order.PlanProgressDTO;
import com.mi.oa.asset.mobile.application.dto.order.PreOrderDTO;
import com.mi.oa.asset.mobile.common.enums.AssetOrderStatusEnum;
import com.mi.oa.asset.mobile.common.enums.OrderTypeEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOrderPO;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/1
 */
public interface ProtoOrderService extends IService<ProtoOrderPO> {

    /**
     * 更新订单状态
     * @param orderIdList
     * @param orderStatusEnum
     */
    void updateStateByIds(List<Integer> orderIdList, AssetOrderStatusEnum orderStatusEnum);

    /**
     * 通知付款时 飞书消息
     * @param orderPO
     */
    void notifySendLark(ProtoOrderPO orderPO);

    /**
     * 验货飞书通知
     * @param orderPO
     * @param houseCode
     * @param templateTag
     */
    void checkNoticeSendLark(List<ProtoOrderPO> orderPO, PreOrderDTO dto, String houseCode, String templateTag);

    /**
     * 完成时 飞书消息
     * @param orderPO
     */
    void completeSendLark(ProtoOrderPO orderPO);

    /**
     * 线下支付 通知去上传
     * @param orderPO
     */
    void notifyUpload(ProtoOrderPO orderPO);

    /**
     * 订单取消时消息通知
     * @param orderPO
     */
    void notifyCancel(ProtoOrderPO orderPO);

    /**
     * 订单管理员取消
     * @param orderPO
     */
    void notifyAdminCancel(ProtoOrderPO orderPO, String userCode, String userName);

    /**
     * 查询订单详情
     * @param salesOrder 根据销售订单号
     * @return
     */
    ProtoOrderPO getBySalesOrder(String salesOrder);

    /**
     * 通过订单查询详情
     * @param orderId
     * @return
     */
    ProtoOrderPO getByOrderId(String orderId);

    /**
     * 通过销售订单号查询
     * @param salesOrderList
     * @return
     */
    List<ProtoOrderPO> listBySalesOrder(List<String> salesOrderList);

    /**
     * 查询数据
     * @param orderNo
     * @return
     */
    Integer countByStatus(String orderNo, OrderTypeEnum orderTypeEnum, AssetOrderStatusEnum statusEnum);

    /**
     * 实际内购收益
     * @param orderNo
     * @return
     */
    BigDecimal sumEndProfit(String orderNo);

    /**
     * 内购进度
     * @param planCode
     * @return
     */
    PlanProgressDTO planProgress(String planCode);

    /**
     * 查询内购明细
     * @param planCode
     * @return
     */
    List<ProtoOrderPO> listByPlanCode(String planCode, AssetOrderStatusEnum assetOrderStatusEnum);
}
