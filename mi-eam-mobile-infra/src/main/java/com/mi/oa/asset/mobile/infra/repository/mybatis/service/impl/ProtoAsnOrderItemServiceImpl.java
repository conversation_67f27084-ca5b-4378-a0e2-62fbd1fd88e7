package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.ErrorCodeEnum;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.infra.dto.srm.ProtoAsnOrderListDTO;
import com.mi.oa.asset.mobile.infra.dto.srm.SnDetailListDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderItemPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoAsnOrderItemMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoAsnOrderDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoAsnOrderItemService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SrmService;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/1
 */
@Service
@Slf4j
public class ProtoAsnOrderItemServiceImpl extends ServiceImpl<ProtoAsnOrderItemMapper, ProtoAsnOrderItemPO>
        implements ProtoAsnOrderItemService {

    private final String IS_IDENTIFICATION = "X";

    private final String HAS_SN_NUM = "hasSnNum";

    private final String NO_SN_NUM = "noSnNum";

    @Autowired
    private SrmService srmService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProtoAsnOrderDetService protoAsnOrderDetService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveItems(ProtoAsnOrderListDTO.ProtoAsnOrderDTO orderDTO) {
        List<ProtoAsnOrderListDTO.ProtoAsnOrderItemDTO> items = orderDTO.getItems();
        List<ProtoAsnOrderItemPO> itemDOList = new ArrayList<>(items.size());

        // 校验item SN数量是否正确
        Map<String, Integer> snNumMap = checkItemSN(items, orderDTO.getOrderCode());
        for (ProtoAsnOrderListDTO.ProtoAsnOrderItemDTO itemDTO : items){
            ProtoAsnOrderItemPO itemDO = new ProtoAsnOrderItemPO();
            BeanUtils.copyProperties(itemDTO, itemDO);
            itemDTO.setOrderId(orderDTO.getOrderId());
            itemDTO.setIsDelete(0);
            itemDTO.setItemId(commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_ASN_ORDER_ITEM));
            itemDTO.setAddDate(LocalDateTime.now());
            itemDTO.setModifyDate(LocalDateTime.now());
            itemDTO.setProjectName(orderDTO.getProjectName());
            itemDTO.setMrpType(orderDTO.getMrpType());
            BeanUtils.copyProperties(itemDTO, itemDO);
            itemDOList.add(itemDO);
        }
        // 先删除
        this.deleteByOrderId(orderDTO.getOrderCode());
        this.saveBatch(itemDOList);

        // 收货表
        List<ProtoAsnOrderDetPO> detList = this.getAsnOrderDetListPO(orderDTO, snNumMap);

        // 先删除 后插入det表
        protoAsnOrderDetService.deleteByOrderDetId(orderDTO.getOrderCode());
        protoAsnOrderDetService.saveBatch(detList);

        return detList.size();
    }

    @Override
    public List<ProtoAsnOrderItemPO> getListByAsnOrder(List<String> list) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.in("asn_no", list);
        queryWrapper.orderByAsc("item_id");
        return this.list(queryWrapper);
    }

    /**
     * 校验SN数据的正确性
     * @param items
     */
    private Map<String, Integer> checkItemSN(List<ProtoAsnOrderListDTO.ProtoAsnOrderItemDTO> items, String asnNo) {
        Map<String, Integer> result = new HashMap<>(4);
        SnDetailListDTO snDetail = srmService.getSnDetail(asnNo);
        if (null == snDetail || CollectionUtils.isEmpty(snDetail.getDetailList()) ){
            log.error("find sn item error asnNo:{}, response:{}", asnNo, snDetail);
            throw new BizException(ErrorCodeEnum.SRM_DATA_ERROR, "未找到SN详情数据");
        }
        int hasSnNum = 0;
        int noSnNum = 0;
        for (ProtoAsnOrderListDTO.ProtoAsnOrderItemDTO dto : items){
            int quantity = dto.getQuantity().intValue();
            if (IS_IDENTIFICATION.equals(dto.getIsIdentification())){
                hasSnNum += quantity;
            } else {
                noSnNum += quantity;
            }
        }
        // SN数量不准确
        if (hasSnNum != snDetail.getDetailList().size()){
            log.error("has sn num error asnNO:{}, snDetail:{}", asnNo, snDetail);
            throw new BizException(ErrorCodeEnum.SRM_DATA_ERROR, "行项目中SN数量和SN明细数量不匹配");
        }
        result.put(HAS_SN_NUM, hasSnNum);
        result.put(NO_SN_NUM, noSnNum);
        return result;
    }

    private List<ProtoAsnOrderDetPO> getAsnOrderDetListPO(ProtoAsnOrderListDTO.ProtoAsnOrderDTO orderDTO, Map<String, Integer> snNumMap){
        List<SnDetailListDTO.SnDetailDTO> detailList = srmService.getSnDetail(orderDTO.getOrderCode()).getDetailList();
        int noSnNum = snNumMap.get(NO_SN_NUM);
        int sumNum = snNumMap.get(HAS_SN_NUM) + noSnNum;
        List<ProtoAsnOrderDetPO> result = new ArrayList<>(sumNum);
        String stageName = "";
        // 有SN
        for (SnDetailListDTO.SnDetailDTO dto : detailList){
            ProtoAsnOrderDetPO detPO = getDefaultProtoAsnOrderDetPO(orderDTO);
            detPO.setDeviceCode(dto.getSn());
            detPO.setImei(dto.getImei());
            detPO.setLaserCode(dto.getLdh());
            detPO.setDeviceType(dto.getConfiguration());
            detPO.setIsPseudoSn(0);
            detPO.setSku(dto.getSku());
            detPO.setSkuCode(dto.getSku());
            detPO.setSkuName(dto.getMaktx());
            detPO.setItemRowNo(dto.getAsnItemNo());
            result.add(detPO);
            // 设置阶段
            if (StringUtils.isBlank(stageName)){
                stageName = dto.getStageName();
                orderDTO.setStageName(stageName);
            }
        }
        // 无SN收货数量
        if (noSnNum != 0) {
            ProtoAsnOrderDetPO detPO = getDefaultProtoAsnOrderDetPO(orderDTO);
            detPO.setDeliveryNum(noSnNum);
            detPO.setIsPseudoSn(1);
            result.add(detPO);
        }

        return result;
    }

    private ProtoAsnOrderDetPO getDefaultProtoAsnOrderDetPO(ProtoAsnOrderListDTO.ProtoAsnOrderDTO orderDTO){
        ProtoAsnOrderListDTO.ProtoAsnOrderItemDTO itemDTO = orderDTO.getItems().get(0);
        ProtoAsnOrderDetPO detPO = new ProtoAsnOrderDetPO();
        detPO.setDetId(commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_ASN_ORDER_DET_ITEM));
        BeanUtils.copyProperties(itemDTO, detPO);
        detPO.setOrderId(itemDTO.getOrderId());
        detPO.setDeliveryNum(1);
        return detPO;
    }

    /**
     * 通过asnNo删除
     * @param asnNo
     */
    private void deleteByOrderId(String asnNo){
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("asn_no", asnNo);
        baseMapper.delete(queryWrapper);
    }
}
