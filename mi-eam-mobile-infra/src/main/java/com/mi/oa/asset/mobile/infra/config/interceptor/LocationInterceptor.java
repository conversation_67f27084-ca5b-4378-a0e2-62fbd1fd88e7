package com.mi.oa.asset.mobile.infra.config.interceptor;

import com.mi.info.comb.neptune.client.LanguageTypeThreadLocal;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2021/12/7 10:33
 */
public class LocationInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        LocaleContextHolder.setLocale(LanguageTypeThreadLocal.getLanguageType().getLocale());
        return true;
    }

}
