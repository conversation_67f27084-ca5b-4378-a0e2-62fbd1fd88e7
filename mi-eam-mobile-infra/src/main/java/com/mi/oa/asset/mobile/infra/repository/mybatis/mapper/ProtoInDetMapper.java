package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoInNoAsnDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoInSapDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoPushSapDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInDetPO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * table_name : proto_in_det
 * <AUTHOR>
 * @date 2021/12/10/03:36
 */
public interface ProtoInDetMapper extends BaseMapper<ProtoInDetPO> {

    List<ProtoInSapDTO> getNotAsnPushSap(@Param("inId") String inId);

    List<ProtoInNoAsnDTO> getNotAsnDetPushSAPList(@Param("inId") String inId, @Param("orderCode") String orderCode);

    @Select("select count(1) cnt from proto_in_det where in_id = #{inId} and (sap_ret_type is null or sap_ret_type in ('E', 'N', ''))")
    Integer countNotSuccess(@Param("inId") String inId);

    List<ProtoPushSapDTO> getPushSapList(@Param("inId") String inId);
}