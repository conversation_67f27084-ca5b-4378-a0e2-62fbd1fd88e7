package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MailTemplatePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MailTemplateParamPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.MailTemplateMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.MailTemplateParamService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.MailTemplateService;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
@Service
@Slf4j
public class MailTemplateServiceImpl extends ServiceImpl<MailTemplateMapper, MailTemplatePO> implements MailTemplateService {

    @Autowired
    private MailTemplateParamService mailTemplateParamService;

    @Override
    public MailTemplatePO getByTemplateTag(String templateTag) {
        LambdaQueryWrapper<MailTemplatePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MailTemplatePO::getTemplateTag, templateTag);
        MailTemplatePO templatePO = this.getOne(wrapper);
        if (null == templatePO){
            throw new BizException(ApplicationErrorCodeEnum.MAIL_TEMPLATE_001);
        }
        return templatePO;
    }

    @Override
    public String getMailContent(String templateTag, Map<String, String> valueMap) {
        MailTemplatePO templatePO = this.getByTemplateTag(templateTag);
        List<MailTemplateParamPO> paramPOList = mailTemplateParamService.listByTemplateId(templatePO.getTemplateId());
        String templateCont = templatePO.getTemplateCont();
        for (MailTemplateParamPO paramPO : paramPOList) {
            String paramName = (paramPO.getParamName() == null ? "" : paramPO.getParamName());
            String paramValue = (paramPO.getParamValue() == null ? "" : paramPO.getParamValue());
            String value = valueMap.get(paramName) == null ? paramValue : valueMap.get(paramName);
            templateCont = templateCont.replace("{" + paramName + "}", value);
        }
        return templateCont;
    }
}
