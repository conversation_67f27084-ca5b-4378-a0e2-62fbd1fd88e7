package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmProjectVO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectFactoryPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoProjectFactoryMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectFactoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/14
 */
@Service
public class ProtoProjectFactoryServiceImpl extends ServiceImpl<ProtoProjectFactoryMapper, ProtoProjectFactoryPO>
        implements ProtoProjectFactoryService {

    @Autowired
    private CommonService commonService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveProjectFactory(List<MdmProjectVO.TrialProductionFactoryDTO> factoryList, String cfgId) {

        // 先查询试产工厂
        List<ProtoProjectFactoryPO> projectFactoryList = this.listByCfgId(cfgId);
        Map<String, ProtoProjectFactoryPO> factoryMap = projectFactoryList.stream().filter(o -> !StringUtils.isEmpty(o.getUserCode()) || !StringUtils.isEmpty(o.getFactoryPm()))
                .collect(Collectors.toMap(ProtoProjectFactoryPO::getFactoryCode, o -> o, (k1, k2) -> k1));

        // 先删除
        this.deleteByCfgId(cfgId);
        if (CollectionUtils.isEmpty(factoryList)){
            return;
        }
        List<ProtoProjectFactoryPO> list = new ArrayList<>(factoryList.size());
        Date nowDate = new Date();
        for (MdmProjectVO.TrialProductionFactoryDTO factory : factoryList){
            ProtoProjectFactoryPO factoryPO = new ProtoProjectFactoryPO();
            factoryPO.setFactoryId(commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_PROJECT_FACTORY));
            factoryPO.setFactoryName(factory.getName());
            factoryPO.setFactoryId(commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_PROJECT_LIST));
            factoryPO.setFactoryCode(factory.getCode());
            factoryPO.setAddDate(nowDate);
            factoryPO.setModifyDate(nowDate);
            factoryPO.setCfgId(cfgId);
            ProtoProjectFactoryPO dbFactoryPO = factoryMap.get(factory.getCode());
            if (null != dbFactoryPO){
                factoryPO.setUserCode(dbFactoryPO.getUserCode());
                factoryPO.setUserName(dbFactoryPO.getUserName());
                factoryPO.setFactoryPm(dbFactoryPO.getFactoryPm());
            }
            list.add(factoryPO);

        }
        this.saveBatch(list);
    }

    @Override
    public void deleteByCfgId(String cfgId) {
        LambdaQueryWrapper<ProtoProjectFactoryPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoProjectFactoryPO::getCfgId, cfgId);
        this.remove(wrapper);
    }

    @Override
    public Integer countByCfgId(String cfgId) {
        LambdaQueryWrapper<ProtoProjectFactoryPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoProjectFactoryPO::getCfgId, cfgId);
        return count(wrapper);
    }

    @Override
    public List<ProtoProjectFactoryPO> listByCfgId(String cfgId) {
        LambdaQueryWrapper<ProtoProjectFactoryPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoProjectFactoryPO::getCfgId, cfgId);
        return this.list(wrapper);
    }


}
