package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysProxyPO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/7/12
 */
public interface SysProxyService extends IService<SysProxyPO> {

    /**
     * 获取代理账号
     * @param userCodeList
     * @return
     */
    List<String> getProxy(List<String> userCodeList);
}
