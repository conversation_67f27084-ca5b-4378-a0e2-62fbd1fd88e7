package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.dto.BackConfirmDto;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoInSapDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInPO;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/12/20
 */

public interface ProtoInService extends IService<ProtoInPO> {

    List<ProtoInPO> listByInIds(Collection<String> inIds);

    /**
     * 查询存在的待确认单据
     *
     * @param device_code
     * @return
     */
    List<BackConfirmDto> getbackConfirmByDeviceCode(String deviceCode);

    /**
     * 更新确认人
     * @param in_id
     * @param user_name
     * @param user_code
     * @param user_emp
     */
    void updateBackConfirmToUser(String inId,String userName,String userCode,String userEmp);
    /**
     * 根据多参数查询
     * @param po 查询参数
     * @return
     */
    List<ProtoInPO> findListByParam(ProtoInPO po);
    /**
     * 根据多参数查询
     * @param po 查询参数
     * @return
     */
    ProtoInPO findOneByParam(ProtoInPO po);

    /**
     * 查询所有已收货并且未抛SAP数据
     * @param pageNum
     * @return
     */
    List<ProtoInPO> getNotPushSAPList(Integer pageNum, Integer pageSize);

    /**
     * 无ASN抛送SAP
     * @param sapDTO
     */
    void noAsnOrderPushSAP(ProtoInSapDTO sapDTO, ProtoInPO protoInPO);

    /**
     * 获取所有待匹配清单
     * @param pageNum
     * @return
     */
    List<ProtoInPO> getUnMachList(int pageNum);

    /**
     * 处理单个待匹配清单
     * @param proto
     */
    void dealUnMach(ProtoInPO proto);

    /**
     * 通过收货单号查询数据
     * @param inCodes
     * @return
     */
    List<ProtoInPO> listByInCodeList(Collection<String> inCodes);

    /**
     * 收货
     * @param orderPO
     * @param itemList
     * @param inType 收货类型 InTypeEnum
     * @param userCode 工号
     * @param houseName 仓库名称
     * @param houseCode 仓库编码
     * @param houseId 仓库id
     * @return
     */
    String confirmReceiving(ProtoAsnOrderPO orderPO, List<ProtoAsnOrderDetPO> itemList, String inType, String userCode, String houseName, String houseCode, String houseId);

    /**
     * 通过主键id和收货类型查询数据
     * @param inIds
     * @param inType
     * @return
     */
    List<ProtoInPO> getByInIdsAndInType(List<String> inIds, String ... inType);

    /**
     * 抛SAP
     * @param inId 主键id
     * @param userName 用户
     */
    void pushSAP(String inId, String userName);

    /**
     * 查询empCode为null的小米内部员工信息的收货记录
     *
     * @return
     */
    List<ProtoInPO> getInByEmpCode();

    /**
     * 修改empCode为null的小米内部员工信息的收货记录
     */
    void updateInByEmpCode(String empCode, String userCode);

    /**
     * 查询最近7天ASN收货情况
     * @return
     */
    List<ProtoInPO> getAsnLastSevenDay();

    /**
     * 获取最新入库时间
     * @param snList
     * @return
     */
    List<Map<String, Object>> getEntryDate(List<String> snList);
}
