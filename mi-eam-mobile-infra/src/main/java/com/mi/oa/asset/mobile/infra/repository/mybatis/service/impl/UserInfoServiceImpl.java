package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.oa.asset.mobile.common.enums.RedisCachePrefixKeyEnum;
import com.mi.oa.asset.mobile.infra.dto.hrod.EmployeeInfo;
import com.mi.oa.asset.mobile.infra.dto.hrod.HrodEmployeeVO;
import com.mi.oa.asset.mobile.infra.errorcode.InfraErrorCodeEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoThirdUserPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysDeptPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.HrodService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoThirdUserService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysDeptService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.UserInfoService;
import com.mi.oa.asset.mobile.utils.BeanUtil;
import com.mi.oa.asset.mobile.utils.UserUtil;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.idm.api.IdmAccountService;
import com.mi.oa.infra.oaucf.idm.api.IdmAccountV2Service;
import com.mi.oa.infra.oaucf.idm.api.IdmUserService;
import com.mi.oa.infra.oaucf.idm.api.rep.AccountInfoDto;
import com.mi.oa.infra.oaucf.idm.api.rep.Resp;
import com.mi.oa.infra.oaucf.idm.api.rep.UserBaseInfoDto;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/8/27 1:22
 */

@Slf4j
@Service
public class UserInfoServiceImpl implements UserInfoService {

    public static final Integer SUCCESS_CODE = 0;

    @Autowired
    private HrodService hrodService;

    @Autowired
    private SysDeptService sysDeptService;

    @Autowired
    private IdmUserService idmUserService;

    @Autowired
    private IdmAccountService idmAccountService;

    @Autowired
    private ProtoThirdUserService protoThirdUserService;

    @Autowired
    private IdmAccountV2Service idmAccountV2Service;

    @Override
    public EmployeeInfo getEmpInfoByUserName(String userName) {
        HrodEmployeeVO employeeVO = hrodService.getEmpByUserName(userName);
        if (null == employeeVO) {
            return null;
        }

        EmployeeInfo employeeInfo = new EmployeeInfo();
        BeanUtils.copyProperties(employeeVO, employeeInfo);
        employeeInfo.setDeptNameLv3(employeeInfo.getDeptName());
        SysDeptPO res = sysDeptService.getCacheByDeptId(employeeInfo.getDeptId());
        if (null != res) {
            employeeInfo.setDeptName(res.getLongDeptName());
        }
        return employeeInfo;
    }

    @Override
    public UserBaseInfoDto getUserInfoByUserName(String userName) {
        String uid = getUidByUserName(userName);
        UserBaseInfoDto userInfo = getUserInfoByUid(uid);
        return userInfo;
    }

    @Override
    public UserBaseInfoDto getCacheUserInfoByUserName(String userName) {
        String cacheKey = RedisCachePrefixKeyEnum.IDM_CACHE_USER_CODE.getPrefixKey()+userName;
        Object obj = RedisUtils.get(cacheKey);
        if (null != obj){
            return (UserBaseInfoDto) obj;
        }
        UserBaseInfoDto baseInfoDto = getUserInfoByUserName(userName);
        RedisUtils.setEx(cacheKey, baseInfoDto, 30, TimeUnit.MINUTES);
        return baseInfoDto;
    }

    @Override
    public UserBaseInfoDto getNullCacheUserInfoByUserName(String userName) {
        if (StringUtils.isBlank(userName)){
            return new UserBaseInfoDto();
        }
        UserBaseInfoDto userInfo = null;
        try {
            userInfo = this.getCacheUserInfoByUserName(userName);
        } catch (Exception e){
            log.error("search_user_info_error:{}", userName);
        }
        return userInfo == null ? new UserBaseInfoDto() : userInfo;
    }

    @Override
    public String getEmailByName(String userName) {
        String email = "";
        if (UserUtil.isPAccount(userName)) {
            ProtoThirdUserPO protoThirdUserPO = protoThirdUserService.getOne(Wrappers
                    .<ProtoThirdUserPO>lambdaQuery()
                    .eq(ProtoThirdUserPO::getUserCode, userName), false);
            if (protoThirdUserPO != null) {
                email = protoThirdUserPO.getEmail();
            }
            return email;
        }
        UserBaseInfoDto userInfo = this.getUserInfoByUserName(userName);
        if (userInfo != null) {
            email = userInfo.getEmail();
        }
        return email;
    }

    private String getUidByUserName(String userName) {
        Resp<String> res = idmAccountService.findUidByUserName(userName);
        if (!SUCCESS_CODE.equals(res.getCode())) {
            throw new BizException(InfraErrorCodeEnum.IDM_SERVICE_ERROR, res.getMsg());
        }
        String uid = res.getData();
        if (StringUtils.isBlank(uid)) {
            throw new BizException(InfraErrorCodeEnum.IDM_USER_NOT_FOUNT, userName);
        }
        return uid;
    }

    private UserBaseInfoDto getUserInfoByUid(String uid) {
        Resp<UserBaseInfoDto> res = idmUserService.findUserBaseInfoByUid(uid);
        if (!SUCCESS_CODE.equals(res.getCode())) {
            throw new BizException(InfraErrorCodeEnum.IDM_SERVICE_ERROR, res.getMsg());
        }
        return res.getData();
    }

    @Override
    public EmployeeInfo getEmpInfoByEmpId(String empId) {
        HrodEmployeeVO employeeVO = hrodService.getEmpByEmpId(empId);
        if(null == employeeVO) return null;

        EmployeeInfo employeeInfo = new EmployeeInfo();
        BeanUtil.copyProperties(employeeVO, employeeInfo);
        employeeInfo.setDeptNameLv3(employeeInfo.getDeptName());
        SysDeptPO res = sysDeptService.getCacheByDeptId(employeeInfo.getDeptId());
        if (null != res) {
            employeeInfo.setDeptName(res.getLongDeptName());
        }
        return employeeInfo;
    }

    @Override
    public AccountInfoDto getAccountInfoByUserName(String userName) {
        BaseResp<AccountInfoDto> res = idmAccountV2Service.getAccount(userName);
        if(null == res) return null;

        return res.getData();
    }
}


