package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.dto.UnReturnDTO;
import com.mi.oa.asset.mobile.application.dto.message.LarkDTO;
import com.mi.oa.asset.mobile.application.service.MessageService;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOrderPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOrderPayPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoUnReturnPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysAttachPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoOrderPayMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/2
 */
@Service
@Slf4j
public class ProtoOrderPayServiceImpl extends ServiceImpl<ProtoOrderPayMapper, ProtoOrderPayPO> implements ProtoOrderPayService {

    private static final String INNER_PURCHASE_SUFFIX = "_inner_purchase";

    private static final String DISPOSAL_SUFFIX = "_disposal";

    @Autowired
    private ProtoOrderService protoOrderService;

    @Autowired
    private ProtoUnReturnService protoUnReturnService;

    @Autowired
    private MailTemplateService mailTemplateService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private SysAttachService sysAttachService;

    @Autowired
    private SysVarService sysVarService;

    @Override
    public UnReturnDTO getBySalesOrder(String salesOrder) {
        ProtoOrderPayPO orderPayPO = this.getByOrderCode(salesOrder);
        if (null == orderPayPO){
            return null;
        }
        ProtoOrderPO orderPO = protoOrderService.getBySalesOrder(salesOrder);
        StringBuffer buffer = new StringBuffer();
        buffer.append("工程机内购");
        if (null != orderPO){
            buffer.append("+").append(orderPO.getProjectCode()).append("+").append(orderPO.getDeviceCode());
        }

        PayInfoEnum payInfoEnum = PayInfoEnum.valuesOf(orderPayPO.getMrpType() + INNER_PURCHASE_SUFFIX);
        UnReturnDTO result = UnReturnDTO.builder()
                .payInfo(UnReturnDTO.PayInfo.builder()
                        .companyName(payInfoEnum.getCompanyName())
                        .accountBank(payInfoEnum.getAccountBank())
                        .referenceRemark(buffer.toString())
                        .openBank(payInfoEnum.getOpenBank())
                        .build()
                )
                .unreturnId(orderPayPO.getId().toString())
                .unreturnCode(orderPayPO.getOrderCode())
                .mrpType(MrpTypeEnum.valueOf(orderPayPO.getMrpType()).getDescription())
                .disposalTypeDesc("内购")
                .sumActualDamages(orderPayPO.getPayAmount())
                .taskStatus(orderPayPO.getTaskStatus())
                .taskStatusDesc(UnReturnTaskStatusEnum.getDesc(orderPayPO.getTaskStatus()))
                .build();
        return result;
    }

    @Override
    public UnReturnDTO getByUnturnCode(String unreturnCode) {
        ProtoOrderPayPO orderPayPO = this.getByOrderCode(unreturnCode);
        ProtoUnReturnPO returnPO = protoUnReturnService.getByUnreturnCode(unreturnCode);
        if (null == orderPayPO || null == returnPO){
            return null;
        }
        String disDisposalTypeDesc = DisposalTypeEnum.getDesc(returnPO.getDisposalType());
        StringBuffer buffer = new StringBuffer();
        buffer.append("工程机丢失赔款+").append(returnPO.getApplyUserName()).append("+")
                .append(returnPO.getApplyUserCode()).append("+ 实际支付金额")
                .append(orderPayPO.getPayAmount().toString())
                .append("元");

        PayInfoEnum payInfoEnum = PayInfoEnum.valuesOf(orderPayPO.getMrpType() + DISPOSAL_SUFFIX);
        UnReturnDTO result = UnReturnDTO.builder()
                .payInfo(UnReturnDTO.PayInfo.builder()
                        .companyName(payInfoEnum.getCompanyName())
                        .accountBank(payInfoEnum.getAccountBank())
                        .referenceRemark(buffer.toString())
                        .openBank(payInfoEnum.getOpenBank())
                        .build()
                )
                .mrpType(MrpTypeEnum.valuesOf(orderPayPO.getMrpType()).getDescription())
                .disposalTypeDesc("处置-"+disDisposalTypeDesc)
                .sumActualDamages(orderPayPO.getPayAmount())
                .taskStatusDesc(UnReturnTaskStatusEnum.getDesc(orderPayPO.getTaskStatus()))
                .build();
        BeanUtils.copyProperties(returnPO, result);
        result.setMrpType(MrpTypeEnum.valuesOf(orderPayPO.getMrpType()).getDescription());
        result.setTaskStatus(orderPayPO.getTaskStatus());
        return result;
    }

    @Override
    public ProtoOrderPayPO getByOrderCode(String orderCode) {
        LambdaQueryWrapper<ProtoOrderPayPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoOrderPayPO::getOrderCode, orderCode);
        return this.getOne(wrapper);
    }

    @Override
    public void orderSubmit(String salesOrder) {
        ProtoOrderPayPO orderPayPO = this.getByOrderCode(salesOrder);
        log.info("unreturnSubmit_orderPayPO:{}", orderPayPO);
        if (null == orderPayPO){
            return;
        }
        orderPayPO.setTaskStatus(UnReturnTaskStatusEnum.COMPLETE.getType());
        this.updateById(orderPayPO);

        Map<String, String> valueMap = new HashMap<>(4);
        valueMap.put("orderCode", orderPayPO.getOrderCode());
        valueMap.put("userInfo", orderPayPO.getUserName()+orderPayPO.getUserCode());

        String financeKey = "proto.dis.finance.confirm_"+orderPayPO.getMrpType();
        String financePerson = sysVarService.findValByVarCode(financeKey);
        // 给财务飞书提醒
        String content = mailTemplateService.getMailContent(CommonConstant.PROTO_ORDER_UPLOAD_FINANCE, valueMap);
        String url = sysVarService.findValByVarCode(CommonConstant.EAM_URL)
                + "/page.html?pagetype=grid&nodeid=proto_pay_off_unconfirm&keyid="+orderPayPO.getId();
        messageService.sendLark(LarkDTO.builder()
                .userNames(Lists.newArrayList(financePerson.split(";")))
                .content(content)
                .title("【工程机】内购待确认")
                .url(url)
                .buttonName("去确认")
                .botType(CommonConstant.MALL_BOT_TYPE)
                .build());

        // 给申请人飞书提醒
        content = mailTemplateService.getMailContent(CommonConstant.PROTO_ORDER_UPLOAD_USER, valueMap);
        messageService.sendLark(LarkDTO.builder()
                .userNames(Lists.newArrayList(orderPayPO.getUserCode()))
                .content(content)
                .title("【工程机】内购待支付")
                .buttonName(CommonConstant.NO_BUTTON)
                .botType(CommonConstant.MALL_BOT_TYPE)
                .build());
    }

    @Override
    public List<ProtoOrderPayPO> listUnturnCodeList(List<String> unturnCodeList) {
        if (CollectionUtils.isEmpty(unturnCodeList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoOrderPayPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoOrderPayPO::getOrderCode, unturnCodeList);
        return this.list(wrapper);
    }

    @Override
    public void copyAttach(List<SysAttachPO> attachList) {
        if (CollectionUtils.isEmpty(attachList)){
            return;
        }
        List<String> dataIds = attachList.stream().map(SysAttachPO::getDataId).collect(Collectors.toList());
        List<ProtoOrderPayPO> orderPayPOList = this.listByIds(dataIds);
        Map<String, ProtoOrderPayPO> orderPayMap = orderPayPOList.stream().collect(Collectors.toMap(o -> o.getId().toString(), Function.identity()));
        List<String> orderCodeList = orderPayPOList.stream().map(ProtoOrderPayPO::getOrderCode).collect(Collectors.toList());
        Map<String, ProtoOrderPO> orderMap = protoOrderService.listBySalesOrder(orderCodeList)
                .stream().collect(Collectors.toMap(ProtoOrderPO::getSalesOrder, Function.identity()));
        for (SysAttachPO attachPO : attachList){
            String dataId = attachPO.getDataId();
            ProtoOrderPayPO orderPayPO = orderPayMap.get(dataId);
            if (null == orderPayPO){
                continue;
            }
            ProtoOrderPO orderPO = orderMap.get(orderPayPO.getOrderCode());
            if (null == orderPO){
                continue;
            }
            attachPO.setAttachId(null);
            attachPO.setDataId(orderPO.getId().toString());
            attachPO.setFunId("proto_inner_end");
            attachPO.setTableName("proto_order");
        }
        List<SysAttachPO> list = attachList.stream().filter(o -> "proto_order".equals(o.getTableName())).collect(Collectors.toList());
        sysAttachService.saveBatch(list);
    }
}
