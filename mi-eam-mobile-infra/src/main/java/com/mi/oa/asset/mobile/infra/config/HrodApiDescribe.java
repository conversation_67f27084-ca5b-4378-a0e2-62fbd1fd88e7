package com.mi.oa.asset.mobile.infra.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/11/3 22:05
 */

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface HrodApiDescribe {

    String apiName();

    String conditionName();
}
