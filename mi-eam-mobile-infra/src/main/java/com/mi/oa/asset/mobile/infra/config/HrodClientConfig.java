package com.mi.oa.asset.mobile.infra.config;

import com.google.gson.JsonObject;
import com.mi.oa.asset.mobile.common.enums.ErrorCodeEnum;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import com.mi.oa.asset.x5.common.JacksonUtils;
import com.mi.oa.asset.x5.common.X5Response;
import com.mi.oa.asset.x5.consumer.X5FeignClientsConfiguration;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import feign.RequestInterceptor;
import feign.codec.Encoder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.asm.TypeReference;
import org.springframework.context.annotation.Bean;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * @Desc HROD 接口请求 feign 客户端配置
 * <AUTHOR>
 * @Date 2021/9/9 22:07
 * @see com.mi.oa.asset.eam.infra.config.X5ClientConfig
 */

@Slf4j
public class HrodClientConfig extends X5FeignClientsConfiguration {

    @Bean
    protected RequestInterceptor requestInterceptor() {
        return requestTemplate -> {
            Method method = requestTemplate.methodMetadata().method();
            HrodApiDescribe ann = method.getAnnotation(HrodApiDescribe.class);
            if(null == ann) {
                throw new BizException(ErrorCodeEnum.HROD_MISSING_API_DESCRIBE, method.getName());
            }

            String apiName = ann.apiName();
            String conditionName = ann.conditionName();

            if(StringUtils.isBlank(apiName) || StringUtils.isBlank(conditionName)) {
                throw new BizException(ErrorCodeEnum.HROD_MISSING_API_DESCRIBE, method.getName());
            }

            Map<String, String> body = new HashMap<>();
            body.put("api_name", apiName);
            body.put(conditionName, StringUtils.toEncodedString(requestTemplate.body(), StandardCharsets.UTF_8));

            requestTemplate.body(JacksonUtils.bean2Json(body));
        };
    }

    @Override
    protected Object responseHandler(String resDataString, X5Response res, Type type) {
        if(!res.getHeader().getCode().equals(X5_RESPONSE_SUCCESS_CODE)) {
            throw new BizException(ErrorCodeEnum.X5_RESPONSE_ERROR, res.getHeader().getDesc());
        }

        Map bodyMap = JacksonUtils.json2Bean(JacksonUtils.bean2Json(res.getBody()),Map.class);
        Object data = bodyMap.get("data");
        if(type instanceof Class){
            return JacksonUtils.json2Bean(JacksonUtils.bean2Json(data), (Class) type);
        }else {
            return JacksonUtils.json2Bean(JacksonUtils.bean2Json(data), (Class) ((ParameterizedType)type).getRawType());
        }
    }
}


