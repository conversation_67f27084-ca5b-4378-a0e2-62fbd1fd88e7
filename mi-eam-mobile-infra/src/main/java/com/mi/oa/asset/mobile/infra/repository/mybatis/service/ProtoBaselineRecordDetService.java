package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoBaselineRecordDetPO;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/2/28 16:06
 */
public interface ProtoBaselineRecordDetService extends IService<ProtoBaselineRecordDetPO> {
    List<ProtoBaselineRecordDetPO> findListByParam(ProtoBaselineRecordDetPO po);
    ProtoBaselineRecordDetPO findOneByParam(ProtoBaselineRecordDetPO po);
}
