package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysRoleFunPO;

/**
 * table_name : sys_role_fun
 * <AUTHOR>
 * @date 2022/11/21/02:26
 */
public interface SysRoleFunMapper extends BaseMapper<SysRoleFunPO> {
    /**
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String roleFunId);

    /**
     *
     * @mbg.generated
     */
    int insert(SysRoleFunPO record);

    /**
     *
     * @mbg.generated
     */
    int insertSelective(SysRoleFunPO record);
}