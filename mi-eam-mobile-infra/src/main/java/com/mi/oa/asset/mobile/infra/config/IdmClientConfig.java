package com.mi.oa.asset.mobile.infra.config;

import com.mi.oa.asset.mobile.utils.GsonUtil;
import com.mi.oa.asset.x5.common.X5Response;
import com.mi.oa.asset.x5.config.ErrorCodeEnum;
import com.mi.oa.asset.x5.consumer.X5FeignClientsConfiguration;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.idm.api.rep.Resp;
import feign.RequestInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Bean;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Type;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/10/9 15:51
 */

@Slf4j
public class IdmClientConfig extends X5FeignClientsConfiguration {

    protected static final String SUCCESS_CODE = "0";

    @Bean
    protected RequestInterceptor requestInterceptor() {
        return requestTemplate -> {
            Map<String, String> bodyMap = new HashMap<>();
            requestTemplate.queries().forEach((k, v) -> {
                String val = StringUtils.join(v, ',');
                try {
                    val = URLDecoder.decode(val, StandardCharsets.UTF_8.name());
                } catch (UnsupportedEncodingException e) {
                    // nothing to do
                }
                bodyMap.put(k, val);
            });

            requestTemplate.body(GsonUtil.toJsonString(bodyMap));
        };
    }

    @Override
    protected Object responseHandler(String resDataString, X5Response res, Type type) {
        Resp resp = GsonUtil.toBean(resDataString, Resp.class);
        if(!SUCCESS_CODE.equals(resp.getCode().toString())) {
            throw new BizException(ErrorCodeEnum.X5_RESPONSE_ERROR, resp.getMsg());
        }

        return GsonUtil.toBean(GsonUtil.toJsonString(resp.getData()), type);
    }
}


