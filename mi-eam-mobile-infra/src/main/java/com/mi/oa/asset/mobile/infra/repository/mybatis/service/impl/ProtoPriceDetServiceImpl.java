package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPriceDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoPriceDetMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoPriceDetService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/22
 */
@Service
public class ProtoPriceDetServiceImpl extends ServiceImpl<ProtoPriceDetMapper, ProtoPriceDetPO> implements ProtoPriceDetService {
    @Override
    public List<ProtoPriceDetPO> listByPriceIdList(List<Integer> priceIdList) {
        if (CollectionUtils.isEmpty(priceIdList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoPriceDetPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoPriceDetPO::getPriceId, priceIdList)
                .orderByDesc(ProtoPriceDetPO::getId);
        return this.list(wrapper);
    }
}
