package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDelayPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleevePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoDelayMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoDelayService;
import org.springframework.stereotype.Service;

/**
 * 工程机延期归还
 *
 * <AUTHOR>
 * @date 2022/2/18 15:03
 */
@Service
public class ProtoDelayServiceImpl extends ServiceImpl<ProtoDelayMapper, ProtoDelayPO> implements ProtoDelayService {

    /**
     * 根据主键查询
     *
     * @param delayId
     * @return
     */
    @Override
    public ProtoDelayPO findByDelayId(String delayId) {
        QueryWrapper<ProtoDelayPO> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(ProtoDelayPO::getDelayId, delayId);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    /**
     * 更新领用方类型
     *
     * @param keyId
     * @param applyUserType
     * @return
     */
    @Override
    public boolean updateApplyUserType(String keyId, String applyUserType) {
        LambdaUpdateWrapper<ProtoDelayPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoDelayPO::getDelayId, keyId)
                .set(ProtoDelayPO::getApplyUserType, applyUserType);
        return this.update(updateWrapper);
    }

    /**
     * 修改流程信息
     *
     * @param delayId
     * @param auditing
     * @param modifyDate
     * @param businessKey
     * @return
     */
    @Override
    public boolean updProtoDelayStatusByDelayId(String delayId, String auditing, String modifyDate, String businessKey) {
        LambdaUpdateWrapper<ProtoDelayPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoDelayPO::getDelayId, delayId)
                .set(ProtoDelayPO::getAuditing, auditing)
                .set(ProtoDelayPO::getModifyDate, modifyDate)
                .set(ProtoDelayPO::getBusinessKey, businessKey);
        return this.update(updateWrapper);
    }

    /**
     * 修改流程信息
     *
     * @param delayId
     * @param auditing
     * @param modifyDate
     * @return
     */
    @Override
    public boolean updProtoDelayStatusByDelayId(String delayId, String auditing, String modifyDate) {
        LambdaUpdateWrapper<ProtoDelayPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoDelayPO::getDelayId, delayId)
                .set(ProtoDelayPO::getAuditing, auditing)
                .set(ProtoDelayPO::getModifyDate, modifyDate);
        return this.update(updateWrapper);
    }

    /**
     * 根据bpm唯一标识来查询工程机延期归还信息
     *
     * @param businessKey
     * @return
     */
    @Override
    public ProtoDelayPO findByBusinessKey(String businessKey){
        QueryWrapper<ProtoDelayPO> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(ProtoDelayPO::getBusinessKey,businessKey);
        return this.getBaseMapper().selectOne(queryWrapper);
    }
}
