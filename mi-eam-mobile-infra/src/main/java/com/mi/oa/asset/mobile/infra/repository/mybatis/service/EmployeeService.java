package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.mi.oa.asset.mobile.infra.dto.idm.ReportLineDTO;
import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/26
 */
public interface EmployeeService {

    /**
     * 当名下工程机的数量为0时 调用PS接口更新其名下工程机的数量
     * @param emplId
     * @return
     */
    Boolean leaveCheckPhoneNum(String emplId);

    /**
     * 根据账号查汇报线
     * @param userName
     * @return
     */
    List<ReportLineDTO> findReportLineByUserName(String userName);
}
