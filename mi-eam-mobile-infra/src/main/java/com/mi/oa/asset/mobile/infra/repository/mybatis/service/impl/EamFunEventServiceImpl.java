package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.mi.oa.asset.mobile.application.config.FunEventEnum;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.EamFunEventService;
import com.mi.oa.asset.mobile.utils.SpringContextUtil;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/5/24
 */
@Service
@Slf4j
public class EamFunEventServiceImpl implements EamFunEventService {


    @Override
    public Map<String, String> invokeFunEvent(RequestContext req) {
        String eventCode = req.getEventCode();
        String funId = req.getFunID();
        try {
            FunEventEnum funEventEnum = FunEventEnum.getFunEventEnum(funId,eventCode);
            Method method = funEventEnum.getBizClazz().getMethod(funEventEnum.getMethod(),RequestContext.class);
            Object service = SpringContextUtil.getBean(funEventEnum.getBizClazz());
            return (Map<String, String>) method.invoke(service,req);
        } catch (NoSuchMethodException e) {
            log.error(ExceptionUtils.getStackTrace(e));
            throw new BizException(ApplicationErrorCodeEnum.FUN_EVENT_NOSUCHMETHODEXCEPTION_ERROR);
        } catch (InvocationTargetException e) {
            log.error(ExceptionUtils.getStackTrace(e));
            throw new BizException(ApplicationErrorCodeEnum.FUN_EVENT_INVOCATIONTARGETEXCEPTION_ERROR,e.getCause().getMessage());
        } catch (IllegalAccessException e) {
            log.error(ExceptionUtils.getStackTrace(e));
            throw new BizException(ApplicationErrorCodeEnum.FUN_EVENT_ILLEGALACCESSEXCEPTION_ERROR);
        }
    }
}
