package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPricePO;

/**
 * table_name : proto_price
 * <AUTHOR>
 * @date 2022/06/22/03:35
 */
public interface ProtoPriceMapper extends BaseMapper<ProtoPricePO> {
    /**
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     *
     * @mbg.generated
     */
    int insert(ProtoPricePO record);

    /**
     *
     * @mbg.generated
     */
    int insertSelective(ProtoPricePO record);
}