package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.dto.mall.InnerProductReq;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOrderPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPlanDetPO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/1
 */
public interface ProtoPlanDetService extends IService<ProtoPlanDetPO> {

    /**
     * 通过planId查询详情
     * @param planId
     * @return
     */
    List<ProtoPlanDetPO> listByPlanId(Integer planId);

    /**
     * 查询明细
     * @param orderList
     * @return
     */
    List<ProtoPlanDetPO> listByOrders(List<ProtoOrderPO> orderList);

    /**
     * 根据计划单号和SN查询明细
     * @param innerProductReqs
     * @return
     */
    List<ProtoPlanDetPO> listByPlanNoAndDeviceCode(List<InnerProductReq> innerProductReqs);

    /**
     * 查询配置
     * @param deviceCode
     * @return
     */
    String getProductConfigByDeviceCode(String deviceCode);
}
