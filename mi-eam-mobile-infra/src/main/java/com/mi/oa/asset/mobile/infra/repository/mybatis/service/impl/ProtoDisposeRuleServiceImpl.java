package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDisposeRulePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoDisposeRuleMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoDisposeRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/22
 */
@Service
public class ProtoDisposeRuleServiceImpl extends ServiceImpl<ProtoDisposeRuleMapper, ProtoDisposeRulePO> implements ProtoDisposeRuleService {
}
