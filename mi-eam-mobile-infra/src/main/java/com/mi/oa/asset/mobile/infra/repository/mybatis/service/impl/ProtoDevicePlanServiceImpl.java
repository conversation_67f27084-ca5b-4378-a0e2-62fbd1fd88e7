package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.dto.message.EmailDTO;
import com.mi.oa.asset.mobile.application.dto.message.LarkDTO;
import com.mi.oa.asset.mobile.application.service.MessageService;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.PlanStatusEnum;
import com.mi.oa.asset.mobile.common.enums.RedisCachePrefixKeyEnum;
import com.mi.oa.asset.mobile.infra.dto.hrod.EmployeeInfo;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.*;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoDevicePlanMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.*;
import com.mi.oa.asset.mobile.utils.BeanUtil;
import com.mi.oa.asset.mobile.utils.DateUtil;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/23
 */
@Service
@Slf4j
public class ProtoDevicePlanServiceImpl extends ServiceImpl<ProtoDevicePlanMapper, ProtoDevicePlanPO> implements ProtoDevicePlanService {

    @Autowired
    private ProtoDeviceScanDetService protoDeviceScanDetService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private MailTemplateService mailTemplateService;

    @Autowired
    private ProtoDeviceScanService protoDeviceScanService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private ProtoCardService protoCardService;

    @Autowired
    private SysVarService sysVarService;

    @Value("${planEmail-cc}")
    private String reminderEmailCc;

    @Autowired
    private ProtoProjectCfgService protoProjectCfgService;

    @Override
    public void sendPlanMessage(String planId) {
        String cacheKey = RedisCachePrefixKeyEnum.PROTO_PLAN_MESSAGE_REMIND.getPrefixKey()+planId;
        if (RedisUtils.get(cacheKey) != null){
            log.info("plan_have_send_message planId:{}", planId);
            return;
        }
        ProtoDevicePlanPO planPO = this.getById(planId);
        if (null == planPO || planPO.getPlanStartDate() == null || planPO.getPlanEndDate() == null
                || !PlanStatusEnum.PLANING.getType().equals(planPO.getPlanStatus())){
            return;
        }
        String planStartDateStr = DateUtil.getDateStr(planPO.getPlanStartDate(), DateUtils.YEAR_MONTH_DATE);
        String planEndDateStr = DateUtil.getDateStr(planPO.getPlanEndDate(), DateUtils.YEAR_MONTH_DATE);

        // 提前30分钟
        Long minuteDiff = DateUtil.minuteDiff(planPO.getPlanStartDate(), new Date());
        if (minuteDiff < 0 || minuteDiff > 30){
            log.info("plan_not_in_remind_time planId:{}", planId);
            return;
        }
        // 我的工程机页面
        String url = sysVarService.findValByVarCode(CommonConstant.EAM_URL) + "/page.html?nodeid=proto_card_user";
        List<ProtoDeviceScanDetPO> scanDetList = protoDeviceScanDetService.listByPlanId(planId);
        Map<String, List<ProtoDeviceScanDetPO>> scanDetMap = scanDetList.stream().collect(Collectors.groupingBy(ProtoDeviceScanDetPO::getScanId));

        List<ProtoDeviceScanPO> scanList = protoDeviceScanService.listByPlanId(planId);
        Map<String, ProtoDeviceScanPO> scanMap = scanList.stream()
                .filter(o->StringUtils.isNotBlank(o.getSendEmpCode()))
                .collect(Collectors.toMap(ProtoDeviceScanPO::getSendUserCode, Function.identity(), (o1, o2)->o1));

        for (Map.Entry<String, ProtoDeviceScanPO> entry : scanMap.entrySet()){
            String sendUserCode = entry.getKey();
            ProtoDeviceScanPO deviceScan = scanMap.get(sendUserCode);
            String scanId = deviceScan.getScanId();
            // 盘点人
            String userCode = deviceScan.getUserCode();
            List<ProtoDeviceScanDetPO> detList = scanDetMap.get(scanId);
            try {
                this.sendMessage(sendUserCode, detList, planStartDateStr, planEndDateStr, url, userCode);
            } catch (Exception e){
                log.error("send_plan_message_error planId:{} error:{}", planId, e);
            }
        }
        RedisUtils.setEx(cacheKey, 1, 1, TimeUnit.DAYS);
    }

    @Override
    public void submitSendMessage(String planId) {
        String cacheKey = RedisCachePrefixKeyEnum.PROTO_PLAN_SUBMIT_MESSAGE_REMIND.getPrefixKey()+planId;
        if (RedisUtils.get(cacheKey) != null){
            log.info("plan_submit_have_send_message planId:{}", planId);
            return;
        }
        ProtoDevicePlanPO planPO = this.getById(planId);
        if (null == planPO || planPO.getPlanStartDate() == null){
            return;
        }
        String planDateStr = DateUtil.getDateStr(planPO.getPlanStartDate(), DateUtils.YEAR_MONTH_DATE);
        String url = sysVarService.findValByVarCode(CommonConstant.EAM_URL) + "/page.html?nodeid=proto_card_user";

        List<ProtoDeviceScanPO> scanList = protoDeviceScanService.listByPlanId(planId);
        List<String> sendUserCodeList = scanList.stream().map(ProtoDeviceScanPO::getSendUserCode).distinct().collect(Collectors.toList());
        List<String> userCodeList = scanList.stream().map(ProtoDeviceScanPO::getUserCode).distinct().collect(Collectors.toList());
        List<String> projectCodeList = protoDeviceScanDetService.getProjectCodeByPlanId(planId);
        this.sendSubmitMessage(sendUserCodeList, projectCodeList, planDateStr, url, userCodeList);
        RedisUtils.setEx(cacheKey, 1, 1, TimeUnit.DAYS);
    }

    /**
     * 盘点提交时发送飞书、邮件提醒
     * @param sendUserCodeList 使用人
     * @param projectCodeList 项目编码
     * @param planDateStr 抽盘时间
     * @param url 链接
     * @param userCodeList 抽盘人
     */
    private void sendSubmitMessage(List<String> sendUserCodeList, List<String> projectCodeList, String planDateStr, String url, List<String> userCodeList) {
        log.info("submit_send_message_find_userInfo sendUserCode:{},userCode:{}", sendUserCodeList, userCodeList);
        // 直属领导
        List<String> leaderUserCodeList = new ArrayList<>(sendUserCodeList.size());
        for (String userCode : sendUserCodeList){
            EmployeeInfo userInfo = userInfoService.getEmpInfoByUserName(userCode);
            if (null != userInfo && StringUtils.isNotBlank(userInfo.getLeaderUserName())) {
                leaderUserCodeList.add(userInfo.getLeaderUserName());
            }
        }
        Map<String, String> valueMap = new HashMap<>();
        valueMap.put("planDateStr", planDateStr);
        valueMap.put("projectCode", String.join(",", projectCodeList));
        String title = "《工程机抽盘计划提醒》";
        String mailContent = mailTemplateService.getMailContent(CommonConstant.PROTO_PLAN_SUBMIT_EMAIL, valueMap);
        String larkContent = mailTemplateService.getMailContent(CommonConstant.PROTO_PLAN_SUBMIT_LARK, valueMap);

        List<String> sendList = new ArrayList<>(sendUserCodeList.size());
        for (String userCode : sendUserCodeList){
            sendList.add(userCode+"@xiaomi.com");
        }
        // 抄送 配置人员+直属领导+盘点人 +PM
        List<String> ccList  = Lists.list(reminderEmailCc.split(","));
        for (String userCode : leaderUserCodeList){
            ccList.add(userCode+"@xiaomi.com");
        }
        for (String userCode : userCodeList){
            ccList.add(userCode+"@xiaomi.com");
        }
        // 查询项目PM
        List<ProtoProjectCfgPO> cfgPOList = protoProjectCfgService.listProjectCodeList(projectCodeList);
        List<String> pmUserList = cfgPOList.stream().filter(o -> StringUtils.isNotBlank(o.getPmUserCode()))
                .map(ProtoProjectCfgPO::getPmUserCode).distinct().collect(Collectors.toList());
        for (String pmUserCode : pmUserList){
            ccList.add(pmUserCode+"@xiaomi.com");
        }
        log.info("submit_send_message_find_userInfo sendList:{},ccList:{}", sendList, ccList);
        // 邮件
        messageService.sendEmail(EmailDTO.builder()
                .emails(sendList)
                .content(mailContent)
                .title(title)
                .ccEmails(ccList)
                .build());

        // 飞书 使用人
        messageService.sendLark(LarkDTO.builder()
                .userNames(sendUserCodeList)
                .content(larkContent)
                .title(title)
                .buttonName("查询个人台账")
                .url(url)
                .build());

    }

    @Override
    public List<ProtoDevicePlanPO> listPlaning() {
        LambdaQueryWrapper<ProtoDevicePlanPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoDevicePlanPO::getPlanStatus, PlanStatusEnum.PLANING.getType());
        return this.list(wrapper);
    }

    /**
     *
     * @param sendUserCode 使用人工号
     * @param detList SN明细
     * @param planStartDateStr 开始盘点时间
     * @param planEndDateStr 结束盘点时间
     * @param url 跳转链接
     * @param userCode 盘点人
     */
    private void sendMessage(String sendUserCode, List<ProtoDeviceScanDetPO> detList, String planStartDateStr, String planEndDateStr, String url, String userCode){
        if (CollectionUtils.isEmpty(detList)){
            return;
        }
        List<ProtoCardPO> cardList = BeanUtil.copyListProperties(detList, ProtoCardPO.class);
        Map<String, String> valueMap = new HashMap<>();
        EmployeeInfo sendUserInfo = userInfoService.getEmpInfoByUserName(sendUserCode);
        EmployeeInfo userInfo = userInfoService.getEmpInfoByUserName(userCode);
        if (null == sendUserInfo || null == userInfo){
            log.error("can_not_find_userInfo sendUserCode:{},userCode:{}", sendUserCode, userCode);
            return;
        }
        // 使用人直属领导
        EmployeeInfo leaderUserInfo = userInfoService.getEmpInfoByUserName(sendUserInfo.getLeaderUserName());

        valueMap.put("planStartDateStr", planStartDateStr);
        valueMap.put("planUser", userInfo.getDisplayName()+" "+userCode);
        valueMap.put("planEndDateStr", planEndDateStr);
        valueMap.put("useUser", sendUserInfo.getDisplayName()+" "+sendUserCode);
        valueMap.put("planEmailTable", protoCardService.getEmailTableContent(cardList, true));
        valueMap.put("planLarkTable", protoCardService.getLarkTableContent(cardList, true));

        String mailContent = mailTemplateService.getMailContent(CommonConstant.PROTO_PLAN_FORWARD_EMAIL, valueMap);
        String larkContent = mailTemplateService.getMailContent(CommonConstant.PROTO_PLAN_FORWARD_LARK, valueMap);
        String title = "《工程机抽盘任务提醒》";

        List<String> sendList = Lists.list(sendUserInfo.getEmail());
        List<String> ccList = Lists.list(userInfo.getEmail());
        if (null != leaderUserInfo){
            ccList.add(leaderUserInfo.getEmail());
        }
        // 邮件
        messageService.sendEmail(EmailDTO.builder()
                .emails(sendList)
                .content(mailContent)
                .title(title)
                .ccEmails(ccList)
                .build());

        // 使用人、抽盘人
        List<String> userCodeList = Lists.list(sendUserCode, userCode);
        // 飞书
        messageService.sendLark(LarkDTO.builder()
                .userNames(userCodeList)
                .content(larkContent)
                .title(title)
                .buttonName("查询个人台账")
                .url(url)
                .build());

    }
}
