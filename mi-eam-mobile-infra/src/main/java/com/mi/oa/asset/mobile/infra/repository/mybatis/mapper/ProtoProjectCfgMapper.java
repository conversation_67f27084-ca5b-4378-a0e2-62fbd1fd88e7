package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectCfgPO;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * table_name : proto_project_cfg
 * <AUTHOR>
 * @date 2022/01/21/11:11
 */
public interface ProtoProjectCfgMapper extends BaseMapper<ProtoProjectCfgPO> {
    /**
     *
     * @mbg.generated
     */
    ProtoProjectCfgPO selectByPrimaryKey(String cfgId);

    @Select("select project_code from proto_project_cfg where  auditing not in ('0', '7') group by project_code having count(1) > 1")
    List<String> getSameProjectCode();

    @Select("select project_code from proto_project_cfg where auditing not in ('0') group by project_code having count(1) = 1")
    List<String> getNotSameProjectCode();
}