package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysRolePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysRoleMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysRoleService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 操作角色
 *
 * <AUTHOR>
 * @date 2022/1/25 16:00
 */
@Service
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRolePO> implements SysRoleService {

    /**
     * 根据角色编号查询角色
     *
     * @param roleNo
     * @return
     */
    @Override
    public SysRolePO getSysRoleByRoleNo(String roleNo) {
        LambdaQueryWrapper<SysRolePO> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(SysRolePO::getRoleNo, roleNo);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<String> getMrpType(List<String> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)){
            return new ArrayList<>();
        }
        return baseMapper.getMrpType(roleIds);
    }
}
