package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysDataTypePO;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/6
 */
public interface SysDataTypeService extends IService<SysDataTypePO> {

    /**
     * 通过字段查询主键id
     * @param field
     * @return
     */
    String getDataTypeIdByField(String field);
}
