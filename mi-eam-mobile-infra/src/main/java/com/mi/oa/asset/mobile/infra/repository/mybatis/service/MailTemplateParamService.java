package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MailTemplatePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MailTemplateParamPO;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-23
 */
public interface MailTemplateParamService extends IService<MailTemplateParamPO> {

    /**
     * 通过模板id查询模板参数
     * @param templateId
     * @return
     */
    List<MailTemplateParamPO> listByTemplateId(String templateId);

    /**
     * 替换模板的参数
     * @param templateId
     * @param templateCont
     * @param dataMap
     * @return
     */
    String getTemplateContent(String templateId, String templateCont, Map<String, String> dataMap);
}
