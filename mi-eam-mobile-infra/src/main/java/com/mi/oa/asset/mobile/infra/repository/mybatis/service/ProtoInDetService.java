package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.ProtoInDetIsBackEnum;
import com.mi.oa.asset.mobile.common.enums.FeedBackEnum;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoInNoAsnDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoInSapDTO;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoPushSapDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInPO;

import java.util.Collection;
import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/12/17
 */
public interface ProtoInDetService extends IService<ProtoInDetPO> {

    /**
     * 通过sn查询明细
     * @param snList
     * @return
     */
    List<ProtoInDetPO> listBySnList(Collection<String> snList);

    /**
     * 更新 auditing 状态
     */
    void updateDetAuditing();

    /**
     * 通过inId查询明细 未抛送SAP的数据
     * @param inId
     * @return
     */
    List<ProtoInDetPO> listByInId(String inId);

    /**
     * 清除已匹配的数据
     * @param detId
     */
    void clearProtoInDetMach(String detId);
    /**
     * 根据多参数查询
     * @param po 查询参数
     * @return
     */
    List<ProtoInDetPO> findListByParam(ProtoInDetPO po);
    /**
     * 根据多参数查询
     * @param po 查询参数
     * @return
     */
    ProtoInDetPO findOneByParam(ProtoInDetPO po);

    /**
     * 查询指定归还状态明细数量
     * @param inId
     * @param isBackEnum
     * @return
     */
    int countDet(String inId, ProtoInDetIsBackEnum isBackEnum, CommonConstant.ProtoCardIsComplete isComplete);

    /**
     *  in_id = ? and is_complete in ('1','2','3','9')
     * @param inId
     * @return
     */
    List<ProtoInDetPO> findIsComplete(String inId);

    /**
     * 更新明细是否已归还信息
     * @param inId
     * @param isBackEnum
     * @param isComplete
     */
    void updateProtoInDetToIsBack(String inId, ProtoInDetIsBackEnum isBackEnum, CommonConstant.ProtoCardIsComplete isComplete);

    /**
     * 通过inID和反馈状态查询总数
     * @param inID
     * @param backEnum
     * @return
     */
    Integer countInIdAndFeedBack(String inID, FeedBackEnum backEnum);

    /**
     * 获取无ASN单抛送SAP信息
     * @param inId
     * @return
     */
    List<ProtoInSapDTO> getNotAsnPushSap(String inId);

    /**
     * 更新收货记录编号
     * @param inCode
     * @param inId
     * @param orderCode
     */
    void updateInCode(String inCode, String inId, String orderCode);

    /**
     * 更新子表抛送SAP状态
     * @param inId
     * @param orderCode
     * @param sapRetType
     */
    void updateSapRetType(String inId, String orderCode, Boolean sapRetType);

    /**
     * 更新抛送SAP行号
     * @param sapItemRowNo
     * @param inId
     * @param skuCode
     */
    void updateSapItemRowNo(String sapItemRowNo, String inId, String skuCode);

    /**
     * 获取无ASN抛送SAP明细
     * @param inId
     * @param orderCode
     * @return
     */
    List<ProtoInNoAsnDTO> getNotAsnDetPushSAPList(String inId, String orderCode);

    /**
     * 查询未成功抛送SAP的数量
     * @param inId
     * @return
     */
    Integer countNotSuccess(String inId);

    /**
     * 保存收货明细表
     * @param itemList
     * @param inId 主表id
     * @param userCode 工号
     */
    void saveAsnDet(List<ProtoAsnOrderDetPO> itemList, String inId, String userCode);

    /**
     * 获取收货记录明细SAP信息
     * @param inId
     * @return
     */
    List<ProtoPushSapDTO> getPushSapList(String inId);

    /**
     * 更新SAP行号
     * @param inId
     * @param skuCode 物料描
     * @param sapItemRowNo 抛送SAP行号
     */
    void updateProtoInDetToSapItemRowNo(String inId, String skuCode, String sapItemRowNo);

    /**
     * 通过inId和归还状态查询明细
     * @param inId
     * @return
     */
    List<ProtoInDetPO> listByInIdAndIsComplete(String inId, String isComplete);

    /**
     * 删除子单
     * @param inId
     */
    void deletedByInId(String inId);
}
