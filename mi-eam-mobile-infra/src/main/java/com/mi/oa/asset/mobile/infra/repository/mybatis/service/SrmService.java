package com.mi.oa.asset.mobile.infra.repository.mybatis.service;


import com.mi.oa.asset.mobile.infra.dto.srm.AsnMobileDTO;
import com.mi.oa.asset.mobile.infra.dto.srm.PoDetailListDTO;
import com.mi.oa.asset.mobile.infra.dto.srm.SnDetailListDTO;
import com.mi.oa.asset.mobile.infra.dto.srm.SrmResponseDTO;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/10/29
 */
public interface SrmService {

    /**
     * 同步asn收货数据
     * @param dto
     * @return
     */
    SrmResponseDTO synAsnOrder(AsnMobileDTO dto);

    /**
     * 通过asn单号同步数据
     * @param asnNo
     */
    SrmResponseDTO synAsnOrderByAsnNo(String asnNo);

    /**
     * 通过日期同步数据
     * @param bizDate yyyy-MM-dd
     */
    SrmResponseDTO synAsnOrderByBizDate(String bizDate);

    /**
     * 获取PO详情
     * @param poNo PO单号
     * @return
     */
    PoDetailListDTO getPODetail(String poNo);

    /**
     * 通过asn单号获取SN详情
     * @param asnNo
     * @return
     */
    SnDetailListDTO getSnDetail(String asnNo);
}
