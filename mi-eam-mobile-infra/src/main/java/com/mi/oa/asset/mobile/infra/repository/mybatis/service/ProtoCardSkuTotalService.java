package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardSkuTotalPO;

import java.util.List;

/**
 * 台账物料汇总
 *
 * <AUTHOR>
 * @date 2022/3/25 10:05
 */
public interface ProtoCardSkuTotalService extends IService<ProtoCardSkuTotalPO> {


    List<ProtoCardSkuTotalPO> countCardProjectList();

    Boolean statistics();

}
