package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoUnReturnPO;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/2/25 14:32
 */
public interface ProtoUnReturnService extends IService<ProtoUnReturnPO> {
    List<ProtoUnReturnPO> findListByParam(ProtoUnReturnPO param);

    ProtoUnReturnPO getByUnreturnCode(String unreturnCode);

    List<ProtoUnReturnPO> offline();

    List<ProtoUnReturnPO> listByCodeList(List<String> orderCodeList);
}
