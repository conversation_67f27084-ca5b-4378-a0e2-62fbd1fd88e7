package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardDeptTotalPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardDeptproTotalPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardStorePO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * table_name : proto_card
 * <AUTHOR>
 * @date 2022/01/04/04:04
 */
public interface ProtoCardMapper extends BaseMapper<ProtoCardPO> {

    @Select("select count(1) from proto_card where emp_code=#{empCode} and use_state in ('2', '4', '6', '14', '94')")
    Integer countByEmpCode(@Param("empCode") String empCode);

    @Select("select count(1) from proto_card where user_code=#{userCode} and use_state in ('2', '4', '6', '14', '94')")
    Integer countByUserCode(@Param("userCode")String userCode);

    @Update("update proto_card set out_code = #{newOutCode} where out_code = #{oldOutCode}")
    void updateOutCode(@Param("oldOutCode") String oldOutCode, @Param("newOutCode") String newOutCode);

    @Select("select distinct user_code from proto_card where user_code is not null and user_code != ''")
    List<String> getNotRefreshUserCode();

    @Select("select distinct emp_code from proto_card where syc_flag = '1' and emp_code is not null and emp_code != '' limit 100")
    List<String> getNotRefreshEmpCode();

    List<ProtoCardDeptTotalPO> listGroupByDeptCode();

    List<ProtoCardDeptproTotalPO> listGroupByDeptproCode();

    List<ProtoCardStorePO> listGroupByStore();


}