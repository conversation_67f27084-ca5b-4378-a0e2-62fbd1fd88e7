package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPriceDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPricePO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/6/22
 */
public interface ProtoPriceDetService extends IService<ProtoPriceDetPO> {

    List<ProtoPriceDetPO> listByPriceIdList(List<Integer> priceId);
}
