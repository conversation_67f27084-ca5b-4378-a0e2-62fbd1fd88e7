package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleeveInfoPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleevePO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * table_name : proto_sleeve
 * <AUTHOR>
 * @date 2022/01/28/02:37
 */
public interface ProtoSleeveMapper extends BaseMapper<ProtoSleevePO> {
    /**
     *
     * @mbg.generated
     */
    ProtoSleevePO selectByPrimaryKey(String sleeveId);

    /**
     * 连表查询工程机保密套和物料信息
     *
     * @param sleeveId
     * @return
     */
    List<ProtoSleeveInfoPO> selectSleeveInfoById(String sleeveId);
}