package com.mi.oa.asset.mobile.infra.remote.sdk;

import com.mi.oa.asset.mobile.infra.remote.entity.BpmDTO;
import com.mi.oa.asset.x5.consumer.X5FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Map;

/**
 * @Desc TODO
 * <AUTHOR>
 * @Date 2021/9/28 19:11
 */

@X5FeignClient(value = "bpm", url = "${bpm.host}/api/bpm", appId = "${bpm.appId}", appKey = "${bpm.appKey}", form = true)
public interface BpmClient {

    @PostMapping(value = "/createCommon")
    Map<String, String> createCommon(BpmDTO param);
}
