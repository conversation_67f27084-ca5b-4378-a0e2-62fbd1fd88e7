package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoOutSapDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOutDetPO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/17
 */
public interface ProtoOutDetService extends IService<ProtoOutDetPO> {

    /**
     * 通过SN获取抛送SAP成功数量
     * @param sn
     * @return
     */
    Integer getProtoOutSapSuccess(String sn);

    /**
     * 通过sn查询领用申请单号
     * @param sn
     * @return
     */
    String getApplyCode(String sn);

    /**
     * 是否能抛送SAP
     * @param outId
     * @return
     */
    Integer countNotPushSap(String outId);

    /**
     * 查询抛送SAP明细
     * @param outId
     * @return
     */
    List<ProtoOutSapDTO> getProtoOutDetSAPList(String outId);

    /**
     * 通过主单id查询明细
     * @param outId
     * @return
     */
    List<ProtoOutDetPO> listByOutId(String outId);

    /**
     * 保存明细出库SAP行号
     * @param outId
     * @param skuCode
     * @param orderCode
     * @param outSapItemRowNo
     */
    void updateDetOutSapRow(String outId, String skuCode, String orderCode, String outSapItemRowNo);

    /**
     * 更新收货记录详情表的状态
     */
    void updateDetInStatus();

    /**
     * 查询未出库的设备编号SN
     *
     * @param deviceCodes
     * @return
     */
    List<String> selectProtoOutDetDeviceCodes(List<String> deviceCodes);
}
