package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;



import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.dto.srm.*;
import com.mi.oa.asset.mobile.infra.dto.upc.MobileSkuDetailDTO;
import com.mi.oa.asset.mobile.infra.dto.upc.MobileSkuDetailReq;
import com.mi.oa.asset.mobile.infra.remote.sdk.SkuClient;
import com.mi.oa.asset.mobile.infra.remote.sdk.SrmClient;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoAsnOrderDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoAsnOrderService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SrmService;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import com.mi.oa.asset.x5.config.ErrorCodeEnum;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import feign.codec.DecodeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;


import java.util.Iterator;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/10/29
 */
@Service
@Slf4j
public class SrmServiceImpl implements SrmService {

    @Autowired
    private SrmClient srmClient;

    @Autowired
    private ProtoAsnOrderService protoAsnOrderService;

    @Autowired
    private ProtoAsnOrderDetService protoAsnOrderDetService;

    @Autowired
    private SkuClient skuClient;

    /**
     * 镭雕号
     */
    private final String LDH = "LDH";

    /**
     * 阶段
     */
    private final String STAGE_NAME = "JD";

    /**
     * 配置
     */
    private final String CONFIGURATION = "CONFIGURATION";

    /**
     * 手机类型代码
     */
    private final String MOBILE = "mobile";

    @Override
    public SrmResponseDTO synAsnOrder(AsnMobileDTO dto) {
        SrmResponseDTO result = new SrmResponseDTO();
        dto.setLif(CommonConstant.I_LIFNR);
        ProtoAsnOrderListDTO orderList = null;
        try {
            orderList = srmClient.synMobile(dto);
        } catch (DecodeException bizException){
            result.setSuccess(false);
            String message = StringUtils.replace(bizException.getMessage(), "X5 返回错误:", "");
            result.setMessage(message);
            if ((StringUtils.contains(message, "送货单号") && StringUtils.contains(message, "已被取消"))
                || (StringUtils.contains(message, "ASN") && StringUtils.contains(message, "had been canceled"))){
                // 更新ASN订单为已取消状态
                protoAsnOrderService.updateAsnOrderStatus(dto.getAsn(), AsnInStatusEnum.CANCEL_DELIVERY.getState());
                protoAsnOrderDetService.updateAsnOrderStatus(dto.getAsn(), AsnInStatusEnum.CANCEL_DELIVERY.getState());
            }
            return result;
        } catch (Exception e){
            log.info("synAsnOrder error:{}", e.getMessage());
            result.setMessage(ErrorCodeEnum.UNKNOWN_ERROR.getErrDesc());
            result.setSuccess(false);
            return result;
        }
        log.info("synAsnOrder request:{}, response:{}", dto, orderList);
        if (null == orderList || CollectionUtils.isEmpty(orderList.getAsnOrderList())){
            result.setSuccess(false);
            result.setMessage("SRM接口返回数据错误");
            return result;
        }
        List<ProtoAsnOrderListDTO.ProtoAsnOrderDTO> asnOrderList = orderList.getAsnOrderList();
        List<ProtoAsnOrderListDTO.ProtoAsnOrderItemDTO> items = asnOrderList.get(0).getItems();
        if (CollectionUtils.isEmpty(items)){
            result.setSuccess(false);
            result.setMessage("SRM接口返回数据错误");
            return result;
        }
        for (ProtoAsnOrderListDTO.ProtoAsnOrderDTO orderDTO : asnOrderList){
            try {
                String projectCode = orderDTO.getItems().get(0).getProjectCode();
                result = this.dealOneOrder(orderDTO, projectCode);
            } catch (BizException bizException){
                log.error("SRM syn data biz error asnNO:{}, orderDTO:{}, e:{}", orderDTO.getOrderCode(), bizException);
                result.setMessage(bizException.getMessage());
                result.setSuccess(false);
            }catch (Exception e){
                log.error("SRM syn data error orderDTO:{}, e:{}", orderDTO, e);
                result.setMessage(ErrorCodeEnum.UNKNOWN_ERROR.getErrDesc());
                result.setSuccess(false);
            }
        }
        return result;
    }

    @Override
    public SrmResponseDTO synAsnOrderByAsnNo(String asnNo) {
        String key = RedisCachePrefixKeyEnum.SRM_ASN_CACHE.getPrefixKey()+asnNo;
        Object obj = RedisUtils.get(key);
        if (null != obj){
            return (SrmResponseDTO)obj;
        }
        AsnMobileDTO dto = new AsnMobileDTO();
        dto.setAsn(asnNo);
        SrmResponseDTO result = synAsnOrder(dto);
        RedisUtils.setEx(key,  result, 5, TimeUnit.MINUTES);
        return result;
    }

    @Override
    public SrmResponseDTO synAsnOrderByBizDate(String bizDate) {
        AsnMobileDTO dto = new AsnMobileDTO();
        dto.setBizDate(bizDate);
        return synAsnOrder(dto);
    }

    /**
     * 处理单个同步数据
     * @param orderDTO
     */
    private SrmResponseDTO dealOneOrder(ProtoAsnOrderListDTO.ProtoAsnOrderDTO orderDTO, String projectCode){
        SrmResponseDTO result = new SrmResponseDTO();
        // 已经有收货的数据就不做处理
        boolean haveReceive = protoAsnOrderDetService.haveReceive(orderDTO.getOrderCode());
        if (haveReceive){
            return result;
        }
        Integer eamStatus = SrmAsnStatusEnum.getEamStatusByAsnStatus(orderDTO.getStatus());
        // 状态错误或者等待发送时 不更新
        if (null == eamStatus || eamStatus.equals(SrmAsnStatusEnum.WAIT_SEND.getEamStatus())){
            result.setSuccess(false);
            result.setMessage("ASN单状态错误，请核实ASN是否为“已发送”状态。");
            return result;
        }
        orderDTO.setOrderStatus(eamStatus);
        String shipDate = orderDTO.getShipDate();
        orderDTO.setOutDate(shipDate);
        orderDTO.setProjectCode(projectCode);

        // PO子项目
        List<ProtoAsnOrderListDTO.ProtoAsnOrderItemDTO> itemsList = orderDTO.getItems();
        if (CollectionUtils.isEmpty(itemsList)){
            result.setSuccess(false);
            result.setMessage("SRM返回数据错误");
            return result;
        }
        Iterator<ProtoAsnOrderListDTO.ProtoAsnOrderItemDTO> iterator = itemsList.iterator();
        while (iterator.hasNext()){
            ProtoAsnOrderListDTO.ProtoAsnOrderItemDTO next = iterator.next();
            // 异常数据处理
            if (!checkPO(next, orderDTO)){
                iterator.remove();
            }

        }
        // 有子项时 保存主单信息
        if (!CollectionUtils.isEmpty(itemsList)){
            protoAsnOrderService.saveMainOrder(orderDTO);
            return result;
        }
        result.setSuccess(false);
        result.setMessage("未找到PO数据或采购组织不是手机");
        return result;
    }

    @Override
    public PoDetailListDTO getPODetail(String poNo) {
        if (StringUtils.isBlank(poNo)){
            return null;
        }
        String cacheKey = RedisCachePrefixKeyEnum.PO_DETAIL_NO.getPrefixKey()+poNo;
        Object object = RedisUtils.get(cacheKey);
        if (null != object){
            return GsonUtil.parseObject(object.toString(), PoDetailListDTO.class);
        }
        PoRequestDTO dto = new PoRequestDTO();
        dto.setBizDate("");
        dto.setLif(CommonConstant.I_LIFNR);
        dto.setPoNo(poNo);
        PoDetailListDTO result = srmClient.getPODetail(dto);
        RedisUtils.setEx(cacheKey, GsonUtil.toJsonString(result), 5, TimeUnit.MINUTES);
        return result;
    }

    @Override
    public SnDetailListDTO getSnDetail(String asnNo) {
        if (StringUtils.isBlank(asnNo)){
            return null;
        }
        String cacheKey = RedisCachePrefixKeyEnum.SN_DETAIL_ASN_NO.getPrefixKey()+asnNo;
        Object object = RedisUtils.get(cacheKey);
        if (null != object){
            return GsonUtil.parseObject(object.toString(), SnDetailListDTO.class);
        }
        SnRequestDTO request = new SnRequestDTO();
        request.setAsnNo(asnNo);
        SnDetailListDTO result = srmClient.getSnDetail(request);
        if (null != result && !CollectionUtils.isEmpty(result.getDetailList())){
            for (SnDetailListDTO.SnDetailDTO dto : result.getDetailList()){
                SnDetailListDTO.SnExDataDTO snExDataDTO = GsonUtil.toBean(dto.getSnExDataStr(), SnDetailListDTO.SnExDataDTO.class);
                if (CollectionUtils.isEmpty(snExDataDTO.getSubSnList())){
                    continue;
                }
                dto.setSku(snExDataDTO.getSku());
                dto.setMaktx(snExDataDTO.getMaktx());
                dto.setAsnItemNo(snExDataDTO.getAsnItemNo());
                // 处理镭雕号和配置
                for (SnDetailListDTO.SubSnDataDTO subDto : snExDataDTO.getSubSnList()){
                    if (LDH.equals(subDto.getFieldName())){
                        dto.setLdh(subDto.getFieldValue());
                    } else if (CONFIGURATION.equals(subDto.getFieldName())){
                        dto.setConfiguration(subDto.getFieldValue());
                    } else if (STAGE_NAME.equals(subDto.getFieldName())){
                        dto.setStageName(subDto.getFieldValue());
                    }
                }
            }
        }
        RedisUtils.setEx(cacheKey, GsonUtil.toJsonString(result), 5, TimeUnit.MINUTES);
        return result;
    }


    /**
     * 校验PO单号正确性 并处理异常数据
     * @param next
     * @return
     */
    private boolean checkPO(ProtoAsnOrderListDTO.ProtoAsnOrderItemDTO next, ProtoAsnOrderListDTO.ProtoAsnOrderDTO orderDTO){
        PoDetailListDTO poDetailListDto = this.getPODetail(next.getPoNo());
        // 未获取到PO数据，则该条记录不满足要求
        if (null == poDetailListDto || CollectionUtils.isEmpty(poDetailListDto.getList())){
            return false;
        }
        PoDetailListDTO.PoDetailDTO poDetailDTO = poDetailListDto.getList().get(0);
        // PO接口EKORG 采购组织未获取到 也是不合理数据
        String organization = poDetailDTO.getOrganization();
        if (StringUtils.isBlank(organization)){
            return false;
        }
        // 亦庄的采购组织改成1110
        if ("1112".equals(organization)){
            organization = "1110";
        }
        // 不是手机工程机
        if (!"1110".equals(organization)){
            return false;
        }
        if (CollectionUtils.isEmpty(poDetailDTO.getItems())){
            return false;
        }
        String supplierCode = poDetailDTO.getSupplierCode();
        if (SapFactoryEnum.YI_ZHUANG.getSapFactoryCode().equals(supplierCode)){
            poDetailDTO.setSupplierCode(SapFactoryEnum.YI_ZHUANG.getSapFactory());
        }
        if (SapFactoryEnum.CHANG_PING.getSapFactoryCode().equals(supplierCode)){
            poDetailDTO.setSupplierCode(SapFactoryEnum.CHANG_PING.getSapFactory());
        }
        if (SapFactoryEnum.CHANG_PING2.getSapFactoryCode().equals(supplierCode)){
            poDetailDTO.setSupplierCode(SapFactoryEnum.CHANG_PING2.getSapFactory());
        }
        if (SapFactoryEnum.ROBOT.getSapFactoryCode().equals(supplierCode)){
            poDetailDTO.setSupplierCode(SapFactoryEnum.ROBOT.getSapFactory());
        }
        PoDetailListDTO.PoItemDTO poItemDTO = poDetailDTO.getItems().get(0);
        // PO接口  VENDOR_NAME 供应商名称 ，LIFNR 供应商，BSART 订单类型，lgort仓库，werks sap工厂
        // 任意一个信息不存在 就是PO信息不完整 也是不合理数据
        String purchaseType = poDetailDTO.getPurchaseType();
        if (StringUtils.isBlank(poDetailDTO.getSupplierCode())
            || StringUtils.isBlank(poDetailDTO.getSupplierName())
            || StringUtils.isBlank(purchaseType)
            || StringUtils.isBlank(poItemDTO.getSapFactory())
            || StringUtils.isBlank(poItemDTO.getWarehouse())){
            return false;
        }
        // PO订单类型 BSART 只能是 ’ZWW‘ 或者 'ZUB' 其他都是不允许的  针对手机业务
        if  (!"ZWW".equals(purchaseType) && !"ZUB".equals(purchaseType)){
            return false;
        }
        // 成品 || 半成品
        if ("ZWW".equals(purchaseType) || "ZPJ".equals(purchaseType)){
            next.setSkuCode(next.getSku());
            // 成品
            if (orderDTO.getItems().get(0).getSku().length() == 9){
                next.setGoodsId(getGoodsIdBySkuCode(next.getSku()));
            }
        } else {
            next.setSku(next.getProductId());
        }
        orderDTO.setFactoryCode(poDetailDTO.getSupplierCode());
        orderDTO.setFactoryName(poDetailDTO.getSupplierName());
        orderDTO.setBsart(purchaseType);
        orderDTO.setMrpType(MOBILE);
        orderDTO.setSapFactory(poItemDTO.getSapFactory());
        return true;
    }

    /**
     * 通过 skuCode 查询goodsId
     * @param skuCode
     * @return
     */
    private String getGoodsIdBySkuCode(String skuCode){
        String cacheKey = RedisCachePrefixKeyEnum.SKU_DETAIL_CODE.getPrefixKey()+skuCode;
        Object obj = RedisUtils.get(cacheKey);
        if (null != obj){
            return obj.toString();
        }
        MobileSkuDetailReq requestDTO = new MobileSkuDetailReq();
        requestDTO.setSkuCodes(skuCode);
        List<MobileSkuDetailDTO> skuList = skuClient.getMobileSkuList(requestDTO);
        if (CollectionUtils.isEmpty(skuList)){
            return null;
        }
        String result = skuList.get(0).getGoodsId();
        RedisUtils.setEx(cacheKey, result, 10, TimeUnit.MINUTES);
        return result;
    }
}
