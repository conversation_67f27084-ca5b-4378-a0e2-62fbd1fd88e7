package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.common.enums.*;
import com.mi.oa.asset.mobile.infra.dto.sap.*;
import com.mi.oa.asset.mobile.infra.remote.sdk.SapClient;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.MdmService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoSapExeLogService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SapService;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.redis.annotation.OACacheSet;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import feign.Request;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/12
 */
@Service
@Slf4j
public class SapServiceImpl implements SapService {

    @Autowired
    private ProtoSapExeLogService protoSapExeLogService;

    @Autowired
    private SapClient sapClient;

    @Autowired
    private MdmService mdmService;

    private static final String GM = "GM";

    @Override
    public SapCustomVO sap107Json(Sap107JsonDTO jsonDTO, String funId, String orderId, String orderCode, String orderName, String doUser, String mrpType) {
        String nowDateStr = DateUtils.dateToStringByType(new Date(), DateUtils.YEAR_MONTH_DATE_INT);

        SapMierDTO request = new SapMierDTO();
        SapMierDTO.MainOrder mainOrder = new SapMierDTO.MainOrder();
        BeanUtils.copyProperties(jsonDTO, mainOrder);
        mainOrder.setCommHdr(nowDateStr);
        mainOrder.setBusiDate(nowDateStr);
        // 可穿戴 111J 电视1111 盒子2110 手机1110
        if (MrpTypeEnum.TV.getType().equals(mrpType)){
            if (SapMakeCnEnum.TV.getSapFactory().equals(jsonDTO.getSapFactory())){
                mainOrder.setPurcOrg(OrganizationEnum.tv_chain1.getType());
            } else {
                mainOrder.setPurcOrg(OrganizationEnum.box_chain.getType());
            }
        } else if (MrpTypeEnum.WEARABLE.getType().equals(mrpType)){
            mainOrder.setPurcOrg(OrganizationEnum.wearable_items1.getType());
        } else if (MrpTypeEnum.ECOCHAIN.getType().equals(mrpType)) {
            //智能硬件工厂1185（米电智能产品生产工厂），采购组织2120（数码配件供应链），公司代码1180米电。
            //生态链其它产品工厂（1112-米通商品外采工厂），采购组织1122（生态链供应链），公司代码1110米通。
            if (SapMakeCnEnum.ECO_MIDIAN.getSapFactory().equals(jsonDTO.getSapFactory())) {
                mainOrder.setPurcOrg(OrganizationEnum.smart_hardware.getType());
            } else if (SapMakeCnEnum.ECO_MITONG.getSapFactory().equals(jsonDTO.getSapFactory())){
                mainOrder.setPurcOrg(OrganizationEnum.eco_chain.getType());
            }else{
                throw new BizException(ApplicationErrorCodeEnum.EAM_BIZ_ERROR,"生态链SAP工厂错误");
            }
        } else if (MrpTypeEnum.LAPTOP.getType().equals(mrpType)) {
            mainOrder.setPurcOrg(OrganizationEnum.laptop_chain.getType());
        } else if (MrpTypeEnum.ROBOT.getType().equals(mrpType)) {
            mainOrder.setPurcOrg(OrganizationEnum.robot_chain.getType());
        }else if(MrpTypeEnum.HEA_FACTORY.getType().equals(mrpType)){
            mainOrder.setPurcOrg(OrganizationEnum.hea_factory.getType());
        }else if(MrpTypeEnum.MI_AUTOEACC.getType().equals(mrpType)){
            mainOrder.setPurcOrg(OrganizationEnum.MI_AUTOEACC.getType());
        }
        String sapFactory = jsonDTO.getSapFactory();
        if (SapFactoryEnum.YI_ZHUANG.getSapFactoryCode().equals(sapFactory) || SapFactoryEnum.YI_ZHUANG.getSapFactory().equals(sapFactory)){
            mainOrder.setVendor(SapFactoryEnum.YI_ZHUANG.getSapFactoryCode());
        }
        if (SapFactoryEnum.CHANG_PING.getSapFactoryCode().equals(sapFactory) || SapFactoryEnum.CHANG_PING.getSapFactory().equals(sapFactory)) {
            //昌平-米通试产采购订单模式 传代工厂（VENDOR）是从ASN里获取的M10001
            if (SrmPurchaseTypeEnum.ZDB.getCode().equals(jsonDTO.getBsart())) {
                mainOrder.setVendor(CommonConstant.I_LIFNR);
            } else {
                mainOrder.setVendor(SapFactoryEnum.CHANG_PING.getSapFactoryCode());
            }
        }
        if (SapFactoryEnum.CHANG_PING2.getSapFactoryCode().equals(sapFactory) || SapFactoryEnum.CHANG_PING2.getSapFactory().equals(sapFactory)){
            mainOrder.setVendor(SapFactoryEnum.CHANG_PING2.getSapFactoryCode());
        }
        //机器人
        if (SapFactoryEnum.ROBOT.getSapFactoryCode().equals(sapFactory) || SapFactoryEnum.ROBOT.getSapFactory().equals(sapFactory)){
            mainOrder.setVendor(SapFactoryEnum.ROBOT.getSapFactoryCode());
        }
        //大家电-空调 代工厂传默认 I_LIFNR
        if (SapFactoryEnum.HEA_FACTORY.getSapFactoryCode().equals(sapFactory) || SapFactoryEnum.HEA_FACTORY.getSapFactory().equals(sapFactory)){
            mainOrder.setVendor(SapFactoryEnum.HEA_FACTORY.getSapFactoryCode());
        }
        //汽车电子 同手机昌平-米通的模式保持一致，代工厂传M10001
        if (MrpTypeEnum.MI_AUTOEACC.getType().equals(mrpType)) {
            mainOrder.setVendor(CommonConstant.I_LIFNR);
        }
        //昌平二期
        List<SapMierDTO.OrderItems> items = new ArrayList<>(jsonDTO.getItemDTOList().size());
        for (Sap107JsonDTO.SapItemDTO itemDTO : jsonDTO.getItemDTOList()) {
            SapMierDTO.OrderItems orderItem = new SapMierDTO.OrderItems();
            BeanUtils.copyProperties(itemDTO, orderItem);
            orderItem.setBatchId(mainOrder.getBatchId());
            orderItem.setMidocNum(mainOrder.getMidocNum());
            orderItem.setWareType(mainOrder.getWareType());
            orderItem.setMatDescOne(StringUtils.substring(this.replaceSpecialChat(orderItem.getMatDescOne()), 0, 30));
            // 发放时项目1设置为null
            if (CommonConstant.SapBusiTypeEnum.RECEIPT_GOODS.getType().equals(jsonDTO.getBusiType())) {
                orderItem.setMiProjOne(null);
                if (SrmPurchaseTypeEnum.ZDB.getCode().equals(jsonDTO.getBsart())) {
                    //样机入库YJRK时：当订单类型ZDB,供应商P3882，传入M10001，且WARE_TYPE赋值为GM，GOOD_BAD赋值为GM。
                    orderItem.setWareType(GM);
                    orderItem.setGoodBad(GM);
                }
                if (MrpTypeEnum.MI_AUTOEACC.getType().equals(mrpType) && (SrmPurchaseTypeEnum.ZDB.getCode().equals(jsonDTO.getBsart())
                        || SrmPurchaseTypeEnum.ZUB.getCode().equals(jsonDTO.getBsart()))) {
                    //样机入库YJRK，汽车电子 找SAP老师汤优确认ZDB和ZUB调拨单增加WARE_TYPE=GM
                    orderItem.setWareType(GM);
                    orderItem.setGoodBad(GM);
                }
            }
            items.add(orderItem);
        }
        request.setMainOrder(mainOrder);
        request.setItems(items);
        BeanUtils.copyProperties(jsonDTO,mainOrder);

        String exelogId = protoSapExeLogService.insertLog(request, funId, orderId, orderCode, orderName, doUser);
        log.info("sapClient.sap107Json request:{}", GsonUtil.toJsonString(request));
        //跟方法单独加超时时间，SAP接口默认超时5分钟 执行可能大于1分钟
        Request.Options options = new Request.Options(3, TimeUnit.SECONDS, 5, TimeUnit.MINUTES, true);
        SapCustomVO sapCustomVO = sapClient.sap107Json(request, options);
        log.info("sapClient.sap107Json result:{}", GsonUtil.toJsonString(sapCustomVO));
        String exeCode = (sapCustomVO.getSuccess() ? SapRetEnum.SUCCESS.getKey() : SapRetEnum.ERROR.getKey());
        protoSapExeLogService.updateLog(exelogId, exeCode, sapCustomVO.getMessage());
        return sapCustomVO;
    }

    @Override
    public SapCustomVO enginePhoneTransfer(SapPhoneTransferDTO request, String funId, String orderId, String orderCode, String orderName, String doUser, String mrpType) {
        String dateStr = DateUtils.dateToStringByType(new Date(), DateUtils.YEAR_MONTH_DATE_INT);
        request.setCreateDate(dateStr);
        List<SapPhoneTransferDTO.Item> items = request.getItems();
        for (SapPhoneTransferDTO.Item item : items){
            item.setMidocNum(request.getMidocNum());
        }
        // 亦庄
        if (SapFactoryEnum.YI_ZHUANG.getSapFactory().equals(request.getSapFactory())){
            request.setFoundryCode(SapFactoryEnum.YI_ZHUANG.getSapFactoryCode());
        } else if (SapFactoryEnum.CHANG_PING.getSapFactory().equals(request.getSapFactory())) {
            if (SrmPurchaseTypeEnum.ZDB.getCode().equals(request.getBsart())) {
                request.setFoundryCode(CommonConstant.I_LIFNR);
            } else {
                request.setFoundryCode(SapFactoryEnum.CHANG_PING.getSapFactoryCode());
            }
            // 如果是昌平工厂，且业务类型为YJFF， SAP工厂通过成本中心更新，否则为国内1110
            if (CommonConstant.SapBusiTypeEnum.MRP_PROVIDE.getType().equals(request.getBusinessType())) {
                request.setSapFactory(SapFactoryEnum.IN_LAND.getSapFactory());
            } else {
                request.setSapFactory(SapFactoryEnum.CHANG_PING.getMakeCn().split("-")[1].equals(request.getKostl().substring(1, 5))
                        ? SapFactoryEnum.CHANG_PING.getSapFactory()
                        : SapFactoryEnum.IN_LAND.getSapFactory());
            }
        } else if (SapFactoryEnum.CHANG_PING2.getSapFactory().equals(request.getSapFactory())) {
            request.setFoundryCode(SapFactoryEnum.CHANG_PING2.getSapFactoryCode());
        }
        //机器人
        if (SapFactoryEnum.ROBOT.getSapFactory().equals(request.getSapFactory())){
            request.setFoundryCode(SapFactoryEnum.ROBOT.getSapFactoryCode());
        }
        // 可穿戴 111J 电视1111 盒子2110 手机1110 笔记本电脑111K
        if (MrpTypeEnum.TV.getType().equals(mrpType)) {
            if (SapMakeCnEnum.TV.getSapFactory().equals(request.getSapFactory())) {
                request.setOrganization(OrganizationEnum.tv_chain1.getType());
            } else {
                request.setOrganization(OrganizationEnum.box_chain.getType());
            }
        } else if (MrpTypeEnum.WEARABLE.getType().equals(mrpType)) {
            request.setOrganization(OrganizationEnum.wearable_items1.getType());
        } else if (MrpTypeEnum.ECOCHAIN.getType().equals(mrpType)) {
            //智能硬件工厂1185（米电智能产品生产工厂），采购组织2120（数码配件供应链），公司代码1180米电。
            //生态链其它产品工厂（1112-米通商品外采工厂），采购组织1122（生态链供应链），公司代码1110米通。
            if (SapMakeCnEnum.ECO_MIDIAN.getSapFactory().equals(request.getSapFactory())) {
                request.setOrganization(OrganizationEnum.smart_hardware.getType());
            } else {
                request.setOrganization(OrganizationEnum.eco_chain.getType());
            }
        } else if (MrpTypeEnum.LAPTOP.getType().equals(mrpType)) {
            request.setOrganization(OrganizationEnum.laptop_chain.getType());
        }else if (MrpTypeEnum.ROBOT.getType().equals(mrpType)) {
            request.setOrganization(OrganizationEnum.robot_chain.getType());
        } else if (MrpTypeEnum.HEA_FACTORY.getType().equals(mrpType)) {
            // SAP工厂 8510  采购组织 1134 公司代码 8510  P
            request.setFoundryCode(SapFactoryEnum.HEA_FACTORY.getSapFactoryCode());
            request.setOrganization(OrganizationEnum.hea_factory.getType());
            //参考昌平 3882-1110   大家电 8510-1700  参考1184-1110
            request.setSapFactory(SapMakeCnEnum.HEA_FACTORY.getMakeCn().split("-")[1]);
        } else if (MrpTypeEnum.MI_AUTOEACC.getType().equals(mrpType)) {
            request.setOrganization(OrganizationEnum.MI_AUTOEACC.getType());
            request.setFoundryCode(CommonConstant.I_LIFNR);
            request.setSapFactory(SapMakeCnEnum.MI_AUTOEACC.getSapFactory());//sap工厂传111N
        }
        String exelogId = protoSapExeLogService.insertLog(request, funId, orderId, orderCode, orderName, doUser);
        log.debug("sapClient.enginePhoneTransfer request:{}", GsonUtil.toJsonString(request));
        SapCustomVO result = sapClient.enginePhoneTransfer(request);
        log.debug("sapClient.enginePhoneTransfer result:{}", GsonUtil.toJsonString(result));
        String exeCode = (result.getSuccess() ? SapRetEnum.SUCCESS.getKey() : SapRetEnum.ERROR.getKey());
        protoSapExeLogService.updateLog(exelogId, exeCode, result.getMessage());
        // 成本中心不存在时 调mdm接口创建成本中心
        if (!result.getSuccess() && StringUtils.endsWith(result.getMessage(), CommonConstant.COST_CENTER_NOT_EXIST)){
            String centerCost = StringUtils.trim(StringUtils.replace(result.getMessage(), CommonConstant.COST_CENTER_NOT_EXIST, ""));
            String userCode = "";
            if (StringUtils.equals(request.getZzKostl(), centerCost)){
                userCode = request.getApplyPerson();
            } else {
                userCode = request.getUsePerson();
            }
            String message = String.format("%s对应的成本中心%s不存在，系统已自动推送MDM创建成本中心，请稍后再试；", userCode, centerCost);
            result.setMessage(message);
            if(StringUtils.isNotBlank(request.getApplyPerson()) && StringUtils.isNotBlank(request.getZzKostl())){
                mdmService.createCostCenter(request.getApplyPerson(), request.getZzKostl());
            }
            if(StringUtils.isNotBlank(request.getUsePerson()) && StringUtils.isNotBlank(request.getKostl())){
                mdmService.createCostCenter(request.getUsePerson(), request.getKostl());
            }
        }
        return result;
    }

    @Override
    @OACacheSet(cacheEnum = OACacheKeyEnum.class, cacheEnumField = "SAP_PROJECT_INFO_CACHE", param = "project", refreshCacheTime = 5 * 60)
    public List<SapProjectInfoDTO> getProjectInfo(String project) {
        SapProjectDTO request = new SapProjectDTO();
        request.setProject(project);
        SapBaseVO customVO = sapClient.getProjectInfo(request);
        if (!customVO.getSuccess()){
            return new ArrayList<>();
        }
        List<SapProjectInfoDTO> result = customVO.getData();
        Iterator<SapProjectInfoDTO> iterator = result.iterator();
        while (iterator.hasNext()) {
            SapProjectInfoDTO next = iterator.next();
            String matnr = next.getMatnr();
            // 90/52/51/57/95放开
            if (!StringUtils.startsWith(matnr, "90") && !StringUtils.startsWith(matnr, "52")
                    && !StringUtils.startsWith(matnr, "51") && !StringUtils.startsWith(matnr, "57")
                    && !StringUtils.startsWith(matnr, "95")) {
                iterator.remove();
                continue;
            }
            if(StringUtils.startsWith(matnr, "95")){
                next.setMatnr(next.getSkucode());
                next.setMaktx(next.getSmaktx());
            }
        }
        //料号按照，95，90，57,51，52排序
        result.sort(Comparator.comparing(SapProjectInfoDTO::getMatnr).reversed());
        return result;
    }

    private String replaceSpecialChat(String matDescOne){
        if (StringUtils.isBlank(matDescOne)){
            return "";
        }
        String regEx = "[\n`~!@#$%^&*+=|{}':;',\\[\\].<>/?~！@#￥%……&*——+|{}【】‘；：”“’。， 、？]";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(matDescOne);
        return m.replaceAll("").trim();
    }
}
