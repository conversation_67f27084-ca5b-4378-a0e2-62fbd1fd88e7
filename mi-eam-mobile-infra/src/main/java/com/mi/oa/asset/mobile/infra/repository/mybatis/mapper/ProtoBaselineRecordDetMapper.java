package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoBaselineRecordDetPO;

/**
 * table_name : proto_baseline_record_det
 * <AUTHOR>
 * @date 2022/03/03/05:25
 */
public interface ProtoBaselineRecordDetMapper extends BaseMapper<ProtoBaselineRecordDetPO> {
    /**
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String baselineRecordDetId);
}