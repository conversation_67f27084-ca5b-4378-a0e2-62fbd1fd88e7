package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSapExeLogPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoSapExeLogMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoSapExeLogService;
import com.mi.oa.asset.x5.common.JacksonUtils;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/12/10
 */
@Service
public class ProtoSapExeLogServiceImpl extends ServiceImpl<ProtoSapExeLogMapper, ProtoSapExeLogPO>
        implements ProtoSapExeLogService {

    @Autowired
    private CommonService commonService;

    @Override
    @Transactional(propagation= Propagation.REQUIRES_NEW)
    public String insertLog(Object obj, String funId, String orderId, String orderCode, String orderName, String doUser) {
        return insertLog(obj,funId,orderId, orderCode, orderName, doUser, null);
    }

    @Override
    public String insertLog(Object obj, String funId, String orderId, String orderCode, String orderName, String doUser, String exeCode) {
        String uniqueId = commonService.getUniqueId(RedisUniqueKeyEnum.SAP_EXELOG);
        ProtoSapExeLogPO exeLogPO = new ProtoSapExeLogPO();
        exeLogPO.setExelogId(uniqueId);
        exeLogPO.setExeBody(JacksonUtils.bean2Json(obj));
        exeLogPO.setFunId(funId);
        exeLogPO.setOrderId(orderId);
        exeLogPO.setOrderCode(orderCode);
        exeLogPO.setOrderName(orderName);
        exeLogPO.setDoUser(doUser);
        exeLogPO.setDoDate(new Date());
        exeLogPO.setExeCode(exeCode);
        if (StringUtils.isBlank(exeCode)){
            exeLogPO.setExeCode("SEND");
        }
        if ("asn_order".equals(funId) || "asn_po".equals(funId) || "asn_sn".equals(funId)
                || "updateOutCode".equals(funId)){
            exeLogPO.setExeCode("S");
        }
        baseMapper.insert(exeLogPO);
        return uniqueId;
    }

    @Override
    @Transactional(propagation= Propagation.REQUIRES_NEW)
    public void updateLog(String exelogId, String exeCode, String exeDesc) {
        ProtoSapExeLogPO exeLogPO = new ProtoSapExeLogPO();
        exeLogPO.setExelogId(exelogId);
        exeLogPO.setExeCode(exeCode);
        exeLogPO.setExeDesc(exeDesc);
        baseMapper.updateById(exeLogPO);
    }
}
