package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.common.enums.AsnInStatusEnum;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.common.enums.SrmAsnStatusEnum;
import com.mi.oa.asset.mobile.infra.dto.srm.ProtoAsnOrderListDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoAsnOrderMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoAsnOrderDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoAsnOrderItemService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoAsnOrderService;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/1
 */
@Service
public class ProtoAsnOrderServiceImpl extends ServiceImpl<ProtoAsnOrderMapper, ProtoAsnOrderPO> implements ProtoAsnOrderService {

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProtoAsnOrderItemService protoAsnOrderItemService;

    @Autowired
    private ProtoAsnOrderDetService protoAsnOrderDetService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMainOrder(ProtoAsnOrderListDTO.ProtoAsnOrderDTO orderDTO) {
        orderDTO.setAddDate(new Date());
        orderDTO.setModifyDate(new Date());
        orderDTO.setSynStatus("0");
        ProtoAsnOrderPO asnOrderByCode = getAsnOrderByCode(orderDTO.getOrderCode());
        // 第一次同步
        if (asnOrderByCode == null){
            orderDTO.setOrderId(commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_ASN_ORDER));
            // 保存子表单的数据
            Integer itemsSize = protoAsnOrderItemService.saveItems(orderDTO);

            ProtoAsnOrderPO orderDO = new ProtoAsnOrderPO();
            BeanUtils.copyProperties(orderDTO, orderDO);
            orderDO.setOutNum(itemsSize);
            orderDO.setDueInNum(itemsSize);
            baseMapper.insert(orderDO);
            return;
        }
        // 已有的先删除  再插入
        baseMapper.deleteById(asnOrderByCode.getOrderId());
        orderDTO.setOrderId(asnOrderByCode.getOrderId());
        // 保存子表单的数据
        Integer itemsSize = protoAsnOrderItemService.saveItems(orderDTO);
        ProtoAsnOrderPO orderDO = new ProtoAsnOrderPO();
        BeanUtils.copyProperties(orderDTO, orderDO);
        orderDO.setOutNum(itemsSize);
        orderDO.setDueInNum(itemsSize);
        baseMapper.insert(orderDO);
    }


    /**
     *
     * @param orderCode
     * @return
     */
    @Override
    public ProtoAsnOrderPO getAsnOrderByCode(String orderCode) {
        if (StringUtils.isEmpty(orderCode)){
            return null;
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("order_code", orderCode);
        return baseMapper.selectOne(queryWrapper);
    }

    @Override
    public void updateAsnOrderStatus(String asnNo, String orderStatus) {
        if (StringUtils.isEmpty(asnNo)){
            return;
        }
        UpdateWrapper updateWrapper = new UpdateWrapper();
        updateWrapper.eq("order_code", asnNo);
        updateWrapper.set("order_status", orderStatus);
        this.update(updateWrapper);
    }

    @Override
    public void feedBackReceivingToAsnOrder(ProtoInPO protoInPO, String orderCode) {
        ProtoAsnOrderPO asnOrderPO = this.getAsnOrderByCode(orderCode);
        if (null == asnOrderPO){
            return;
        }
        LambdaUpdateWrapper<ProtoAsnOrderPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoAsnOrderPO::getOrderCode, orderCode);
        // 总收货数量
        Integer outNum = asnOrderPO.getOutNum();
        Integer haveNum = protoAsnOrderDetService.countByAsnAndStatus(orderCode, AsnInStatusEnum.HAVE_DELIVERY.getState());
        Integer dueNum = outNum - haveNum;
        updateWrapper.set(ProtoAsnOrderPO::getInNum, haveNum);
        updateWrapper.set(ProtoAsnOrderPO::getDueInNum, dueNum);
        // 收货日期
        Date asnInDate = asnOrderPO.getInDate();
        Date protoInDate =  protoInPO.getInDate();
        if (protoInDate != null && (null == asnInDate || protoInDate.compareTo(asnInDate) > 0)){
            updateWrapper.set(ProtoAsnOrderPO::getUserName, protoInPO.getUserName());
            updateWrapper.set(ProtoAsnOrderPO::getUserCode, protoInPO.getUserCode());
            updateWrapper.set(ProtoAsnOrderPO::getEmpCode, protoInPO.getEmpCode());
            updateWrapper.set(ProtoAsnOrderPO::getInDate, protoInDate);
        }
        if (StringUtils.isEmpty(asnOrderPO.getHouseName())){
            updateWrapper.set(ProtoAsnOrderPO::getHouseName, protoInPO.getHouseName());
        }
        if (StringUtils.isEmpty(asnOrderPO.getHouseCode())){
            updateWrapper.set(ProtoAsnOrderPO::getHouseCode, protoInPO.getHouseCode());
        }
        if (StringUtils.isEmpty(asnOrderPO.getHouseId())){
            updateWrapper.set(ProtoAsnOrderPO::getHouseId, protoInPO.getHouseId());
        }
        Integer orderStatus = (dueNum == 0 ? SrmAsnStatusEnum.HAVE_RECEIVE.getEamStatus() : SrmAsnStatusEnum.SECTION_RECEIVE.getEamStatus());
        updateWrapper.set(ProtoAsnOrderPO::getOrderStatus, orderStatus+"");
        this.update(updateWrapper);
    }

    @Override
    public void checkOutNum(String orderCode) {
        ProtoAsnOrderPO asnOrderPO = this.getAsnOrderByCode(orderCode);
        if (null == asnOrderPO){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_ASN_ORDER_001, orderCode);
        }
        if (asnOrderPO.getInNum() > asnOrderPO.getOutNum()){
            throw new BizException(ApplicationErrorCodeEnum.PROTO_IN_002);
        }
    }
}
