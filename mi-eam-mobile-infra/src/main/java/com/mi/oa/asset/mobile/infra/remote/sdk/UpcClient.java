package com.mi.oa.asset.mobile.infra.remote.sdk;


import com.mi.oa.asset.mobile.infra.config.UpcClientConfig;
import com.mi.oa.asset.mobile.infra.config.X5FeignMethod;
import com.mi.oa.asset.mobile.infra.dto.upc.UpcProjectDTO;
import com.mi.oa.asset.mobile.infra.dto.upc.UpcProjectDetailDTO;
import com.mi.oa.asset.mobile.infra.dto.upc.UpcProjectItemReq;
import com.mi.oa.asset.mobile.infra.dto.upc.UpcProjectReq;
import com.mi.oa.asset.x5.consumer.X5FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * @Desc https://xiaomi.f.mioffice.cn/docs/dock4MULQJziUsJKEPhVibaaYLb#
 * 生态链项目获取项目从UPC提供对外X5获取
 * <AUTHOR>
 */
@X5FeignClient(value = "upcX5", url = "${upcX5.host}", appId = "${upcX5.appId}",
        appKey = "${upcX5.appKey}", configuration = UpcClientConfig.class)
public interface UpcClient {

    /**
     * 2.1根据传入时间范围和级别，返回项目集合
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/skuProjectQuery/timeQuery")
    @X5FeignMethod("skuProjectQuery")
    UpcProjectDTO skuProjectQuery(UpcProjectReq req);

    /**
     * 2.2根据父项目查询所有子项目，返回项目集合
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/skuProjectQuery/queryChildProject")
    @X5FeignMethod("queryChildProject")
    UpcProjectDetailDTO queryChildProject(UpcProjectItemReq req);

}
