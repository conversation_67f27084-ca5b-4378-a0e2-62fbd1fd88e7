package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysDataTypePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysDataTypeMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysDataTypeService;
import org.springframework.stereotype.Service;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/4/6
 */
@Service
public class SysDataTypeServiceImpl extends ServiceImpl<SysDataTypeMapper, SysDataTypePO> implements SysDataTypeService {

    @Override
    public String getDataTypeIdByField(String field) {
        LambdaQueryWrapper<SysDataTypePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysDataTypePO::getDtypeField, field);
        wrapper.last("limit 1");
        return this.getOne(wrapper).getDtypeId();
    }
}
