package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmProjectVO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectListPO;

import java.util.List;


/**
 * 项目目录
 *
 * <AUTHOR>
 * @date 2022/1/24 17:32
 */
public interface ProtoProjectListService extends IService<ProtoProjectListPO> {

    /**
     * 更新项目目录表PM信息
     *
     * @param pmUserName
     * @param pmUserCode
     * @param pmEmpCode
     * @param modifyDate
     * @param modifyUserid
     * @param projectCode
     * @return
     */
    boolean updateProjectListPM(String pmUserName, String pmUserCode, String pmEmpCode, String modifyDate, String modifyUserid, String projectCode);

    /**
     * 批量更新项目目录表PM信息
     *
     * @param pmUserName
     * @param pmUserCode
     * @param pmEmpCode
     * @param modifyDate
     * @param modifyUserid
     * @param projectCodes
     * @return
     */
    boolean batchUpdateProjectListPM(String pmUserName, String pmUserCode, String pmEmpCode, String modifyDate, String modifyUserid, String[] projectCodes);

    /**
     * 查询所有项目列表
     *
     * @return
     */
    List<ProtoProjectListPO> getAllProjectList();

    /**
     * 保存或者更新
     * @param projectListPO
     */
    void saveOrUpdateProject(ProtoProjectListPO projectListPO, List<MdmProjectVO.TrialProductionFactoryDTO> factoryList);

    /**
     * 通过项目编码查询项目
     * @param projectCode
     * @return
     */
    ProtoProjectListPO getByProjectCode(String projectCode);

    /**
     * 通过项目编码和MRP类型查询项目
     * @param projectCode
     * @param mrpType
     * @return
     */
    ProtoProjectListPO getByProjectCodeAndMrpType(String projectCode,String mrpType);

    /**
     * 通过项目编码集合查询项目
     * @param projectCodes
     * @return
     */
    List<ProtoProjectListPO> getByProjectCodes(List<String> projectCodes);


}
