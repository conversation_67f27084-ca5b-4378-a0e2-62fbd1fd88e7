package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoBaselineRecordPO;

/**
 * table_name : proto_baseline_record
 * <AUTHOR>
 * @date 2022/02/28/04:01
 */
public interface ProtoBaselineRecordMapper extends BaseMapper<ProtoBaselineRecordPO> {
    /**
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(String baselineRecordId);
}