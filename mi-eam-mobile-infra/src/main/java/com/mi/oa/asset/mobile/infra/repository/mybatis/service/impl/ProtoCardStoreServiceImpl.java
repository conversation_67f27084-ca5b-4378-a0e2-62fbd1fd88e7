package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardStorePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoCardStoreMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardStoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/5/17
 */
@Service
public class ProtoCardStoreServiceImpl extends ServiceImpl<ProtoCardStoreMapper, ProtoCardStorePO> implements ProtoCardStoreService {

    @Autowired
    private ProtoCardService protoCardService;

    @Override
    public Boolean statistics() {
        // 先删除 后插入
        this.remove(new QueryWrapper<>());
        List<ProtoCardStorePO> list = protoCardService.listGroupByStore();
        this.saveBatch(list);
        return true;
    }
}
