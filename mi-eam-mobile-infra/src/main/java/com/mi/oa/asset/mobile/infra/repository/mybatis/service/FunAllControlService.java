package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.FunAllControlPO;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/20 17:21
 */
public interface FunAllControlService extends IService<FunAllControlPO> {
    public String getComboValue(String controlCode, String valueData);
    public List<FunAllControlPO> getComboValue(String controlCode);
}
