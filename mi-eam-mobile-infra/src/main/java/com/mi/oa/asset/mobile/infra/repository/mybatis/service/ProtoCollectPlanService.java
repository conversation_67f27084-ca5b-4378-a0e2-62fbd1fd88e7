package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCollectPlanPO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/17
 */
public interface ProtoCollectPlanService extends IService<ProtoCollectPlanPO> {

    void updateAuditingInvalid(List<String> collectCodeList);
}
