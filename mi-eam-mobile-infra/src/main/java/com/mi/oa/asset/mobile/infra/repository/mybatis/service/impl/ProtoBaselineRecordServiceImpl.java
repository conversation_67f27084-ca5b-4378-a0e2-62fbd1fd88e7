package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoBaselineRecordPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoBaselineRecordMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoBaselineRecordService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2022/2/28 16:05
 */
@Service
public class ProtoBaselineRecordServiceImpl extends ServiceImpl<ProtoBaselineRecordMapper, ProtoBaselineRecordPO> implements ProtoBaselineRecordService {
    @Override
    public ProtoBaselineRecordPO findOneByParam(ProtoBaselineRecordPO po) {
        QueryWrapper<ProtoBaselineRecordPO> queryWrapper = new QueryWrapper(po);
        List<ProtoBaselineRecordPO> rs = list(queryWrapper);
        return CollectionUtils.isEmpty(rs)?null:rs.get(0);
    }

    @Override
    public List<ProtoBaselineRecordPO> findAllByParam(ProtoBaselineRecordPO po) {
        QueryWrapper<ProtoBaselineRecordPO> queryWrapper = new QueryWrapper(po);
        return list(queryWrapper);
    }
}
