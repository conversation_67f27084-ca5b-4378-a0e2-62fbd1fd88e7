package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.MdmX5Enum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysOperLogPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysOperLogMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysOperLogService;
import com.mi.oa.asset.x5.common.X5Response;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * description: your description
 *
 * <AUTHOR>
 * @date 2023/9/20
 */
@Service
@Slf4j
public class SysOperLogServiceImpl extends ServiceImpl<SysOperLogMapper, SysOperLogPO> implements SysOperLogService {

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveOrUpdateLog(X5Response x5Response, String paramJson, String orderCode, String doUser) {
        SysOperLogPO po = new SysOperLogPO();
        po.setOrderCode(orderCode);
        po.setDoUser(doUser);
        po.setDoDate(new Date());
        po.setOptParam(paramJson);
        if (x5Response == null) {
            po.setOptCode(MdmX5Enum.E.getCode());
            po.setOptDesc(MdmX5Enum.E.getDesc());
        } else if (MdmX5Enum.S.getCode().equals(x5Response.getHeader().getCode())) {
            po.setOptCode(MdmX5Enum.S.getCode());
            po.setOptDesc(MdmX5Enum.S.getDesc());
        } else {
            po.setOptCode(x5Response.getHeader().getCode());
            po.setOptDesc(MdmX5Enum.valuesOf(x5Response.getHeader().getCode()));
        }
        LambdaQueryWrapper<SysOperLogPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysOperLogPO::getOrderCode, po.getOrderCode());
        SysOperLogPO logPO = this.getOne(wrapper);
        if (logPO == null) {
            this.save(po);
        } else {
            po.setId(logPO.getId());
            this.updateById(po);
        }
    }
}
