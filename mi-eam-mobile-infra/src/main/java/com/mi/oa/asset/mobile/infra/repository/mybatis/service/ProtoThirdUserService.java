package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoThirdUserPO;

import java.util.Date;
import java.util.List;

/**
 * 第三方人员配置
 *
 * <AUTHOR>
 * @date 2022/1/6 16:55
 */
public interface ProtoThirdUserService extends IService<ProtoThirdUserPO> {
    ProtoThirdUserPO findUserInfo(String userCode);

    /**
     * 缓存中
     * @param userCode
     * @return
     */
    ProtoThirdUserPO findUserInfoCache(String userCode);
    /**
     * 根据主键查询用户信息
     *
     * @param userId
     * @return
     */
    ProtoThirdUserPO findUserById(String userId);

    /**
     * 同步P账号有效期
     */
    void synValidDate();

    /**
     * 通过状态查询数据
     * @param recordStatus
     * @return
     */
    List<ProtoThirdUserPO> listByAuditing(CommonConstant.RecordStatus recordStatus);


    /**
     * 工程机P账号到期提醒
     */
    void sendReminderEmail();

    /**
     * 查询第三方用户信息
     *
     * @param sqlStr
     * @return
     */
    //List<ProtoThirdUserPO> getNoUserCodeList(String sqlStr);

    /**
     * 查询第三方用户信息
     *
     * @param sqlStr
     * @return
     */
    //List<ProtoThirdUserPO> getNoProviderCodeList(String sqlStr);

    /**
     * 查询第三方用户信息
     *
     * @param sqlStr
     * @return
     */
    //List<ProtoThirdUserPO> getImpRepeatList(String sqlStr);

    /**
     * 查询第三方用户信息
     *
     * @param sqlStr
     * @return
     */
    //List<ProtoThirdUserPO> getRepeatList(String sqlStr);

}
