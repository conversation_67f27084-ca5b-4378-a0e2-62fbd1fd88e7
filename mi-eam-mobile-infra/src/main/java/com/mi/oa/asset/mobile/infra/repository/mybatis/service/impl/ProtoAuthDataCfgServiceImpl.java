package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAuthDataCfgPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoAuthDataCfgMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoAuthDataCfgService;
import org.springframework.stereotype.Service;

/**
 * 数据权限配置
 *
 * <AUTHOR>
 * @date 2022/4/7 10:25
 */
@Service
public class ProtoAuthDataCfgServiceImpl extends ServiceImpl<ProtoAuthDataCfgMapper, ProtoAuthDataCfgPO> implements ProtoAuthDataCfgService {

    /**
     * 根据主键查询
     *
     * @param keyId
     * @return
     */
    @Override
    public ProtoAuthDataCfgPO findProtoAuthDataCfg(String keyId) {
        QueryWrapper<ProtoAuthDataCfgPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ProtoAuthDataCfgPO::getCfgId, keyId);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    /**
     * 根据接口人账号和代管部门编号统计数据
     */
    @Override
    public int countAuthDataCfg(String cpUserCode, String agDeptCode, String mrpType) {
        LambdaQueryWrapper<ProtoAuthDataCfgPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoAuthDataCfgPO::getCpUserCode, cpUserCode)
                .eq(ProtoAuthDataCfgPO::getAgDeptCode, agDeptCode)
                .eq(ProtoAuthDataCfgPO::getAuditing, CommonConstant.RecordStatus.COMMITTED.getStatus())
                .eq(ProtoAuthDataCfgPO::getMrpType, mrpType);
        return this.count(wrapper);
    }
}
