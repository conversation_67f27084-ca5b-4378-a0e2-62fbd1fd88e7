package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.dto.srm.ProtoAsnOrderListDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderItemPO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/1
 */

public interface ProtoAsnOrderItemService extends IService<ProtoAsnOrderItemPO> {

    /**
     * 保存子单
     * @param orderDTO
     */
    Integer saveItems(ProtoAsnOrderListDTO.ProtoAsnOrderDTO orderDTO);

    List<ProtoAsnOrderItemPO> getListByAsnOrder(List<String> list);
}
