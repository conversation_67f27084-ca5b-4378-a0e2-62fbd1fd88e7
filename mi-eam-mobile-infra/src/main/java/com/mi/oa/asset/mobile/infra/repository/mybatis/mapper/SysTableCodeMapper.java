package com.mi.oa.asset.mobile.infra.repository.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysTableCodePO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * table_name : sys_tablecode
 * <AUTHOR>
 * @date 2022/01/10/03:12
 */
public interface SysTableCodeMapper extends BaseMapper<SysTableCodePO> {

    @Update("update sys_tablecode set max_value = max_value + 1 where table_name = #{tabName} and code_ext = #{codeExt}")
    void updateMaxValue(@Param("tabName") String tabName, @Param("codeExt") String codeExt);
}