package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.RedisFlowNumEnum;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.common.enums.YesNoEnum;
import com.mi.oa.asset.mobile.infra.dto.mdm.MdmProjectVO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectCfgPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectListPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoProjectCfgMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoCardService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectCfgService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoProjectFactoryService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 项目配置
 *
 * <AUTHOR>
 * @date 2022/1/21 10:43
 */
@Service
@Slf4j
public class ProtoProjectCfgServiceImpl extends ServiceImpl<ProtoProjectCfgMapper, ProtoProjectCfgPO> implements ProtoProjectCfgService {

    @Autowired
    private ProtoProjectFactoryService protoProjectFactoryService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private ProtoCardService protoCardService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateCfg(ProtoProjectListPO projectListPO, List<MdmProjectVO.TrialProductionFactoryDTO> factoryList) {
        // 获取并初始化项目配置
        ProtoProjectCfgPO cfgPO = getAndInitProtoProjectCfgPO(projectListPO);
        // 更新台账中上市时间
        protoCardService.updateListDateByProjectCode(cfgPO.getProjectCode(), cfgPO.getListDate());
        this.saveOrUpdate(cfgPO);
        // 保存子表试产工厂
        protoProjectFactoryService.saveProjectFactory(factoryList, cfgPO.getCfgId());
    }

    @NotNull
    @Override
    public ProtoProjectCfgPO getAndInitProtoProjectCfgPO(ProtoProjectListPO projectListPO) {
        ProtoProjectCfgPO cfgPO = getByProjectCode(projectListPO.getProjectCode());
        if (null == cfgPO){
            cfgPO = new ProtoProjectCfgPO();
            cfgPO.setCfgId(commonService.getUniqueId(RedisUniqueKeyEnum.PROTO_PROJECT_CFG));
            cfgPO.setCfgCode(commonService.getFlowNum(RedisFlowNumEnum.PROJECT_CODE_NUM));
            cfgPO.setProjectCode(cfgPO.getProjectCode());
            cfgPO.setIsJis(YesNoEnum.INNER_TO_INNER.getType());
        }
        BeanUtils.copyProperties(projectListPO, cfgPO);
        cfgPO.setParentProjectCode(projectListPO.getParentCode());
        cfgPO.setAuditing(CommonConstant.RecordStatus.COMMITTED.getStatus());
        cfgPO.setModifyDate(new Date());
        cfgPO.setFinalUserName(CommonConstant.ADMIN_KEY);
        return cfgPO;
    }

    @Override
    public List<ProtoProjectCfgPO> listByProjectCodeList(List<String> projectCodeList) {
        if (CollectionUtils.isEmpty(projectCodeList)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<ProtoProjectCfgPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoProjectCfgPO::getProjectCode, projectCodeList);
        return this.list(wrapper);
    }

    @Override
    public List<ProtoProjectCfgPO> listProjectCodeList(List<String> projectCodeList) {
        LambdaQueryWrapper<ProtoProjectCfgPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProtoProjectCfgPO::getProjectCode, projectCodeList);
        wrapper.in(ProtoProjectCfgPO::getAuditing, CommonConstant.RecordStatus.COMMITTED.getStatus(), CommonConstant.RecordStatus.UNCOMMITTED.getStatus());
        wrapper.orderByDesc(ProtoProjectCfgPO::getCfgCode);
        return this.list(wrapper);
    }

    private ProtoProjectCfgPO getByProjectCode(String projectCode){
        LambdaQueryWrapper<ProtoProjectCfgPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoProjectCfgPO::getProjectCode, projectCode);
        wrapper.in(ProtoProjectCfgPO::getAuditing, CommonConstant.RecordStatus.COMMITTED.getStatus(), CommonConstant.RecordStatus.UNCOMMITTED.getStatus());
        wrapper.orderByDesc(ProtoProjectCfgPO::getCfgCode);
        wrapper.last("limit 1");
        return this.getOne(wrapper);
    }

}
