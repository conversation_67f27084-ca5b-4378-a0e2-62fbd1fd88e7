package com.mi.oa.asset.mobile.infra.service.impl;

import com.mi.oa.asset.mobile.application.dto.statusnotify.ProtoListDTO;
import com.mi.oa.asset.mobile.infra.service.ProtoListStatusNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 工程机单据状态通知
 *
 * <AUTHOR>
 * @date 2022/4/14 17:42
 */
@Service
@Slf4j
public class ProtoListStatusNotifyServiceImpl implements ProtoListStatusNotifyService {

    @Autowired
    RocketMQTemplate rocketMQTemplate;

    /**
     * 发送MQ消息
     *
     * @param orderTrace
     */
    @Value("${rocketmq.protoStatusNotify.topic}")
    private String statusNotifyTopic;

    /**
     * 发送工程机单据状态通知
     * @param tag
     * @param  protoListDTO
     */
    public void sendMessage(String tag, ProtoListDTO protoListDTO) {
        if (StringUtils.isBlank(tag) || Objects.isNull(protoListDTO)) {
            log.info("send status_notify is invalid");
            return;
        }
        try {
            rocketMQTemplate.asyncSend(statusNotifyTopic + ":" + tag, protoListDTO, new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.info("async status_notify onSuccess SendResult: {}", sendResult);
                }

                @Override
                public void onException(Throwable throwable) {
                    log.error("async status_notify onException Throwable: {}", throwable);
                }
            });
        } catch (Exception e) {
            log.error("async onException Throwable: {}", e);
        }
    }

}
