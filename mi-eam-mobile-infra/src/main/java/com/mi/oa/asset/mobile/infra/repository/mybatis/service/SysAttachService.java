package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysAttachPO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/7/11
 */
public interface SysAttachService extends IService<SysAttachPO> {

    /**
     * 更新 attachField字段
     * @param attachField 字段名
     * @param funId 功能id
     * @param dataId 记录ID
     * @param attachPath 附件路径
     */
    void updateAttachField(String attachField, String funId, String dataId, String attachPath);

    /**
     * 查询附件
     * @param tableName 表名
     * @param dataId 主键
     */
    List<SysAttachPO> getByTableDataId(String tableName, String dataId);
}
