package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.application.dto.UnReturnDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOrderPayPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysAttachPO;

import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/2
 */
public interface ProtoOrderPayService extends IService<ProtoOrderPayPO> {

    UnReturnDTO getBySalesOrder(String salesOrder);

    UnReturnDTO getByUnturnCode(String unreturnCode);

    ProtoOrderPayPO getByOrderCode(String orderCode);

    /**
     * 提交接口
     * @param salesOrder
     */
    void orderSubmit(String salesOrder);

    /**
     *
     * @param unturnCodeList
     * @return
     */
    List<ProtoOrderPayPO> listUnturnCodeList(List<String> unturnCodeList);

    /**
     * 拷贝附件
     * @param attachList
     */
    void copyAttach(List<SysAttachPO> attachList);
}
