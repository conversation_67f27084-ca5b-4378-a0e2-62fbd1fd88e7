package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysProxyPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysProxyMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysProxyService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/7/12
 */
@Service
public class SysProxyServiceImpl extends ServiceImpl<SysProxyMapper, SysProxyPO> implements SysProxyService {

    @Override
    public List<String> getProxy(List<String> userCodeList) {
        if (CollectionUtils.isEmpty(userCodeList)){
            return new ArrayList<>();
        }
        Date nowDate = new Date();
        LambdaQueryWrapper<SysProxyPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysProxyPO::getAuditing, CommonConstant.RecordStatus.COMMITTED.getStatus())
                .le(SysProxyPO::getStartDate, nowDate)
                .ge(SysProxyPO::getEndDate, nowDate)
                .in(SysProxyPO::getToUserCode, userCodeList);
        List<SysProxyPO> list = this.list(wrapper);
        return list.stream().map(SysProxyPO::getUserCode).distinct().collect(Collectors.toList());
    }
}
