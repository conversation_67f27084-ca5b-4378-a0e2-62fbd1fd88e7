package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.dto.mall.InnerPlanInfo;
import com.mi.oa.asset.mobile.infra.dto.mall.InnerProductDTO;
import com.mi.oa.asset.mobile.infra.dto.mall.InnerProductReq;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInnerPurchasePlanPO;

import java.text.ParseException;
import java.util.List;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/1
 */
public interface ProtoInnerPurchasePlanService extends IService<ProtoInnerPurchasePlanPO> {

    /**
     * 重新计算内购 已下架 实际内购数量 实际内购收益
     * @param orderList
     */
    void calculatePurchase(List<String> orderList);

    /**
     * 通过计划编码获取计划ID
     */
    ProtoInnerPurchasePlanPO getInnerPurchasePlanByCode(String planCode);

    /**
     * 通过计划ID拉取该计划所有商品
     *
     * @param req
     * @return
     */
    List<InnerProductDTO> pullProduct(InnerProductReq req);

    /**
     * 更新台账的状态
     *
     * @param innerProductReqs
     * @return
     */
    void productOffUpdateAssetCard(List<InnerProductReq> innerProductReqs);

    /**
     * 通过计划编码获取业务类型
     *
     * @param planCode
     * @return
     */
    String getMrpType(String planCode);

    /**
     * 查询计划信息
     */
    InnerPlanInfo getProtoPlanInfo() throws ParseException;

    /**
     * 统计内购计划
     */
    void checkInnerPurchaseOrder(String planCode);

    /**
     * 查询审批状态
     *
     * @param planCode
     * @return
     */
    String queryApprovalStatus(String planCode);
}
