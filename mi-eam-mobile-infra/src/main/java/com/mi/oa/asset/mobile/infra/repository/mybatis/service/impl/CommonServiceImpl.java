package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mi.oa.asset.mobile.application.dto.CommonConditionDTO;
import com.mi.oa.asset.mobile.application.dto.ConditionDTO;
import com.mi.oa.asset.mobile.application.errorcode.ApplicationErrorCodeEnum;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.ErrorCodeEnum;
import com.mi.oa.asset.mobile.common.enums.RedisCachePrefixKeyEnum;
import com.mi.oa.asset.mobile.common.enums.RedisFlowNumEnum;
import com.mi.oa.asset.mobile.common.enums.RedisUniqueKeyEnum;
import com.mi.oa.asset.mobile.common.enums.SapBatchIdEnum;
import com.mi.oa.asset.mobile.infra.dto.FunRepeatValDTO;
import com.mi.oa.asset.mobile.infra.dto.RequestContext;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.FunBasePO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.FunColPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.FunStatusPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.CommonService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.FunBaseService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.FunColService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.FunStatusService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.SysCodeRuleService;
import com.mi.oa.asset.mobile.utils.ArrayUtil;
import com.mi.oa.asset.mobile.utils.MapUtil;
import com.mi.oa.asset.mobile.utils.StringUtil;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import com.mi.oa.infra.oaucf.utils.DateUtils;
import com.mi.oa.infra.oaucf.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2021/11/1
 */
@Service
@Slf4j
public class CommonServiceImpl implements CommonService {

    @Autowired
    private FunBaseService funBaseService;

    @Autowired
    private FunColService funColService;

    @Autowired
    private SysCodeRuleService sysCodeRuleService;

    @Autowired
    private FunStatusService funStatusService;
    @Override
    public String getUniqueId(RedisUniqueKeyEnum keyEnum) {
        String yearMonth = DateUtils.getFormatDate("yyMMdd");
        String key = keyEnum.getTableName()+":"+yearMonth;
        Long nextSequence = RedisUtils.incrBy(key, 1);
        RedisUtils.expire(key, 2, TimeUnit.DAYS);
        return yearMonth + StringUtils.leftPad(String.valueOf(nextSequence), 6, "0");
    }

    @Override
    public String getAutoIncId(RedisUniqueKeyEnum keyEnum, int size) {
        String key = "mobile:"+keyEnum.getTableName()+":maxId";
        Long nextSequence = RedisUtils.incrBy(key, 1);
        if(nextSequence!=null&&nextSequence>=Math.pow(10,size)){
            throw new BizException(ApplicationErrorCodeEnum.APPLICATION_UNKNOWN_ERROR);
        }
        return StringUtils.leftPad(String.valueOf(nextSequence), size, "0");
    }

    @Override
    public String getBatchIdUnique(SapBatchIdEnum keyEnum) {
        String monthDay = DateUtils.getFormatDate("yyMMdd");
        String key = this.getBatchKey(keyEnum);
        if (keyEnum.equals(SapBatchIdEnum.ASN_RECEIVE_GOODS) || keyEnum.equals(SapBatchIdEnum.YI_ZHUANG)
                || keyEnum.equals(SapBatchIdEnum.CHANG_PING) ||  keyEnum.equals(SapBatchIdEnum.CHANG_PING2)
                ||  keyEnum.equals(SapBatchIdEnum.ROBOT)){
            Long nextSequence = RedisUtils.incrBy(key, 1);
            if (keyEnum.equals(SapBatchIdEnum.ASN_RECEIVE_GOODS)){
                if (nextSequence < 20000){
                    nextSequence = RedisUtils.incrBy(key, 20000);
                }
            }
            RedisUtils.expire(key, 2, TimeUnit.DAYS);
            return keyEnum.getBillType()+monthDay+StringUtils.leftPad(String.valueOf(nextSequence), keyEnum.getLength(), "0");
        }
        Long nextSequence = RedisUtils.incrBy(key, 1);
        return keyEnum.getBillType()+StringUtils.leftPad(String.valueOf(nextSequence), keyEnum.getLength(), "0");
    }

    @Override
    public String getFlowNum(RedisFlowNumEnum flowNumEnum) {
        String key = RedisCachePrefixKeyEnum.FLOW_NUM_REDIS_KEY.getPrefixKey()+flowNumEnum.getPrefix();
        Long nextSequence = RedisUtils.incrBy(key, 1);
        RedisUtils.expire(key, 120, TimeUnit.DAYS);
        SimpleDateFormat formatter = new SimpleDateFormat(flowNumEnum.getDataType());
        String dataFlow = formatter.format(new Date());
        return flowNumEnum.getPrefix()+dataFlow+StringUtils.leftPad(String.valueOf(nextSequence), flowNumEnum.getLength(), "0");
    }

    /**
     * 通用保存单个接口 返回主键
     * @param requestContext
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateEvent(RequestContext requestContext) {
        String funID = requestContext.getFunID();
        FunBasePO basePO = funBaseService.getById(funID);
        List<FunColPO> funColPOS = funColService.listByFunID(funID);
        if (null == basePO || CollectionUtils.isEmpty(funColPOS)){
            throw new BizException(ErrorCodeEnum.NOT_FUNCTION);
        }
        String tableName = basePO.getTableName();
        String sKeyValue = requestContext.getRequestValue(CommonConstant.KEY_ID);
        if (StringUtils.isBlank(sKeyValue)){
            throw new BizException(ErrorCodeEnum.UPDATE_NO_KEY);
        }
        Map<String, Object> requestMap = requestContext.getRequestMap();
        Map<String,String> valueMap = getDirtyData(requestMap, tableName);
        if (valueMap.isEmpty()){
            throw new BizException(ErrorCodeEnum.SAVE_NO_DATA);
        }
        dirtySave(valueMap, sKeyValue, requestContext, basePO);
        return sKeyValue;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createEvent(RequestContext requestContext) {
        String funID = requestContext.getFunID();
        FunBasePO basePO = funBaseService.getById(funID);
        List<FunColPO> funColPOS = funColService.listByFunID(funID);
        if (null == basePO || CollectionUtils.isEmpty(funColPOS)){
            throw new BizException(ErrorCodeEnum.NOT_FUNCTION);
        }
        String pkCol = this.getPkCol(basePO);
        String tableName = basePO.getTableName();
        Map<String, Object> requestMap = requestContext.getRequestMap();
        Map<String,String> valueMap = getInsertData(requestMap, funColPOS);
        if (valueMap.isEmpty()){
            throw new BizException(ErrorCodeEnum.SAVE_NO_DATA);
        }
        this.checkRepeatVal(valueMap, requestContext, "", basePO);
        String keyId = this.getUniqueId(tableName);
        valueMap.put(pkCol, keyId);
        String codeCol = basePO.getCodeCol();
        if (StringUtils.isNotBlank(codeCol)){
            valueMap.put(codeCol, sysCodeRuleService.getBusCode(funID, valueMap));
        }
        String isUserinfo = basePO.getIsUserinfo();
        Map<String, String> userInfo = requestContext.getUserInfo();
        if ("1".equals(isUserinfo)) {
            valueMap.put("add_date", DateUtils.getFormatDate(DateUtils.COMMON_PATTERN));
            valueMap.put("add_userid", userInfo.get("user_id"));
        }
        funBaseService.insertCommonValue(tableName, valueMap);
        return keyId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String gridSaveEvent(RequestContext requestContext) {
        List<String> ids = new ArrayList<>();
        String funID = requestContext.getFunID();
        FunBasePO basePO = funBaseService.getById(funID);
        List<FunColPO> funColPOS = funColService.listByFunID(funID);
        if (null == basePO || CollectionUtils.isEmpty(funColPOS)){
            throw new BizException(ErrorCodeEnum.NOT_FUNCTION);
        }
        String pkCol = this.getPkCol(basePO);
        String tableName = basePO.getTableName();
        List<Map<String, String>> valuesMap = getValuesMap(requestContext.getRequestMap(), funColPOS);
        for (Map<String, String> tempMap : valuesMap){
            String sValue = tempMap.get(pkCol);
            // 修改
            if (StringUtils.isNotBlank(sValue)){
                ids.add(sValue);
                dirtySave(tempMap, sValue, requestContext, basePO);
            } else {
                // 自增Id主键
                String autoIncCol = tableName+".id";
                boolean isAutoIncCol = autoIncCol.equals(pkCol);
                String keyId = "";
                if (!isAutoIncCol){
                    keyId = this.getUniqueId(tableName);
                    ids.add(keyId);
                    tempMap.put(pkCol, keyId);
                } else {
                    tempMap.remove(pkCol);
                }
                // 外键
                String fkCol = basePO.getFkCol();
                String fkValue = tempMap.get(fkCol);
                if (StringUtils.isBlank(fkValue) && StringUtils.isNotBlank(fkCol)){
                    tempMap.put(fkCol, requestContext.getRequestValue(CommonConstant.FK_VALUE));
                }

                // 新增
                funBaseService.insertCommonValue(tableName, tempMap);
                if (isAutoIncCol){
                    ids.add(tempMap.get("id"));
                }
            }
        }
        return String.join(",", ids);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String commonSave(RequestContext requestContext) {
        String eventCode = requestContext.getEventCode();
        if (CommonConstant.UPDATE_EVENT.equals(eventCode)){
            return this.updateEvent(requestContext);
        } else if (CommonConstant.CREATE_EVENT.equals(eventCode)){
            return this.createEvent(requestContext);
        } else if (CommonConstant.SAVE_EG_EVENT.equals(eventCode)){
            return this.gridSaveEvent(requestContext);
        } else if (CommonConstant.COMMON_AUDIT.equals(eventCode)){
            return this.commonAudit(requestContext);
        }
        throw new BizException(ErrorCodeEnum.EVENT_CODE_ERROR);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String commonAudit(RequestContext requestContext) {
        String funID = requestContext.getFunID();
        FunStatusPO funStatusPO = funStatusService.getByFunId(funID);
        String audit1 = "1";
        if (null != funStatusPO && StringUtils.isEmpty(funStatusPO.getAudit1())){
            audit1 = funStatusPO.getAudit1();
        }
        String auditVal = requestContext.getRequestValue(CommonConstant.AUDIT_VALUE, audit1);
        String[] asKey = requestContext.getRequestValues(CommonConstant.KEY_ID);
        if (null == asKey || asKey.length == 0){
            throw new BizException(ErrorCodeEnum.SUBMIT_NOT_VALUE);
        }
        FunBasePO basePO = funBaseService.getById(funID);
        List<FunColPO> funColPOS = funColService.listByFunID(funID);
        if (null == basePO || CollectionUtils.isEmpty(funColPOS)){
            throw new BizException(ErrorCodeEnum.NOT_FUNCTION);
        }
        String pkCol = this.getPkCol(basePO);
        String tableName = basePO.getTableName();
        String auditCol = basePO.getAuditCol();
        for (String keyId : asKey){
            Integer auditNum = checkAuditNum(keyId, auditVal, basePO);
            if (auditNum > 0){
                throw new BizException(ErrorCodeEnum.RECORD_HAVE_SUBMIT, auditNum+"");
            }
            Map<String, String> valueMap = new HashMap<>();
            valueMap.put(auditCol, auditVal);
            if ("1".equals(basePO.getIsUserinfo())) {
                valueMap.put("modify_date", DateUtils.getFormatDate(DateUtils.COMMON_PATTERN));
                valueMap.put("modify_userid", requestContext.getUserInfo().get("user_id"));
            }
            this.checkSubDetail(basePO, keyId);
            funBaseService.updateCommonValue(tableName, valueMap, keyId, pkCol);
        }
        return String.join(",", asKey);
    }

    @Override
    public List<ConditionDTO> getConditionList(CommonConditionDTO commonConditionDTO) {
        if(StringUtils.isAnyBlank(commonConditionDTO.getFieldNames(),commonConditionDTO.getFieldConds(),commonConditionDTO.getFieldValues())){
            return Collections.EMPTY_LIST;
        }
        String[] names = commonConditionDTO.getFieldNames().split(";");
        String[] conds = commonConditionDTO.getFieldConds().split(";");
        String[] values = commonConditionDTO.getFieldValues().split(";");
        List<ConditionDTO> result = new ArrayList<>();
        for (int i = 0; names != null  && i < names.length; i++) {
            if (StringUtils.isNotBlank(names[i])) {
                ConditionDTO conditionDTO = new ConditionDTO();
                conditionDTO.setNames(names[i]);
                conditionDTO.setConds(conds[i]);
                conditionDTO.setValues(values[i]);
                result.add(conditionDTO);
            }
        }
        return result;
    }

    @Override
    public void appendQueryWrapper(QueryWrapper queryWrapper, List<ConditionDTO> conditionList) {
        for (ConditionDTO conditionDTO : conditionList){
            String names = conditionDTO.getNames();
            String conds = conditionDTO.getConds();
            String values = conditionDTO.getValues();
            if ("like".equals(conds) || "llike".equals(conds)){
                queryWrapper.likeRight(names, values);
            } else {
                queryWrapper.eq(names, values);
            }
        }
    }

    /**
     * 校验子工程是否有数据
     * @param basePO
     * @param keyId
     */
    private void checkSubDetail(FunBasePO basePO, String keyId){
        String valSubid = basePO.getValSubid();
        if (StringUtils.isEmpty(valSubid)){
            return;
        }
        String[] split = valSubid.split(",");
        for (String subFunId : split){
            FunBasePO subFunBase = funBaseService.getById(subFunId);
            if (null == subFunBase){
                throw new BizException(ErrorCodeEnum.EVENT_CODE_ERROR);
            }
            String subPkCol = subFunBase.getFkCol();
            if (StringUtils.isEmpty(subPkCol)){
                subPkCol = StringUtil.getNoTableCol(basePO.getPkCol());
            }
            Integer count = funBaseService.getCount(subFunBase.getTableName(), subPkCol, keyId);
            if (count == 0){
                throw new BizException(ErrorCodeEnum.SUB_NOT_DATA, subFunBase.getFunName());
            }
        }
    }

    /**
     * 检查是否已提交
     * @param keyId
     * @param auditValue
     * @param basePO
     * @return
     */
    private Integer checkAuditNum(String keyId, String auditValue, FunBasePO basePO){
        String auditCol = basePO.getAuditCol();
        String pkCol = this.getPkCol(basePO);
        if (StringUtils.isEmpty(auditCol)){
            return 0;
        }
        return funBaseService.getHaveAuditCount(basePO.getTableName(), basePO.getAuditCol(), auditValue, pkCol, keyId);
    }

    /**
     * 表格操作时获取要保存的数据
     * @param requestMap
     * @param funColPOS
     * @return
     */
    private List<Map<String,String>> getValuesMap(Map<String, Object> requestMap, List<FunColPO> funColPOS){
        List<Map<String, String>> result = new ArrayList<>();
        for (FunColPO colPO : funColPOS){
            // 默认值
            String defaultValue = colPO.getDefaultValue();
            String colCode = colPO.getColCode();
            String[] values = getValues(requestMap, colCode);
            if (null == values){
                continue;
            }
            if (result.size() == 0){
                for (int i = 0; i < values.length; i++){
                    Map<String, String> tempMap = new HashMap<>();
                    result.add(tempMap);
                }
            }
            for (int i = 0; i < values.length; i++){
                result.get(i).put(colCode, values[i]);
            }
        }
        return result;
    }

    public String[] getValues(Map mp, String name) {
        String[] asRet = null;
        Object obj = mp.get(name);
        if (obj instanceof String) {
            asRet = new String[]{(String) obj};
        } else if (obj instanceof String[]) {
            asRet = (String[]) obj;
        } else if (obj instanceof List){
            List objList = (List)obj;
            asRet = new String[objList.size()];
            for (int i = 0; i < objList.size(); i++){
                asRet[i] = String.valueOf(objList.get(i));
            }
        } else {
            asRet = new String[0];
        }
        return asRet;
    }
    /**
     * 获取修改的值
     * @param valueMap
     * @param sKeyValue
     * @param requestContext
     * @param basePO
     */
    private void dirtySave(Map<String, String> valueMap, String sKeyValue, RequestContext requestContext, FunBasePO basePO) {
        // 校验是否有重读值
        checkRepeatVal(valueMap, requestContext, sKeyValue, basePO);
        Map<String, String> userInfo = requestContext.getUserInfo();
        String isUserinfo = basePO.getIsUserinfo();
        if ("1".equals(isUserinfo)) {
            valueMap.put("modify_date", DateUtils.getFormatDate(DateUtils.COMMON_PATTERN));
            valueMap.put("modify_userid", userInfo.get("user_id"));
        }
        funBaseService.updateCommonValue(basePO.getTableName(), valueMap, sKeyValue, basePO.getPkCol());
    }

    private String getBatchKey(SapBatchIdEnum keyEnum){
        String monthDay = "";
        if (keyEnum.equals(SapBatchIdEnum.ASN_RECEIVE_GOODS) || keyEnum.equals(SapBatchIdEnum.YI_ZHUANG)
                || keyEnum.equals(SapBatchIdEnum.CHANG_PING) || keyEnum.equals(SapBatchIdEnum.CHANG_PING2)
                ||  keyEnum.equals(SapBatchIdEnum.ROBOT)){
            monthDay = ":"+DateUtils.getFormatDate("yyMMdd");
        }
        return RedisCachePrefixKeyEnum.BATCH_ID_UNIQUE.getPrefixKey()+keyEnum.getBillType()+monthDay;
    }

    public String getUniqueId(String tableName) {
        String yearMonth = DateUtils.getFormatDate("yyMMdd");
        String key = tableName+":"+yearMonth;
        Long nextSequence = RedisUtils.incrBy(key, 1);
        RedisUtils.expire(key, 2, TimeUnit.DAYS);
        return yearMonth + StringUtils.leftPad(String.valueOf(nextSequence), 6, "0");
    }

    private Map<String, String> getInsertData(Map<String, Object> requestMap, List<FunColPO> funColPOS){
        Map<String, String> result = new HashMap<>();
        for (FunColPO colPO : funColPOS){
            String colCode = colPO.getColCode();
            Object value = requestMap.get(colCode);
            if (null != value && !"".equals(value.toString())){
                result.put(colCode, value.toString());
            }
        }
        return result;
    }

    /**
     * 取可以修改的值
     * @param requestMap
     * @param tableName
     * @return
     */
    private Map<String, String> getDirtyData(Map<String, Object> requestMap, String tableName){
        Map<String, String> result = new HashMap<>();
        Iterator<String> keys = requestMap.keySet().iterator();

        //取修改了值的字段名
        String dirtyFields = MapUtil.getValue(requestMap, "dirtyfields");
        List<String> lsField = ArrayUtil.arrayToList(dirtyFields.split(";"));

        while(keys.hasNext()) {
            String name = keys.next();

            if (name.length() > 0 && name.indexOf(tableName+".") == 0 && lsField.contains(name)) {
                String field = StringUtil.getNoTableCol(name);
                String value = MapUtils.getString(requestMap, name, "");
                result.put(field, value);
            }
        }
        return result;
    }

    /**
     * 校验是否有重复的数据
     * @param valueMap
     * @param requestContext
     * @param keyId
     * @param funBasePO
     */
    private void checkRepeatVal(Map<String, String> valueMap, RequestContext requestContext, String keyId, FunBasePO funBasePO){
        String funID = requestContext.getFunID();
        List<FunRepeatValDTO> repeatCol = funBaseService.getRepeatCol(funID);
        if (CollectionUtils.isEmpty(repeatCol)){
            return;
        }
        for (FunRepeatValDTO valDTO : repeatCol){
            String colCode = valDTO.getColCode();
            String colName = valDTO.getColName();
            String sRepVal = valueMap.get(colCode);
            // 如果是保存前判断，且字段值未被修改，则不需要判断重复
            if (StringUtils.isBlank(sRepVal)){
                continue;
            }
            // sub
            String regType = funBasePO.getRegType();
            String fkColName = funBasePO.getFkCol();
            String fkValue = requestContext.getRequestValue("fkValue");
            if (!"sub".equals(regType) || fkColName.length() == 0 || fkValue.length() == 0) {
                fkValue = "";
            }
            int repeatValue = funBaseService.getRepeatValue(funBasePO.getTableName(), colCode, colName, keyId, fkColName, fkValue);
            if (repeatValue > 0){
                throw new BizException(ErrorCodeEnum.DATA_HAVE_REPEAT, colName);
            }
        }
    }

    /**
     * 如果外键名没有添加表名，则添加
     * @param funBasePO
     * @return
     */
    private String getPkCol(FunBasePO funBasePO){
        String pkCol = funBasePO.getPkCol();
        String tableName = funBasePO.getTableName();
        if (pkCol.indexOf(".") < 0) {
            pkCol = tableName + "." + pkCol;
        }
        return pkCol;
    }
}
