package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoBaselineRecordPO;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/2/28 16:04
 */
public interface ProtoBaselineRecordService extends IService<ProtoBaselineRecordPO> {
    ProtoBaselineRecordPO findOneByParam(ProtoBaselineRecordPO po);
    List<ProtoBaselineRecordPO> findAllByParam(ProtoBaselineRecordPO po);
}
