package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.FunAllControlPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.FunAllControlMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.FunAllControlService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/20 17:21
 */
@Service
public class FunAllControlServiceImpl extends ServiceImpl<FunAllControlMapper, FunAllControlPO> implements FunAllControlService {
    @Override
    public String getComboValue(String controlCode, String valueData) {
        if(StringUtils.isBlank(valueData)) {
            return "";
        }
        QueryWrapper<FunAllControlPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FunAllControlPO::getControlType,"combo").eq(FunAllControlPO::getControlCode,controlCode).eq(FunAllControlPO::getValueData,valueData);
        FunAllControlPO po = this.getOne(queryWrapper);
        if(!ObjectUtils.isEmpty(po)){
            return po.getDisplayData();
        }else {
            return "";
        }
    }

    @Override
    public List<FunAllControlPO> getComboValue(String controlCode) {
        if(StringUtils.isBlank(controlCode)) {
            return null;
        }
        QueryWrapper<FunAllControlPO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FunAllControlPO::getControlType,"combo").eq(FunAllControlPO::getControlCode,controlCode);
        return this.list(queryWrapper);
    }
}
