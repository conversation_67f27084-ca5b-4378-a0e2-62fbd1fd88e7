package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPayAccountPO;

import java.util.Date;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/8/23
 */
public interface ProtoPayAccountService extends IService<ProtoPayAccountPO> {

    /**
     * 通过对账日期查询对账数量
     *
     * @param mrpType
     * @param accountDate
     * @return
     */
    Integer countByAccountDate(String mrpType, Date accountDate);
}
