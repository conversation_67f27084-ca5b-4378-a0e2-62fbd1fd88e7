package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.InStatusEnum;
import com.mi.oa.asset.mobile.common.enums.SapRetEnum;
import com.mi.oa.asset.mobile.infra.dto.sap.ProtoOutSapDTO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAsnOrderDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoInPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoOutDetPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoOutDetMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoAsnOrderDetService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoInService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoOutDetService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Desc:
 * @Author: huy
 * @Date: 2022/1/17
 */
@Service
public class ProtoOutDetServiceImpl extends ServiceImpl<ProtoOutDetMapper, ProtoOutDetPO> implements ProtoOutDetService {

    @Autowired
    private ProtoAsnOrderDetService protoAsnOrderDetService;

    @Autowired
    private ProtoInService protoInService;

    @Override
    public Integer getProtoOutSapSuccess(String sn) {
        return baseMapper.getProtoOutSapSuccess(sn);
    }

    @Override
    public String getApplyCode(String sn) {
        return baseMapper.getApplyCode(sn);
    }

    @Override
    public Integer countNotPushSap(String outId) {
        LambdaQueryWrapper<ProtoOutDetPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProtoOutDetPO::getOutId, outId);
        queryWrapper.in(ProtoOutDetPO::getInStatus, InStatusEnum.NOT_DELIVERY.getState(), InStatusEnum.HAVE_DELIVERY.getState());
        return this.count(queryWrapper);
    }

    @Override
    public List<ProtoOutSapDTO> getProtoOutDetSAPList(String outId) {
        return baseMapper.getProtoOutDetSAPList(outId);
    }

    @Override
    public List<ProtoOutDetPO> listByOutId(String outId) {
        LambdaQueryWrapper<ProtoOutDetPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProtoOutDetPO::getOutId, outId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void updateDetOutSapRow(String outId, String skuCode, String orderCode, String outSapItemRowNo) {
        LambdaUpdateWrapper<ProtoOutDetPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ProtoOutDetPO::getOutSapItemRowNo, outSapItemRowNo);
        updateWrapper.eq(ProtoOutDetPO::getOutId, outId);
        updateWrapper.eq(ProtoOutDetPO::getSkuCode, skuCode);
        updateWrapper.eq(ProtoOutDetPO::getOrderCode, orderCode);
        this.update(updateWrapper);
    }

    @Override
    public void updateDetInStatus() {
        this.updateProtoOutDetAsnNo();
        int pageNum = 1;
        while (true) {
            List<ProtoOutDetPO> outDetPOList = listNotPushSap(pageNum, CommonConstant.DEFAULT_PAGE_SIZE);
            List<String> inCodeList = outDetPOList.stream().map(ProtoOutDetPO::getInCode).distinct().collect(Collectors.toList());
            Map<String, ProtoInPO> inCodeMap = protoInService.listByInCodeList(inCodeList)
                    .stream().collect(Collectors.toMap(ProtoInPO::getInCode, a -> a, (k1, k2) -> k1));
            for (ProtoOutDetPO detPO : outDetPOList){
                ProtoInPO proto = inCodeMap.get(detPO.getInCode());
                if (null != proto && SapRetEnum.SUCCESS.getKey().equals(proto.getSapRetType())){
                    detPO.setInStatus(InStatusEnum.DUMPED_SAP.getState());
                }
            }
            List<ProtoOutDetPO> updateList = outDetPOList.stream().filter(o -> InStatusEnum.DUMPED_SAP.getState().equals(o.getInStatus())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(updateList)){
                this.updateBatchById(updateList);
            }
            pageNum++;
            if (outDetPOList.size() < CommonConstant.DEFAULT_PAGE_SIZE){
                return;
            }
        }

    }

    /**
     * 更新proto_out_det表ASN订单号和物料描述
     * @param
     */
    private void updateProtoOutDetAsnNo(){
        int pageNum = 1;
        while (true) {
            List<ProtoOutDetPO> list = listNotOrderCode(pageNum, CommonConstant.DEFAULT_PAGE_SIZE);
            List<String> snList = list.stream().map(ProtoOutDetPO::getDeviceCode).distinct().collect(Collectors.toList());
            Map<String, ProtoAsnOrderDetPO> snMap = protoAsnOrderDetService.listBySnList(snList)
                    .stream().collect(Collectors.toMap(ProtoAsnOrderDetPO::getDeviceCode, a -> a, (k1, k2) -> k1));

            for (ProtoOutDetPO proto : list){
                String sn = proto.getDeviceCode();
                ProtoAsnOrderDetPO detPO = snMap.get(sn);
                if (null == detPO){
                    continue;
                }
                proto.setBsart(detPO.getBsart());
                proto.setOrderCode(detPO.getAsnNo());
                proto.setSkuName(detPO.getSkuName());
            }
            List<ProtoOutDetPO> updateList = list.stream().filter(o -> StringUtils.isNotEmpty(o.getOrderCode())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(updateList)){
                this.updateBatchById(updateList);
            }
            pageNum ++;
            if (list.size() < CommonConstant.DEFAULT_PAGE_SIZE){
                return;
            }
        }
    }

    /**
     * 查询所有ASN订单为空的数据
     * @param pageNum
     * @param pageSize
     * @return
     */
    private List<ProtoOutDetPO> listNotOrderCode(Integer pageNum, Integer pageSize){
        Page page = new Page(pageNum, pageSize);
        LambdaQueryWrapper<ProtoOutDetPO>  wrapper = new LambdaQueryWrapper<>();
        wrapper.apply("order_code is null or order_code = ''");
        return baseMapper.selectPage(page, wrapper).getRecords();
    }

    /**
     * 查询所有未抛SAP数据
     * @return
     */
    public List<ProtoOutDetPO> listNotPushSap(Integer pageNum, Integer pageSize){
        Page page = new Page(pageNum, pageSize);
        LambdaQueryWrapper<ProtoOutDetPO> wrapper = Wrappers.lambdaQuery(ProtoOutDetPO.class);
        wrapper.isNotNull(ProtoOutDetPO::getOrderCode)
                .ne(ProtoOutDetPO::getOrderCode, StringUtils.EMPTY)
                .ne(ProtoOutDetPO::getInStatus, InStatusEnum.DUMPED_SAP.getState());
        return baseMapper.selectPage(page, wrapper).getRecords();
    }

    /**
     * 查询未出库的设备编号SN
     *
     * @param deviceCodes
     * @return
     */
    @Override
    public List<String> selectProtoOutDetDeviceCodes(List<String> deviceCodes){
        if (CollectionUtils.isEmpty(deviceCodes)){
            return new ArrayList<>();
        }
        return baseMapper.selectProtoOutDetDeviceCodes(deviceCodes);
    }
}
