package com.mi.oa.asset.mobile.infra.config.message;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/24 10:40
 */

@Data
@Component
@ConfigurationProperties("message.whitelist")
public class ProtoBackWhitelistConfig {
    protected List<String> protoBackWhitelist;
}
