package com.mi.oa.asset.mobile.infra.repository.mybatis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.oa.asset.mobile.common.enums.CommonConstant;
import com.mi.oa.asset.mobile.common.enums.SapRetEnum;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyPO;
import com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoApplyMapper;
import com.mi.oa.asset.mobile.infra.repository.mybatis.service.ProtoApplyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/5 16:00
 */
@Service
public class ProtoApplyServiceImpl extends ServiceImpl<ProtoApplyMapper, ProtoApplyPO> implements ProtoApplyService {
    @Override
    public ProtoApplyPO findByApplyId(String applyId) {
        QueryWrapper<ProtoApplyPO> queryWrapper=new QueryWrapper();
        queryWrapper.lambda().eq(ProtoApplyPO::getApplyId,applyId);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    public ProtoApplyPO findByBusinessKey(String businessKey) {
        QueryWrapper<ProtoApplyPO> queryWrapper=new QueryWrapper();
        queryWrapper.lambda().eq(ProtoApplyPO::getBusinessKey,businessKey);
        return this.getBaseMapper().selectOne(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCostCenter(String applyId, String centerCode, String compDeptCode) {
        LambdaUpdateWrapper<ProtoApplyPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoApplyPO::getApplyId,applyId)
                .set(ProtoApplyPO::getCenterCode,centerCode)
                .set(ProtoApplyPO::getCompDeptCode,compDeptCode);
        return this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateApplyUserTypeModifyUseridModifyDate(String applyId, String applyUserType, String userId,Date date) {
        LambdaUpdateWrapper<ProtoApplyPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoApplyPO::getApplyId,applyId)
                .set(ProtoApplyPO::getApplyUserType,applyUserType)
                .set(ProtoApplyPO::getModifyUserid,userId)
                .set(ProtoApplyPO::getModifyDate,date);
        return this.update(updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAuditingModifyDateBusinessKeyByApplyId(String applyId, String auditing, Date modifyDate, String businessKey) {
        LambdaUpdateWrapper<ProtoApplyPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ProtoApplyPO::getApplyId,applyId)
                .set(ProtoApplyPO::getAuditing,auditing)
                .set(ProtoApplyPO::getModifyDate,modifyDate)
                .set(ProtoApplyPO::getBusinessKey,businessKey);
        return this.update(updateWrapper);
    }

    @Override
    public ProtoApplyPO getByApplyCode(String applyCode) {
        if (StringUtils.isEmpty(applyCode)){
            return null;
        }
        LambdaQueryWrapper<ProtoApplyPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProtoApplyPO::getApplyCode, applyCode);
        queryWrapper.last(" limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public void updateApplyStatusByApplyId(String applyStatus, String applyId) {
        LambdaUpdateWrapper<ProtoApplyPO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(ProtoApplyPO::getApplyStatus, applyStatus);
        wrapper.eq(ProtoApplyPO::getApplyId, applyId);
        if (applyStatus.equals(CommonConstant.ProtoCardApplyStatus.SHIPMENTCOMPLETED.getType())) {
            wrapper.set(ProtoApplyPO::getCheckDate, new Date());
        }
        this.update(wrapper);
    }

    @Override
    public List<ProtoApplyPO> listToPushSap() {
        LambdaQueryWrapper<ProtoApplyPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoApplyPO::getAuditing, CommonConstant.RecordStatus.APPROVED.getStatus())
                .in(ProtoApplyPO::getSrcType, CommonConstant.ProtoCardSrcType.FIRSTHAND.getType(), CommonConstant.ProtoCardSrcType.FIRSTHANDDEMAND.getType())
                .apply("(sap_ret_type is null or sap_ret_type in ('E', 'N', ''))");
        return this.list(wrapper);
    }

    @Override
    public List<ProtoApplyPO> listMackCnIsBlank() {
        LambdaQueryWrapper<ProtoApplyPO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProtoApplyPO::getAuditing, CommonConstant.RecordStatus.APPROVED.getStatus())
                .in(ProtoApplyPO::getSrcType, CommonConstant.ProtoCardSrcType.FIRSTHAND.getType(), CommonConstant.ProtoCardSrcType.FIRSTHANDDEMAND.getType())
                .apply("make_cn is null or make_cn = ''");
        return this.list(wrapper);
    }
}
