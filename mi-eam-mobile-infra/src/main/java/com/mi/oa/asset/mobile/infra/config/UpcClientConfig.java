package com.mi.oa.asset.mobile.infra.config;


import com.mi.oa.asset.mobile.common.enums.ErrorCodeEnum;
import com.mi.oa.asset.mobile.utils.GsonUtil;
import com.mi.oa.asset.x5.common.X5Request;
import com.mi.oa.asset.x5.common.X5RequestHeader;
import com.mi.oa.asset.x5.common.X5Response;
import com.mi.oa.asset.x5.consumer.X5FeignClient;
import com.mi.oa.asset.x5.consumer.X5FeignClientsConfiguration;
import com.mi.oa.infra.oaucf.core.exception.BizException;
import feign.RequestInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;
import org.springframework.http.MediaType;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

/**
 * @Desc UPC 接口请求 feign 客户端配置
 */

@Slf4j
public class UpcClientConfig extends X5FeignClientsConfiguration {

    private final String SIGN_TYPE = "md5";

    @Autowired
    private Environment environment;

    @Bean
    @Override
    protected RequestInterceptor x5RequestInterceptor() {
        return requestTemplate -> {
            X5FeignClient ann = requestTemplate.feignTarget().type().getAnnotation(X5FeignClient.class);
            if(null == ann) {
                throw new BizException(ErrorCodeEnum.X5_INIT_ERROR);
            }

            String appId = environment.resolvePlaceholders(ann.appId());
            String appKey = environment.resolvePlaceholders(ann.appKey());
            if(StringUtils.isBlank(appId) || StringUtils.isBlank(appKey)) {
                throw new BizException(ErrorCodeEnum.X5_EMPTY_ID_OR_KEY);
            }
            X5FeignMethod annotation = requestTemplate.methodMetadata().method().getAnnotation(X5FeignMethod.class);
            String method  = (annotation == null) ? "" : annotation.value();
            String bodyString = StringUtils.toEncodedString(requestTemplate.body(), StandardCharsets.UTF_8);
            X5RequestHeader x5Header = new X5RequestHeader(appId, bodyString, appKey, method, SIGN_TYPE);
            X5Request x5Request = new X5Request(x5Header, bodyString);

            requestTemplate.header("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE);
            requestTemplate.body("data=" + x5Request.toString());
        };
    }

    protected Object responseHandler(String resDataString, X5Response res, Type type) {
        if(!res.getHeader().getCode().equals(X5_RESPONSE_SUCCESS_CODE)) {
            throw new BizException(ErrorCodeEnum.X5_RESPONSE_ERROR, res.getHeader().getDesc());
        }
        return GsonUtil.toBean(GsonUtil.toJsonString(res.getBody()), type);
    }
}


