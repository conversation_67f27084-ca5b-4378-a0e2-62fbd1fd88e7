package com.mi.oa.asset.mobile.infra.repository.mybatis.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysDeptPO;

import java.util.List;

/**
 * TODO
 *
 * <AUTHOR>
 * @date 2022/1/5 15:23
 */
public interface SysDeptService extends IService<SysDeptPO> {

    SysDeptPO getCacheByDeptId(String deptId);

    /**
     * 通过部门编码查询部门信息
     * @param deptCodeList
     * @return
     */
    List<SysDeptPO> listByDeptCodeList(List<String> deptCodeList);
}
