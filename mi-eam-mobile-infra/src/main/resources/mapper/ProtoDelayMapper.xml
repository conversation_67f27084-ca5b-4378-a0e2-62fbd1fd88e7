<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoDelayMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDelayPO">
    <id column="delay_id" jdbcType="VARCHAR" property="delayId" />
    <result column="auditing" jdbcType="CHAR" property="auditing" />
    <result column="delay_code" jdbcType="VARCHAR" property="delayCode" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="apply_user_code" jdbcType="VARCHAR" property="applyUserCode" />
    <result column="apply_emp_code" jdbcType="VARCHAR" property="applyEmpCode" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
    <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate" />
    <result column="delay_date" jdbcType="TIMESTAMP" property="delayDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="apply_user_type" jdbcType="VARCHAR" property="applyUserType" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="check_user" jdbcType="VARCHAR" property="checkUser" />
    <result column="check_user_code" jdbcType="VARCHAR" property="checkUserCode" />
    <result column="check_emp_code" jdbcType="VARCHAR" property="checkEmpCode" />
    <result column="business_key" jdbcType="VARCHAR" property="businessKey" />
    <result column="pm_user_name" jdbcType="VARCHAR" property="pmUserName" />
    <result column="pm_user_code" jdbcType="VARCHAR" property="pmUserCode" />
    <result column="pm_emp_code" jdbcType="VARCHAR" property="pmEmpCode" />
  </resultMap>
  <sql id="Base_Column_List">
    delay_id, auditing, delay_code, apply_user_name, apply_user_code, apply_emp_code, 
    dept_name, dept_code, apply_date, delay_date, remark, add_userid, add_date, modify_userid, 
    modify_date, tenant_id, apply_user_type, project_code, check_user, check_user_code, 
    check_emp_code, business_key, pm_user_name, pm_user_code, pm_emp_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from proto_delay
    where delay_id = #{delayId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from proto_delay
    where delay_id = #{delayId,jdbcType=VARCHAR}
  </delete>
</mapper>