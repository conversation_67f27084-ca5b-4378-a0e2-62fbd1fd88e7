<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoPriceMapper">
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from proto_price
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPricePO">
    insert into proto_price (id, bus_type, price_code, 
      is_jis, remark, status, 
      modify_name, add_userid, add_date, 
      modify_userid, modify_date, tenant_id
      )
    values (#{id,jdbcType=INTEGER}, #{busType,jdbcType=VARCHAR}, #{priceCode,jdbcType=VARCHAR}, 
      #{isJis,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{modifyName,jdbcType=VARCHAR}, #{addUserid,jdbcType=VARCHAR}, #{addDate,jdbcType=TIMESTAMP}, 
      #{modifyUserid,jdbcType=VARCHAR}, #{modifyDate,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPricePO">
    insert into proto_price
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="busType != null">
        bus_type,
      </if>
      <if test="priceCode != null">
        price_code,
      </if>
      <if test="isJis != null">
        is_jis,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="modifyName != null">
        modify_name,
      </if>
      <if test="addUserid != null">
        add_userid,
      </if>
      <if test="addDate != null">
        add_date,
      </if>
      <if test="modifyUserid != null">
        modify_userid,
      </if>
      <if test="modifyDate != null">
        modify_date,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="busType != null">
        #{busType,jdbcType=VARCHAR},
      </if>
      <if test="priceCode != null">
        #{priceCode,jdbcType=VARCHAR},
      </if>
      <if test="isJis != null">
        #{isJis,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="modifyName != null">
        #{modifyName,jdbcType=VARCHAR},
      </if>
      <if test="addUserid != null">
        #{addUserid,jdbcType=VARCHAR},
      </if>
      <if test="addDate != null">
        #{addDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyUserid != null">
        #{modifyUserid,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>