<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoThirdUserMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoThirdUserPO">
    <id column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="auditing" jdbcType="CHAR" property="auditing" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="emp_code" jdbcType="VARCHAR" property="empCode" />
    <result column="user_code" jdbcType="VARCHAR" property="userCode" />
    <result column="center_code" jdbcType="VARCHAR" property="centerCode" />
    <result column="is_old_out_right" jdbcType="CHAR" property="isOldOutRight" />
    <result column="is_quit" jdbcType="CHAR" property="isQuit" />
    <result column="handover_code" jdbcType="VARCHAR" property="handoverCode" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="center_name" jdbcType="VARCHAR" property="centerName" />
    <result column="provider_code" jdbcType="VARCHAR" property="providerCode" />
    <result column="provider_name" jdbcType="VARCHAR" property="providerName" />
    <result column="email" jdbcType="VARCHAR" property="email" />
  </resultMap>
  <sql id="Base_Column_List">
    user_id, auditing, user_name, emp_code, user_code, center_code, is_old_out_right, 
    is_quit, handover_code, add_userid, add_date, modify_userid, modify_date, tenant_id, 
    center_name, provider_code, provider_name, email
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from proto_third_user
    where user_id = #{userId,jdbcType=VARCHAR}
  </select>
</mapper>