<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoCardDeptTotalMapper">
    <select id="statisticsByDeptLevel"
            resultType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardDeptTotalPO">

        SELECT t.mrp_type, d.parent_id deptCode, sum(t.dgh_num) dghNum,
               sum(t.zyz_num) zyzNum, sum(t.ghz_num) ghzNum, sum(t.wfghz_num) wfghzNum
        from proto_card_dept_total t
                 inner join sys_dept d on t.dept_code = d.dept_id
        where d.dept_level = #{deptLevel}
        and t.mrp_type = #{mrpType}
        GROUP BY d.parent_id, t.mrp_type
    </select>
</mapper>