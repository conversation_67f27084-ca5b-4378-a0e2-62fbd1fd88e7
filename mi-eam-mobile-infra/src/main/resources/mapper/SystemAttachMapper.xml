<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SystemAttachMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SystemAttachPO">
    <id column="attach_id" jdbcType="VARCHAR" property="attachId" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="data_id" jdbcType="VARCHAR" property="dataId" />
    <result column="attach_name" jdbcType="VARCHAR" property="attachName" />
    <result column="content_type" jdbcType="VARCHAR" property="contentType" />
    <result column="attach_field" jdbcType="VARCHAR" property="attachField" />
    <result column="fun_id" jdbcType="VARCHAR" property="funId" />
    <result column="fun_name" jdbcType="VARCHAR" property="funName" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="upload_user" jdbcType="VARCHAR" property="uploadUser" />
    <result column="upload_date" jdbcType="TIMESTAMP" property="uploadDate" />
    <result column="attach_path" jdbcType="VARCHAR" property="attachPath" />
    <result column="attach_type" jdbcType="VARCHAR" property="attachType" />
    <result column="store_id" jdbcType="VARCHAR" property="storeId" />
    <result column="store_no" jdbcType="DECIMAL" property="storeNo" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="file_size" jdbcType="DECIMAL" property="fileSize" />
  </resultMap>
  <sql id="Base_Column_List">
    attach_id, `table_name`, data_id, attach_name, content_type, attach_field, fun_id, 
    fun_name, add_userid, add_date, modify_userid, modify_date, upload_user, upload_date, 
    attach_path, attach_type, store_id, store_no, tenant_id, file_size
  </sql>
</mapper>