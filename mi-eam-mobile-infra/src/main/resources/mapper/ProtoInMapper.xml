<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoInMapper">

    <select id="getEntryDate" resultType="java.util.Map">
        SELECT max(proto_in.in_date) inDate, proto_in_det.device_code sn
        from proto_in inner join proto_in_det on proto_in.in_id = proto_in_det.in_id
        where
            ((proto_in.in_type in ('0', '1') and in_status in ('1', '3')) or (proto_in.in_type = '2' and in_status = '3'))
            and proto_in_det.device_code in
            <foreach collection="snList" open="(" separator="," item="sn" close=")">
                #{sn}
            </foreach>
        group by proto_in_det.device_code
    </select>
</mapper>