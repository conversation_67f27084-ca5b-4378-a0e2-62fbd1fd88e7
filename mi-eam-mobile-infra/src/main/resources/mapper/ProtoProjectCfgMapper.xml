<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoProjectCfgMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectCfgPO">
    <id column="cfg_id" jdbcType="VARCHAR" property="cfgId" />
    <result column="cfg_code" jdbcType="VARCHAR" property="cfgCode" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="pm_emp_code" jdbcType="VARCHAR" property="pmEmpCode" />
    <result column="pm_user_code" jdbcType="VARCHAR" property="pmUserCode" />
    <result column="pm_user_name" jdbcType="VARCHAR" property="pmUserName" />
    <result column="factory_name" jdbcType="VARCHAR" property="factoryName" />
    <result column="factory_code" jdbcType="VARCHAR" property="factoryCode" />
    <result column="emp_code" jdbcType="VARCHAR" property="empCode" />
    <result column="user_code" jdbcType="VARCHAR" property="userCode" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="fac_trial_date" jdbcType="TIMESTAMP" property="facTrialDate" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="final_user_name" jdbcType="VARCHAR" property="finalUserName" />
    <result column="auditing" jdbcType="CHAR" property="auditing" />
  </resultMap>
  <sql id="Base_Column_List">
    cfg_id, cfg_code, project_code, project_name, pm_emp_code, pm_user_code, pm_user_name, 
    factory_name, factory_code, emp_code, user_code, user_name, email, fac_trial_date, 
    add_userid, add_date, modify_userid, modify_date, tenant_id, final_user_name, auditing
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from proto_project_cfg
    where cfg_id = #{cfgId,jdbcType=VARCHAR}
  </select>
</mapper>