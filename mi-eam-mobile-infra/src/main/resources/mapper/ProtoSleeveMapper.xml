<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoSleeveMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleevePO">
    <id column="sleeve_id" jdbcType="VARCHAR" property="sleeveId" />
    <result column="apply_code" jdbcType="VARCHAR" property="applyCode" />
    <result column="auditing" jdbcType="CHAR" property="auditing" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="stage_name" jdbcType="VARCHAR" property="stageName" />
    <result column="pm_user_name" jdbcType="VARCHAR" property="pmUserName" />
    <result column="pm_emp_code" jdbcType="VARCHAR" property="pmEmpCode" />
    <result column="pm_user_code" jdbcType="VARCHAR" property="pmUserCode" />
    <result column="apply_status" jdbcType="VARCHAR" property="applyStatus" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="apply_emp_code" jdbcType="VARCHAR" property="applyEmpCode" />
    <result column="apply_user_code" jdbcType="VARCHAR" property="applyUserCode" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
    <result column="apply_date" jdbcType="TIMESTAMP" property="applyDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="business_key" jdbcType="VARCHAR" property="businessKey" />
    <result column="check_user" jdbcType="VARCHAR" property="checkUser" />
    <result column="check_user_code" jdbcType="VARCHAR" property="checkUserCode" />
    <result column="check_emp_code" jdbcType="VARCHAR" property="checkEmpCode" />
    <result column="apply_user_type" jdbcType="VARCHAR" property="applyUserType" />
  </resultMap>
  <sql id="Base_Column_List">
    sleeve_id, apply_code, auditing, project_name, project_code, stage_name, pm_user_name, 
    pm_emp_code, pm_user_code, apply_status, apply_user_name, apply_emp_code, apply_user_code, 
    dept_name, dept_code, apply_date, remark, add_userid, add_date, modify_userid, modify_date, 
    tenant_id, business_key, check_user, check_user_code, check_emp_code, apply_user_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from proto_sleeve
    where sleeve_id = #{sleeveId,jdbcType=VARCHAR}
  </select>

  <select id="selectSleeveInfoById" parameterType="java.lang.String" resultMap="SleeveResultMap">
    select mat.device_code,main.apply_user_code,main.project_code from proto_sleeve_mat mat
            join proto_sleeve main on mat.sleeve_id = main.sleeve_id
            where main.sleeve_id = #{sleeveId,jdbcType=VARCHAR}
  </select>
  
  <resultMap id="SleeveResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleeveInfoPO">
    <result column="apply_user_code" jdbcType="VARCHAR" property="applyUserCode" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
  </resultMap>
</mapper>