<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.FunAllControlMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.FunAllControlPO">
    <id column="control_id" jdbcType="VARCHAR" property="controlId" />
    <result column="control_type" jdbcType="VARCHAR" property="controlType" />
    <result column="control_code" jdbcType="VARCHAR" property="controlCode" />
    <result column="control_name" jdbcType="VARCHAR" property="controlName" />
    <result column="value_data" jdbcType="VARCHAR" property="valueData" />
    <result column="display_data" jdbcType="VARCHAR" property="displayData" />
    <result column="control_index" jdbcType="DECIMAL" property="controlIndex" />
    <result column="layout_page" jdbcType="VARCHAR" property="layoutPage" />
    <result column="fun_id" jdbcType="VARCHAR" property="funId" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="control_prop" jdbcType="VARCHAR" property="controlProp" />
    <result column="title_en" jdbcType="VARCHAR" property="titleEn" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="control_state" jdbcType="CHAR" property="controlState" />
    <result column="title_t1" jdbcType="VARCHAR" property="titleT1" />
    <result column="title_t2" jdbcType="VARCHAR" property="titleT2" />
    <result column="title_t3" jdbcType="VARCHAR" property="titleT3" />
  </resultMap>
  <sql id="Base_Column_List">
    control_id, control_type, control_code, control_name, value_data, display_data, control_index, 
    layout_page, fun_id, add_userid, add_date, modify_userid, modify_date, control_prop, 
    title_en, tenant_id, control_state, title_t1, title_t2, title_t3
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from funall_control
    where control_id = #{controlId,jdbcType=VARCHAR}
  </select>
</mapper>