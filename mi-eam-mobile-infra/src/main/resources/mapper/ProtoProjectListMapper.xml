<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoProjectListMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoProjectListPO">
    <id column="project_list_id" jdbcType="VARCHAR" property="projectListId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="parent_code" jdbcType="VARCHAR" property="parentCode" />
    <result column="is_old" jdbcType="DECIMAL" property="isOld" />
    <result column="project_type" jdbcType="VARCHAR" property="projectType" />
    <result column="pm_user_name" jdbcType="VARCHAR" property="pmUserName" />
    <result column="pm_emp_code" jdbcType="VARCHAR" property="pmEmpCode" />
    <result column="pm_user_code" jdbcType="VARCHAR" property="pmUserCode" />
    <result column="auditing" jdbcType="CHAR" property="auditing" />
  </resultMap>
  <sql id="Base_Column_List">
    project_list_id, project_name, project_code, add_userid, add_date, modify_userid, 
    modify_date, tenant_id, parent_code, is_old, project_type, pm_user_name, pm_emp_code, 
    pm_user_code, auditing
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from proto_project_list
    where project_list_id = #{projectListId,jdbcType=VARCHAR}
  </select>
</mapper>