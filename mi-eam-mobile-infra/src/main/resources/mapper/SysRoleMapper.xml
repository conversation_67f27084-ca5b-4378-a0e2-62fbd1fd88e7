<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysRoleMapper">
    <select id="getMrpType" resultType="java.lang.String">
        select distinct mrp_type from sys_role where role_id in
        <foreach collection="roleIds" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>
</mapper>