<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoAuthDataCfgMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoAuthDataCfgPO">
    <id column="cfg_id" jdbcType="VARCHAR" property="cfgId" />
    <result column="auditing" jdbcType="CHAR" property="auditing" />
    <result column="cp_user_name" jdbcType="VARCHAR" property="cpUserName" />
    <result column="cp_emp_code" jdbcType="VARCHAR" property="cpEmpCode" />
    <result column="cp_user_code" jdbcType="VARCHAR" property="cpUserCode" />
    <result column="cp_dept_code" jdbcType="VARCHAR" property="cpDeptCode" />
    <result column="cp_long_dept_name" jdbcType="VARCHAR" property="cpLongDeptName" />
    <result column="ag_dept_code" jdbcType="VARCHAR" property="agDeptCode" />
    <result column="ag_long_dept_name" jdbcType="VARCHAR" property="agLongDeptName" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    cfg_id, auditing, cp_user_name, cp_emp_code, cp_user_code, cp_dept_code, cp_long_dept_name, 
    ag_dept_code, ag_long_dept_name, add_userid, add_date, modify_userid, modify_date, 
    tenant_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from proto_auth_data_cfg
    where cfg_id = #{cfgId,jdbcType=VARCHAR}
  </select>
</mapper>