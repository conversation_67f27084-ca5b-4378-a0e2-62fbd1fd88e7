<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoUnReturnMatMapper">
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from proto_unreturn_mat
    where unreturn_mat_id = #{unreturnMatId,jdbcType=VARCHAR}
  </delete>
</mapper>