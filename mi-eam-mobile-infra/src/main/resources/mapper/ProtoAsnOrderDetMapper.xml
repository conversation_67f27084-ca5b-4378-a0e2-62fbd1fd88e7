<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoAsnOrderDetMapper">

    <select id="getMachAsn" resultType="com.mi.oa.asset.mobile.infra.dto.AsnOrderDTO">
        select o.order_code orderCode, o.sap_factory sapFactory, o.stage_name stageName,
               det.det_id detId, det.device_type deviceType, det.laser_code laserCode,
               det.imei imei, det.sku_code skuCode, det.device_code deviceCode, det.project_code projectCode,
               det.sku_name skuName, det.item_row_no itemRowNo, det.goods_id goodsId
        from proto_asn_order_det det, proto_asn_order o
		where det.device_code = #{sn}
		     and det.asn_no = o.order_code
		     and o.order_status != '5'
    </select>
</mapper>