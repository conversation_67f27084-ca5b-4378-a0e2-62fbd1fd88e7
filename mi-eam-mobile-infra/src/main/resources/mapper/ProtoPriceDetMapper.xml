<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoPriceDetMapper">
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from proto_price_det
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPriceDetPO">
    insert into proto_price_det (id, market_month, cur_stage, 
      price_stage, remark, add_userid, 
      add_date, modify_userid, modify_date, 
      tenant_id, price_id)
    values (#{id,jdbcType=INTEGER}, #{marketMonth,jdbcType=DECIMAL}, #{curStage,jdbcType=VARCHAR}, 
      #{priceStage,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, #{addUserid,jdbcType=VARCHAR}, 
      #{addDate,jdbcType=TIMESTAMP}, #{modifyUserid,jdbcType=VARCHAR}, #{modifyDate,jdbcType=TIMESTAMP}, 
      #{tenantId,jdbcType=VARCHAR}, #{priceId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPriceDetPO">
    insert into proto_price_det
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="marketMonth != null">
        market_month,
      </if>
      <if test="curStage != null">
        cur_stage,
      </if>
      <if test="priceStage != null">
        price_stage,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="addUserid != null">
        add_userid,
      </if>
      <if test="addDate != null">
        add_date,
      </if>
      <if test="modifyUserid != null">
        modify_userid,
      </if>
      <if test="modifyDate != null">
        modify_date,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="priceId != null">
        price_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="marketMonth != null">
        #{marketMonth,jdbcType=DECIMAL},
      </if>
      <if test="curStage != null">
        #{curStage,jdbcType=VARCHAR},
      </if>
      <if test="priceStage != null">
        #{priceStage,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="addUserid != null">
        #{addUserid,jdbcType=VARCHAR},
      </if>
      <if test="addDate != null">
        #{addDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyUserid != null">
        #{modifyUserid,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="priceId != null">
        #{priceId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
</mapper>