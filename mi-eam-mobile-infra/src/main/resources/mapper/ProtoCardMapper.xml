<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoCardMapper">

    <select id="listGroupByDeptCode"
            resultType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardDeptTotalPO">
        select mrp_type                                          mrpType,
               dept_code                                         deptCode,
               sum(case when use_state = '2' then 1 else 0 end)  ghzNum,
               sum(case when use_state = '4' then 1 else 0 end)  dghNum,
               sum(case when use_state = '6' then 1 else 0 end)  zyzNum,
               sum(case when use_state = '94' then 1 else 0 end) wfghzNum,
               count(1)                                          totalNum
        from proto_card
        where use_state in ('2', '4', '6', '94')
          and dept_code is not null
          and dept_code != ''
        group by mrp_type, dept_code
    </select>

    <select id="listGroupByDeptproCode"
            resultType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardDeptproTotalPO">
        select mrp_type                                          mrpType,
               dept_code                                         deptCode,
               project_code                                      projectCode,
               sku_code_type                                     skuCodeType,
               sum(case when use_state = '2' then 1 else 0 end)  ghzNum,
               sum(case when use_state = '4' then 1 else 0 end)  dghNum,
               sum(case when use_state = '6' then 1 else 0 end)  zyzNum,
               sum(case when use_state = '94' then 1 else 0 end) wfghzNum,
               count(1)                                          totalNum
        from proto_card
        where use_state in ('2', '4', '6', '94')
          and dept_code is not null
          and dept_code != ''
        group by mrp_type, dept_code, project_code, sku_code_type
    </select>

    <select id="listGroupByStore"
            resultType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardStorePO">
        select mrp_type mrpType,
               project_code projectCode,
               stage_name stageName,
               sku_code_type skuCodeType,
               sku_code skuCode,
               sku_name skuName,
               asset_newold assetNewold,
               is_complete isComplete,
               house_code houseCode,
               count(1) num,
               sum(case when occupy_apply_code = '1' then 1 else 0 end) occupyNum
        from proto_card
        where use_state = '1'
        group by mrp_type, project_code, stage_name, sku_code_type, sku_code, asset_newold, is_complete, house_code
    </select>
</mapper>