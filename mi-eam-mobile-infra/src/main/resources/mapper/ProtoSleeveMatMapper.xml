<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoSleeveMatMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSleeveMatPO">
    <id column="sleeve_mat_id" jdbcType="VARCHAR" property="sleeveMatId" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="laser_code" jdbcType="VARCHAR" property="laserCode" />
    <result column="imei" jdbcType="VARCHAR" property="imei" />
    <result column="device_type" jdbcType="VARCHAR" property="deviceType" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="apply_code" jdbcType="VARCHAR" property="applyCode" />
    <result column="last_apply_user_name" jdbcType="VARCHAR" property="lastApplyUserName" />
    <result column="last_apply_emp_code" jdbcType="VARCHAR" property="lastApplyEmpCode" />
    <result column="last_apply_user_code" jdbcType="VARCHAR" property="lastApplyUserCode" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="sleeve_id" jdbcType="VARCHAR" property="sleeveId" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="apply_user_code" jdbcType="VARCHAR" property="applyUserCode" />
    <result column="apply_emp_code" jdbcType="VARCHAR" property="applyEmpCode" />
    <result column="asset_newold" jdbcType="CHAR" property="assetNewold" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="stage_name" jdbcType="VARCHAR" property="stageName" />
    <result column="check_status" jdbcType="CHAR" property="checkStatus" />
    <result column="check_result" jdbcType="VARCHAR" property="checkResult" />
  </resultMap>
  <sql id="Base_Column_List">
    sleeve_mat_id, device_code, laser_code, imei, device_type, sku_code, sku_name, apply_code, 
    last_apply_user_name, last_apply_emp_code, last_apply_user_code, remark, sleeve_id, 
    add_userid, add_date, modify_userid, modify_date, tenant_id, apply_user_name, apply_user_code, 
    apply_emp_code, asset_newold, project_code, stage_name, check_status, check_result
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from proto_sleeve_mat
    where sleeve_mat_id = #{sleeveMatId,jdbcType=VARCHAR}
  </select>

  <select id="selectDeviceCodes" parameterType="java.lang.String" resultMap="BaseResultMap">
    select mat.device_code from proto_sleeve main
                join proto_sleeve_mat mat
                on main.sleeve_id = mat.sleeve_id
                where main.sleeve_id = #{sleeveId,jdbcType=VARCHAR}
  </select>
</mapper>