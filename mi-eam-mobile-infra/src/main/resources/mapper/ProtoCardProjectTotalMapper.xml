<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoCardProjectTotalMapper">

  <select id="countCardProject" parameterType="java.lang.String" resultType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardProjectTotalPO">
    select
          c.mrp_type mrp_type, -- 业务类型
          c.project_code project_code,  -- 项目编号
          c.list_date list_date,  -- 上市时间
          c.sku_code_type sku_code_type,  -- 类别
          sum(case when c.use_state = '1' and c.asset_newold = '1' then 1 else 0 end) zkys_num, -- 在库一手
          sum(case when c.use_state in('1','90') and c.asset_newold = '2' then 1 else 0 end) zkes_num, -- 在库二手
          sum(case when c.use_state in('2','4','6','94') and c.asset_newold = '1' then 1 else 0 end) yszy_num, -- 一手在用
          sum(case when c.use_state in('2','4','6','94') and c.asset_newold = '2' then 1 else 0 end) eszy_num, -- 二手在用
          sum(case when c.use_state = '96' then 1 else 0 end) wfgh_num, -- 无法归还
          sum(case when c.use_state = '92' then 1 else 0 end) ybf_num, -- 已报废
          sum(case when c.use_state = '13' then 1 else 0 end) ykk_num  -- 已扣款
        from proto_card c
        group by c.mrp_type,c.project_code,c.sku_code_type
  </select>
</mapper>