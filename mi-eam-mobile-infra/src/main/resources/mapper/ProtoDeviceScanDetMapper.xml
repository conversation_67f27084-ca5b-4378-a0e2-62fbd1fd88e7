<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoDeviceScanDetMapper">
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from proto_device_scan_det
    where det_id = #{detId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDeviceScanDetPO">
    insert into proto_device_scan_det (det_id, device_code, project_code, 
      stage_name, laser_code, imei, 
      device_type, sku_code, sku_name, 
      scan_result, remark, scan_id, 
      add_userid, add_date, modify_userid, 
      modify_date, tenant_id, plan_id, 
      is_error, end_date, list_date, 
      is_open, use_state, error_a, 
      error_b, error_c, error_d, 
      error_e, error_f, error_g, 
      error_h, total_error)
    values (#{detId,jdbcType=INTEGER}, #{deviceCode,jdbcType=VARCHAR}, #{projectCode,jdbcType=VARCHAR}, 
      #{stageName,jdbcType=VARCHAR}, #{laserCode,jdbcType=VARCHAR}, #{imei,jdbcType=VARCHAR}, 
      #{deviceType,jdbcType=VARCHAR}, #{skuCode,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, 
      #{scanResult,jdbcType=CHAR}, #{remark,jdbcType=VARCHAR}, #{scanId,jdbcType=VARCHAR}, 
      #{addUserid,jdbcType=VARCHAR}, #{addDate,jdbcType=TIMESTAMP}, #{modifyUserid,jdbcType=VARCHAR}, 
      #{modifyDate,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}, #{planId,jdbcType=VARCHAR}, 
      #{isError,jdbcType=CHAR}, #{endDate,jdbcType=TIMESTAMP}, #{listDate,jdbcType=TIMESTAMP}, 
      #{isOpen,jdbcType=CHAR}, #{useState,jdbcType=VARCHAR}, #{errorA,jdbcType=DECIMAL}, 
      #{errorB,jdbcType=DECIMAL}, #{errorC,jdbcType=DECIMAL}, #{errorD,jdbcType=DECIMAL}, 
      #{errorE,jdbcType=DECIMAL}, #{errorF,jdbcType=DECIMAL}, #{errorG,jdbcType=DECIMAL}, 
      #{errorH,jdbcType=DECIMAL}, #{totalError,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDeviceScanDetPO">
    insert into proto_device_scan_det
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="detId != null">
        det_id,
      </if>
      <if test="deviceCode != null">
        device_code,
      </if>
      <if test="projectCode != null">
        project_code,
      </if>
      <if test="stageName != null">
        stage_name,
      </if>
      <if test="laserCode != null">
        laser_code,
      </if>
      <if test="imei != null">
        imei,
      </if>
      <if test="deviceType != null">
        device_type,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="skuName != null">
        sku_name,
      </if>
      <if test="scanResult != null">
        scan_result,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="scanId != null">
        scan_id,
      </if>
      <if test="addUserid != null">
        add_userid,
      </if>
      <if test="addDate != null">
        add_date,
      </if>
      <if test="modifyUserid != null">
        modify_userid,
      </if>
      <if test="modifyDate != null">
        modify_date,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="planId != null">
        plan_id,
      </if>
      <if test="isError != null">
        is_error,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="listDate != null">
        list_date,
      </if>
      <if test="isOpen != null">
        is_open,
      </if>
      <if test="useState != null">
        use_state,
      </if>
      <if test="errorA != null">
        error_a,
      </if>
      <if test="errorB != null">
        error_b,
      </if>
      <if test="errorC != null">
        error_c,
      </if>
      <if test="errorD != null">
        error_d,
      </if>
      <if test="errorE != null">
        error_e,
      </if>
      <if test="errorF != null">
        error_f,
      </if>
      <if test="errorG != null">
        error_g,
      </if>
      <if test="errorH != null">
        error_h,
      </if>
      <if test="totalError != null">
        total_error,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="detId != null">
        #{detId,jdbcType=INTEGER},
      </if>
      <if test="deviceCode != null">
        #{deviceCode,jdbcType=VARCHAR},
      </if>
      <if test="projectCode != null">
        #{projectCode,jdbcType=VARCHAR},
      </if>
      <if test="stageName != null">
        #{stageName,jdbcType=VARCHAR},
      </if>
      <if test="laserCode != null">
        #{laserCode,jdbcType=VARCHAR},
      </if>
      <if test="imei != null">
        #{imei,jdbcType=VARCHAR},
      </if>
      <if test="deviceType != null">
        #{deviceType,jdbcType=VARCHAR},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null">
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="scanResult != null">
        #{scanResult,jdbcType=CHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="scanId != null">
        #{scanId,jdbcType=VARCHAR},
      </if>
      <if test="addUserid != null">
        #{addUserid,jdbcType=VARCHAR},
      </if>
      <if test="addDate != null">
        #{addDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyUserid != null">
        #{modifyUserid,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="planId != null">
        #{planId,jdbcType=VARCHAR},
      </if>
      <if test="isError != null">
        #{isError,jdbcType=CHAR},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="listDate != null">
        #{listDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isOpen != null">
        #{isOpen,jdbcType=CHAR},
      </if>
      <if test="useState != null">
        #{useState,jdbcType=VARCHAR},
      </if>
      <if test="errorA != null">
        #{errorA,jdbcType=DECIMAL},
      </if>
      <if test="errorB != null">
        #{errorB,jdbcType=DECIMAL},
      </if>
      <if test="errorC != null">
        #{errorC,jdbcType=DECIMAL},
      </if>
      <if test="errorD != null">
        #{errorD,jdbcType=DECIMAL},
      </if>
      <if test="errorE != null">
        #{errorE,jdbcType=DECIMAL},
      </if>
      <if test="errorF != null">
        #{errorF,jdbcType=DECIMAL},
      </if>
      <if test="errorG != null">
        #{errorG,jdbcType=DECIMAL},
      </if>
      <if test="errorH != null">
        #{errorH,jdbcType=DECIMAL},
      </if>
      <if test="totalError != null">
        #{totalError,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>