<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysOperLogMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysOperLogPO">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="do_user" jdbcType="VARCHAR" property="doUser" />
    <result column="do_date" jdbcType="TIMESTAMP" property="doDate" />
    <result column="opt_param" jdbcType="VARCHAR" property="optParam" />
    <result column="opt_code" jdbcType="VARCHAR" property="optCode" />
    <result column="opt_desc" jdbcType="VARCHAR" property="optDesc" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_code, do_user, do_date, opt_param, opt_code, opt_desc, add_userid, add_date, 
    modify_userid, modify_date
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sys_oper_log
    where id = #{id,jdbcType=INTEGER}
  </select>
</mapper>