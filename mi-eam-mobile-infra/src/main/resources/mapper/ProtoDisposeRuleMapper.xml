<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoDisposeRuleMapper">
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from proto_dispose_rule
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDisposeRulePO">
    insert into proto_dispose_rule (id, order_code, bus_sub_type, 
      remark, auditing, add_userid, 
      add_date, modify_userid, modify_date, 
      tenant_id, disposal_type)
    values (#{id,jdbcType=INTEGER}, #{orderCode,jdbcType=VARCHAR}, #{busSubType,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{auditing,jdbcType=VARCHAR}, #{addUserid,jdbcType=VARCHAR}, 
      #{addDate,jdbcType=TIMESTAMP}, #{modifyUserid,jdbcType=VARCHAR}, #{modifyDate,jdbcType=TIMESTAMP}, 
      #{tenantId,jdbcType=VARCHAR}, #{disposalType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDisposeRulePO">
    insert into proto_dispose_rule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="busSubType != null">
        bus_sub_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="auditing != null">
        auditing,
      </if>
      <if test="addUserid != null">
        add_userid,
      </if>
      <if test="addDate != null">
        add_date,
      </if>
      <if test="modifyUserid != null">
        modify_userid,
      </if>
      <if test="modifyDate != null">
        modify_date,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="disposalType != null">
        disposal_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="busSubType != null">
        #{busSubType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="auditing != null">
        #{auditing,jdbcType=VARCHAR},
      </if>
      <if test="addUserid != null">
        #{addUserid,jdbcType=VARCHAR},
      </if>
      <if test="addDate != null">
        #{addDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyUserid != null">
        #{modifyUserid,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="disposalType != null">
        #{disposalType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>