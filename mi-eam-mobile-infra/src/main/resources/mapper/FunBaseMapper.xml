<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.FunBaseMapper">

    <insert id="insertCommonValue">
        insert into  ${tableName}
        <foreach collection="valueMap.entrySet()" index="key" item="value" separator="," open="(" close=")">
            ${key}
        </foreach>
        values
        <foreach collection="valueMap.entrySet()" index="key" item="value" separator="," open="(" close=")">
            #{value}
        </foreach>
    </insert>

    <update id="updateCommonValue">
        update ${tableName} set
        <foreach collection="valueMap.entrySet()" index="key" item="value" separator=",">
            ${key} = #{value}
        </foreach>
        where ${pkCol} = #{sKeyValue}
    </update>

    <select id="getRepeatValue" resultType="java.lang.Integer">
        select count(1) from ${tableName}
        where ${colCode} = #{colName}
            <if test="keyId != null and keyId != ''">
                and ${colCode} != #{keyId}
            </if>
            <if test="fkValue != null and fkValue != '' and fkColName != null and fkColName != ''">
                and ${fkColName} = #{fkValue}
            </if>
    </select>

    <select id="getHaveAuditCount" resultType="java.lang.Integer">
        select count(1) from ${tableName} where ${pkCol} = #{keyId} and ${auditCol} = #{auditValue}
    </select>

    <select id="getCount" resultType="java.lang.Integer">
        select count(1) from ${tableName} where ${keyCol} = #{keyValue}
    </select>
</mapper>