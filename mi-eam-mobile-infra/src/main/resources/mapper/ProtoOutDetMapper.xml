<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoOutDetMapper">

  <select id="getApplyCode" resultType="java.lang.String">
    select o.apply_code
	from proto_out o, proto_out_det d
	where d.device_code = #{sn}
	  and o.out_status != '7'
	  and o.out_type = '1'
	  and o.out_id = d.out_id
  </select>

  <select id="getProtoOutSapSuccess" resultType="java.lang.Integer">
   select count(1)
    from proto_out o ,proto_out_det od
    where o.out_id = od.out_id and od.device_code = #{sn}
    and o.out_type = '1' and o.sap_ret_type = 'S'
  </select>

    <select id="getProtoOutDetSAPList" resultType="com.mi.oa.asset.mobile.infra.dto.sap.ProtoOutSapDTO">
        select out_sap_item_row_no, sap_item_row_no, sku_code, sku_name, count(sku_code) pushCount, order_code
            from proto_out_det where out_id = #{outId}
            group by out_sap_item_row_no, sap_item_row_no, sku_code, sku_name, order_code order by sku_code
    </select>

    <select id="selectProtoOutDetDeviceCodes" resultType="java.lang.String">
        select det.device_code from proto_out_det det
                 join proto_out pout
                 on det.out_id = pout.out_id
                 where det.device_code in
                   <foreach collection="list" item="deviceCodes" index="index" open="(" close=")" separator=",">
                        #{deviceCodes}
                   </foreach>
                 and pout.out_type = '1' and pout.out_status = '1'
    </select>
</mapper>