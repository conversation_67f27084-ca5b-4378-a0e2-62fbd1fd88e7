<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoPlanDetMapper">

    <select id="listByOrders"
            resultType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoPlanDetPO">
        select det.* from proto_plan_det det
        inner join proto_inner_purchase_plan pp on det.inner_purchase_plan_id = pp.id
        where
            (det.device_code, pp.order_no) in
            <foreach collection="orderList" item="item" open="(" separator="," close=")">
                (#{item.deviceCode}, #{item.planCode})
            </foreach>
    </select>
</mapper>