<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.BpmExeLogMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.BpmExeLogPO">
    <id column="exelog_id" jdbcType="VARCHAR" property="exelogId" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="order_name" jdbcType="VARCHAR" property="orderName" />
    <result column="do_user" jdbcType="VARCHAR" property="doUser" />
    <result column="do_date" jdbcType="TIMESTAMP" property="doDate" />
    <result column="exe_code" jdbcType="VARCHAR" property="exeCode" />
    <result column="exe_desc" jdbcType="VARCHAR" property="exeDesc" />
    <result column="fun_id" jdbcType="VARCHAR" property="funId" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.BpmExeLogPO">
    <result column="exe_body" jdbcType="LONGVARCHAR" property="exeBody" />
  </resultMap>
  <sql id="Base_Column_List">
    exelog_id, order_code, order_id, order_name, do_user, do_date, exe_code, exe_desc, 
    fun_id, add_userid, add_date, modify_userid, modify_date, tenant_id
  </sql>
  <sql id="Blob_Column_List">
    exe_body
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from bpm_exelog
    where exelog_id = #{exelogId,jdbcType=VARCHAR}
  </select>
</mapper>