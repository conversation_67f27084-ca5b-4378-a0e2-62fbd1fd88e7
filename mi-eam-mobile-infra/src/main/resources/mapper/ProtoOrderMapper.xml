<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoOrderMapper">

    <select id="planProgress" resultType="com.mi.oa.asset.mobile.application.dto.order.PlanProgressDTO">
        select
            sum(case when order_status = '0' then 1 else 0 end) waitSellNum,
            sum(case when order_status = '0' then pay_amount else 0 end) waitSellAmount,
            sum(case when order_status = '1' then 1 else 0 end) waitPayNum,
            sum(case when order_status = '1' then pay_amount else 0 end) waitPayAmount,
            sum(case when order_status = '2' then 1 else 0 end) completedNum,
            sum(case when order_status = '2' then pay_amount else 0 end) completedAmount,
            sum(case when order_type = '1'   then 1 else 0 end) soldOutNum,
            sum(case when order_type = '1'   then pay_amount else 0 end) soldOutAmount
        from proto_order
        where plan_code = #{planCode}
    </select>
</mapper>