<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoInDetMapper">
    <select id="getNotAsnPushSap" resultType="com.mi.oa.asset.mobile.infra.dto.sap.ProtoInSapDTO">
        select distinct o.order_code,o.sap_factory,det.in_code,o.bsart
        from proto_asn_order o, proto_in_det det
		where det.in_id =  #{inId}
		    and det.order_code = o.order_code
		    and det.auditing in ('1','3','4','5')
            and (det.sap_ret_type is null or det.sap_ret_type in ('E', 'N', ''))
    </select>
    <select id="getNotAsnDetPushSAPList" resultType="com.mi.oa.asset.mobile.infra.dto.sap.ProtoInNoAsnDTO">
        select item_row_no, sku_code, sku_name, count(1) cnt
        from proto_in_det
        where in_id = #{inId} and order_code = #{orderCode}
        group by item_row_no, sku_code, sku_name order by sku_code
    </select>
    <select id="getPushSapList" resultType="com.mi.oa.asset.mobile.infra.dto.sap.ProtoPushSapDTO">
        select item_row_no itemRowNo, sku_code skuCode, sku_name skuName, count(sku_code) countSku
        from proto_in_det where in_id = #{inId}
        group by item_row_no, sku_code, sku_name order by sku_code
    </select>
</mapper>
