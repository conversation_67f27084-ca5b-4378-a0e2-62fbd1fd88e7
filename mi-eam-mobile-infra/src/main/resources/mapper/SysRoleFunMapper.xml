<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysRoleFunMapper">
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from sys_role_fun
    where role_fun_id = #{roleFunId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysRoleFunPO">
    insert into sys_role_fun (role_fun_id, role_id, fun_id, 
      is_edit, is_print, is_audit, add_userid, 
      add_date, modify_userid, modify_date, 
      is_other, tenant_id)
    values (#{roleFunId,jdbcType=VARCHAR}, #{roleId,jdbcType=VARCHAR}, #{funId,jdbcType=VARCHAR}, 
      #{isEdit,jdbcType=CHAR}, #{isPrint,jdbcType=CHAR}, #{isAudit,jdbcType=CHAR}, #{addUserid,jdbcType=VARCHAR}, 
      #{addDate,jdbcType=TIMESTAMP}, #{modifyUserid,jdbcType=VARCHAR}, #{modifyDate,jdbcType=TIMESTAMP}, 
      #{isOther,jdbcType=CHAR}, #{tenantId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysRoleFunPO">
    insert into sys_role_fun
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roleFunId != null">
        role_fun_id,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="funId != null">
        fun_id,
      </if>
      <if test="isEdit != null">
        is_edit,
      </if>
      <if test="isPrint != null">
        is_print,
      </if>
      <if test="isAudit != null">
        is_audit,
      </if>
      <if test="addUserid != null">
        add_userid,
      </if>
      <if test="addDate != null">
        add_date,
      </if>
      <if test="modifyUserid != null">
        modify_userid,
      </if>
      <if test="modifyDate != null">
        modify_date,
      </if>
      <if test="isOther != null">
        is_other,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roleFunId != null">
        #{roleFunId,jdbcType=VARCHAR},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=VARCHAR},
      </if>
      <if test="funId != null">
        #{funId,jdbcType=VARCHAR},
      </if>
      <if test="isEdit != null">
        #{isEdit,jdbcType=CHAR},
      </if>
      <if test="isPrint != null">
        #{isPrint,jdbcType=CHAR},
      </if>
      <if test="isAudit != null">
        #{isAudit,jdbcType=CHAR},
      </if>
      <if test="addUserid != null">
        #{addUserid,jdbcType=VARCHAR},
      </if>
      <if test="addDate != null">
        #{addDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyUserid != null">
        #{modifyUserid,jdbcType=VARCHAR},
      </if>
      <if test="modifyDate != null">
        #{modifyDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isOther != null">
        #{isOther,jdbcType=CHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>