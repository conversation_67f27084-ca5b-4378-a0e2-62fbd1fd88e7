<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoApplyMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyPO">
    <id column="apply_id" jdbcType="VARCHAR" property="applyId" />
    <result column="apply_code" jdbcType="VARCHAR" property="applyCode" />
    <result column="auditing" jdbcType="CHAR" property="auditing" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="stage_name" jdbcType="VARCHAR" property="stageName" />
    <result column="pm_user_name" jdbcType="VARCHAR" property="pmUserName" />
    <result column="pm_emp_code" jdbcType="VARCHAR" property="pmEmpCode" />
    <result column="pm_user_code" jdbcType="VARCHAR" property="pmUserCode" />
    <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="apply_emp_code" jdbcType="VARCHAR" property="applyEmpCode" />
    <result column="apply_user_code" jdbcType="VARCHAR" property="applyUserCode" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="emp_code" jdbcType="VARCHAR" property="empCode" />
    <result column="user_code" jdbcType="VARCHAR" property="userCode" />
    <result column="apply_type" jdbcType="VARCHAR" property="applyType" />
    <result column="is_other" jdbcType="CHAR" property="isOther" />
    <result column="provider_code" jdbcType="VARCHAR" property="providerCode" />
    <result column="provider_name" jdbcType="VARCHAR" property="providerName" />
    <result column="provider_email" jdbcType="VARCHAR" property="providerEmail" />
    <result column="debit_onwer" jdbcType="CHAR" property="debitOnwer" />
    <result column="make_cn" jdbcType="VARCHAR" property="makeCn" />
    <result column="pre_project_code" jdbcType="VARCHAR" property="preProjectCode" />
    <result column="src_type" jdbcType="CHAR" property="srcType" />
    <result column="apply_status" jdbcType="CHAR" property="applyStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="plan_code" jdbcType="VARCHAR" property="planCode" />
    <result column="plan_id" jdbcType="VARCHAR" property="planId" />
    <result column="apply_user_type" jdbcType="VARCHAR" property="applyUserType" />
    <result column="business_key" jdbcType="VARCHAR" property="businessKey" />
    <result column="check_user" jdbcType="VARCHAR" property="checkUser" />
    <result column="check_user_code" jdbcType="VARCHAR" property="checkUserCode" />
    <result column="check_emp_code" jdbcType="VARCHAR" property="checkEmpCode" />
    <result column="center_code" jdbcType="VARCHAR" property="centerCode" />
    <result column="sap_ret_type" jdbcType="VARCHAR" property="sapRetType" />
    <result column="comp_dept_code" jdbcType="VARCHAR" property="compDeptCode" />
    <result column="leader" jdbcType="VARCHAR" property="leader" />
    <result column="house_code" jdbcType="VARCHAR" property="houseCode" />
    <result column="house_name" jdbcType="VARCHAR" property="houseName" />
  </resultMap>
  <sql id="Base_Column_List">
    apply_id, apply_code, auditing, project_code, stage_name, pm_user_name, pm_emp_code, 
    pm_user_code, dept_code, dept_name, apply_user_name, apply_emp_code, apply_user_code, 
    user_name, emp_code, user_code, apply_type, is_other, provider_code, provider_name, 
    provider_email, debit_onwer, make_cn, pre_project_code, src_type, apply_status, remark, 
    add_userid, add_date, modify_userid, modify_date, tenant_id, plan_code, plan_id, 
    apply_user_type, business_key, check_user, check_user_code, check_emp_code, center_code, 
    sap_ret_type, comp_dept_code, leader, house_code, house_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from proto_apply
    where apply_id = #{applyId,jdbcType=VARCHAR}
  </select>
</mapper>