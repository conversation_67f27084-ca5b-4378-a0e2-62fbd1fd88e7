<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoSecondCardMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoSecondCardPO">
    <id column="device_id" jdbcType="VARCHAR" property="deviceId" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="stage_name" jdbcType="VARCHAR" property="stageName" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="house_code" jdbcType="VARCHAR" property="houseCode" />
    <result column="house_name" jdbcType="VARCHAR" property="houseName" />
    <result column="is_complete" jdbcType="CHAR" property="isComplete" />
    <result column="num" jdbcType="DECIMAL" property="num" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    device_id, project_code, stage_name, sku_code, sku_name, house_code, house_name, 
    is_complete, num, add_userid, add_date, modify_userid, modify_date, tenant_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from proto_second_card
    where device_id = #{deviceId,jdbcType=VARCHAR}
  </select>
</mapper>