<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysUserRoleMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysUserRolePO">
    <id column="user_role_id" jdbcType="VARCHAR" property="userRoleId" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="role_id" jdbcType="VARCHAR" property="roleId" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    user_role_id, user_id, role_id, add_userid, add_date, modify_userid, modify_date, 
    tenant_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sys_user_role
    where user_role_id = #{userRoleId,jdbcType=VARCHAR}
  </select>
    <select id="getUserIdsByRoleNo" resultType="java.lang.String">
      SELECT DISTINCT
        u.user_id
      FROM
        sys_role r
          LEFT JOIN sys_user_role u ON r.role_id = u.role_id
      WHERE
        r.role_no = #{roleNo,jdbcType=VARCHAR}
    </select>
</mapper>