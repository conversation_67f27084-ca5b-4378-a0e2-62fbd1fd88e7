<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoCardDeptproTotalMapper">
  <select id="statisticsByDeptLevel"
          resultType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoCardDeptproTotalPO">

    SELECT d.parent_id deptCode, t.project_code projectCode, t.sku_code_type skuCodeType,t.mrp_type,
           sum(t.dgh_num) dghNum, sum(t.zyz_num) zyzNum,
           sum(t.ghz_num) ghzNum, sum(t.wfghz_num) wfghzNum
    from proto_card_deptpro_total t
           inner join sys_dept d on t.dept_code = d.dept_id
    where d.dept_level = #{deptLevel}
    and t.mrp_type = #{mrpType}
    GROUP BY d.parent_id, t.project_code,t.sku_code_type
  </select>
</mapper>