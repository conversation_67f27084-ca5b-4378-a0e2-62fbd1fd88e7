<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoApplyMatMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoApplyMatPO">
    <id column="apply_mat_id" jdbcType="VARCHAR" property="applyMatId" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="apply_num" jdbcType="DECIMAL" property="applyNum" />
    <result column="secret_num" jdbcType="DECIMAL" property="secretNum" />
    <result column="det_remark" jdbcType="VARCHAR" property="detRemark" />
    <result column="apply_id" jdbcType="VARCHAR" property="applyId" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="out_num" jdbcType="DECIMAL" property="outNum" />
    <result column="due_claimed_num" jdbcType="DECIMAL" property="dueClaimedNum" />
    <result column="out_open_num" jdbcType="DECIMAL" property="outOpenNum" />
    <result column="sap_item_row_no" jdbcType="VARCHAR" property="sapItemRowNo" />
  </resultMap>
  <sql id="Base_Column_List">
    apply_mat_id, sku_code, sku_name, apply_num, secret_num, det_remark, apply_id, add_userid, 
    add_date, modify_userid, modify_date, tenant_id, out_num, due_claimed_num, out_open_num, 
    sap_item_row_no
  </sql>
</mapper>