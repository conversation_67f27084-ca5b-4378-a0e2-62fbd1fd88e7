<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.MailTemplateParamMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MailTemplateParamPO">
    <id column="param_id" jdbcType="VARCHAR" property="paramId" />
    <result column="param_index" jdbcType="DECIMAL" property="paramIndex" />
    <result column="param_name" jdbcType="VARCHAR" property="paramName" />
    <result column="param_title" jdbcType="VARCHAR" property="paramTitle" />
    <result column="param_value" jdbcType="VARCHAR" property="paramValue" />
    <result column="template_id" jdbcType="VARCHAR" property="templateId" />
    <result column="param_type" jdbcType="VARCHAR" property="paramType" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
  </resultMap>
  <sql id="Base_Column_List">
    param_id, param_index, param_name, param_title, param_value, template_id, param_type, 
    add_userid, add_date, modify_userid, modify_date, tenant_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mail_template_param
    where param_id = #{paramId,jdbcType=VARCHAR}
  </select>
</mapper>