<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoDelayMatMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoDelayMatPO">
    <id column="delay_mat_id" jdbcType="VARCHAR" property="delayMatId" />
    <result column="project_code" jdbcType="VARCHAR" property="projectCode" />
    <result column="stage_name" jdbcType="VARCHAR" property="stageName" />
    <result column="device_type" jdbcType="VARCHAR" property="deviceType" />
    <result column="laser_code" jdbcType="VARCHAR" property="laserCode" />
    <result column="imei" jdbcType="VARCHAR" property="imei" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="delay_id" jdbcType="VARCHAR" property="delayId" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />
    <result column="sku_name" jdbcType="VARCHAR" property="skuName" />
    <result column="delay_date" jdbcType="TIMESTAMP" property="delayDate" />
    <result column="asset_newold" jdbcType="CHAR" property="assetNewold" />
  </resultMap>
  <sql id="Base_Column_List">
    delay_mat_id, project_code, stage_name, device_type, laser_code, imei, device_code, 
    end_date, add_userid, add_date, modify_userid, modify_date, tenant_id, delay_id, 
    sku_code, sku_name, delay_date, asset_newold
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from proto_delay_mat
    where delay_mat_id = #{delayMatId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from proto_delay_mat
    where delay_mat_id = #{delayMatId,jdbcType=VARCHAR}
  </delete>
</mapper>