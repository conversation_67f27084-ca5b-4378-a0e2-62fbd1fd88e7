<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.MailTemplateMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MailTemplatePO">
    <id column="template_id" jdbcType="VARCHAR" property="templateId" />
    <result column="template_tag" jdbcType="VARCHAR" property="templateTag" />
    <result column="template_name" jdbcType="VARCHAR" property="templateName" />
    <result column="fun_id" jdbcType="VARCHAR" property="funId" />
    <result column="is_default" jdbcType="CHAR" property="isDefault" />
    <result column="is_attach" jdbcType="CHAR" property="isAttach" />
    <result column="fun_name" jdbcType="VARCHAR" property="funName" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="edit_user" jdbcType="VARCHAR" property="editUser" />
    <result column="edit_userid" jdbcType="VARCHAR" property="editUserid" />
    <result column="edit_date" jdbcType="TIMESTAMP" property="editDate" />
    <result column="template_type" jdbcType="CHAR" property="templateType" />
    <result column="apply_user_type" jdbcType="VARCHAR" property="applyUserType" />
    <result column="query_sql" jdbcType="VARCHAR" property="querySql" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.MailTemplatePO">
    <result column="template_cont" jdbcType="LONGVARCHAR" property="templateCont" />
  </resultMap>
  <sql id="Base_Column_List">
    template_id, template_tag, template_name, fun_id, is_default, is_attach, fun_name, 
    add_userid, add_date, modify_userid, modify_date, tenant_id, edit_user, edit_userid, 
    edit_date, template_type, apply_user_type, query_sql
  </sql>
  <sql id="Blob_Column_List">
    template_cont
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from mail_template
    where template_id = #{templateId,jdbcType=VARCHAR}
  </select>
</mapper>