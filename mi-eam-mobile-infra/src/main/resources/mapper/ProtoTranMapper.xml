<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.ProtoTranMapper">

    <select id="getNoAsnOrderTranList" resultType="com.mi.oa.asset.mobile.infra.dto.sap.ProtoTranSapDTO">
        select pt.tran_id,pt.tran_code,pt.user_name,det.sap_ret_type,det.is_success
        from proto_tran pt
        join proto_tran_det det on pt.tran_id = det.tran_id
        join proto_out pout on pout.out_code = det.out_code
        where pt.auditing = '3'
              and det.asset_newold = '1'
              and pt.mrp_type = 'mobile'
              and (det.sap_ret_type in ('E','N') or det.is_success in ('E','N') )
              and pout.out_status = '2'
              and pout.sap_ret_type = 'S'
            order by pt.modify_date
    </select>
    <select id="getTranList" resultType="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.ProtoTranPO">
        select * from proto_tran
        where 1 = 1
             <if test="whereSql != null and whereSql != ''">
                 and ${whereSql}
             </if>
             <include refid="tranCondition"/>
             order by tran_code desc
        limit #{start}, #{limit}
    </select>
    <select id="countTranList" resultType="java.lang.Integer">
        select count(1) from proto_tran
        where 1 = 1
        <if test="whereSql != null and whereSql != ''">
            and ${whereSql}
        </if>
        <include refid="tranCondition"/>
    </select>

    <sql id="tranCondition">
        <!-- 不是管理员时 -->
        <if test="count == 0 ">
            and  (
            ((proto_tran.acc_user_code = #{userCode} and proto_tran.auditing in( '1','2','3','7')) or proto_tran.user_code = #{userCode})
            <!-- 部门权限 -->
            <if test="deptCodeList != null and deptCodeList.size() > 0">
                or (
                <foreach collection="deptCodeList" separator="or" open="(" close=")" item="item">
                    proto_tran.dept_code like concat(#{item}, '%')
                    or proto_tran.in_dept_code like concat(#{item}, '%')
                </foreach>
                )
            </if>
            )
        </if>
        <if test="conditionList != null and conditionList.size() > 0">
            and (
            <foreach collection="conditionList" open="(" close=")" separator="and" item="item">
                <if test="item.conds == 'like'">
                    ${item.names} like concat('%', #{item.values}, '%')
                </if>
                <if test="item.conds == 'llike'">
                    ${item.names} like concat(#{item.values}, '%')
                </if>
                <if test="item.conds != 'like' and #{item.conds} != 'llike'">
                    ${item.names} = #{item.values}
                </if>
            </foreach>
            )
        </if>
    </sql>
</mapper>