<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.oa.asset.mobile.infra.repository.mybatis.mapper.SysDeptMapper">
  <resultMap id="BaseResultMap" type="com.mi.oa.asset.mobile.infra.repository.mybatis.entity.SysDeptPO">
    <id column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="dept_code" jdbcType="VARCHAR" property="deptCode" />
    <result column="dept_name" jdbcType="VARCHAR" property="deptName" />
    <result column="dept_level" jdbcType="DECIMAL" property="deptLevel" />
    <result column="manager" jdbcType="VARCHAR" property="manager" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="is_novalid" jdbcType="VARCHAR" property="isNovalid" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="dept_type" jdbcType="VARCHAR" property="deptType" />
    <result column="add_userid" jdbcType="VARCHAR" property="addUserid" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="modify_userid" jdbcType="VARCHAR" property="modifyUserid" />
    <result column="modify_date" jdbcType="TIMESTAMP" property="modifyDate" />
    <result column="sign_pic" jdbcType="VARCHAR" property="signPic" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="center_name" jdbcType="VARCHAR" property="centerName" />
    <result column="center_code" jdbcType="VARCHAR" property="centerCode" />
    <result column="bus_dept_code" jdbcType="VARCHAR" property="busDeptCode" />
    <result column="bus_dept_name" jdbcType="VARCHAR" property="busDeptName" />
    <result column="comp_dept_code" jdbcType="VARCHAR" property="compDeptCode" />
    <result column="fac_dept_code" jdbcType="VARCHAR" property="facDeptCode" />
    <result column="fac_dept_id" jdbcType="VARCHAR" property="facDeptId" />
    <result column="bus_dept_id" jdbcType="VARCHAR" property="busDeptId" />
    <result column="address_name" jdbcType="VARCHAR" property="addressName" />
    <result column="address_id" jdbcType="VARCHAR" property="addressId" />
    <result column="begin_date" jdbcType="TIMESTAMP" property="beginDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="long_dept_name" jdbcType="VARCHAR" property="longDeptName" />
    <result column="psection" jdbcType="VARCHAR" property="psection" />
    <result column="comp_dept_name" jdbcType="VARCHAR" property="compDeptName" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="is_valid" jdbcType="CHAR" property="isValid" />
  </resultMap>
  <sql id="Base_Column_List">
    dept_id, dept_code, dept_name, dept_level, manager, phone, is_novalid, memo, dept_type, 
    add_userid, add_date, modify_userid, modify_date, sign_pic, tenant_id, center_name, 
    center_code, bus_dept_code, bus_dept_name, comp_dept_code, fac_dept_code, fac_dept_id, 
    bus_dept_id, address_name, address_id, begin_date, end_date, long_dept_name, psection, 
    comp_dept_name, parent_id, is_valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sys_dept
    where dept_id = #{deptId,jdbcType=VARCHAR}
  </select>
</mapper>