<?xml version="1.0"?>
<!DOCTYPE module PUBLIC
        "-//Puppy Crawl//DTD Check Configuration 1.3//EN"
        "http://www.puppycrawl.com/dtds/configuration_1_3.dtd">

<module name="Checker">

<!--    &lt;!&ndash; 文件长度不超过1500行 &ndash;&gt;-->
<!--    <module name="FileLength">-->
<!--        <property name="max" value="1500"/>-->
<!--    </module>-->

<!--    &lt;!&ndash; 每个java文件一个语法树 &ndash;&gt;-->
<!--    <module name="TreeWalker">-->
<!--        &lt;!&ndash; import检查&ndash;&gt;-->
<!--        &lt;!&ndash; 检查是否import了违法的包。默认拒绝import所有sun.*包。 &ndash;&gt;-->
<!--        <module name="IllegalImport"/>-->
<!--        &lt;!&ndash; 检查是否有重复的import。 &ndash;&gt;-->
<!--        <module name="RedundantImport"/>-->
<!--        &lt;!&ndash; 没用的import检查，比如：1.没有被用到 2.重复的 3.import java.lang的 4.import 与该类在同一个package的 &ndash;&gt;-->
<!--&lt;!&ndash;        <module name="UnusedImports"/>&ndash;&gt;-->

<!--        &lt;!&ndash; 注释检查 &ndash;&gt;-->
<!--        &lt;!&ndash; 检查类和接口的javadoc 默认不检查author 和version tags-->
<!--            authorFormat: 检查author标签的格式-->
<!--			versionFormat: 检查version标签的格式-->
<!--			scope: 可以检查的类的范围，例如：public只能检查public修饰的类，private可以检查所有的类-->
<!--			excludeScope: 不能检查的类的范围，例如：public，public的类将不被检查，但访问权限小于public的类仍然会检查，其他的权限以此类推-->
<!--			tokens: 该属性适用的类型，例如：CLASS_DEF,INTERFACE_DEF &ndash;&gt;-->
<!--&lt;!&ndash;        <module name="JavadocType">&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="tokens" value="INTERFACE_DEF"/>&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="allowUnknownTags" value="true"/>&ndash;&gt;-->
<!--&lt;!&ndash;            <message key="javadoc.missing" value="接口或类缺少Javadoc注释。"/>&ndash;&gt;-->
<!--&lt;!&ndash;        </module>&ndash;&gt;-->

<!--        &lt;!&ndash; 检查方法的javadoc的注释-->
<!--			scope: 可以检查的方法的范围，例如：public只能检查public修饰的方法，private可以检查所有的方法-->
<!--			allowMissingParamTags: 是否忽略对参数注释的检查-->
<!--			allowMissingThrowsTags: 是否忽略对throws注释的检查-->
<!--			allowMissingReturnTag: 是否忽略对return注释的检查 &ndash;&gt;-->
<!--&lt;!&ndash;        <module name="JavadocMethod">&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="scope" value="private"/>&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="allowMissingParamTags" value="false"/>&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="allowMissingThrowsTags" value="true"/>&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="allowMissingReturnTag" value="true"/>&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="tokens" value="METHOD_DEF"/>&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="allowUndeclaredRTE" value="true"/>&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="allowThrowsTagsForSubclasses" value="true"/>&ndash;&gt;-->
<!--&lt;!&ndash;            &lt;!&ndash;允许get set 方法没有注释&ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="allowMissingPropertyJavadoc" value="true"/>&ndash;&gt;-->
<!--&lt;!&ndash;            <message key="javadoc.missing" value="方法缺少Javadoc注释。"/>&ndash;&gt;-->
<!--&lt;!&ndash;        </module>&ndash;&gt;-->

<!--        &lt;!&ndash; 命名检查 &ndash;&gt;-->
<!--        &lt;!&ndash; 局部的final变量，包括catch中的参数的命名规则检查，默认^[a-z][a-zA-Z0-9]*(_[a-zA-Z0-9]+)*$ &ndash;&gt;-->
<!--        <module name="LocalFinalVariableName"/>-->
<!--        &lt;!&ndash; 局部的非final型的变量，包括catch中的参数的命名规则检查，默认^[a-z][a-zA-Z0-9]*(_[a-zA-Z0-9]+)*$ &ndash;&gt;-->
<!--        <module name="LocalVariableName"/>-->
<!--        &lt;!&ndash; 包名的检查（只允许小写字母），默认^[a-z]+(\.[a-zA-Z_][a-zA-Z_0-9_]*)*$ &ndash;&gt;-->
<!--        <module name="PackageName">-->
<!--            <property name="format" value="^[a-z]+(\.[a-z][a-z0-9]*)*$"/>-->
<!--            <message key="name.invalidPattern" value="包名 ''{0}'' 要符合 ''{1}''格式."/>-->
<!--        </module>-->
<!--        &lt;!&ndash; 仅仅是static型的变量（不包括static final型）的检查，默认^[a-z][a-zA-Z0-9]*(_[a-zA-Z0-9]+)*$ &ndash;&gt;-->
<!--        <module name="StaticVariableName"/>-->
<!--        &lt;!&ndash; Class或Interface名检查，默认^[A-Z][a-zA-Z0-9]*$&ndash;&gt;-->
<!--        <module name="TypeName">-->
<!--            <property name="severity" value="warning"/>-->
<!--            <message key="name.invalidPattern" value="名称 ''{0}'' 要符合 ''{1}''格式."/>-->
<!--        </module>-->
<!--        &lt;!&ndash; 非static型变量的检查 &ndash;&gt;-->
<!--        <module name="MemberName"/>-->
<!--        &lt;!&ndash; 方法名的检查 &ndash;&gt;-->
<!--        <module name="MethodName"/>-->
<!--        &lt;!&ndash; 方法的参数名 &ndash;&gt;-->
<!--        <module name="ParameterName "/>-->
<!--        &lt;!&ndash; 常量名的检查（只允许大写），默认^[A-Z][A-Z0-9]*(_[A-Z0-9]+)*$ &ndash;&gt;-->
<!--        <module name="ConstantName"/>-->

<!--        &lt;!&ndash; 定义检查 &ndash;&gt;-->
<!--        &lt;!&ndash; 检查数组类型定义的样式 &ndash;&gt;-->
<!--        <module name="ArrayTypeStyle"/>-->
<!--        &lt;!&ndash; 检查long型定义是否有大写的“L” &ndash;&gt;-->
<!--        <module name="UpperEll"/>-->

<!--        &lt;!&ndash; 长度检查 &ndash;&gt;-->
<!--        &lt;!&ndash; 每行不超过160个字符 &ndash;&gt;-->
<!--&lt;!&ndash;        <module name="LineLength">&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="max" value="160"/>&ndash;&gt;-->
<!--&lt;!&ndash;        </module>&ndash;&gt;-->
<!--        &lt;!&ndash; 方法不超过60行 去除 &ndash;&gt;-->
<!--&lt;!&ndash;        <module name="MethodLength">&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="tokens" value="METHOD_DEF"/>&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="max" value="60"/>&ndash;&gt;-->
<!--&lt;!&ndash;        </module>&ndash;&gt;-->
<!--        &lt;!&ndash; 方法的参数个数不超过5个。 并且不对构造方法进行检查&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="ParameterNumber">&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="max" value="5"/>&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="ignoreOverriddenMethods" value="true"/>&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="tokens" value="METHOD_DEF"/>&ndash;&gt;-->
<!--&lt;!&ndash;        </module>&ndash;&gt;-->

<!--        &lt;!&ndash; 空格检查&ndash;&gt;-->
<!--        &lt;!&ndash; 方法名后跟左圆括号"(" &ndash;&gt;-->
<!--&lt;!&ndash;        <module name="MethodParamPad"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 在类型转换时，不允许左圆括号右边有空格，也不允许与右圆括号左边有空格 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="TypecastParenPad"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查在某个特定关键字之后应保留空格 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="NoWhitespaceAfter"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查在某个特定关键字之前应保留空格 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="NoWhitespaceBefore"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 操作符换行策略检查 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="OperatorWrap"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 圆括号空白 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="ParenPad"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查分隔符是否在空白之后 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="WhitespaceAfter"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查分隔符周围是否有空白 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="WhitespaceAround"/>&ndash;&gt;-->

<!--        &lt;!&ndash; 修饰符检查 &ndash;&gt;-->
<!--        &lt;!&ndash; 检查修饰符的顺序是否遵照java语言规范，默认public、protected、private、abstract、static、final、transient、volatile、synchronized、native、strictfp &ndash;&gt;-->
<!--&lt;!&ndash;        <module name="ModifierOrder"/>&ndash;&gt;-->
<!--        &lt;!&ndash; 检查接口和annotation中是否有多余修饰符，如接口方法不必使用public &ndash;&gt;-->
<!--        <module name="RedundantModifier"/>-->

<!--        &lt;!&ndash; 代码块检查 &ndash;&gt;-->
<!--        &lt;!&ndash; 检查是否有嵌套代码块 &ndash;&gt;-->
<!--&lt;!&ndash;        <module name="AvoidNestedBlocks"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查是否有空代码块 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="EmptyBlock"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查左大括号位置 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="LeftCurly"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查代码块是否缺失{} &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="NeedBraces"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查右大括号位置 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="RightCurly"/>&ndash;&gt;-->

<!--        &lt;!&ndash; 代码检查 &ndash;&gt;-->
<!--        &lt;!&ndash; 检查空的代码段 &ndash;&gt;-->
<!--&lt;!&ndash;        <module name="EmptyStatement"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查在重写了equals方法后是否重写了hashCode方法 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="EqualsHashCode"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查局部变量或参数是否隐藏了类中的变量 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="HiddenField">&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="tokens" value="VARIABLE_DEF"/>&ndash;&gt;-->
<!--&lt;!&ndash;        </module>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查子表达式中是否有赋值操作 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="InnerAssignment"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查switch语句是否有default &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="MissingSwitchDefault"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查是否有过度复杂的布尔表达式 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="SimplifyBooleanExpression"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查是否有过于复杂的布尔返回代码段 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="SimplifyBooleanReturn"/>&ndash;&gt;-->

<!--        &lt;!&ndash; 类设计检查 &ndash;&gt;-->
<!--        &lt;!&ndash; 检查类是否为扩展设计l &ndash;&gt;-->
<!--        &lt;!&ndash; 检查只有private构造函数的类是否声明为final &ndash;&gt;-->
<!--&lt;!&ndash;        <module name="FinalClass"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查接口是否仅定义类型 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="InterfaceIsType"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查类成员的可见度 检查类成员的可见性。只有static final 成员是public的&ndash;&gt;-->
<!--&lt;!&ndash;        除非在本检查的protectedAllowed和packagedAllowed属性中进行了设置&ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="VisibilityModifier">&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="packageAllowed" value="true"/>&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="protectedAllowed" value="true"/>&ndash;&gt;-->
<!--&lt;!&ndash;        </module>&ndash;&gt;-->

<!--        &lt;!&ndash; 语法 &ndash;&gt;-->
<!--        &lt;!&ndash; String的比较不能用!= 和 == &ndash;&gt;-->
<!--&lt;!&ndash;        <module name="StringLiteralEquality"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 限制for循环最多嵌套3层 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="NestedForDepth">&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="max" value="3"/>&ndash;&gt;-->
<!--&lt;!&ndash;        </module>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; if最多嵌套3层 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="NestedIfDepth">&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="max" value="3"/>&ndash;&gt;-->
<!--&lt;!&ndash;        </module>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 检查未被注释的main方法,排除以Appllication或者Test结尾命名的类 &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="UncommentedMain">&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="excludedClasses" value=".*[Application,Test]$"/>&ndash;&gt;-->
<!--&lt;!&ndash;        </module>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; 禁止使用System.out.println &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="Regexp">&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="format" value="System\.out\.println"/>&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="illegalPattern" value="true"/>&ndash;&gt;-->
<!--&lt;!&ndash;        </module>&ndash;&gt;-->
<!--        &lt;!&ndash; return个数 3个  去除&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="ReturnCount">&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="max" value="3"/>&ndash;&gt;-->
<!--&lt;!&ndash;        </module>&ndash;&gt;-->
<!--        &lt;!&ndash;try catch 异常处理数量 3&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="NestedTryDepth ">&ndash;&gt;-->
<!--&lt;!&ndash;            <property name="max" value="3"/>&ndash;&gt;-->
<!--&lt;!&ndash;        </module>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; clone方法必须调用了super.clone() &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="SuperClone"/>&ndash;&gt;-->
<!--&lt;!&ndash;        &lt;!&ndash; finalize 必须调用了super.finalize() &ndash;&gt;&ndash;&gt;-->
<!--&lt;!&ndash;        <module name="SuperFinalize"/>&ndash;&gt;-->
<!--    </module>-->
</module>
