#!/bin/bash

prefix=""

if `git status | grep "master" &>/dev/null`; then
	prefix="prod"
elif `git status | grep "test" &>/dev/null`; then
    prefix="test"
elif `git status | grep "pre" &>/dev/null`; then
    prefix="pre"
else
   echo "mast checkout branch RELEASE or testing"
   exit
fi

function mi_tag() {
    git push
    git pull --tags
}

function build_tag_push() {
    local new_tag=$(echo ${prefix}-$(date +'%Y%m%d')-$(git tag -l "${prefix}-$(date +'%Y%m%d')-*" | wc -l | xargs printf '%02d'))
    echo ${new_tag}
    git tag -a ${new_tag} -m 'create new RELEASE tag'
    git push origin ${new_tag}
}

function print_time() {
  timeStr=$(date '+%Y-%m-%d %H:%M:%S')
  echo '############## build time ################'
  echo '#                                        #'
  echo "#          $timeStr           #"
  echo '#                                        #'
  echo '##########################################'
}

mi_tag;
build_tag_push;
print_time;
