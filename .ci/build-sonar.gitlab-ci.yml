#目前使用的sonar，暂时不用
sonar:
  stage: sonar
  image: hub.pf.xiaomi.com/infoarch/maven-openjdk8-onbuild-sonar:latest
  script:
    - curl -s https://git.n.xiaomi.com/oaucf-share/oaucf-script-utils/raw/master/sonar_analyze_gitn.sh | bash /dev/stdin -Dsonar.projectKey=oa:asset:mobile -Dsonar.projectName=oa:asset:mobile
  only:
    - master
  artifacts:
    paths:
      - "**/target"
      - "target"


# fusion-cli是云平台提供的命令行工具, 提供了sonar扫描的能力，暂时不使用此能力
#.fusion-cli: &fusion-cli
#  image:
#    name: cr.d.xiaomi.net/container/xiaomi_alpine_fusion_cli
#  before_script:
#    - fusion-cli sonar config --token ${CI_SONAR_TOKEN}

# sonar阶段会依赖build和test的结果, 另外pom.xml里需要配置jacoco-maven-plugin，暂时不用
#sonar:
#  stage: sonar
#  dependencies:
#    - build
#    - test
#  <<: *fusion-cli
#  script:
#    - fusion-cli sonar scan --badges --report --sources mi-eam-mobile-interface/mi-eam-mobile-api/src/main,mi-eam-mobile-interface/mi-eam-mobile-task/src/main,mi-eam-mobile-infra/src/main,mi-eam-mobile-application/src/main --tests mi-eam-mobile-interface/mi-eam-mobile-api/src/test -- -Dsonar.java.binaries=mi-eam-mobile-interface/mi-eam-mobile-api/target/classes,mi-eam-mobile-interface/mi-eam-mobile-task/target/classes,mi-eam-mobile-infra/target/classes,mi-eam-mobile-application/target/classes -Dsonar.coverage.jacoco.xmlReportPaths=mi-eam-mobile-interface/mi-eam-mobile-api/target/jacoco.xml -Dsonar.junit.reportPaths=mi-eam-mobile-interface/mi-eam-mobile-api/target/surefire-reports
#  only:
#    refs:
#      - merge_requests
#      - master
