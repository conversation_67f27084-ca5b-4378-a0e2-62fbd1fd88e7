build:api-test:
  extends:
    - .build-image:kaniko
  variables:
    PROFILE_NAME: test
    SERVICE_NAME: mi-eam-mobile-api
    IMAGE: ${CI_REGISTRY_IMAGE}/${SERVICE_NAME}/${PROFILE_NAME}:${CI_COMMIT_TAG}
  only:
    - /^test-.*$/

build:api-pre:
  extends:
    - .build-image:kaniko
  variables:
    PROFILE_NAME: pre
    SERVICE_NAME: mi-eam-mobile-api
    IMAGE: ${CI_REGISTRY_IMAGE}/${SERVICE_NAME}/${PROFILE_NAME}:${CI_COMMIT_TAG}
  only:
    - /^pre-.*$/
    - /^hotfix-.*$/

build:api-prod:
  extends:
    - .build-image:kaniko
  variables:
    PROFILE_NAME: prod
    SERVICE_NAME: mi-eam-mobile-api
    IMAGE: ${CI_REGISTRY_IMAGE}/${SERVICE_NAME}/${PROFILE_NAME}:${CI_COMMIT_TAG}
  only:
    - /^prod-.*$/
